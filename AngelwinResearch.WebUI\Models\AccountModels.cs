﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace AngelwinResearch.WebUI.Models
{
    public class AccountModels
    {
    }

    public class MenuTreeDTO
    {
        public int MenuID { get; set; }

        public string MenuName { get; set; }

        public string MenuIcon { get; set; }

        public int MenuOrder { get; set; }

        public int ParentID { get; set; }

        public string MenuArea { get; set; }
        public string MenuController { get; set; }
        public string MenuAction { get; set; }
        public string MenuNote { get; set; }

        public List<MenuTreeDTO> Children { get; set; }
    }

    public class RoleNewViewModel
    {
        public string Description { get; set; }
        public int Id { get; set; }
        [Required(AllowEmptyStrings = false)]
        public string Name { get; set; }

    }

    public class LayUIMenuTreeDTO
    {
        public int id { get; set; }
        public string title { get; set; }
        public bool @checked { get; set; }
        public bool spread { get; set; }
        public int menuOrder { get; set; }
        public List<LayUIMenuTreeDTO> children { get; set; }

    }

    public class RegisterViewModel
    {
        [Display(Name = "用户名")]
        [Required]
        public string UserName { get; set; }
        [Required]
        public string TrueName { get; set; }
        public string PhoneNumber { get; set; }
        public string Email { get; set; }
        public string TwoFactorEnabled { get; set; }
        public string DDUserId { get; set; }
        public int? HospitalDeptId { get; set; }
        public string EmployeeNum { get; set; }

        public int MultiCenterId { get; set; }

    }
    public class EditUserViewModel
    {
        [Required]
        public int Id { get; set; }
        [Required]
        [Display(Name = "用户名")]
        public string UserName { get; set; }
        public string TrueName { get; set; }

        public int? HospitalDeptId { get; set; }

        public string PhoneNumber { get; set; }
        public string Email { get; set; }

        public string TwoFactorEnabled { get; set; }
        public string IsLockedOut { get; set; }
        public string LockoutEnd { get; set; }
        public string StopUsing { get; set; }
        public string DDUserId { get; set; }
        public string EmployeeNum { get; set; }

        public int MultiCenterId { get; set; }
    }

    public class AddViewDeptsModel
    {
        public string DeptCode { get; set; }
        public string Id { get; set; }
        public string DeptName { get; set; }
        public string DeptIntro { get; set; }
        public string Level { get; set; }
        public int ParentId { get; set; }


    }

    public class EditViewDeptsModel
    {
        public int Id { get; set; }
        public string DeptCode { get; set; }

        public string DeptName { get; set; }
        public string DeptIntro { get; set; }
        public string Level { get; set; }
        public string ParentId { get; set; }


    }

    public class MenuPartialDTO
    {
        public List<MenuTreeDTO> MenuTreeDTOList { get; set; }
        public string Ticket { get; set; }

        public string UserName { get; set; }
    }
}
