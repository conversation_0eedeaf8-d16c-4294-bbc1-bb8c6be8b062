#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\StructuredData\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "2baccc0db1329f847da1670df687c4023827b3042de7288cd0b46d185d32fd6f"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_Demo_Views_StructuredData_Index), @"mvc.1.0.view", @"/Areas/Demo/Views/StructuredData/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"2baccc0db1329f847da1670df687c4023827b3042de7288cd0b46d185d32fd6f", @"/Areas/Demo/Views/StructuredData/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_Demo_Views_StructuredData_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<
#nullable restore
#line 5 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\StructuredData\Index.cshtml"
       AngelwinResearch.Models.ResearchPatient

#line default
#line hidden
#nullable disable
    >
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/muihk/css/mui.min.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/font/eyes_icon/iconfont.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/jquery/dist/jquery.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/muihk/js/mui.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\StructuredData\Index.cshtml"
  
    ViewBag.Title = "数据采集";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("<!DOCTYPE html>\r\n<html lang=\"en\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2baccc0db1329f847da1670df687c4023827b3042de7288cd0b46d185d32fd6f8058", async() => {
                WriteLiteral("\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\">\r\n\r\n    <meta http-equiv=\"X-UA-Compatible\" content=\"ie=edge\">\r\n    <title>数据采集</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "2baccc0db1329f847da1670df687c4023827b3042de7288cd0b46d185d32fd6f8591", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "2baccc0db1329f847da1670df687c4023827b3042de7288cd0b46d185d32fd6f9793", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "2baccc0db1329f847da1670df687c4023827b3042de7288cd0b46d185d32fd6f10995", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "2baccc0db1329f847da1670df687c4023827b3042de7288cd0b46d185d32fd6f12198", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2baccc0db1329f847da1670df687c4023827b3042de7288cd0b46d185d32fd6f13405", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2baccc0db1329f847da1670df687c4023827b3042de7288cd0b46d185d32fd6f14529", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"

    <style>

        /* 移动端头部 */
        .headrow {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            background-color: #f6f6f6;
            height: 38px;
            line-height: 38px;
        }

            .headrow .layui-btn-primary {
                background-color: transparent;
                border: none;
                color: #333;
            }
        /* 移动端头部 */



        .flex_row {
            display: flex;
            flex-direction: row;
        }

        .layui-card {
            margin-bottom: 5px;
        }

        .content-inline {
            display: flex;
            flex-direction: row;
            line-height: 34px;
            padding-left: 20px;
        }

        .patient_information {
            align-items: center;
        }

        .user_information {
            flex: 1;
            flex-wrap: wrap;
            background-color: #fff;
            padding: 1");
                WriteLiteral(@"0px 0;
        }

        .user_name {
            padding-right: 20px;
            line-height: 38px;
            font-size: 18px;
        }

        .user_value {
            color: #000;
            padding-left: 10px;
        }

        .reporting_content {
            position: relative;
        }

        .content_R {
            position: relative;
            flex: 1;
        }

        .sex {
            font-size: 14px;
            font-weight: normal;
        }

        .sex0 {
            color: #FF5722;
        }

        .sex1 {
            color: #1E9FFF;
        }

        .reporting {
            overflow: hidden;
        }

        .form_name {
            margin-bottom: 15px;
        }

        .nav_item {
            background-color: #fff;
            border: 1px solid #eee;
            padding: 10px 15px;
            border-radius: 4px;
            margin: 0 5px 10px;
        }

        .active {
            background: linear-gradient");
                WriteLiteral(@"(70deg, #dcffff, #62ffff);
            border-color: transparent;
            color: #1E9FFF;
        }

            .active .layui-progress-text {
                color: #1E9FFF;
            }

        .layui-colla-content {
            padding: 0;
        }

            .layui-colla-content .layui-card-body {
                padding: 10px 0;
            }

        .search_hidden {
            text-align: center;
        }


            .search_hidden i {
                font-size: 22px;
                margin-right: 5px;
            }
        /* 折叠状态下左侧菜单的宽度 */
        .sider_btn {
            position: absolute;
            display: block;
            left: -5px;
            bottom: 10%;
            z-index: 99;
            line-height: 0;
            padding: 18px 0px 18px 0px;
            border: none;
            color: #fff;
            border-radius: 0 50% 50% 0;
            background: linear-gradient(to right, #56d1f9, #1eaddc);
        }

        .sider_btn_");
                WriteLiteral(@"R {
            position: absolute;
            display: block;
            right: -5px;
            bottom: 10%;
            z-index: 99;
            line-height: 0;
            padding: 18px 0px 18px 4px;
            border: none;
            color: #fff;
            border-radius: 50% 0 0 50%;
            background: linear-gradient(to right, #56d1f9, #1eaddc);
        }


        .sider_btn i {
            font-size: 22px !important;
            padding-right: 10px;
        }

        .sider_btn_R i {
            font-size: 22px !important;
            padding-left: 5px;
            padding-right: 5px;
        }

        .Menu_side {
            width: 210px;
            overflow-y: auto;
            overflow-x: hidden;
            transition: width 0.5s ease;
        }

            .Menu_side.folded {
                width: 0px;
            }


        .Menu_sideR {
            width: 0px;
            overflow-y: auto;
            overflow-x: hidden;
            ");
                WriteLiteral(@"transition: width 0.5s ease;
        }

            .Menu_sideR.folded {
                width: 316px;
            }

            .Menu_sideR .layui-textarea {
                min-height: 100% !important;
                resize: none;
                font-size: 14px;
            }

        .side_menu_title {
            font-size: 18px;
            color: #000;
            text-align: center;
            line-height: 30px;
            padding: 10px 0;
        }

        .content_L {
            overflow-y: auto;
        }

        .mui-btn .layui-icon {
            font-size: 24px;
        }

        .hide_report_list {
            padding: 15px;
        }

        .stretch {
            padding-right: 20px;
            flex-grow: 1;
        }

        .collapse_list {
            overflow-y: auto;
        }

        .null_wrap {
            text-align: center;
            margin-top: 10%;
        }

            .null_wrap img {
                width: 200px;
  ");
                WriteLiteral("          }\r\n    </style>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2baccc0db1329f847da1670df687c4023827b3042de7288cd0b46d185d32fd6f21684", async() => {
                WriteLiteral(@"
    <div class=""wrap"">
        <div class=""head_wrap patient_information"" id=""IpadHead"">
            <div class=""headrow"">
                <div class=""left"">
                    <button id=""previous"" class=""layui-btn layui-btn-primary layui-border-green"" style=""margin-right:15px;display:none""><i class=""layui-icon layui-icon-return""></i></button>
                </div>
                <div class=""middle"">
                    <h2 class=""user_name"">");
                Write(
#nullable restore
#line 263 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\StructuredData\Index.cshtml"
                                           Model?.PatientName

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(" <span");
                BeginWriteAttribute("class", " class=\"", 6431, "\"", 6483, 1);
                WriteAttributeValue("", 6439, 
#nullable restore
#line 263 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\StructuredData\Index.cshtml"
                                                                             Model?.Sex=="男" ? "sex sex1" : "sex sex0"

#line default
#line hidden
#nullable disable
                , 6439, 44, false);
                EndWriteAttribute();
                WriteLiteral(">");
                Write(
#nullable restore
#line 263 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\StructuredData\Index.cshtml"
                                                                                                                          Model.Sex

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</span></h2>
                </div>
                <div class=""right"">
                    <div id=""popbtn"" style=""padding-right:10px;"">
                        <i class=""layui-icon layui-icon-survey"" style=""font-size:24px;""></i>
                    </div>

                </div>
            </div>
            <div class=""user_information flex_row"">
                <div class=""content-inline"">编号：<p class=""user_value"">");
                Write(
#nullable restore
#line 273 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\StructuredData\Index.cshtml"
                                                                      Model?.ResearchID

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</p></div>\r\n                <div class=\"content-inline\">专病组：<p class=\"user_value\">");
                Write(
#nullable restore
#line 274 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\StructuredData\Index.cshtml"
                                                                       Model?.DiseaseSpecificGroup.GroupName

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</p></div>
                <div class=""content-inline""> <button class=""layui-btn layui-btn-primary"" id=""btnAI"" title=""AI提取数据""><i class=""layui-icon icon-zhinengwendangchouqu""></i></button></div>
                <div class=""content-inline""> <button class=""layui-btn layui-btn-primary"" id=""btnAI2"" title=""AI提取数据""><i class=""layui-icon icon-zhinengwendangchouqu""></i></button></div>

            </div>
        </div>

");
                WriteLiteral(@"
        <div class=""reporting_content flex_row"">
            <!-- 左侧折叠菜单 -->
            <div id=""sideMenu"" class=""Menu_side content_L layui-card mui-table-view"">
                <div class=""layui-collapse"" id=""OA_task_1"">
                    <div class=""collapse_list""></div>
                </div>
            </div>

            <div class=""layui-card mui-table-view"" style=""width:260px;position:relative;"">
                <div id=""trees"" style=""height:100%""></div>

                <!--菜单折叠-->
                <button type=""button"" id=""toggleSidebar"" class=""layui-btn layui-btn-sm layui-btn-primary sider_btn"">
                    <i class=""layui-icon layui-icon-left"" style=""font-size:22px;""></i>
                </button>

            </div>

            <!-- 填报内容区域 -->
            <div id=""mainContent"" class=""reporting content_R"">



                <!-- 这里是主体内容 -->
                <iframe id=""reportingFrom"" frameborder=""0"" width=""100%"" height=""100%""></iframe>


                <!--");
                WriteLiteral(@"折叠消息-->
                <button type=""button"" id=""toggleSidebarR"" class=""layui-btn layui-btn-sm layui-btn-primary sider_btn_R"">
                    <i class=""layui-icon layui-icon-left"" style=""font-size:22px;""></i>
                </button>


            </div>

            <!-- 右侧折叠菜 -->
            <div id=""sideMenuR"" class=""Menu_sideR content_L layui-card mui-table-view"">
                <div class=""layui-form layui-form-pane"" style=""height:100%"">
                    <div class=""layui-form-item layui-form-text"" style=""height:100%"">

                        <div class=""layui-input-block"" style=""height:100%"">
                            <label class=""layui-form-label"" style=""text-align:center"">原始数据</label>
                            <table class=""layui-hide"" id=""tbResult"" lay-filter=""tbResult""></table>
                        </div>
                    </div>
                </div>




            </div>

        </div>


    </div>

    <!--隐藏报表列表-->
    <div id=""hideReport"" ");
                WriteLiteral(@"style=""display:none"">
        <div class=""hide_report_list"">
            <ul id=""ulHideList"">
                <li class=""nav_item flex_row"" data-id="""" data-formId="""">
                    <div class=""stretch"">
                        <p class=""form_name"">随访信息-影像学检查-IM</p>
                        <div class=""layui-progress"" lay-showPercent=""true"">
                            <div class=""layui-progress-bar layui-bg-blue"" lay-percent=""5/10""></div>
                        </div>
                    </div>
                    <a class=""mui-btn mui-btn-red show_report"">
                        <i class=""layui-icon layui-icon-add-circle""></i>
                    </a>
                </li>
            </ul>
        </div>
    </div>
    <div style=""display: none;"" id=""isShow"">
        <div class=""mui-popup mui-popup-in"">
            <div class=""mui-popup-inner"">
                <div class=""mui-popup-title"">提示</div>
                <div class=""mui-popup-text"">是否展示当前报表？</div>
            </div>
  ");
                WriteLiteral(@"          <div class=""mui-popup-buttons"">
                <span class=""mui-popup-button Confirmation_btn"">确认</span>
                <span class=""mui-popup-button mui-popup-button-bold"">取消</span>
            </div>
        </div>
        <div class=""mui-popup-backdrop mui-active""></div>
    </div>

    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2baccc0db1329f847da1670df687c4023827b3042de7288cd0b46d185d32fd6f29081", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2baccc0db1329f847da1670df687c4023827b3042de7288cd0b46d185d32fd6f30205", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2baccc0db1329f847da1670df687c4023827b3042de7288cd0b46d185d32fd6f31329", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    <!--滑动删除插件-->\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2baccc0db1329f847da1670df687c4023827b3042de7288cd0b46d185d32fd6f32474", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"

    <script>
        layui.use(['element', 'layer', 'table', 'laydate', 'form', 'tree'], function() {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , laydate = layui.laydate
                , tree = layui.tree
                , $ = layui.$
                , form = layui.form;

            if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
                $(""#previous"").show();
            }
            else {
                $(""#previous"").hide();
            }
            $(""#previous"").click(function() {
                window.location.href = ""/ReportingManage/PatientManage/Index"";
            })

            $(""#btnAI"").click(function() {
                AIExtract();
            });

            $(""#btnAI2"").click(function() {
                AIExtract2();
   ");
                WriteLiteral(@"         })

            $(""#popbtn"").on(""click"", function() {
                layer.open({
                    type: 1,
                    area: ['60%', '80%'],
                    resize: false,
                    shadeClose: true,
                    title: '帮助',
                    content: $(""#popwrap""),
                    success: function() {
                        // $(""#model_wrapL"").val(modelText);
                    }
                })
            })
            // 点击折叠按钮时触发的函数
            document.getElementById('toggleSidebar').addEventListener('click', function() {

                var sideMenu = document.getElementById('sideMenu');

                var spreadIcon = document.getElementById('toggleSidebar');

                // 切换folded类来实现折叠效果
                sideMenu.classList.toggle('folded');

                // 根据菜单的状态切换图标显示
                if (sideMenu.classList.contains('folded')) {
                    spreadIcon.children[0].classList.remove('layui-icon-lef");
                WriteLiteral(@"t'); // 显示展开图标
                    spreadIcon.children[0].classList.add('layui-icon-right'); // 隐藏展开图标
                } else {
                    spreadIcon.children[0].classList.remove('layui-icon-right'); // 显示展开图标
                    spreadIcon.children[0].classList.add('layui-icon-left'); // 隐藏展开图标
                }

                // 如果需要，可以通过Layui重新初始化相关元素，以保证样式和事件正常工作
                element.init();



            });

            document.getElementById('toggleSidebarR').addEventListener('click', function() {


                var sideMenuR = document.getElementById('sideMenuR');

                var spreadIconR = document.getElementById('toggleSidebarR');



                // 切换folded类来实现折叠效果
                sideMenuR.classList.toggle('folded');

                console.log(""sideMenuR"", sideMenuR);

                // 根据菜单的状态切换图标显示
                if (sideMenuR.classList.contains('folded')) {
                    spreadIconR.children[0].classList.remove('layui-icon-le");
                WriteLiteral(@"ft');
                    spreadIconR.children[0].classList.add('layui-icon-right');
                } else {
                    spreadIconR.children[0].classList.remove('layui-icon-right');
                    spreadIconR.children[0].classList.add('layui-icon-left');
                };

                // 使用Layui的element模块重新初始化相关元素
                element.init();
            });


            // 动态添加菜单项示例（如果需要）
            // $('#navMenu').append('');



            //患者列表的高度
            function setReportingContenttH() {
                var winH = $(window).height();
                var informationH = $("".patient_information"").height();
                var menuH = $("".Menu_side "").height();
                // var menuTitleH = $("".side_menu_title"").height();
                // if (menuTitleH == 0) {
                //     menuTitleH = 106;
                // }

                var contentH = winH - informationH - 15 + ""px"";
                $("".reporting_content"").css(""height"", co");
                WriteLiteral(@"ntentH);

                // var collapseListH = winH - informationH - menuTitleH - 45 + ""px"";
                // console.log(""collapse_list:"", collapseListH);
                // $("".collapse_list"").css(""height"", collapseListH);
            }
            setReportingContenttH();

            $(window).resize(function() {
                setReportingContenttH();
            });

            var isSwiping = false;

            //模拟器
            function detectScreenOrientation() {
                var userAgent = navigator.userAgent.toLowerCase();
                if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
                    isSwiping = true;

                    //ipad端                    var orientation = window.orientation;
                    if (orientation === 0 || orientation === 180) {
                        // 竖屏状态
                        cons");
                WriteLiteral(@"ole.log(""竖屏"");
                    } else if (orientation === 90 || orientation === -90) {
                        $(document).ready(function() {
                            $('#LAY_app').removeClass('layadmin-side-shrink');
                        });
                        // 横屏状态
                        console.log(""横屏"");
                    }
                } else {
                    //pc端
                    isSwiping = false;
                    // $("".patient_btn"").removeClass(""patient_btn_1"").addClass(""patient_btn_2"");
                    // $("".patient_info"").removeClass(""flex-row"").addClass(""flex-column"");
                }
            }

            // 初始化时检查一次屏幕方向

            detectScreenOrientation();
            var id = 0;
            var firstId = 0;
            var formId = """";

            function getFormLit() {
                var patientId = '");
                Write(
#nullable restore
#line 548 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\StructuredData\Index.cshtml"
                                  Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"';

                $.get({
                    url: '/Demo/StructuredData/GetCRFList?ResearchPatientId=' + patientId, // 你的请求URL
                    async: false, // 设置为false以使请求同步执行
                    success: function(res) {
                        if (res.code == 0) {
                            var htm = """";
                            $("".layui-collapse"").html("""");
                            $.each(res.data, function(index, item) {

                                htm += `<div class=""layui-colla-item"">
                                                                <h2 class=""layui-colla-title"">`+ item.title + `</h2>

                                                                <div class=""layui-colla-content layui-show"">

                                                                    <ul class=""navMenu layui-card-body"" lay-filter=""navMenu"">`;
                                var childHtm = """";

                                $.each(item.children, function(index1, child) {
");
                WriteLiteral(@"                                    // console.log(index1);
                                    if (index1 == 0 && index == 0) {
                                        firstId = child.id;
                                    }
                                    childHtm += `<li class=""nav_item mui-table-view-cell"" data-id=""` + child.id + `"" data-formId=""` + child.formId + `"" data-formName=""` + child.title + `"">
                                                                            <div class=""mui-slider-handle"">
                                                                                <p class=""form_name"">`+ child.title + `</p>
                                                                                <div class=""layui-progress"" lay-showPercent=""true"">
                                                                                    <div class=""layui-progress-bar layui-bg-blue"" lay-percent=""`+ child.per + `""></div>
                                                                   ");
                WriteLiteral(@"             </div>
                                                                            </div>
                                                                            <div class=""mui-slider-right mui-disabled"">
                                                                                                        <a class=""mui-btn mui-btn-red""><i class=""layui-icon icon-yingcangxinxi""></i></a>
                                                                            </div>
                                                                        </li>`;
                                });

                                htm += childHtm + `</ul>
                                                                </div>
                                                            </div>`;

                            })

                            htm = ` <div class=""side_menu_title"">
                                                        <div>基线</div>
                                  ");
                WriteLiteral(@"                          <div class=""search_hidden"" id=""ShowHidden"">
                                                            <button type=""button"" class=""layui-btn layui-btn-xs layui-btn-primary"">
                                                            <i class=""layui-icon icon-shuyi_yanjing-xianshi""></i>查看隐藏表单
                                                            </button>
                                                            </div></div>` + htm;

                            $("".layui-collapse"").append(htm);
                            element.render('collapse');
                            // 渲染进度条组件
                            element.render('progress');

                            //报表切换事件
                            $("".navMenu"").on(""click"", "".nav_item"", function() {
                                $("".nav_item"").removeClass(""active"");
                                $(this).addClass(""active"");
                                var randVersion = Math.random();

      ");
                WriteLiteral("                          formId = $(this).attr(\"data-formId\");\r\n                                id = $(this).attr(\"data-id\");\r\n                                var url = \"");
                Write(
#nullable restore
#line 612 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\StructuredData\Index.cshtml"
                                             $"{ViewBag.formUrl}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\" + formId + \"");
                Write(
#nullable restore
#line 612 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\StructuredData\Index.cshtml"
                                                                                  $".form?token={ViewBag.token}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"&version="" + randVersion;

                                $(""#reportingFrom"").attr(""src"", url);

                                if (isSwiping) {
                                    document.getElementById('toggleSidebar').click();
                                }
                                var patientId = '");
                Write(
#nullable restore
#line 619 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\StructuredData\Index.cshtml"
                                                  Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"';
                                setTimeout(function() {
                                    loadData(patientId, id);
                                }, 1500);

                                //如果是人口学资料 显示AI提取按钮
                                var formName = $(this).attr(""data-formName"");
                                // if (formName.indexOf(""人口学"") != -1 || formName.indexOf(""基本信息"") != -1) {
                                //     $(""#btnAI"").show();
                                //     $(""#toggleSidebarR"").show();
                                // }
                                // else {
                                //     $(""#btnAI"").hide();
                                //      $(""#toggleSidebarR"").hide();
                                // }
                            })

                            $(""#ShowHidden"").on(""click"", function() {
                                layer.open({
                                    type: 1
                                    , title");
                WriteLiteral(@": '已隐藏的表单'
                                    , area: ['70%', '80%']
                                    , shade: 0.3
                                    , content: $('#hideReport')
                                    , success: function(layero, index) {
                                        var id = 0;
                                        $(""#ulHideList"").on(""click"", "".show_report"", function() {
                                            $('#isShow').show();
                                            id = $(this).attr(""data-id"");
                                            var elem = this;
                                            $(""#layui-layer-shade1"").css(""z-index"", ""9999998"");
                                        })
                                        $("".Confirmation_btn"").on(""click"", function(event) {
                                            event.stopImmediatePropagation();
                                            var obj = { id: id };
                           ");
                WriteLiteral(@"                 $.post('/Demo/StructuredData/RemoveHide', obj, function(res) {
                                                if (res.code == 0) {
                                                    getHideList();
                                                    getFormLit();
                                                }
                                                else {
                                                    layer.msg(""操作失败"");
                                                }
                                            })
                                            $('#isShow').hide();
                                        })
                                        $("".mui-popup-button-bold"").on(""click"", function() {
                                            $('#isShow').hide();
                                        })
                                    }
                                });

                            })

                        }
       ");
                WriteLiteral(@"             }
                });
            }

            getFormLit();


            //右滑影藏start
            mui.init();
            $('#OA_task_1').on('tap', '.mui-btn', function(event) {
                var elem = this;
                var li = elem.parentNode.parentNode;
                // 在这个例子中，我们只关心确认按钮的回调
                mui.confirm('是否隐藏当前填报表单？', '提示', btnArray, function(e) {
                    // 这里是点击确认按钮后的回调函数
                    console.log('用户点击了确认按钮');
                    // 在这里执行你想要在点击确认后进行的操作
                    if (e.index == 0) {
                        var crformId = li.getAttribute(""data-id"");
                        var patid = '");
                Write(
#nullable restore
#line 693 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\StructuredData\Index.cshtml"
                                      Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"'
                        var obj = { CRFormId: crformId, ResearchPatientId: patid };
                        $.post('/Demo/StructuredData/HideForm', obj, function(res) {
                            if (res.code == 0) {
                                getHideList();
                                li.parentNode.removeChild(li);
                            }
                            else {
                                layer.msg(res.msg);
                            }
                        })
                    }
                }, function() {
                    // 这里是点击取消按钮后的回调函数
                    console.log('用户点击了取消按钮');
                });
            });
            var btnArray = ['确认', '取消'];
            //右滑影藏end




            //第一次默认选中第一个;
            // 选择data-id为的li元素
            var targetLi = $('li[data-id=""' + firstId + '""]');
            // 模拟点击事件
            targetLi.trigger('click');

            // 父窗口
            window.addEventListener('message', ");
                WriteLiteral("function(event) {\r\n                if (event.data.action === \'save\') {\r\n                    var ResearchPatientId = \'");
                Write(
#nullable restore
#line 725 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\StructuredData\Index.cshtml"
                                              Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"'
                    var formData = event.data.data;
                    var obj = { ResearchPatientId: ResearchPatientId, CRFormId: id, CRFJsonValue: formData };
                    $.post('/Demo/StructuredData/SaveForm', obj, function(res) {
                        if (res.code == 0) {
                            getFormLit();
                            // 选择data-id为的li元素
                            var targetLi = $('li[data-id=""' + id + '""]');

                            // 模拟点击事件
                            targetLi.trigger('click');
                            layer.msg(""操作成功"");
                        }
                        else
                            layer.msg(""操作失败"");

                    })

                }

            }, false);

            function loadData(ResearchPatientId, formId) {
                var url = ""/Demo/StructuredData/LoadFormData?ResearchPatientId="" + ResearchPatientId + ""&CRFormId="" + formId;
                $.get(url, function(res) {
       ");
                WriteLiteral(@"             var iframe = document.getElementById('reportingFrom');

                    if (res.code == 0) {
                        //  var iframe = document.getElementById('reportingFrom');
                        // 发送消息到子页面
                        iframe.contentWindow.postMessage({ action: ""show"", data: res.data }, ""*"");
                    }
                    else {
                        // 发送消息到子页面
                        iframe.contentWindow.postMessage({ action: ""show"" }, ""*"");
                    }
                    $(""#model_wrapR"").val(res.tips);

                })

            }

            function getHideList() {
                var patientId = '");
                Write(
#nullable restore
#line 768 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\StructuredData\Index.cshtml"
                                  Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"';
                $.get('/Demo/StructuredData/getHideList?ResearchPatientId=' + patientId, function(res) {

                    if (res.code == 0) {
                        $(""#ulHideList"").html("""");

                        if (res.data.length > 0) {
                            $.each(res.data, function(index, item) {
                                var htm = `<li class=""nav_item flex_row"" data-id=""` + item.id + `""><div class=""stretch"" >
                                                <p class=""form_name"">`+ item.formName + `</p>
                                                <div class=""layui-progress"" lay-showPercent=""true"" style='display:none'>
                                                    <div class=""layui-progress-bar layui-bg-blue"" lay-percent=""5/10""></div>
                                                </div>
                                            </div >
                                                    <a class=""mui-btn mui-btn-red show_report"" data-id=""` + item.id + `""");
                WriteLiteral(@">
                                                                <i class=""layui-icon icon-shuyi_yanjing-xianshi"" data-id=""` + item.id + `""></i>
                                                    </a>
                                        </li >`;
                                $(""#ulHideList"").append(htm);

                            })

                        } else {
                            var nullDataHtml = `<div class=""null_wrap"">
                                                                    <img src=""/images/datanull.png"" />
                                                                    <p>暂无隐藏表单</p>
                                                                </div>`;
                            $(""#ulHideList"").append(nullDataHtml);
                        }
                    }
                })
            }
            var timer;
            function AIExtract() {
                var loadIndex = layer.open({
                    type: 1,
            ");
                WriteLiteral(@"        area: ['600px', '49px'],
                    shade: 0.1,
                    closeBtn: 0,
                    resize: false,
                    title: false,
                    content: $(""#form_window_load""),
                    success: function() {
                        // $(""#model_wrapL"").val(modelText);
                        element.progress('demo-filter-progress', '0%');
                        load();
                    }
                });
                var selectTrees = tree.getChecked('demo-id-1');
                console.log(selectTrees);
                var patientId = '");
                Write(
#nullable restore
#line 818 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\StructuredData\Index.cshtml"
                                  Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"';
                $.post('/Demo/StructuredData/AIExtract', { ""ResearchPatientId"": patientId, ""formId"": formId, ""tips"": $(""#model_wrapR"").val(), ""treeNode"": selectTrees }, function(res) {

                    element.progress('demo-filter-progress', '100%');
                    clearInterval(timer);
                    if (res.code == 0) {
                        //$(""#txtAIResult"").val(res.result);
                        table.reload(""tbResult"", {
                            data: res.result
                        })
                        var iframe = document.getElementById('reportingFrom');
                        // 发送消息到子页面
                        iframe.contentWindow.postMessage({ data: res.data }, ""*"");
                    }
                    else
                        layer.msg(res.msg);
                    layer.close(loadIndex);
                    element.progress('demo-filter-progress', '0%');
                })
            }

            function AIExtract2() {
     ");
                WriteLiteral(@"           var loadIndex = layer.open({
                    type: 1,
                    area: ['600px', '49px'],
                    shade: 0.1,
                    closeBtn: 0,
                    resize: false,
                    title: false,
                    content: $(""#form_window_load""),
                    success: function() {
                        // $(""#model_wrapL"").val(modelText);
                        element.progress('demo-filter-progress', '0%');
                        load();
                    }
                });
                var patientId = '");
                Write(
#nullable restore
#line 854 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\StructuredData\Index.cshtml"
                                  Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"';
                $.post('/Demo/StructuredData/GetDemoList', { ""ResearchPatientId"": patientId, ""formId"": formId, ""tips"": $(""#model_wrapR"").val() }, function(res) {
                    element.progress('demo-filter-progress', '100%');
                    clearInterval(timer);
                    if (res.code == 0) {
                        table.reload(""tbResult"", {
                            data: res.result
                        })
                        var iframe = document.getElementById('reportingFrom');
                        // 发送消息到子页面
                        iframe.contentWindow.postMessage({ data: res.data }, ""*"");
                    }
                    else
                        layer.msg(res.msg);
                    layer.close(loadIndex);
                    element.progress('demo-filter-progress', '0%');
                })
            }

            getHideList();

            function load() {
                var n = 0;
                timer = setInterval(func");
                WriteLiteral(@"tion() {
                    n = n + Math.random() * 10 | 0;
                    if (n > 99) {
                        n = 99;
                        clearInterval(timer);

                    }
                    element.progress('demo-filter-progress', n + '%');
                }, 300 + Math.random() * 4000);
            }

            var heights = $("".reporting_content"").height();
            heights -= 38;
            table.render({
                elem: '#tbResult'
                , id: 'tbResult',

                data: []
                , limit: Number.MAX_VALUE
                , cols: [[
                    { field: 'zizeng', title: '', type: 'numbers' }
                    , { field: 'fields', title: '字段名', width: 110 }
                    , { field: 'fieldsValue', title: '字段值' }

                ]],
                height: heights
                , page: false // 关闭分页
            });

            var treeNodeId = 0;
            var patientId = 0;
            functi");
                WriteLiteral(@"on getTree() {

                $.get(""/Demo/StructuredData/getTreeList"", function(res) {

                    // 渲染
                    tree.render({
                        elem: '#trees',
                        data: res.data,
                        edit: [""preview""],
                        showCheckbox: true,
                        onlyIconControl: true,  // 是否仅允许节点左侧图标控制展开收缩
                        id: 'demo-id-1',
                        isJump: true, // 是否允许点击节点时弹出新窗口跳转
                        click: function(obj) {
                            var data = obj.data;  //获取当前点击的节点数据
                            //  layer.msg('状态：' + obj.state + '<br>节点数据：' + JSON.stringify(data));
                            console.log(obj);
                            //obj.elem.addClass(""active"");
                            // treeNodeId = data.id;
                            //   patientId = data.patientId;
                            //  $(""#trees"").find("".layui-tree-txt"").removeClass(""active"")");
                WriteLiteral(@";
                            //  $(obj.elem).find("".layui-tree-txt"").eq(0).addClass(""active"");


                        },
                        oncheck: function(obj) {
                            // console.log(obj.data); // 得到当前点击的节点数据
                            // console.log(obj.checked); // 节点是否被选中
                            //a79c1970fb4a4d29a1a865db532296c9
                            if (obj.checked) {
                                var iframeSrc = $(""#reportingFrom"").attr(""src"");
                                if (iframeSrc.indexOf(""a79c1970fb4a4d29a1a865db532296c9"") == -1) {
                                    var targetLi2 = $('li[data-formid=""a79c1970fb4a4d29a1a865db532296c9""]');

                                    // 模拟点击事件
                                    targetLi2.trigger('click');

                                    var listContainer = $(""#sideMenu"");
                                    // 计算目标 li 元素相对于列表容器的偏移量
                                    var targetOffs");
                WriteLiteral(@"et = targetLi2.offset().top - listContainer.offset().top + listContainer.scrollTop() - 300;

                                    // 滚动到目标位置
                                    listContainer.scrollTop(targetOffset);
                                }

                            }


                        },
                        operate: function(obj) {
                            var type = obj.type; //得到操作类型：add、edit、del
                            var data = obj.data; //得到当前节点的数据
                            var elem = obj.elem; //得到当前节点元素

                            //Ajax 操作
                            var id = data.id; //得到节点索引
                            if (type === 'preview') { //增加节点
                                //返回 key 值
                                if (data.docmentType == ""文件"") {
                                    layer.open({
                                        type: 2,
                                        title: 'PDF预览',
                                   ");
                WriteLiteral(@"     resize: true,
                                        maxmin: true,
                                        shadeClose: true, // 点击遮罩关闭层
                                        area: ['90%', '90%'], // 设置弹窗大小
                                        content: data.fileUrl
                                    });
                                }
                                else {
                                    layer.msg(""当前目录不支持预览！"");
                                }

                            }
                        }
                    });
                })
            }
            getTree();
        })
    </script>



");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"

<div class=""window_wrap"" id=""form_window_load"" style=""display: none;background-color:transparent"">
    <div class=""layui-progress layui-progress-big"" lay-showPercent=""true"" lay-filter=""demo-filter-progress"">
        <div class=""layui-progress-bar"" lay-percent=""0%"">
        </div>

    </div>
    <p style=""text-align:center""> AI数据提取中...</p>
</div>

<div id=""popwrap"" style=""display:none;height:100%;overflow:hidden;"">
    <div class=""layui-row layui-col-space30"" style=""height:100%"">
        <div class=""layui-col-md12"" style=""height:100%"">
            <div style=""background-color: #fff;height: 100%; padding:15px 15px;"">
                <textarea id=""model_wrapR"" class=""layui-textarea"" style="" height:100%;width:100%;line-height:40px;font-size:20px;resize:none"">

</textarea>
            </div>
        </div>
    </div>


</div>
</html>
");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<AngelwinResearch.Models.ResearchPatient> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
