#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\PatientDiscoveryManage\Views\DataCollectionManagement\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "91383ca4464572b4524d63d06267f5a9d4a13792e27e760835ad0a2cbce006bc"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_PatientDiscoveryManage_Views_DataCollectionManagement_Index), @"mvc.1.0.view", @"/Areas/PatientDiscoveryManage/Views/DataCollectionManagement/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"91383ca4464572b4524d63d06267f5a9d4a13792e27e760835ad0a2cbce006bc", @"/Areas/PatientDiscoveryManage/Views/DataCollectionManagement/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_PatientDiscoveryManage_Views_DataCollectionManagement_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("type", new global::Microsoft.AspNetCore.Html.HtmlString("text/javascript"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/echarts/echarts.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/echarts/theme/walden.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\PatientDiscoveryManage\Views\DataCollectionManagement\Index.cshtml"
  
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "91383ca4464572b4524d63d06267f5a9d4a13792e27e760835ad0a2cbce006bc6476", async() => {
                WriteLiteral("\r\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n    <meta charset=\"utf-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>数据采集管理驾驶舱</title>\r\n\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "91383ca4464572b4524d63d06267f5a9d4a13792e27e760835ad0a2cbce006bc6995", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        .container {
            background-color: #fff;
        }

        .line_wrap {
            display: flex;
            flex-direction: row;
            align-items: center;
        }

        .space-between {
            justify-content: space-between;
        }

        .flex_end {
            justify-content: flex-end;
        }

        .layui-form-label {
            width: 85px;
            padding-right: 0;
        }


        .layui-input-block {
            margin-left: 105px;
        }

        .item_Time {
            padding: 9px 15px;
            padding-left: 0;
        }
        .item_Down {
            padding: 9px 15px;
            padding-left: 0;
        }
        .item_Day {
            padding: 9px 15px;
            padding-left: 0;
        }

        .col_item {
            padding: 10px;
        }

        .layui-card-header {
            height: 20px;
            line-height: 20px;
            font-weight: bold;
  ");
                WriteLiteral(@"      }

        .gray_bg {
            background-color: #f2f2f2;
        }

        .inner_top {
            height: 55%;
        }

        .inner_bottom {
            height: 45%;
        }

        .col_item_body {
            overflow-y: auto;
        }

        .zb_item {
            background-color: #fff;
            border: 1px solid #eee;
            padding: 10px 15px;
            border-radius: 4px;
            margin: 0 5px 10px;
        }

        .active {
            background-color: #626C91;
            border-color: transparent;
            color: #fff;
        }



        .statistics_item {
            padding: 8px 10px;
            border-bottom: 1px solid #f6f6f6;
        }

        .statistics_val_warp {
            width: 40px;
            height: 40px;
            line-height: 40px;
            border: 3px solid #626C91;
            border-radius: 50%;
            text-align: center;
        }

        .statistics_key {
            wi");
                WriteLiteral(@"dth: 110px;
        }

        .message_time {
            padding-top: 10px;
            text-align: right;
        }

        .btn_group {
            padding-top: 10px;
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
        }

        .btn {
            padding: 5px 10px;
            color: #1e9fff;
            cursor: pointer;
        }

        .btn2 {
            padding-right: 0;
        }

        .layui-table tr th {
            font-weight: bold !important;
            color: #000;
        }

        .zb_list {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
        }

            .zb_list .zb_item {
                min-width: 38px;
                text-align: center;
            }

        .layui-tab-title {
            border-bottom: 0;
        }

        xm-select > .xm-body {
            left: auto !important;
            right: 0;
        }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "91383ca4464572b4524d63d06267f5a9d4a13792e27e760835ad0a2cbce006bc12042", async() => {
                WriteLiteral(@"
    <div class=""container layui-form"">
        <div class=""layui-tab layui-tab-brief line_wrap space-between"" style=""border-bottom:1px solid #e6e6e6;"">
            <ul class=""layui-tab-title"" style=""width:50%"" id=""ZBZList"">
            </ul>

            <div class=""line_wrap flex_end"">
                <div>
                    <label class=""layui-form-label"">开始时间：</label>
                    <div class=""layui-input-block"">
                        <div class=""item_Time"">
                            <strong style=""line-height:20px;""></strong>
                        </div>
                    </div>
                </div>
                <div>
                    <label class=""layui-form-label"">下载次数：</label>
                    <div class=""layui-input-block"">
                        <div class=""item_Down"">
                            <strong></strong>次
                        </div>
                    </div>
                </div>
                <div>
                    <label class");
                WriteLiteral(@"=""layui-form-label"">距今已过去：</label>
                    <div class=""layui-input-block"">
                        <div class=""item_Day"">
                            <strong></strong>天
                        </div>
                    </div>
                </div>
                <div class=""layui-inline"">
                    <div id=""xmDeptsList"" class=""xm-select-demo"" style=""width:100px""></div>
                </div>
            </div>


        </div>


        <div class=""content_inner_wrap"">
            <div class=""layui-row inner_top"">
                <div class=""layui-col-xs8 layui-col-sm8 layui-col-md8"">
                    <div class=""col_item"">
                        <div class=""layui-card"">
                            <div class=""layui-card-header gray_bg"">整体概况</div>
                            <div class=""layui-row"">
                                <div class=""layui-col-xs4 layui-col-sm4 layui-col-md4"">
                                    <div class=""col_item"">
             ");
                WriteLiteral(@"                           <div class=""layui-card"">
                                            <div class=""layui-card-header"">组成科室</div>
                                            <div class=""layui-card-body col_item_body"">
                                                <ul class=""zb_list"" id=""zb_list"">
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class=""layui-col-xs4 layui-col-sm4 layui-col-md4"">
                                    <div class=""col_item"">
                                        <div class=""layui-card"">
                                            <div class=""layui-card-header"">eCRF表单构成</div>
                                            <div class=""layui-card-body col_item_body"">
                                                <div class=""canvas"" style=""w");
                WriteLiteral(@"idth: 100%;height:100%;"" id=""formComposition""></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class=""layui-col-xs4 layui-col-sm4 layui-col-md4"">
                                    <div class=""col_item"">
                                        <div class=""layui-card"">
                                            <div class=""layui-card-header"">表单涉及业务域</div>
                                            <div class=""layui-card-body col_item_body"">
                                                <div class=""canvas"" style=""width: 100%;height:100%;"" id=""businessDomain""></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
         ");
                WriteLiteral(@"           </div>
                </div>

                <div class=""layui-col-xs4 layui-col-sm4 layui-col-md4"">
                    <div class=""col_item"">
                        <div class=""layui-card"">
                            <div class=""layui-card-header gray_bg"">数据统计</div>
                            <div class=""layui-card-body"">
                                <div class=""layui-card"">
                                    <div class=""layui-card-header line_wrap flex_end"">
                                        <button type=""button"" class=""layui-btn layui-btn-xs layui-btn-normal"" id=""allInquiry"">开始至今</button>
                                        <button type=""button"" class=""layui-btn layui-btn-xs "" id=""weekInquiry"">近一周</button>
                                    </div>
                                    <div class=""layui-card-body col_item_body"">
                                        <ul class=""statistics_list"">
                                            <li class=""statistics_i");
                WriteLiteral(@"tem line_wrap space-between"">
                                                <p class=""statistics_key"">计划采集变量数:</p>
                                                <div class=""statistics_val_warp PlanCollect""></div>条
                                            </li>
                                            <li class=""statistics_item line_wrap space-between"">
                                                <p class=""statistics_key"">已采集变量数:</p>
                                                <div class=""statistics_val_warp Collected""></div>条
                                            </li>
                                            <li class=""statistics_item line_wrap space-between"">
                                                <p class=""statistics_key"">已审核变量数:</p>
                                                <div class=""statistics_val_warp Reviewed""></div>条
                                            </li>
                                            <li class=""statistics_item line_wrap ");
                WriteLiteral(@"space-between"">
                                                <p class=""statistics_key"">待审核变量数:</p>
                                                <div class=""statistics_val_warp PendingReview""></div>条
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class=""layui-row inner_bottom"">
                <div class=""layui-col-xs8 layui-col-sm8 layui-col-md8"">
                    <div class=""col_item"">
                        <div class=""layui-card"">
                            <div class=""layui-card-header gray_bg"">数据质量与处理</div>
                            <div class=""layui-row"">
                                <div class=""layui-col-xs4 layui-col-sm4 layui-col-md4"">
                                    <div class=""col");
                WriteLiteral(@"_item"">
                                        <div class=""layui-card"">
                                            <div class=""layui-card-body col_item_body"">
                                                <div class=""canvas"" style=""width: 100%;height:100%;"" id=""dataProcessing""></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class=""layui-col-xs8 layui-col-sm8 layui-col-md8"">
                                    <div class=""col_item"">
                                        <div class=""col_item_table1"">
                                            <table class=""layui-hide"" id=""dataQuality""></table>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
");
                WriteLiteral(@"                </div>

                <div class=""layui-col-xs4 layui-col-sm4 layui-col-md4"">
                    <div class=""col_item"">
                        <div class=""layui-card"">
                            <div class=""layui-card-header gray_bg"">活动量统计</div>
                            <div class=""col_item_body_all activity_level"">
                                <div class=""activity_level_line"">
                                    <div id=""activityMetrics"" class=""activity_metrics"" style=""width:100%;""></div>
                                </div>
                                <div class=""activity_level_table"">
                                    <table class=""layui-hide"" id=""activityLevel""></table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>





    </div>


    <div id=""messageNotification-wrapper"" style=""display: none;"">
   ");
                WriteLiteral(@"     <div style=""padding:16px;"">
            <div>
                “妊娠相关自身免疫性疾病”专病中的“初诊-此次妊娠情况-PC”CRF表单数据已经采集完毕，等待审核处理
            </div>
            <div class=""message_time"">
                时间:<span id=""newDate""></span>
            </div>

            <div class=""btn_group"">

                <div class=""btn"">已读</div>
                <div class=""btn btn2"">全部已读</div>
            </div>

        </div>
    </div>





");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "91383ca4464572b4524d63d06267f5a9d4a13792e27e760835ad0a2cbce006bc23336", async() => {
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "91383ca4464572b4524d63d06267f5a9d4a13792e27e760835ad0a2cbce006bc24483", async() => {
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "91383ca4464572b4524d63d06267f5a9d4a13792e27e760835ad0a2cbce006bc25630", async() => {
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "91383ca4464572b4524d63d06267f5a9d4a13792e27e760835ad0a2cbce006bc26777", async() => {
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"

<script type=""text/javascript"">
    layui.use(['jquery', 'layer', 'form', 'element', 'code', 'laytpl', 'table'], function () {
        var layer = layui.layer,
            $ = layui.$,
            form = layui.form,
            laytpl = layui.laytpl,
            element = layui.element,
            table = layui.table;

        var ZBZId = """";

        $("".layui-tab-title"").on(""click"", ""li"", function (e) {
            ZBZId = $(this).attr(""data-value"");
            GetDomainList(ZBZId);
            GetFormCompList(ZBZId);
            GetDataStatistics(ZBZId);
            GetActivityStatistics(ZBZId);
        });

        var data1 = [{
            ""username"": ""苏丽郦"",
            ""variable"": ""过敏原类型"",
            ""time"": ""2024-08-25 16:15:55"",
            ""status"": ""已完成""
        },
        {
            ""username"": ""苏丽郦"",
            ""variable"": ""病史种类"",
            ""time"": ""2024-09-03 12:35:22"",
            ""status"": ""已完成""
        },
        {
            ""username"": ""卓雪芳"",
 ");
            WriteLiteral(@"           ""variable"": ""其他疾病名称"",
            ""time"": ""2024-08-27 09:57:35"",
            ""status"": ""已完成""
        },
        {
            ""username"": ""梁肖飞"",
            ""variable"": ""既往有无受孕困难"",
            ""time"": ""2024-08-13 15:02:01"",
            ""status"": ""未完成""
        },
        {
            ""username"": ""梁肖飞"",
            ""variable"": ""末次月经"",
            ""time"": ""2024-08-13 15:02:45"",
            ""status"": ""未完成""
        }
            ,
        {
            ""username"": ""李晴"",
            ""variable"": ""使用何种方式受孕"",
            ""time"": ""2024-08-05 09:13:25"",
            ""status"": ""未完成""
        }
            ,
        {
            ""username"": ""李霖楠"",
            ""variable"": ""糖尿病家族关系"",
            ""time"": ""2024-08-02 10:35:55"",
            ""status"": ""已完成""
        }
            ,
        {
            ""username"": ""李晴"",
            ""variable"": ""抗环瓜氨酸肽抗体"",
            ""time"": ""2024-08-02 10:35:55"",
            ""status"": ""已完成""
        }
            ,
        {
            ""usernam");
            WriteLiteral(@"e"": ""林怡萍"",
            ""variable"": ""过敏原类型"",
            ""time"": ""2024-07-28 14:55:07"",
            ""status"": ""已完成""
        }
            ,
        {
            ""username"": ""林怡萍"",
            ""variable"": ""抗β2糖蛋白抗体"",
            ""time"": ""2024-07-26 08:32:39"",
            ""status"": ""已完成""
        }
        ];

        var data2 = [{
            ""username"": ""张三1"",
            ""activityEvent"": ""活动1"",
            ""time"": ""2024-08-25 16:15:55""

        },
        {
            ""username"": ""张三2"",
            ""activityEvent"": ""活动2"",
            ""time"": ""2024-09-03 12:35:22""
        },
        {
            ""username"": ""张三3"",
            ""activityEvent"": ""活动3"",
            ""time"": ""2024-09-10 17:35:22""
        },
        {
            ""username"": ""张三4"",
            ""activityEvent"": ""活动4"",
            ""time"": ""2024-09-10 17:35:22""
        },
        {
            ""username"": ""张三5"",
            ""activityEvent"": ""活动5"",
            ""time"": ""2024-09-10 17:35:22""
        }
        ];
");
            WriteLiteral(@"
        $(document).ready(function () {

            function setContentInner_H() {
                var winH = $(window).height();
                var navH = $("".layui-tab"").outerHeight();

                var ContentInner_H = winH - navH - 30 + ""px"";
                $("".content_inner_wrap"").css(""height"", ContentInner_H);
            };

            function setItemBody_H() {
                var innerTH = $("".inner_top"").outerHeight();
                var innerBH = $("".inner_bottom"").outerHeight();
                var cardHeaderH = $("".layui-card-header"").outerHeight();

                $("".inner_top .col_item_body"").css(""height"", innerTH - (cardHeaderH * 2) - 60 + ""px"");
                $("".inner_bottom .col_item_body"").css(""height"", innerBH - cardHeaderH - 60 + ""px"");
                $("".inner_bottom .col_item_table1"").css(""height"", innerBH - cardHeaderH - 50 + ""px"");
                $("".inner_bottom .col_item_body_all"").css(""height"", innerBH - cardHeaderH - 40 + ""px"");
            };
");
            WriteLiteral(@"
            function setactivityMetricsH() {

                var actLeveH = $("".activity_level"").height();

                var MetricsH = actLeveH / 2;

                $("".activity_metrics"").css(""height"", MetricsH + ""px"");

                return MetricsH;


            }

            setContentInner_H()
            setItemBody_H()
            $(window).on('resize', function () {
                setContentInner_H();
                setItemBody_H();
            });

            // $("".zb_list"").on(""click"", "".zb_item"", function () {
            //     $("".zb_list"").find("".zb_item"").removeClass(""active"");
            //     $(this).addClass(""active"");
            // })
            //折线图高度
            setactivityMetricsH();
            //eCRF表单构成
            //showFormComposition();
            //表单涉及业务城
            //showBusinessDomain();
            //数据处理完成情况
            showDataProcessing();
            //活动量统计
            //showActivityMetrics();
            //通知弹框
   ");
            WriteLiteral(@"         messageNotification();

            var table1H = $("".col_item_table1"").height();
            var table2H = setactivityMetricsH();

            //数据质量与处理
            var dataQuality = table.render({
                elem: '#dataQuality',
                height: table1H,
                cols: [[ //标题栏
                    { type: 'numbers', title: '序号', width: '10%' },
                    { field: 'username', title: '人员名称', width: ""20%"" },
                    { field: 'variable', title: '变量名', width: ""20%"" },
                    { field: 'time', title: '时间', width: ""30%"" },
                    { field: 'status', title: '状态', width: ""20%"" }
                ]],
                data: data1
            });

            var activityLevel = table.render({
                elem: '#activityLevel',
                height: table2H,
                cols: [[ //标题栏
                    { type: 'numbers', title: '序号', width: '10%' },
                    { field: 'CreateUserName', title: '活动人员', ");
            WriteLiteral(@"width: ""25%"" },
                    { field: 'OperationType', title: '活动事件', width: ""25%"" },
                    { field: 'CreatedTime', title: '活动时间', width: ""40%"" }
                ]]
            });

        });
        var xmDeptsList = xmSelect.render({
            el: '#xmDeptsList',
            model: { label: { type: 'text' } },
            prop: {
                name: 'title',
                value: 'id',
            },
            minWidth: 200,
            radio: true,
            filterable: true,
            clickClose: true,
            //树
            tree: {
                show: true,
                //非严格模式
                strict: false,
                //默认展开节点
                expandedKeys: [-1],
            },
            data: []
            , on: function (val) {
                ZBZList(val.arr[0].id);
            }
        });

        function GetDeptsTree() {
            $.ajax({
                url: '/CommAPI/GetOrgsTreeList',
                type:");
            WriteLiteral(@" ""post"",
                datatype: 'json',
                success: function (result) {
                    xmDeptsList.update({
                        data: result
                    });
                    if (result[0].id) {
                        var arr = new Array();
                        arr.push(result[0].id);
                        xmDeptsList.setValue(arr);
                        ZBZList(arr);
                    }
                }, error: function () {
                    layer.msg(""获取失败！"");
                }
            })
        };

        function GetDomainList(ZBZId) {
            $.ajax({
                url: '/PatientDiscoveryManage/DataCollectionManagement/GetFormDomainList',
                type: ""post"",
                data: { ""GroupId"": ZBZId },
                success: function (res) {
                    if (res.code == 0) {
                        $('.item_Time strong').text(res.Time);
                        $('.item_Day strong').text(res.Day);
   ");
            WriteLiteral(@"                     showBusinessDomain(res.data);
                    }
                }
            })
        };

        function GetFormCompList(ZBZId) {
            $.ajax({
                url: '/PatientDiscoveryManage/DataCollectionManagement/GetFormCompositionList',
                type: ""post"",
                data: { ""GroupId"": ZBZId },
                success: function (res) {
                    if (res.code == 0) {
                        showFormComposition(res.data);
                    }
                }
            })
        };

        function GetDataStatistics(ZBZId,week) {
            $.ajax({
                url: '/PatientDiscoveryManage/DataCollectionManagement/GetDataStatistics',
                type: ""post"",
                data: { ""GroupId"": ZBZId, ""week"": week },
                success: function (res) {
                    if (res.code == 0) {
                        $('.statistics_val_warp.PlanCollect').text(res.TotalFieldSum);
                      ");
            WriteLiteral(@"  $('.statistics_val_warp.Collected').text(res.FillFieldSum);
                        $('.statistics_val_warp.Reviewed').text(res.FillFieldSum);
                        $('.statistics_val_warp.PendingReview').text(res.TotalFieldSum - res.FillFieldSum);
                    }
                }
            })
        };

        function GetActivityStatistics(ZBZId) {
            $.ajax({
                url: '/PatientDiscoveryManage/DataCollectionManagement/GetActivityStatistics',
                type: ""post"",
                data: { ""GroupId"": ZBZId},
                success: function (res) {
                    if (res.code == 0) {
                        showActivityMetrics(res.statistics);
                        table.reload('activityLevel', {
                            data: res.data
                        });
                        $('.item_Down strong').text(res.Count);

                    }
                }
            })
        };

        function ZBZList(deptId) {
  ");
            WriteLiteral(@"          $.ajax({
                url: '/PatientDiscoveryManage/DataCollectionManagement/GetZBZSettingList',
                type: ""post"",
                data: { ""deptId"": deptId },
                success: function (res) {
                    if (res.code == 0) {
                        var ZBZItem = '';
                        var zb_Itme = '';
                        $(""#ZBZList"").html("""");
                        $(""#zb_list"").html("""");
                        for (var i = 0; i < res.data.length; i++) {
                            if (i == 0) {
                                ZBZId = res.data[i].Id;
                                GetDomainList(ZBZId);
                                GetFormCompList(ZBZId);
                                GetDataStatistics(ZBZId);
                                GetActivityStatistics(ZBZId)
                                ZBZItem += '<li class=""layui-this"" data-value=""' + res.data[i].Id + '"">' + res.data[i].GroupName + '</li>';
                        ");
            WriteLiteral(@"    } else {
                                ZBZItem += '<li data-value=""' + res.data[i].Id + '"">' + res.data[i].GroupName + '</li>';
                            }
                        }

                        for (var i = 0; i < res.Orgdata.length; i++) {
                            if (i == 0) {
                                zb_Itme += '<li><div class=""zb_item "">' + res.Orgdata[i].Name + '</div></li>';
                            }
                            else {
                                zb_Itme += '<li><div class=""zb_item"">' + res.Orgdata[i].Name + '</div></li>';
                            }
                        }

                        $(""#zb_list"").append(zb_Itme);
                        $(""#ZBZList"").append(ZBZItem);
                        form.render();
                    } else {

                    }
                }
            });
        }
        GetDeptsTree();

        $(""#allInquiry"").on('click', function () {
            GetDataStatistics(");
            WriteLiteral(@"ZBZId);
        });
        $(""#weekInquiry"").on('click', function () {
            GetDataStatistics(ZBZId,""周"");
        });

        // 饼图展示
        function showFormComposition(res) {
            // 基于准备好的dom，初始化echarts实例
            var myChart = echarts.init(document.getElementById('formComposition'), 'walden');
            // var data = genData(50);
            option = {
                title: {
                    show: false,
                    text: 'Referer of a Website',
                    subtext: 'Fake Data',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    type: 'scroll',
                    bottom: 0,
                    textStyle: {
                        color: '#1e70d1',
                        fontSize: 12
                    }
                },
                series: [{
                    name: '表单构成',
                   ");
            WriteLiteral(@" type: 'pie',
                    radius: '50%',
                    center: ['50%', '35%'],
                    data: res.map(function (item) {
                        return {
                            value: item.ChildCount,
                            name: item.FormName
                        };
                    }),
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            };
            // 使用刚指定的配置项和数据显示图表。
            myChart.setOption(option);
        };

        function showBusinessDomain(res) {
            // 基于准备好的dom，初始化echarts实例
            var myChart = echarts.init(document.getElementById('businessDomain'), 'walden');
            // var data = genData(50);
            option = {
                title: {
                   ");
            WriteLiteral(@" show: false,
                    text: 'Referer of a Website',
                    subtext: 'Fake Data',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    type: 'scroll',
                    bottom: 0,
                    textStyle: {
                        color: '#1e70d1',
                        fontSize: 12
                    }
                },
                series: [{
                    name: '业务域',
                    type: 'pie',
                    radius: '50%',
                    center: ['50%', '35%'],
                    data: res.map(function (item) {
                        return {
                            value: 1,
                            name: item.DataDictName
                        };
                    }),
                    emphasis: {
                        itemStyle: {
                            shadowBlur: ");
            WriteLiteral(@"10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            };
            // 使用刚指定的配置项和数据显示图表。
            myChart.setOption(option);
        };

        function showDataProcessing() {
            // 基于准备好的dom，初始化echarts实例
            var myChart = echarts.init(document.getElementById('dataProcessing'), 'walden');
            // var data = genData(50);
            option = {
                title: {
                    show: false,
                    text: 'Referer of a Website',
                    subtext: 'Fake Data',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    type: 'scroll',
                    bottom: 0,
                    textStyle: {
                        color: '#1e70d1',
                        fontS");
            WriteLiteral(@"ize: 12
                    }
                },
                series: [{
                    name: 'Access From',
                    type: 'pie',
                    radius: '60%',
                    center: ['50%', '40%'],
                    data: [
                        {
                            value: 70,
                            name: '已完成',
                            itemStyle: { normal: { color: '#e2e2e2' } }
                        },
                        {
                            value: 30,
                            name: '未完成',
                            itemStyle: { normal: { color: '#626C91' } }
                        }
                    ],
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            };
   ");
            WriteLiteral(@"         // 使用刚指定的配置项和数据显示图表。
            myChart.setOption(option);
        };
        // 折线图
        function showActivityMetrics(res) {
            var myChart = echarts.init(document.getElementById('activityMetrics'), 'walden');
            var xAxisData = []; 
            var seriesData = {};
            // 遍历数据，填充 xAxisData 和 seriesData
            res.forEach(function (item) {
                // 如果 xAxisData 中还没有该项时间，则添加
                if (xAxisData.indexOf(item.Time) === -1) {
                    xAxisData.push(item.Time);
                }
                // 如果 seriesData 中还没有该项 OperationType，则初始化
                if (!seriesData[item.OperationType]) {
                    seriesData[item.OperationType] = [];
                }
                // 添加对应时间的数据点
                seriesData[item.OperationType][xAxisData.indexOf(item.Time)] = item.Count;
            });

            // 构建 series 数组
            var series = [];
            Object.keys(seriesData).forEach(function (operat");
            WriteLiteral(@"ionType) {
                series.push({
                    name: operationType,
                    type: 'line',
                    stack: 'Total',
                    data: seriesData[operationType]
                });
            });

            option = {
                title: {
                    text: ''
                },
                legend: {
                    width: '75%',
                    left:""center"",
                    data: Object.keys(seriesData),
                    type: 'scroll',
                    textStyle: {
                        color: '#1e70d1',
                        fontSize: 12
                    }
                },
                tooltip: {
                    trigger: 'axis'
                },
                grid: {
                    top: '12%',
                    left: '1%',
                    right: '5%',
                    bottom: '0',
                    containLabel: true
                },
                toolbox: ");
            WriteLiteral(@"{
                    feature: {
                        // saveAsImage: {}
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: xAxisData
                },
                yAxis: {
                    type: 'value',
                    maxInterval: 1000,
                    minInterval: 0
             
                },
                series: series
            };

            myChart.setOption(option);
        }

        function messageNotification() {

            var index1 = layer.open({
                type: 1,
                title: ""消息通知"",
                shade: false,
                offset: 'rb',
                content: $('#messageNotification-wrapper'),
                success: function () {
                    var date = getCurrentDate();
                    $(""#newDate"").html(date);
                    $("".layui-layer-title"").css({ ""background-");
            WriteLiteral(@"color"": ""#7A4D7B "", ""color"": ""#fff"" });
                    $("".btn"").on(""click"", function () {
                        layer.close(index1);
                    });
                },
                end: function () {
                    // layer.msg('关闭后的回调', {icon:6});
                }
            });

        }

        function getCurrentDate() {

            var currentDate = new Date();

            // 获取年月日
            var year = currentDate.getFullYear();
            var month = currentDate.getMonth() + 1; // 月份是从0开始的，所以加1
            var day = currentDate.getDate();

            // 格式化月份和日期，如果它们是一位数，则前面加上0
            month = (month < 10 ? '0' : '') + month;
            day = (day < 10 ? '0' : '') + day;

            // 返回格式化的日期字符串
            return year + '-' + month + '-' + day;

        }
    })
</script>

</html>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
