#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\TextAIAnalysis\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "e3e935d5da40e7ff12d172297a7ee2fbee4ef28b70da9d9174fe3fd5a0ce44f7"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_Demo_Views_TextAIAnalysis_Index), @"mvc.1.0.view", @"/Areas/Demo/Views/TextAIAnalysis/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"e3e935d5da40e7ff12d172297a7ee2fbee4ef28b70da9d9174fe3fd5a0ce44f7", @"/Areas/Demo/Views/TextAIAnalysis/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_Demo_Views_TextAIAnalysis_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/chatGTPIndex.css?v=v21"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/lib/codemirror.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/theme/pastel-on-dark.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/marked.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/lib/codemirror.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/mode/python/python.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/mode/sql/sql.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\TextAIAnalysis\Index.cshtml"
  
    ViewBag.Title = "ChatGPTStream";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"zh\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e3e935d5da40e7ff12d172297a7ee2fbee4ef28b70da9d9174fe3fd5a0ce44f77886", async() => {
                WriteLiteral("\r\n    <meta charset=\"UTF-8\">\r\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>chatGTP</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "e3e935d5da40e7ff12d172297a7ee2fbee4ef28b70da9d9174fe3fd5a0ce44f78381", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "e3e935d5da40e7ff12d172297a7ee2fbee4ef28b70da9d9174fe3fd5a0ce44f79583", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n    <!-- 代码高亮 -->\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "e3e935d5da40e7ff12d172297a7ee2fbee4ef28b70da9d9174fe3fd5a0ce44f710810", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "e3e935d5da40e7ff12d172297a7ee2fbee4ef28b70da9d9174fe3fd5a0ce44f712014", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e3e935d5da40e7ff12d172297a7ee2fbee4ef28b70da9d9174fe3fd5a0ce44f713217", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e3e935d5da40e7ff12d172297a7ee2fbee4ef28b70da9d9174fe3fd5a0ce44f714341", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        body {
            background-color: #fafafa;
            overflow: hidden;
        }


        .code_wrap {
            background-color: #3f3f3f;
            border-radius: 6px;
            overflow: hidden;
        }

        .code_top {
            padding: 4px 6px 0;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            background-color: #000;
            color: #c2be9e;
        }

        .code_content {
            padding: 10px;
            border: 10px solid #000;
            color: #c2be9e;
            text-align: left;
            resize: none;
        }

        .layui-btn-xs {
            padding: 0 6px;
        }

        .layui-btn-primary {
            border-color: transparent;
            background-color: transparent;
            color: #fff;
        }

            .layui-btn-primary:hover {
                color: #eee;
                border-color: #eee;
          ");
                WriteLiteral(@"  }

        .message_content {
            flex: 1;
        }

            .message_content p,
            .result_text {
                white-space: pre-line;
            }

        .result_text {
            padding: 10px;
            height: 80px;
            background-color: #3f3f3f;
            font-size: 14px;
            word-wrap: break-word;
            overflow-x: hidden;
            overflow-y: auto;
            border: 10px solid #000;
            border-top: 2px solid #000;
            border-radius: 0;
        }

            .result_text:hover {
                border: 10px solid #000 !important;
                border-top: 2px solid #000 !important;
            }

            .result_text:focus {
                padding: 10px;
                border: 10px solid #000 !important;
                border-top: 2px solid #000 !important;
                color: #c2be9e;
                border-radius: 0;
            }

        .layui-table-view {
            mar");
                WriteLiteral(@"gin: 0 10px;
        }

        .result_bj {
            display: flex;
            flex-direction: row;
            align-items: center;
            background-color: #009688;
            color: #fff;
        }

        .result_icon {
            width: 34px;
            height: 34px;
            border-radius: 10px;
            border: 2px solid #0bbe56;
            overflow: hidden;
            margin-right: 10px;
        }

            .result_icon img {
                vertical-align: top;
                width: 100%;
            }

        .flex_row {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .right_title {
            display: block;
        }

        .layui-btn {
            padding: 0 20px;
        }

        .left_wrap {
            width: 100%;
            margin: 10px;
            margin-left: 0;
            background-color: #fff;
            border: 1px solid #eee;
     ");
                WriteLiteral(@"       border-radius: 10px;
        }

        .right_content {
            margin: 10px;
            margin-left: 0;
            background-color: #fff;
            border: 1px solid #eee;
            border-radius: 10px;
            overflow: hidden;
        }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e3e935d5da40e7ff12d172297a7ee2fbee4ef28b70da9d9174fe3fd5a0ce44f719634", async() => {
                WriteLiteral("\r\n    <div");
                BeginWriteAttribute("class", " class=\"", 4097, "\"", 4105, 0);
                EndWriteAttribute();
                WriteLiteral(@">
        <div class=""chat layui-row"">
            <div class=""layui-col-xs12 layui-col-md12"">
                <div class=""right_title"" style=""margin-top:10px; "">
                    <form class=""layui-form flex_row"" lay-filter=""formModel"" id=""formModel""");
                BeginWriteAttribute("action", " action=\"", 4364, "\"", 4373, 0);
                EndWriteAttribute();
                WriteLiteral(">\r\n                        <div class=\"layui-form right_title_select\">\r\n");
                WriteLiteral(@"                            <div class=""layui-inline"">
                                <label class=""layui-form-label"">选择表单</label>
                                <div class=""layui-input-inline"" style=""width:300px;"">
                                    <select id=""selectForm"" lay-filter=""selectForm"">
                                        <option");
                BeginWriteAttribute("value", " value=\"", 5235, "\"", 5243, 0);
                EndWriteAttribute();
                WriteLiteral(@">请选择表单</option>
                                    </select>
                                </div>

                            </div>
                            <div class=""layui-inline"">
                                <div class=""layui-input-inline"">
                                    <input type=""checkbox"" name=""ChkNeedSource"" id=""ChkNeedSource"" lay-filter=""ChkNeedSource"" title=""原文依据"" lay-skin=""tag"">
                                </div>

                            </div>
                            <div class=""layui-inline"">
                                <div class=""layui-input-inline"">
                                    <button type=""button"" class=""layui-btn layui-btn-normal"" id=""btnLoad"">AI提取</button>
                                </div>
                                <div class=""layui-input-inline"">
                                    <button type=""button"" class=""layui-btn layui-btn-normal"" id=""btnResult"">查看结果</button>

                                </div>
            ");
                WriteLiteral(@"                </div>
                        </div>
                        <div>
                            <button id=""popbtn"" type=""button"" class=""layui-btn layui-bg-blue"">
                                <i class=""layui-icon layui-icon-survey""></i>
                            </button>
                        </div>
                    </form>
                </div>
                <div class=""layui-row layui-col-space10"">
                    <div class=""layui-col-xs3 layui-col-md3"" style=""height:98vh;"">
                        <div class=""left_wrap"">
                            <div id=""trees"" style=""background-color: #fff; height:55vh;""></div>
                            <div style=""height: 43vh;"">
                                <ul class=""layui-tab-title"">
                                    <li class=""layui-this"" lay-id=""11"">PDF文本内容</li>
                                </ul>
                                <div class=""layui-tab-content"">
                                    <div c");
                WriteLiteral(@"lass=""layui-tab-item layui-show""  style="" height:32vh;"">
                                        <pre id=""pdfContent"" style=""overflow-y: auto; border: 1px solid #ccc; height: 100%; width: 100%; line-height: 40px; font-size: 20px; resize: none"" ></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class=""layui-col-xs9 layui-col-md9"">
                        <div class=""layui-col-xs12 layui-col-md12"">
                            <div class=""right_content"" style=""height:50%"">
                                <div class=""layui-col-xs12 layui-col-md12""  style=""height:98%"">
                                    <div class=""layui-col-xs3 layui-col-md3"" style=""height:98%"">
                                        <ul class=""layui-tab-title"">
                                            <li class=""layui-this"" lay-id=""11"">AI分析</li>
                           ");
                WriteLiteral(@"             </ul>
                                        <div class=""layui-tab-content"" style=""height:85%"">
                                            <div class=""layui-tab-item layui-show"" id=""pdfContent"" style=""height:98%;"">
                                                <textarea id=""txtWords"" class=""layui-textarea"" style=""height:100%;width:98%;resize:none;line-height:22px;font-size:14px;""></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class=""layui-col-xs9 layui-col-md9""  style=""height:98%"">
                                        <iframe id=""rptframe"" style=""width:100%;height:100%""></iframe>
                                    </div>
                                </div>                                
                            </div>
                            <div class=""right_content"" style=""height:50%;"">
                                ");
                WriteLiteral(@"<div class=""layui-card-header"">
                                    <div class=""layui-card-body layui-form"">
                                        <div class=""layui-inline"">
                                            <div class=""layui-input-inline"" style=""width:200px;"">
                                                <input type=""text"" class=""layui-input"" name=""keyWord"" placeholder=""请输入变量名称"" id=""keyWord"" />
                                            </div>
                                        </div>
                                        <div class=""layui-inline"">
                                            <button class=""layui-btn layui-btn-normal fr"" id=""SearchCRFormsData"">查询</button>
                                        </div>
                                    </div>
                                </div>
                                <div class=""layui-card-body"">
                                    <table id=""tablelist"" lay-filter=""tablelist""></table>
                         ");
                WriteLiteral(@"           <script type=""text/html"" id=""tableBar"">
                                        <a class=""layui-btn layui-btn-normal layui-btn-xs"" lay-event=""edit"" style=""text-decoration:none""><i class=""layui-icon layui-icon-edit"" style=""width:30px;""></i></a>
                                        <a class=""layui-btn layui-btn-danger layui-btn-xs"" lay-event=""del"" style=""text-decoration:none""><i class=""layui-icon layui-icon-delete"" style=""width:30px;""></i></a>
                                    </script>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div id=""popwrap"" style=""display:none;height:100%;overflow:hidden;"">
        <div class=""layui-row layui-col-space30"" style=""height:100%"">
            <div class=""layui-col-md12"" style=""height:100%"">
                <div style=""background-color: #fff;height: 100%; padding:15px 15px;"">
        ");
                WriteLiteral("            <div id=\"model_wrapR\" class=\"layui-textarea\" style=\" height:95%; width:100%; overflow-y:auto; \">\r\n\r\n\r\n                    </div>\r\n");
                WriteLiteral(@"                </div>
            </div>
        </div>


    </div>

    <div id=""resultwrap"" style=""display:none;height:100%;overflow:hidden;"">
        <div class=""layui-row layui-col-space30"" style=""height:100%"">
            <div class=""layui-col-md12"" style=""height:100%"">
                <div style=""background-color: #fff;height: 100%; padding:15px 15px;"">
                    <textarea id=""txtResult"" class=""layui-textarea"" style=""height: 100%; width: 100%; line-height: 40px; font-size: 20px; resize: none "">

                    </textarea>
                </div>
            </div>
        </div>


    </div>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e3e935d5da40e7ff12d172297a7ee2fbee4ef28b70da9d9174fe3fd5a0ce44f728551", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e3e935d5da40e7ff12d172297a7ee2fbee4ef28b70da9d9174fe3fd5a0ce44f729675", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e3e935d5da40e7ff12d172297a7ee2fbee4ef28b70da9d9174fe3fd5a0ce44f730799", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e3e935d5da40e7ff12d172297a7ee2fbee4ef28b70da9d9174fe3fd5a0ce44f731923", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e3e935d5da40e7ff12d172297a7ee2fbee4ef28b70da9d9174fe3fd5a0ce44f733048", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
        layui.use(['jquery', 'layer', 'tree', 'form', 'element', 'code', 'laytpl', 'table'], function () {
            var layer = layui.layer,
                $ = layui.$,
                form = layui.form,
                laytpl = layui.laytpl,
                element = layui.element,
                tree = layui.tree,
                table = layui.table;

            //查询结果表格用的高度  height:""full-"" + table_top_H,
            var table_top_H;
            var crformId = """";
            var url = """";
            var timer;

            table.render({
                elem: '#tablelist'
                , id: 'tablelist'
                , page: true
                , limit: 10
                , height: 250
                , url: '/Demo/TextAIAnalysis/CRFormFieldList'
                , cols: [[
                    { field: 'No', title: '序号', type: 'numbers', fixed: 'left' }
                    , { field: 'FieldName', title: '变量名', with:200 }
                    , { field: 'Fi");
                WriteLiteral(@"eldComment', title: '变量描述', with: 200 }
                    , { field: 'ExtractSet', title: '提取要求', with: 200 }
                    , { title: '操作', toolbar: '#tableBar', width: 160, fixed: 'right' }
                ]]
                , done: function (res, curr, count) {
                }
            });

            //监听tablelist工具条
            table.on('tool(tablelist)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    form.val('fm', data);
                    windowsIndex = layer.open({
                        type: 1,
                        title: '修改【' + data.FormName + '】变量信息',
                        area: '600px',
                        resize: true,
                        content: $('#form_windowCRF')
                    });
                    url = '/Demo/TextAIAnalysis/Edit';
                }
                else if (obj.event === 'del') {
                    layer.confirm('确定要删除名为【' + data.FieldName +");
                WriteLiteral(@" '】的变量以及相关配置吗？将无法恢复。', {
                        title: '',
                        btn: ['确定', '取消'], //按钮
                        resize: false
                    }, function (index) {
                        $.post('/Demo/TextAIAnalysis/Del', { id: data.Id }, function (result) {
                            if (result.okMsg) {
                                layer.msg(result.okMsg);
                                currentIndex = -1;
                                table.reload('tablelist'); //重载表格
                            } else {
                                layer.msg(result.errorMsg);
                            }
                        }, 'json');
                        layer.close(index);
                    });
                }
            });

            //监听提交
            form.on('submit(submit)', function (data) {
                var indes = layer.load(1);
                data.field.CRFormId = -100;
                data.field.FormName = ""无需数据"";
                //提交");
                WriteLiteral(@" Ajax 成功后，关闭当前弹层并重载表格
                $.ajax({
                    url: url,
                    type: ""post"",
                    data: { 'node': data.field },
                    datatype: 'json',
                    success: function (data) {
                        if (data.okMsg) {
                            layer.msg(data.okMsg);
                            layer.close(windowsIndex);//关闭弹出层
                            table.reload('tablelist'); //重载表格
                            loadTips(crformId);
                        }
                        else {
                            layer.msg(data.errorMsg);
                        }
                        layer.close(indes);
                    }, error: function (res) {
                        layer.msg(""加载统计信息错误："" + res.responseText);
                        layer.close(indes);
                    }
                });
                return false;
            });

            layui.code();

            $(window).resize(fu");
                WriteLiteral(@"nction () {
                arrangement();
            })
            arrangement();


            function arrangement() {
                var winH = $(window).height();
                //     right_title_H = $("".right_title"").outerHeight(true),
                //     messageH = $("".chat-message"").outerHeight(true);

                // var _middle_main = winH - right_title_H - messageH - 100 + ""px"";

                // $(""#chat-history"").css(""height"", _middle_main);


                // var leftH = $("".left_content"").height();
                $("".right_content"").css(""height"", (winH - 400) + ""px"");
            }
            $(document).ready(function () {
                //$(""#btnLoad"").on(""click"", function () {
                //    loadData();
                //})

                $(""#btnLoad"").on(""click"", loadData);

                $(document).on('click', '#SearchCRFormsData', function () {
                    getCRFormsData();
                })

                $(""#btnResul");
                WriteLiteral(@"t"").on(""click"", function () {
                    layer.open({
                        type: 1,
                        area: ['60%', '80%'],
                        resize: false,
                        shadeClose: true,
                        title: 'AI结果',
                        content: $(""#resultwrap""),
                        success: function () {

                        }
                    })
                })

                $(""#popbtn"").on(""click"", function () {
                    layer.open({
                        type: 1,
                        area: ['60%', '80%'],
                        resize: false,
                        shadeClose: true,
                        title: '帮助',
                        content: $(""#popwrap""),
                        success: function () {
                            // $(""#model_wrapL"").val(modelText);
                        }
                    })
                })
                loadSelectForm();
                get");
                WriteLiteral(@"Tree();
            });

            // 监听select元素的改变事件
            form.on('select(selectForm)', function (data) {
                // data是事件对象，data.value是被选中的值
                console.log('选中的值：', data.value);
                // 在这里可以进行你需要的操作
                var token = """);
                Write(
#nullable restore
#line 474 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\TextAIAnalysis\Index.cshtml"
                              ViewBag.token

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\";\r\n                var url = \"");
                Write(
#nullable restore
#line 475 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\TextAIAnalysis\Index.cshtml"
                             ViewBag.formUrl

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"""+data.value+"".form?token=""+token;
                crformId = data.value;
                 setTimeout(function () {
                    $(""#rptframe"").attr(""src"", url);
                    $('#rptframe').attr('src', $('#rptframe').attr('src'));
                    }, 1000);

                $(""#txtResult"").val("""");
                loadTips(data.value);

                getCRFormsData();
            });

            function loadData2() {
                if (!$(""#selectForm"").val()) {
                   layer.msg(""请选择表单！"");
                   return;
                }
                var selectTrees = tree.getChecked('demo-id-1');
                console.log(""长度"",selectTrees.length);
                if (selectTrees.length == 0) {
                    layer.msg(""请选择文档！"");
                    return;
                }
                if (selectTrees.length>1)
                {
                    layer.msg(""一次只能选择同一个患者的文档！"");
                    return;
                }
             ");
                WriteLiteral(@"  // var loadIndex = layer.load(1);
                var loadIndex= layer.open({
                    type: 1,
                    area: ['600px', '40px'],
                    shade:0.1,
                    closeBtn:0,
                    resize:false,
                    title:false,
                    content: $(""#form_window_load""),
                    success: function () {
                        // $(""#model_wrapL"").val(modelText);
                        load();
                    }
                })
                var obj = { nodes: selectTrees, formId: $(""#selectForm"").val(), tips: $(""#model_wrapR"").val() };
                $.post('/Demo/TextAIAnalysis/LoadData', obj, function (res) {
                    element.progress('demo-filter-progress', '100%');
                    if (res.code == 0) {
                        $(""#txtResult"").val(res.data);
                        var iframe = document.getElementById('rptframe');
                        // 发送消息到子页面
                      ");
                WriteLiteral(@"  iframe.contentWindow.postMessage({ data: res.data }, ""*"");
                    }
                    else
                      layer.msg(res.msg);
                    layer.close(loadIndex);
                    element.progress('demo-filter-progress', '0%');
                })
            }

            function loadData() {
                $(""#btnLoad"").off(""click"", loadData);
                var i = 0;
                if (!$(""#selectForm"").val()) {
                    layer.msg(""请选择表单！"");
                    return;
                }
                var selectTrees = tree.getChecked('demo-id-1');
                console.log(""长度"", selectTrees.length);
                if (selectTrees.length == 0) {
                    layer.msg(""请选择文档！"");
                    return;
                }
                if (selectTrees.length > 1) {
                    layer.msg(""一次只能选择同一个患者的文档！"");
                    return;
                }
                // var loadIndex = layer.load(1);
       ");
                WriteLiteral(@"         //var loadIndex = layer.open({
                //    type: 1,
                //    area: ['600px', '40px'],
                //    shade: 0.1,
                //    closeBtn: 0,
                //    resize: false,
                //    title: false,
                //    content: $(""#form_window_load""),
                //    success: function () {
                //        // $(""#model_wrapL"").val(modelText);
                //        load();
                //    }
                //})
                var fileIds = getCheckedId(selectTrees);
                console.log(""长度"", fileIds);
                // + ""&tips="" + $(""#model_wrapR"").val()  tips容易超长该从后台出
                var isNeedSource = false;
                if ($(""#ChkNeedSource"").is(':checked')) {
                    isNeedSource = true;
                }
                source = new EventSource('/Demo/TextAIAnalysis/LoadData2?formId=' + $(""#selectForm"").val() + '&isNeedSoure=' + isNeedSource+ '&nodes=' + fileIds);
       ");
                WriteLiteral(@"         source.onmessage = function (event) {
                    var result = JSON.parse(event.data);
                    if (result.okMsg) {
                        var btnloading = document.getElementById(""txtWords"");
                        if (i == 0) {
                            var Info = result.data;
                            btnloading.value = Info;
                        }
                        else {
                            var Info = result.data;
                            btnloading.value += Info;
                        }
                        i = i + 1;
                    }
                    else {
                        layer.msg(result.errorMsg);
                        source.close();
                    }
                    resetHistoryscrollTop();
                };

                source.addEventListener('end', function (event) {
                    $(""#btnLoad"").on(""click"", loadData);
                    var result = JSON.parse(event.data);
   ");
                WriteLiteral(@"                 if (result.okMsg == ""成功"") {
                        $(""#txtResult"").val(result.data);
                        var iframe = document.getElementById('rptframe');
                        // 发送消息到子页面
                        iframe.contentWindow.postMessage({ data: result.data }, ""*"");
                        var btnloading = document.getElementById(""pdfContent"");
                        let content = btnloading.innerHTML;
                        var textarray = [];
                        textarray.push(""入院时情况:患者郭春芳，男，55岁，以“腹胀、尿黄15天。”为主诉步行入院。"");
                        textarray.push(""入院前15天"");
                        textarray.push(""无明显诱因出现持续性中上腹闷胀，进食后加重，活动后可稍缓解，伴皮肤瘙痒、尿"");
                        textarray.push(""黄、嗳气，无恶心、呕吐，无腹痛、腹泻，无呕血、排黑便，无腰痛、尿频、尿急、尿"");
                        textarray.push(""痛，无皮疹、双下肢水肿等不适。"");
                        textarray.push(""治疗经过:入院后检查：肝脏MRI平扫+增强+MRCP：1、肝门区浸润性病变，考虑胆管细胞癌可"");
                        textarray.push(""能，并多发肝内胆管梗阻性扩张；肝门区多发肿大淋巴结，部分为MT待除。"");
    ");
                WriteLiteral(@"                    textarray.push(""4.出院带药："");
                        textarray.push(""胰酶肠溶胶囊(0.3g)(得每通)     每次0.60g      口服       TID    7天"");
                        textarray.push(""(集采)(基)恩替卡韦分散片       每次0.50mg     口服       QD     7天"");
                        textarray.push(""(集采)(基)奥美拉唑肠溶胶囊(海灵 每次20.00mg    口服       QD     7天"");
                        textarray.push(""双环醇片(百赛诺)               每次50.00mg    口服       TID    7天"");
                        textarray.push(""出院医嘱:"");
                        textarray.push(""1.清淡饮食，加强营养，如有腹胀、腹痛、黄疸等不适，我科随诊（严茂林主任每周三上午门"");
                        //textarray.push(""诊"");
                        textarray.push(""2.1个月后返院继续治疗。建议定期复查肝功能、瘤标、肝脏MRI（2 - 3个月）；"");
                        textarray.push(""3.肝胆外科以外科室疾病相关科室就诊；"");
                        textarray.push(""出院时情况:患者无诉恶心、呕吐，无腹痛、腹胀，无发热、畏寒。"");
                        textarray.push(""患者于2024年04月02日在全麻下行“肝门部胆管癌根治术+右半肝切除术+尾状叶切"");
                        textarray.push(""除术 + 胆管空肠吻合术 + 胆管成形术 + 胆囊切除术”，术顺"");
             ");
                WriteLiteral(@"           
                        textarray.push(""入院后予胰酶肠溶胶囊促消化、谷胱甘肽+甘草酸二铵"");
                        textarray.push(""护肝、丁二磺酸腺苷蛋氨酸促胆汁排泄、乳果糖通便等治疗。并分别于2024.03.20、"");
                        textarray.push(""2024.03.22行超声介导下左右胆道置管引流术。"");
                        textarray.forEach(function (element) {
                            content = content.replace(element, ""<span style='color:red; font-weight:bold;'>"" + element+""</span>"");
                        });

                        btnloading.innerHTML = content;
                    }
                    else {
                        layer.msg(result.errorMsg);
                    }
                    source.close();
                }, false);

                source.onerror = function (event) {
                    source.close();
                };
            }


            //获取所有选中的节点id
            function getCheckedId(data) {
                var id = """";
                $.each(data, function (index, item) {
                    if");
                WriteLiteral(@" (item.docmentType == ""文件"") {
                        if (id != """") {
                            id = id + ""$"" + item.id;
                        }
                        else {
                            id = item.id;
                        }
                    }
                    //item 没有children属性
                    if (item.children != null) {
                        var i = getCheckedId(item.children);
                        if (i != """") {
                            if (id != """") {
                                id = id + ""$"" + i;
                            }
                            else {
                                id = i;
                            }
                        }
                    }
                });
                return id;
            }

            function loadSelectForm()
            {
                $.get('/Demo/TextAIAnalysis/GetSelectForm',function(res){
                   if(res.code==0){
                      $.each(res.da");
                WriteLiteral(@"ta,function(index,item){
                            $('#selectForm').append('<option value='+item.id+'>'+item.name+'</option>');
                      })
                      layui.form.render();
                   }
                })
            }

            form.on('checkbox(ChkNeedSource)', function (data) {
                loadTips(crformId);
            });

            function loadTips(formId)
            {
                var isNeedSource = false;
                if ($(""#ChkNeedSource"").is(':checked')) {
                    isNeedSource = true;
                }
                $.get('/CommAPI/GetPromptTips2?formId=' + formId + ""&PropmtIdentifier=TextAIAnalysisController&ModelName=&isNeedSource="" + isNeedSource, function (res) {
                    if (res.code == 0) {
                        debugger
                        //$(""#model_wrapR"").val(res.data);
                        var htmlContent = marked.parse(res.data);
                        $(""#model_wrapR"").html(htm");
                WriteLiteral(@"lContent);  
                    }
                })
            }

            var treeNodeId = 0;
            var patientId=0;
             function getTree() {
                 $.get('/Demo/TextAIAnalysis/GetDocList', function (res) {
                    // 渲染
                    tree.render({
                        elem: '#trees',
                        data: res,
                        edit: [""preview""],
                        showCheckbox: true,
                        onlyIconControl: true,  // 是否仅允许节点左侧图标控制展开收缩
                        id: 'demo-id-1',
                        isJump: true, // 是否允许点击节点时弹出新窗口跳转
                        click: function (obj) {
                            var data = obj.data;  //获取当前点击的节点数据
                            //  layer.msg('状态：' + obj.state + '<br>节点数据：' + JSON.stringify(data));
                            console.log(obj);
                            //obj.elem.addClass(""active"");
                           // treeNodeId = data.id;
   ");
                WriteLiteral(@"                      //   patientId = data.patientId;
                          //  $(""#trees"").find("".layui-tree-txt"").removeClass(""active"");
                          //  $(obj.elem).find("".layui-tree-txt"").eq(0).addClass(""active"");

                        },oncheck: function (obj) {
                            //console.log(obj.data); // 得到当前点击的节点数据
                            //console.log(obj.checked); // 节点是否被选中
                            GetPDFContent();
                        }
                        , operate: function (obj) {
                            var type = obj.type; //得到操作类型：add、edit、del
                            var data = obj.data; //得到当前节点的数据
                            var elem = obj.elem; //得到当前节点元素

                            //Ajax 操作
                            var id = data.id; //得到节点索引
                            if (type === 'preview') { //增加节点
                                //返回 key 值
                                if (data.docmentType==""文件"")
        ");
                WriteLiteral(@"                        {
                                    layer.open({
                                        type: 2,
                                        title: 'PDF预览',
                                        resize: true,
                                        maxmin: true,
                                        shadeClose: true, // 点击遮罩关闭层
                                        area: ['90%', '90%'], // 设置弹窗大小
                                        content: data.fileUrl
                                    });
                                }
                                else{
                                    layer.msg(""当前目录不支持预览！"");
                                }

                            }
                        }
                    });
                })
            }

            //设置Y轴位置
            function resetHistoryscrollTop() {
                var ele = document.getElementById(""txtWords"");
                if (ele.scrollHeight > ele.clientHeight)");
                WriteLiteral(@" {
                    setTimeout(function () {
                        //设置滚动条到最底部
                        ele.scrollTop = ele.scrollHeight;
                    }, 500);
                }
            }

            function hidden() {
                $("".menu_wrap"").css(""display"", ""none"")
                $("".menu_list"").removeClass(""show_menu_list"")
            }

            var btn_type = 0;
            var title_text = """";

            // 父窗口
            window.addEventListener('message', function (event) {
                console.log(event.data.data);
                // alert(event.data.data);
                // if (event.origin !== 'https://example.com') { // 检查来源是否可信
                //     return; // 不是预期源，则忽略
                // }

                if (event.data.action === 'save') {
                    var selectTrees = tree.getChecked('demo-id-1');
                    console.log(selectTrees);
                    console.log(event.data);
                    var projectName ");
                WriteLiteral(@"= $('#selectForm').find('option:selected').text();
                    var projectNo =   $('#selectForm').find('option:selected').text();
                    var patientId = selectTrees[0].patientId;
                    var docDetailedNo = selectTrees[0].patientId;
                    var formId = event.data.formId;
                    var formData = event.data.data;
                    var obj = { projectName: projectName, projectNo: projectNo, patientId: patientId, docDetailedNo: docDetailedNo, formId: formId, formData: formData };
                    //$.post('/Chat/SaveForm', obj, function (res) {
                    //    if (res.code == 0) {
                    //        layer.msg(""操作成功"");
                    //    }
                    //    else
                    //        layer.msg(""操作失败"");
                    //});
                }

            }, false);

            function load(){
                var n = 0;
                timer = setInterval(function () {
               ");
                WriteLiteral(@"     n = n + Math.random() * 10 | 0;
                    if (n > 100) {
                        n = 100;
                        clearInterval(timer);
                    }
                    element.progress('demo-filter-progress', n + '%');
                }, 300 + Math.random() * 4000);
            }

            function getCRFormsData() {
                table.reload('tablelist', {
                    page: {
                        curr: 1
                    },
                    url: '/Demo/TextAIAnalysis/CRFormFieldList'
                    , where: {
                        'formIds': crformId,
                        'keyWords': $.trim($(""#keyWord"").val()),
                    }
                });
            }

            function GetPDFContent() {
                var selectTrees = tree.getChecked('demo-id-1');
                var obj = { nodes: selectTrees };
                $.post('/Demo/TextAIAnalysis/GetPdfContent', obj, function (res) {
                    $(""#pd");
                WriteLiteral("fContent\").html(res.data);\r\n                });\r\n            }\r\n\r\n        });\r\n    </script>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n<div class=\"window_wrap\" id=\"form_window\" style=\"display: none\">\r\n    <form class=\"layui-form\" lay-filter=\"fm\" id=\"fm\"");
            BeginWriteAttribute("action", " action=\"", 35770, "\"", 35779, 0);
            EndWriteAttribute();
            WriteLiteral(@">
        <div class=""form-control cm-s-pastel-on-dark changecode"">
            <textarea class=""layui-textarea code_content result_text "" id=""code-python""
                      name=""code-python""></textarea>
        </div>

    </form>
</div>

<div class=""window_wrap"" id=""form_window_sql"" style=""display: none"">
    <form class=""layui-form"" lay-filter=""fmsql"" id=""fmsql""");
            BeginWriteAttribute("action", " action=\"", 36162, "\"", 36171, 0);
            EndWriteAttribute();
            WriteLiteral(@">
        <div class=""form-control cm-s-pastel-on-dark changecode"">
            <textarea class=""layui-textarea code_content result_text "" id=""code-sql""
                      name=""code-python""></textarea>
        </div>

    </form>

</div>

<div class=""window_wrap"" id=""form_window_load"" style=""display: none;background-color:transparent"">
    <div class=""layui-progress layui-progress-big"" lay-showPercent=""true"" lay-filter=""demo-filter-progress"">
        <div class=""layui-progress-bar"" lay-percent=""0%"">

        </div>

    </div>
    <p style=""text-align:center""> AI数据提取中...</p>
</div>

<!--新增/修改弹框-->
<div class=""window_wrap"" id=""form_windowCRF"" style=""display: none"">
    <form class=""layui-form"" lay-filter=""fm"" id=""fm""");
            BeginWriteAttribute("action", " action=\"", 36923, "\"", 36932, 0);
            EndWriteAttribute();
            WriteLiteral(@">
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">CRF表单</label>
            <div class=""layui-input-block"">
                <input type=""text"" name=""FieldName"" id=""FieldName"" readonly=""readonly"" autocomplete=""off"" class=""layui-input"">
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">变量名</label>
            <div class=""layui-input-block "">
                <input type=""text"" name=""Id"" id=""Id"" style=""display:none;"" />
                <input type=""text"" name=""FieldName"" id=""FieldName"" lay-verify=""required"" placeholder=""请输入变量名"" autocomplete=""off"" class=""layui-input"">
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">变量描述</label>
            <div class=""layui-input-block "">
                <input type=""text"" name=""FieldComment"" id=""FieldComment"" placeholder=""请输入描述"" autocomplete=""off"" class=""layui-input"">
            </div>
        <");
            WriteLiteral(@"/div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">提取要求</label>
            <div class=""layui-input-block"">
                <textarea name=""ExtractSet"" id=""ExtractSet"" class=""layui-textarea"" style=""resize: none""></textarea>
            </div>
        </div>
        <div class=""layui-form-item"">
            <div class=""layui-input-block"">
                <button type=""button"" class=""layui-btn"" lay-submit lay-filter=""submit"">提交</button>
                <button type=""reset"" class=""layui-btn layui-btn-primary"" id=""btn_reset"">重置</button>
            </div>
        </div>
    </form>
</div>
</html>

");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
