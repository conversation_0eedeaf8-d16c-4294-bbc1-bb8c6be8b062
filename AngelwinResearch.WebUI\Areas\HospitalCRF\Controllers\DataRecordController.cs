﻿using AngelwinResearch.ModelExtends;
using AngelwinResearch.Models;
using AngelwinResearch.WebUI.Filters;
using AngelwinResearch.WebUI.Models;
using AngelwinResearch.WebUI.Unity;
using Common.Tools;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using static AAngelwinResearch.WebUI.Controllers.CRFormTraceabilityNewController;

namespace AngelwinResearch.WebUI.Areas.HospitalCRF.Controllers
{
    [Authorizing]
    [Area("HospitalCRF")]
    public class DataRecordController : Controller
    {
        private readonly AngelwinResearchDbContext db;
        public IConfiguration config { get; }
        public IWebHostEnvironment webhostEnv { get; set; }
        public DataRecordController(AngelwinResearchDbContext _db, IConfiguration _config, IWebHostEnvironment _webHostEnv)
        {
            db = _db;
            config = _config;
            webhostEnv = _webHostEnv;
        }

        /// <summary>
        /// http://localhost:63683/HospitalCRF/DataRecord/Index?ResearchID=1
        /// </summary>
        /// <param name="ResearchID"></param>
        /// <returns></returns>
        public IActionResult Index(string PatientId, string GroupName)
        {
            var token = TokenGenerator.getToken();
            ViewBag.token = WebUtility.UrlEncode(token);
            ViewBag.formUrl = config["AppSettings:AnyReportUrl"];
            ViewBag.GroupName = GroupName;
            var patient = db.ResearchPatients.Include(a => a.HospitalDept).FirstOrDefault(a => a.HisPatientId == PatientId);
            var DeptName = patient?.HospitalDept?.DeptName;
            ViewBag.DeptName = DeptName;
            return View(patient);
        }

        public IActionResult GetCRFList(int HospitalDeptId, string GroupName)
        {
            try
            {
                var CRFQueryable = db.HospitalCRForms.Include(a => a.HospitalDept).Where(a => a.StopUsing == true && a.HospitalDept.Id == HospitalDeptId).AsQueryable();

                if (!string.IsNullOrEmpty(GroupName))
                    CRFQueryable = CRFQueryable.Where(a => a.GroupName == GroupName);

                var CRFList = CRFQueryable.ToList();
                var GroupNameArr = CRFList.GroupBy(a => a.GroupName).Select(a => a.Key).ToList();



                var list = new List<dynamic>();
                //三级目录结构  第三级是表单
                foreach (var groupName in GroupNameArr)
                {
                    dynamic data = new ExpandoObject();
                    data.id = groupName;
                    data.formId = groupName;
                    data.title = groupName;
                    var crfChild = CRFList.Where(a => a.GroupName == groupName).OrderBy(a => a.OrderBy).Select(a => new { id = a.Id, formId = a.FormId, title = a.FormName }).ToList();
                    data.children = crfChild;
                    if (crfChild.Count > 0)
                        list.Add(data);
                }
                return Json(new { code = 0, msg = "操作成功！", data = list });
            }
            catch (Exception ex)
            {
                LoggerHelper.WriteErrorLog("获取表单列表数据报错！", ex);
                return Json(new { code = -100, msg = "获取数据失败！", data = ex });
            }
        }


        [HttpPost]
        public IActionResult SaveForm(int ResearchPatientId, string CRFJsonValue, string CRFormIds)
        {
            try
            {
                // 如果需要遍历所有属性，可以使用JObject
                JObject jObject = JObject.Parse(CRFJsonValue);
                var ResearchPatient = db.ResearchPatients.Find(ResearchPatientId);
                var formId = jObject.Value<string>("formId");
                LoggerHelper.WriteInfo("其他日志", $"患者ID：【{ResearchPatientId}】开始提交表单ID：【{formId}】,表单数据：{CRFJsonValue}");
                if (string.IsNullOrEmpty(formId))
                    return Json(new { code = -100, msg = "表单ID未获取到！" });
                if (ResearchPatientId == 0)
                    return Json(new { code = -100, msg = "请选择患者！" });
                var hospitalCRForms = db.HospitalCRForms.FirstOrDefault(a => a.FormId == formId && a.HospitalDeptId == ResearchPatient.HospitalDeptId);
                if (hospitalCRForms == null)
                    return Json(new { code = -100, msg = "CRF表单ID有误！" });



                //  var totalFields = jObject.Count;
                var totalFields = 0;
                int NotEmptyFields = 0;
                var fieldValueDict = new List<FillFieldDTO>();
                var crfForm = db.CRForms.FirstOrDefault(a => a.FormId == formId);
                var listFieldSets = db.CRFormFieldSets.Where(a => a.CRFormId == crfForm.Id).ToList();
                foreach (JProperty property in jObject.Properties())
                {
                    JToken propertyValue = property.Value;
                    var propertyName = property.Name;
                    totalFields++;
                    if (!propertyName.ToLower().Contains("detailarray_"))
                    {
                        var FieldSets = listFieldSets.FirstOrDefault(a => a.FieldName == propertyName);
                        var comment = FieldSets == null ? "" : FieldSets.FieldComment;
                        fieldValueDict.Add(new FillFieldDTO { FieldName = propertyName, FieldValue = propertyValue.ToString(), FieldComment = comment });
                    }
                    else
                    {
                        var DetailList = (JArray)property.Value;
                        foreach (JObject detail in DetailList)
                        {
                            foreach (JProperty pro in detail.Properties())
                            {
                                var proValue = pro.Value;
                                var proName = pro.Name;
                                var proFieldSets = listFieldSets.FirstOrDefault(a => a.FieldName == proName);
                                var comment = proFieldSets == null ? "" : proFieldSets.FieldComment;
                                fieldValueDict.Add(new FillFieldDTO { FieldName = proName, FieldValue = proValue.ToString(), FieldComment = comment });
                            }
                        }
                    }
                    switch (propertyValue.Type)
                    {
                        case JTokenType.String:
                            if (!string.IsNullOrEmpty((string)propertyValue))
                            {
                                NotEmptyFields++;
                            }
                            break;
                        case JTokenType.Integer:
                        case JTokenType.Float:
                            if (((IConvertible)propertyValue).ToString() != "0")
                            {
                                NotEmptyFields++;
                            }
                            break;
                        case JTokenType.Array:
                            //if (((JArray)propertyValue).Count > 0)
                            //{
                            //    NotEmptyFields++; // 如果数组非空，则计数增加
                            //}
                            getDetailCount((JArray)propertyValue, ref totalFields, ref NotEmptyFields);
                            totalFields--;
                            break;
                        // 根据需要添加其他类型的处理
                        default:
                            // 处理其他类型的值或忽略
                            break;
                    }
                }

                var crfData = db.HospitalCRFDatas.FirstOrDefault(a => a.PatientId == ResearchPatient.HisPatientId && a.HospitalCRFormId == hospitalCRForms.Id);
                var commAPI = new CommAPIController(db, webhostEnv, config);


                //fieldValueDict.Add("download", "下载");


                if (crfData != null)
                {
                    crfData.TotalField = totalFields;
                    crfData.FillField = NotEmptyFields;
                    crfData.CRFJsonValue = CRFJsonValue;
                    crfData.CreatedTime = DateTime.Now;
                    db.SaveChanges();


                    var addLogs = commAPI.AddOperationLogs(OperationTypeName.数据修改, User.Identity.Name, ResearchPatient.DiseaseSpecificGroupId
                               , crfForm.Id, ResearchPatientId, fieldValueDict, "CFR表单数据修改", HttpContext);
                }
                else
                {
                    crfData = new HospitalCRFData();
                    crfData.PatientId = ResearchPatient.HisPatientId;
                    crfData.PatientSource = ResearchPatient.HisPatientSource;
                    crfData.PatientName = ResearchPatient.PatientName;
                    crfData.BLH = ResearchPatient.BLH;
                    crfData.Sex = ResearchPatient.Sex;
                    crfData.FormId = formId;
                    crfData.HospitalCRFormId = hospitalCRForms.Id;
                    crfData.CRFJsonValue = CRFJsonValue;
                    crfData.CreatedTime = DateTime.Now;
                    crfData.CreateUserName = User.Identity.Name;
                    crfData.TotalField = totalFields;
                    crfData.FillField = NotEmptyFields;
                    db.HospitalCRFDatas.Add(crfData);
                    db.SaveChanges();

                    var addLogs = commAPI.AddOperationLogs(OperationTypeName.数据填报, User.Identity.Name, ResearchPatient.DiseaseSpecificGroupId
                             , crfForm.Id, ResearchPatientId, fieldValueDict, "CFR表单数据填报", HttpContext);
                }

                return Json(new { code = 0, msg = "操作成功！" });
            }
            catch (Exception ex)
            {
                LoggerHelper.WriteErrorLog("提交表单报错！", ex);
                return Json(new { code = -100, msg = "操作失败！", data = ex });
            }

        }

        private void getDetailCount(JArray detailValue, ref int totalFields, ref int NotEmptyFields)
        {
            foreach (JObject detail in detailValue)
            {
                var detailPropertyList = detail.Properties();
                foreach (var property in detailPropertyList)
                {
                    totalFields++;
                    JToken propertyValue = property.Value;

                    switch (propertyValue.Type)
                    {
                        case JTokenType.String:
                            if (!string.IsNullOrEmpty((string)propertyValue))
                            {
                                NotEmptyFields++;
                            }
                            break;
                        case JTokenType.Integer:
                        case JTokenType.Float:
                            if (((IConvertible)propertyValue).ToString() != "0")
                            {
                                NotEmptyFields++;
                            }
                            break;
                        case JTokenType.Array:
                            if (((JArray)propertyValue).Count > 0)
                            {
                                NotEmptyFields++; // 如果数组非空，则计数增加
                            }

                            break;
                        // 根据需要添加其他类型的处理
                        default:
                            // 处理其他类型的值或忽略
                            break;
                    }
                }
            }
        }

        public string TransformData(string jsonInput)
        {

            var inputObject = JObject.Parse(jsonInput);
            Root root = new Root { variables = new List<Variable>() };

            foreach (var property in inputObject.Properties())
            {
                JToken propertyValue = property.Value;

                string value;
                if (propertyValue.Type == JTokenType.Array)
                {
                    // 如果值是数组，将其转换为用逗号分隔的字符串
                    //   value = string.Join(",", ((JArray)propertyValue).Select(token => (string)token));
                    value = JsonConvert.SerializeObject(((JArray)propertyValue));
                }
                else
                {
                    value = (string)propertyValue;
                }

                Variable variable = new Variable
                {
                    variable_name = property.Name,
                    value = value,
                    source = ""
                };
                root.variables.Add(variable);
            }

            return JsonConvert.SerializeObject(root);
        }


        public IActionResult LoadFormData(int ResearchID, int HospitalCRFormId)
        {
            try
            {
                var CRForm = db.HospitalCRForms.Find(HospitalCRFormId);
                var commAPI = new CommAPIController(db, webhostEnv, config);
                var researchPatient = db.ResearchPatients.Find(ResearchID);

                //var tips = getTips(CRForm.FormId);
                var formData = db.HospitalCRFDatas.FirstOrDefault(a => a.PatientId == researchPatient.HisPatientId && a.HospitalCRFormId == HospitalCRFormId);
                if (formData == null)
                    return Json(new { code = -100, msg = "暂未填报！" });

                return Json(new { code = 0, msg = "操作成功！", data = TransformData(formData.CRFJsonValue), aIExtractJson = formData.AIExtractJsonValue });
            }
            catch (Exception ex)
            {
                LoggerHelper.WriteErrorLog("加载表单数据报错！", ex);
                return Json(new { code = -100, msg = "获取数据失败！" + ex.Message });
            }

        }


        [SSE]
        public async Task<ActionResult> GetBLTextResult(int ResearchID, string crFormIds, string tips)
        {
            var response = Response;
            response.Headers.Add("Cache-Control", "no-cache");
            response.Headers.Add("Connection", "keep-alive");
            var modelType = config["AppSettings:modelType"];
            if (string.IsNullOrWhiteSpace(crFormIds))
            {
                var errorMsg = "你未选择CRF表单！";
                await response.WriteAsync($"event: end\ndata:{JsonConvert.SerializeObject(new { errorMsg = $"{errorMsg}" })}\n\n");
                await response.Body.FlushAsync();
                return new EmptyResult();
            }
            var researchPatient = db.ResearchPatients.Find(ResearchID);
            var crFormIdList = crFormIds.Split(",", StringSplitOptions.RemoveEmptyEntries).ToList();
            var crfDataList = db.HospitalCRFDatas.Include(o => o.HospitalCRForm).Where(o => o.PatientId == researchPatient.HisPatientId && crFormIdList.Contains(o.HospitalCRFormId.ToString())).ToList();

            var promptInfo = db.PromptInfos.Where(o => o.PropmtIdentifier == "DataRecordController" && o.IsUsed
                        && (o.ModelName == "通用" || o.ModelName == modelType)).OrderBy(o => o.ModelName).FirstOrDefault();
            if (promptInfo == null)
            {
                return Json(new { code = -100, data = $"不存在该页面[DataRecordController]基于该模型[{modelType}]的通用或专用提示词；" });
            }
            #region 提示词实例
            //            var tips = @"
            //<角色>
            //角色：你是一位经验丰富的医疗文书撰写专家。
            //</角色>
            //<任务>
            //任务：基于用户提供的JSON数据【{{病历内容}}】，生成一份完全连贯、信息完整的病历文书。
            //</任务>
            //<背景>
            //背景：用户期望获得一份专业、连贯且无遗漏的病历文书，类似于经验丰富的医疗专业人员所书写的临床病历。
            //</背景>
            //<执行要求>
            //执行要求：
            //1. 严格遵守用户提供的JSON数据，确保所有关键细节无遗漏。
            //2. 保持文本连贯、专业，避免使用任何分段或子标题。
            //3. 不引入任何额外的假设或模拟数据。
            //4. 避免在输出中添加不相关的前导语。
            //5. 确保整个文本是一个统一的、连续的整体。
            //</执行要求>
            //<输出要求>
            //输出要求：
            //1. 输出一段逻辑严谨、结构清晰的病历文书。
            //2. 文本应包含所有关键细节，信息完整无遗漏。
            //3. 文风应类似于经验丰富的医疗专业人员所书写的临床病历。
            //</输出要求>
            //                        ";
            #endregion
            var promptZHISHI = "";
            var CRFormFieldSets = db.CRFormFieldSets.Include(a => a.CRForm).Where(a => crFormIdList.Contains(a.CRForm.FormId)).ToList();
            foreach (var item in crfDataList)
            {


                var listJsonValue = JsonConvert.DeserializeObject<Dictionary<string, object>>(item.CRFJsonValue);
                var newList = new JArray();
                foreach (var dic in listJsonValue)
                {
                    var key = dic.Key;
                    var value = dic.Value;

                    var formField = CRFormFieldSets.FirstOrDefault(a => a.CRForm.FormId == item.FormId && a.FieldName == key);
                    var jobj = new JObject();
                    jobj["变量描述"] = formField == null ? key : formField.FieldComment;
                    jobj["变量值"] = JToken.FromObject(value);
                    newList.Add(jobj);
                }
                var newJsonVlue = JsonConvert.SerializeObject(newList);
                promptZHISHI += $"【{item.HospitalCRForm.FormName}】数据如下：{newJsonVlue};";
            }


            // promptZHISHI += $"【患者信息】数据如下：患者姓名:{researchPatient.PatientName},性别:{researchPatient.Sex},患者年龄：{researchPatient.ParticipatAge}";
            var prompt = promptInfo.Prompt.Replace("{{病历内容}}", promptZHISHI);

            LoggerHelper.WriteInfo("其他日志", $"提示词：{prompt}");
            try
            {

                string endpoint = "chat/completions";
                dynamic history = new List<dynamic>();
                var ResponseContent = "";
                var thinkResponseContent = "";
                var requestMsgTime = System.DateTime.Now;

                var contentStr = "";
                #region 读取配置文件
                string apiKey = config[$"GPTSetting:{modelType}:ApiKey"];
                string model = config[$"GPTSetting:{modelType}:Model"];
                string apiUrl = config[$"GPTSetting:{modelType}:apiUrl"];
                string maxTokenStr = config[$"GPTSetting:{modelType}:MaxTokens"];
                int maxTokens = 4000;
                if (!string.IsNullOrEmpty(maxTokenStr))
                {
                    int.TryParse(maxTokenStr, out maxTokens);
                }
                HttpClientHandler handler = null;
                string webProxy = config[$"GPTSetting:{modelType}:WebProxy"];
                if (!string.IsNullOrWhiteSpace(webProxy))
                {
                    handler = new HttpClientHandler()
                    {
                        Proxy = new WebProxy(webProxy)
                    };
                }
                else
                {
                    handler = new HttpClientHandler() { };
                }
                #endregion
                handler.ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator;
                var httpClient = new HttpClient(handler);
                httpClient.BaseAddress = new Uri(apiUrl);
                httpClient.Timeout = TimeSpan.FromMinutes(5);
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                history.Add(new { role = "user", content = $"{prompt}" });

                dynamic requstDTO = new ExpandoObject();
                requstDTO.model = model;
                requstDTO.max_tokens = maxTokens;
                requstDTO.messages = history;
                requstDTO.stream = true;
                var requestBody = JsonConvert.SerializeObject(requstDTO);
                LoggerHelper.WriteInfo("其他日志", $"请求大模型参数：【{requestBody}】");
                var request = new HttpRequestMessage(System.Net.Http.HttpMethod.Post, apiUrl + endpoint)
                {
                    Content = new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
                };

                using var APIResponse = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);
                {
                    if (APIResponse.IsSuccessStatusCode)
                    {
                        var stream = await APIResponse.Content.ReadAsStreamAsync();
                        var streamReader = new StreamReader(stream);
                        //  var ddd = await streamReader.ReadLineAsync();
                        var isThink = config.GetValue<int>($"GPTSetting:{modelType}:IsThink") == 1 ? true : false;

                        while (!streamReader.EndOfStream)
                        {
                            var line = await streamReader.ReadLineAsync();
                            if (!string.IsNullOrWhiteSpace(line))
                            {
                                if (line != "data: [DONE]")
                                {
                                    LoggerHelper.WriteInfo("其他日志", $"line：【{line}】");
                                    if (line.StartsWith("data:"))
                                        line = line.Substring(5, line.Length - 5);
                                    var delta = JObject.Parse(line).SelectToken("choices").First().SelectToken("delta");
                                    var finish_reason = JObject.Parse(line).SelectToken("choices").First().SelectToken("finish_reason");
                                    //if (delta != null && ((JContainer)delta).Count > 0)
                                    //{
                                    //    if (delta["content"] != null)
                                    //    {
                                    //        contentStr = delta["content"].ToString();
                                    //        ResponseContent += contentStr;
                                    //        await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { okMsg = "成功", data = contentStr })}\n\n");
                                    //        await response.Body.FlushAsync();
                                    //    }
                                    //}
                                    if (delta != null && ((JContainer)delta).Count > 0)
                                    {
                                        if (delta["content"] != null)
                                        {
                                            contentStr = delta["content"].ToString();
                                            if (contentStr.Contains("<think>") && !isThink)
                                            {
                                                isThink = true;
                                            }
                                            if (contentStr.Contains("</think>") && isThink)
                                            {
                                                isThink = false;
                                            }
                                            if (isThink)
                                            {
                                                thinkResponseContent += contentStr;
                                                await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { okMsg = "成功", data = "" })}\n\n");
                                                await response.Body.FlushAsync();
                                            }
                                            else
                                            {
                                                ResponseContent += contentStr;
                                                await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { okMsg = "成功", data = contentStr })}\n\n");
                                                await response.Body.FlushAsync();
                                            }
                                        }
                                    }
                                    if (finish_reason != null)
                                    {
                                        var resultfinish = finish_reason as JProperty;
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        var errorMsg = "Failed to connect to API.";
                        await response.WriteAsync($"event: end\ndata:{JsonConvert.SerializeObject(new { errorMsg = $"{errorMsg}" })}\n\n");
                        await response.Body.FlushAsync();
                        return new EmptyResult();
                    }
                }
                foreach (var crfData in crfDataList)
                {
                    crfData.AIExtractJsonValue = ResponseContent;
                    db.SaveChanges();
                }
                var resultAll = new
                {
                    okMsg = $"成功",
                    role = "assistant",
                    content = ResponseContent,
                };

                LoggerHelper.WriteInfo("其他日志", $"ThinkContent：【{thinkResponseContent}】,ResponseContent：【{ResponseContent}】");
                await Response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(resultAll)}\n\n");
                await Response.Body.FlushAsync();
                return new EmptyResult();
            }
            catch (Exception ex)
            {
                await Response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(new { errorMsg = $"报错:{ex.Message}-{ex.InnerException?.Message}" })}\n\n");
                await Response.Body.FlushAsync();
                return new EmptyResult();
            }
        }

    }
}
