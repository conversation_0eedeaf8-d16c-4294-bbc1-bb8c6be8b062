#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\BeforeStructured\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "6718e1360880a0f675aa86849e6c498a6e490cf4360360c27591911e24c54ab6"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_CMIS_Views_BeforeStructured_Index), @"mvc.1.0.view", @"/Areas/CMIS/Views/BeforeStructured/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"6718e1360880a0f675aa86849e6c498a6e490cf4360360c27591911e24c54ab6", @"/Areas/CMIS/Views/BeforeStructured/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_CMIS_Views_BeforeStructured_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<
#nullable restore
#line 5 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\BeforeStructured\Index.cshtml"
       AngelwinResearch.WebUI.Areas.CMIS.Models.TempCMIS

#line default
#line hidden
#nullable disable
    >
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/jquery/dist/jquery.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/marked.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\BeforeStructured\Index.cshtml"
  
	ViewBag.Title = "前结构化数据";
	Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "6718e1360880a0f675aa86849e6c498a6e490cf4360360c27591911e24c54ab66592", async() => {
                WriteLiteral("\r\n\t<meta charset=\"utf-8\">\r\n\t<title>前结构化数据</title>\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "6718e1360880a0f675aa86849e6c498a6e490cf4360360c27591911e24c54ab66933", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "6718e1360880a0f675aa86849e6c498a6e490cf4360360c27591911e24c54ab68133", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "6718e1360880a0f675aa86849e6c498a6e490cf4360360c27591911e24c54ab69333", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "6718e1360880a0f675aa86849e6c498a6e490cf4360360c27591911e24c54ab610454", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "6718e1360880a0f675aa86849e6c498a6e490cf4360360c27591911e24c54ab611576", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "6718e1360880a0f675aa86849e6c498a6e490cf4360360c27591911e24c54ab612698", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
	<style>
		html, body {
			height: 100%;
			margin: 0;
			padding: 0;
		}

		.container {
			height: 100%;
			padding: 15px;
			box-sizing: border-box;
		}

		.block {
			background: #f8f8f8;
			border-radius: 4px;
			padding: 15px;
			box-sizing: border-box;
			height: 96%;
		}
		/* 自定义表单间距 */
		.custom-form .layui-form-item {
			margin-bottom: 12px;
		}
		/* iframe样式 */
		.full-iframe {
			width: 100%;
			height: 100%;
			border: none;
			border-radius: 4px;
		}
		/* 备注框样式 */
		.full-textarea {
			width: 100%;
			height: 100%;
			padding: 10px;
			font-size: 14px;
			border: 1px solid #e6e6e6;
			border-radius: 4px;
			resize: none; /* 禁止调整大小 */
		}

		.block_header {
			display: flex;
			flex-direction: row;
			justify-content: flex-end;
			align-items: center;
			padding: 5px;
		}

		.infowrap .layui-input-inline {
			width: 70%;
			margin-bottom: 10px;
		}

			.infowrap .layui-input-inline .layui-input {
				line-height: 38px;
			}

		.formSel_");
                WriteLiteral("wrap {\r\n\t\t\twidth: 810px;\r\n\t\t\tmargin: 0 auto;\r\n\t\t}\r\n\r\n\t\t.crf_wrap {\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\r\n\t\t#txtMedical p {\r\n\t\t\twhite-space: pre-wrap;\r\n\t\t}\r\n\t</style>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "6718e1360880a0f675aa86849e6c498a6e490cf4360360c27591911e24c54ab615786", async() => {
                WriteLiteral(@"
	<div class=""container layui-fluid"">
		<!-- 顶部：患者信息 -->
		<div class=""layui-row"" style=""height: 20%; margin-bottom: 15px;display:none"">
			<div class=""layui-col-md12"">
				<div class=""block"" style=""background: #F0F9EB;border: 1px solid #ccc"">
					<form class=""layui-form custom-form"">
						<div class=""layui-row infowrap"">
							<!-- 每行显示3个表单项 -->

							<div class=""layui-form-item layui-row"">
								<div class=""layui-col-xs4"">
									<label class=""layui-form-label"">患者姓名</label>
									<div class=""layui-input-inline"">
										<div class=""layui-input"">");
                Write(
#nullable restore
#line 104 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\BeforeStructured\Index.cshtml"
                                    Model?.PatientName

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t<div class=\"layui-col-xs4\">\r\n\t\t\t\t\t\t\t\t\t<label class=\"layui-form-label\">患者性别</label>\r\n\t\t\t\t\t\t\t\t\t<div class=\"layui-input-inline\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"layui-input\">");
                Write(
#nullable restore
#line 111 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\BeforeStructured\Index.cshtml"
                                    Model?.SEX

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"layui-col-xs4\">\r\n\t\t\t\t\t\t\t\t\t<label class=\"layui-form-label\">患者卡号</label>\r\n\t\t\t\t\t\t\t\t\t<div class=\"layui-input-inline\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"layui-input\">");
                Write(
#nullable restore
#line 117 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\BeforeStructured\Index.cshtml"
                                    Model?.BRKH

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"layui-col-xs4\">\r\n\t\t\t\t\t\t\t\t\t<label class=\"layui-form-label\">患者来源</label>\r\n\t\t\t\t\t\t\t\t\t<div class=\"layui-input-inline\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"layui-input\">");
                Write(
#nullable restore
#line 123 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\BeforeStructured\Index.cshtml"
                                    Model?.PatientSource

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"layui-col-xs4\">\r\n\t\t\t\t\t\t\t\t\t<label class=\"layui-form-label\">检查项目</label>\r\n\t\t\t\t\t\t\t\t\t<div class=\"layui-input-inline\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"layui-input\">");
                Write(
#nullable restore
#line 129 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\BeforeStructured\Index.cshtml"
                                    Model?.ProjectName

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"layui-col-xs4\">\r\n\t\t\t\t\t\t\t\t\t<label class=\"layui-form-label\">检查部位</label>\r\n\t\t\t\t\t\t\t\t\t<div class=\"layui-input-inline\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"layui-input\">");
                Write(
#nullable restore
#line 135 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\BeforeStructured\Index.cshtml"
                                    Model?.JCBW

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</form>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<!-- 下部布局 -->\r\n\t\t<div class=\"layui-row\" style=\"height: calc(99% - 1px);\">\r\n\t\t\t<!-- 左侧iframe -->\r\n\t\t\t<div");
                BeginWriteAttribute("class", " class=\"", 3669, "\"", 3734, 1);
                WriteAttributeValue("", 3677, 
#nullable restore
#line 149 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\BeforeStructured\Index.cshtml"
                 Model?.HideBtn==true? "layui-col-md12":"layui-col-md9"

#line default
#line hidden
#nullable disable
                , 3677, 57, false);
                EndWriteAttribute();
                WriteLiteral(@" style=""height: 100%; padding-right: 7px;"">
				<div class=""formSel_wrap"" style=""height:45px;display:none;"">
					<div class=""layui-form layui-form-item"" style=""height:4%;padding-bottom:10px;"">
						<label class=""layui-form-label"">选择模板：</label>
						<div class=""layui-input-inline"">
							<select id=""formSel"" lay-filter=""formSel"">
							</select>
						</div>
					</div>
				</div>


				<div class=""block crf_wrap"" style=""padding: 10px;"">
					<iframe class=""full-iframe"" frameborder=""0"" scrolling=""auto"" id=""reportFrame""></iframe>
				</div>
			</div>

			<!-- 右侧备注 -->
			<div class=""layui-col-md3 block""");
                BeginWriteAttribute("style", " style=\"", 4366, "\"", 4447, 5);
                WriteAttributeValue("", 4374, "height:", 4374, 7, true);
                WriteAttributeValue(" ", 4381, "100%;", 4382, 6, true);
                WriteAttributeValue(" ", 4387, "padding-left:", 4388, 14, true);
                WriteAttributeValue(" ", 4401, "7px;", 4402, 5, true);
                WriteAttributeValue("", 4406, 
#nullable restore
#line 167 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\BeforeStructured\Index.cshtml"
                                                                             Model?.HideBtn==true?"display:none":""

#line default
#line hidden
#nullable disable
                , 4406, 41, false);
                EndWriteAttribute();
                WriteLiteral(@">
				<div class=""block_header"">
					<p></p>
					<button type=""button"" class=""layui-btn layui-btn-normal layui-btn-sm"" id=""btnCopy"">一键复制</button>

					<button type=""button"" class=""layui-btn layui-btn-warm layui-btn-sm"" id=""btnlook"">查看表单数据</button>
				</div>
				<div class=""block"" style=""padding: 10px;"">
					<div id=""txtMedical"" style=""height:100%;width:100%;resize:none;line-height:40px;font-size:20px; overflow:auto;border: 1px solid #ccc""></div>
				</div>

			</div>
		</div>
	</div>

	");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "6718e1360880a0f675aa86849e6c498a6e490cf4360360c27591911e24c54ab622863", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
	<script>
		layui.config({
			base: '/layuiadmin/layuiextend/'
		}).use(['element', 'layer', 'form'], function () {
			var element = layui.element
				, layer = layui.layer
				, $ = layui.$
				, form = layui.form;


			var option = '';
			var selectData = ");
                Write(
#nullable restore
#line 194 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\BeforeStructured\Index.cshtml"
                     Html.Raw(Json.Serialize(ViewBag.select))

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@";
			selectData.forEach(function (item, index) {
				if (index == 0) {
					var url = getReportUrl(item.id);
					$(""#reportFrame"").attr(""src"", url);
					if(item.id==""245c13512e3342069b4dfa253200c92f"")
					{
						 setTimeout(function() {
									FillCRForm(item.id);
								}, 3000);
					}
					else
					   setTimeout(function() {
									FillCRForm(item.id);
								}, 1000);

				}
				option += `<option value=${item.id}>${item.name}</option>`;
			});
			$(""#formSel"").html(option);
			form.render();

			form.on('select(formSel)', function (data) {
				var val = data.value;
				var url = getReportUrl(val);
				$(""#reportFrame"").attr(""src"", url);
				 setTimeout(function() {
									FillCRForm(val);
								}, 1000);

			});
			var source;
			// 父窗口
			window.addEventListener('message', function (event) {
				if (event.data.action === 'save') {
					 var formData = event.data.data;
					var obj = {};
					obj.formData = formData;
					obj.cmis = ");
                Write(
#nullable restore
#line 232 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\BeforeStructured\Index.cshtml"
                 Html.Raw(Json.Serialize(Model))

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@";
					obj.formId = $(""#formSel"").val();
					 $.post('/CMIS/BeforeStructured/SubmitData', { ""request"": obj }, function (res) {
							   if(res.code==0)
							   {
								   if (source != undefined) {
						source.close();
					}

					var json = JSON.stringify(obj);
				  //  url = '/CMIS/BeforeStructured/GetResult?words=' + encodeURIComponent(json);
					url = '/CMIS/BeforeStructured/GetResult?Id=' + res.data.id;
					console.log(formData);
					var loadIndex = layer.load(1);
					var i = 0;
					var allData = """";
					source = new EventSource(url);
					source.onmessage = function (event) {
						var result = JSON.parse(event.data);
						if (result.okMsg) {
							if(result.data)
							{
							layer.close(loadIndex);
							}
							allData += result.data;
							var htmlContent = marked.parse(allData);
							$(""#txtMedical"").html(htmlContent);

							var div = $('#txtMedical');
							div.scrollTop(div[0].scrollHeight - div.innerHeight());

						}
						else {
			");
                WriteLiteral(@"				  layer.close(loadIndex);
							layer.msg(result.errorMsg);
							source.close();
						}
					};

					source.addEventListener('end', function (event) {
						var result = JSON.parse(event.data);
						layer.close(loadIndex);
						console.log(""end:"" + event.data);
						var newObj = {};
						newObj.AIResult = result.content;
						newObj.id= res.data.id;
						if (result.okMsg) {
							//  var loading = layer.load(1);
							  $.post('/CMIS/BeforeStructured/Update',newObj, function (res) {
								 if(res.code!=0){
								   layer.msg(res.msg);
								 }
							  })

						}
						else {
							layer.msg(result.errorMsg);
						}
						source.close();
					}, false);

					source.onerror = function (event) {
						  layer.close(loadIndex);
						source.close();
					};
							   }
							   else
								layer.msg(res.msg);
							})

				}
				else if (event.data.action === 'pingti') {
					var formData = event.data.data;
					// 创建一个隐藏的 textarea 元素
					va");
                WriteLiteral(@"r textArea = document.createElement(""textarea"");

					// 将目标文本放入 textarea 中
					textArea.value = formData;

					// 避免出现滚动条，同时确保元素不在视口中可见
					textArea.style.position = 'fixed';
					textArea.style.top = '0';
					textArea.style.left = '-9999px';

					// 添加 textarea 到 DOM 中
					document.body.appendChild(textArea);

					// 选中 textarea 中的内容
					textArea.select();
					textArea.setSelectionRange(0, 99999); /* 为了兼容移动设备 */

					try {
						// 执行复制命令
						var successful = document.execCommand('copy');
						var msg = successful ? 'successful' : 'unsuccessful';
						console.log('Copying text command was ' + msg);
					} catch (err) {
						console.error('Oops, unable to copy', err);
					}

					// 移除临时的 textarea 元素
					document.body.removeChild(textArea);

					 var obj = {};
					obj.formData = formData;
					obj.cmis = ");
                Write(
#nullable restore
#line 337 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\BeforeStructured\Index.cshtml"
                 Html.Raw(Json.Serialize(Model))

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@";
					obj.formId = $(""#formSel"").val();
					  $.post('/CMIS/BeforeStructured/SubmitData', { ""request"": obj }, function (res) {

								layer.msg(res.msg);
							})
				}

			}, false);

			function getReportUrl(formId) {
				var randVersion = Math.random();
				var url = """);
                Write(
#nullable restore
#line 349 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\BeforeStructured\Index.cshtml"
                 $"{ViewBag.formUrl}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\" + formId + \"");
                Write(
#nullable restore
#line 349 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\BeforeStructured\Index.cshtml"
                                                      $".form?token={ViewBag.token}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"&version="" + randVersion;
				return url;
			}

			$(""#btnCopy"").click(function () {
				//var textBox = document.getElementById('txtMedical');
				//textBox.select(); // 选择文本
				//document.execCommand('copy'); // 执行复制命令
				//textBox.blur();
				//layer.msg('病历文书已复制');

				// 创建一个临时的textarea来复制内容
				var $temp = $(""<textarea>"");
				$(""body"").append($temp);
				$temp.val($(""#txtMedical"").text()).select();
				try {
					var successful = document.execCommand('copy');
					var msg = successful ? '成功复制到剪贴板' : '复制失败';
					layer.msg(msg);
				} catch (err) {
					layer.msg('无法复制，浏览器不支持');
				}
				// 移除临时textarea
				$temp.remove();
			});

           $(""#btnlook"").click(function () {
	var obj = {};
	//obj.formId=formId;
	obj.cmis = ");
                Write(
#nullable restore
#line 378 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\BeforeStructured\Index.cshtml"
             Html.Raw(Json.Serialize(Model))

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@";
	$.ajax({
		type: 'POST',
		url: ""/CMIS/BeforeStructured/LookFormData"",
		data: JSON.stringify(obj),
		dataType: 'json',
		contentType: ""application/json; charset=utf-8"",
		success: function (res) {
			if (res.code == 0) {
				layer.open({
					type: 1,
					area: ['60%', '80%'],
					resize: false,
					shadeClose: true,
					title: '帮助',
					content: $(""#popwrap""),
					success: function () {
						$(""#model_wrapR"").val(res.data);
					}
				})
			}
		}
	});
})

			function FillCRForm(formId)
			{
				var obj ={};
				obj.formId=formId;
				obj.cmis = ");
                Write(
#nullable restore
#line 407 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\BeforeStructured\Index.cshtml"
                Html.Raw(Json.Serialize(Model))

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@";
				$.ajax({
					  type: 'POST',
					  url: ""/CMIS/BeforeStructured/FillCRForm"",
					  data: JSON.stringify(obj),
					  dataType: 'json',
					  contentType: ""application/json; charset=utf-8"",
					  success: function (res) {
						  if(res.code==0){
						   var iframe = document.getElementById('reportFrame');
							 iframe.contentWindow.postMessage({ action: ""show"", data: res.data }, ""*"");
						  }
					  }
					 });
			}

		});
	</script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"

<div id=""popwrap"" style=""display:none;height:100%;overflow:hidden;"">
	<div class=""layui-row layui-col-space30"" style=""height:100%"">
		<div class=""layui-col-md12"" style=""height:100%"">
			<div style=""background-color: #fff;height: 100%; padding:15px 15px;"">
				<textarea id=""model_wrapR"" style=""height:100%;width:100%;resize:none;line-height:40px;font-size:20px; overflow:auto;""></textarea>
			</div>
		</div>
	</div>


</div>
</html>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<AngelwinResearch.WebUI.Areas.CMIS.Models.TempCMIS> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
