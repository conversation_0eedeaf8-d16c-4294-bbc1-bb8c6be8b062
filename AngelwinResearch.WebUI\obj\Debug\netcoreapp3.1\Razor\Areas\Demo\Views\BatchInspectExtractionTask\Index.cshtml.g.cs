#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\BatchInspectExtractionTask\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "ad6ece4fdea23df87982f7129de1eab8b7bc1eb75dc28fbe6e869934eacf4ce0"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_Demo_Views_BatchInspectExtractionTask_Index), @"mvc.1.0.view", @"/Areas/Demo/Views/BatchInspectExtractionTask/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"ad6ece4fdea23df87982f7129de1eab8b7bc1eb75dc28fbe6e869934eacf4ce0", @"/Areas/Demo/Views/BatchInspectExtractionTask/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_Demo_Views_BatchInspectExtractionTask_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<
#nullable restore
#line 4 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\BatchInspectExtractionTask\Index.cshtml"
       AngelwinResearch.Models.TaskInfo

#line default
#line hidden
#nullable disable
    >
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/jquery/dist/jquery.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\BatchInspectExtractionTask\Index.cshtml"
  
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "ad6ece4fdea23df87982f7129de1eab8b7bc1eb75dc28fbe6e869934eacf4ce06978", async() => {
                WriteLiteral("\r\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\">\r\n    <title>批量检查特征提取任务管理</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "ad6ece4fdea23df87982f7129de1eab8b7bc1eb75dc28fbe6e869934eacf4ce07499", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "ad6ece4fdea23df87982f7129de1eab8b7bc1eb75dc28fbe6e869934eacf4ce08701", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "ad6ece4fdea23df87982f7129de1eab8b7bc1eb75dc28fbe6e869934eacf4ce09903", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        /*弹窗*/
        .window_wrap {
            padding: 15px;
        }

        .search_wrap {
            background-color: #f0f0f0;
        }

        .layui-tab-brief {
            background-color: #fff;
        }

        .line_wrap {
            padding: 10px 15px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .form_item {
            padding-top: 10px;
        }

        .btnwrap {
            padding-left: 10px;
        }

        .layui-show {
            display: flex !important;
            align-items: flex-end;
        }

        .form_wrap {
            flex: 1;
        }

        .table_wrap {
            overflow: hidden;
        }

        #detail_window .layui-form-label {
            width: 100px;
        }

        #detail_window .layui-form-val {
            padding: 9px 15px;
        }

        #detail_window .mindow_icon .title_icon {
        ");
                WriteLiteral(@"    display: inline-block;
        }

        #detail_window .mindow_icon {
            text-align: center;
            padding: 20px 0;
        }

        #detail_window .layui-form-item {
            margin-bottom: 0;
        }
        /* 定义表头样式 */
        .layui-table-header .layui-table-cell {
            font-weight: bold; /* 加粗 */
            font-size: 14px; /* 可选：调整字体大小以提升可读性 */
            color: #333; /* 可选：调整字体颜色 */
        }

        .layui-table-g {
            background-color: #f2f2f2; /* Light gray background color */
            cursor: pointer; /* Change cursor to pointer */
            transition: background-color 0.3s; /* Smooth transition for background color */
        }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "ad6ece4fdea23df87982f7129de1eab8b7bc1eb75dc28fbe6e869934eacf4ce013526", async() => {
                WriteLiteral(@"
    <div class=""layui-col-md12"">
        <div class=""layui-card"">
            <!--编辑区-->
            <div class=""layui-card-body layui-form"">
                <div class=""layui-inline"">
                    <label class=""layui-form-label"">作业名称</label>
                    <div class=""layui-input-inline"">
                        <input type=""text"" name=""Id"" id=""Id"" style=""display:none;""");
                BeginWriteAttribute("value", " value=\"", 2709, "\"", 2726, 1);
                WriteAttributeValue("", 2717, 
#nullable restore
#line 98 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\BatchInspectExtractionTask\Index.cshtml"
                                                                                           Model.Id

#line default
#line hidden
#nullable disable
                , 2717, 9, false);
                EndWriteAttribute();
                WriteLiteral(" />\r\n                        <input type=\"text\" name=\"TaskName\" id=\"TaskName\" required lay-verify=\"required\" placeholder=\"作业名称\" autocomplete=\"off\" class=\"layui-input\"");
                BeginWriteAttribute("value", " value=\"", 2893, "\"", 2916, 1);
                WriteAttributeValue("", 2901, 
#nullable restore
#line 99 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\BatchInspectExtractionTask\Index.cshtml"
                                                                                                                                                                          Model.TaskName

#line default
#line hidden
#nullable disable
                , 2901, 15, false);
                EndWriteAttribute();
                WriteLiteral(@" style=""width:180px;"" readonly>
                    </div>
                </div>
                <div class=""layui-inline"">
                    <label class=""layui-form-label"" style=""width:100px;"">下次执行时间</label>
                    <div class=""layui-input-block "" style=""margin-left:130px;"">
                        <input type=""text"" name=""Cron"" id=""Cron"" placeholder=""任务执行时间"" autocomplete=""off"" class=""layui-input""");
                BeginWriteAttribute("value", " value=\"", 3340, "\"", 3366, 1);
                WriteAttributeValue("", 3348, 
#nullable restore
#line 105 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\BatchInspectExtractionTask\Index.cshtml"
                                                                                                                                     Model.NextRunTime

#line default
#line hidden
#nullable disable
                , 3348, 18, false);
                EndWriteAttribute();
                WriteLiteral(@" readonly style=""width:150px;"">
                    </div>
                </div>
                <div class=""layui-inline"">
                    <button id=""enable"" class=""layui-btn layui-btn-normal"" lay-filter=""enable"">
                        <i class=""layui-icon layui-icon-play""></i> 启用
                    </button>
                    <button id=""pause"" class=""layui-btn layui-btn-danger"" lay-filter=""pause"">
                        <i class=""layui-icon layui-icon-pause""></i> 暂停
                    </button>
                    <button id=""run"" class=""layui-btn layui-btn-warm"" lay-filter=""run"">
                        <i class=""layui-icon layui-icon-refresh""></i> 立即执行
                    </button>
                </div>
            </div>
            <!--搜索区-->
            <div class=""line_wrap search_wrap"">
                <div>
                    <div class=""layui-inline"">
                        <label class=""layui-form-label"">搜索条件</label>
                        <div class=""layui-in");
                WriteLiteral(@"put-inline"">
                            <div id=""xmGroupsList"" class=""xm-select-demo"" style=""width:150px""></div>
                        </div>

                        <div class=""layui-inline"">
                            <label class=""layui-input-inline"">执行状态</label>
                            <div class=""layui-input-inline"">
                                <div id=""IsExec"" class=""xm-select-demo"" style=""width:150px""></div>
                            
                            </div>
                        </div>
                      

                        <div class=""layui-inline"">
                            <div id=""xmSelectFormList"" class=""xm-select-demo"" style=""width:250px""></div>
                        </div>
                        <div class=""layui-input-inline"">
                            <input type=""text"" class=""layui-input"" name=""keyWord"" placeholder=""请输入患者ID或项目代码或项目名称"" id=""keyWord"" style=""width:250px"" />
                        </div>
                        <butt");
                WriteLiteral(@"on id=""Search"" class=""layui-btn layui-btn-primary layui-border-green""><i class=""layui-icon layui-icon-search""></i> </button>
                    </div>
                </div>
            </div>

            <div class=""layui-row"">
                <div class=""layui-col-xs12"">

                    <div class=""layui-card-body table_wrap"">
                        <table id=""tablelist"" lay-filter=""tablelist""></table>
                        <script type=""text/html"" id=""tableBar1"">
                            <a class=""layui-btn layui-btn-xs"" lay-event=""show"">显示</a>
                        </script>


                    </div>

                </div>

            </div>
        </div>

    </div>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "ad6ece4fdea23df87982f7129de1eab8b7bc1eb75dc28fbe6e869934eacf4ce019543", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "ad6ece4fdea23df87982f7129de1eab8b7bc1eb75dc28fbe6e869934eacf4ce020667", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "ad6ece4fdea23df87982f7129de1eab8b7bc1eb75dc28fbe6e869934eacf4ce021791", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "ad6ece4fdea23df87982f7129de1eab8b7bc1eb75dc28fbe6e869934eacf4ce022915", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
        layui.use(['element', 'layer', 'table', 'laydate', 'form'], function () {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , laydate = layui.laydate
                , $ = layui.$
                , form = layui.form;
            ;

            var data = [
                { title: '全部', id: '全部' },
                { title: '放射', id: '放射' },
                { title: '超声', id: '超声' },
                { title: '消化内镜', id: '消化内镜' },
           
            ];

            var IsExecdata = [
                { title: '全部', id: '' },
                { title: '已执行', id: '1' },
                { title: '未执行', id: '0' },
                { title: '执行异常', id: '-1' },

            ];

            var xmIsExec = xmSelect.render({
                el: '#IsExec',
                model: { label: { type: 'text' } },
                prop: {
                    name: 'title',
                ");
                WriteLiteral(@"    value: 'id',
                },
                minWidth: 350,
                height: 350,
                radio: true,
                filterable: true,
                clickClose: true,
                tree: {
                    show: true,
                    strict: false,
                    expandedKeys: [-1],
                },
                data: IsExecdata,
                initValue: [IsExecdata[0]],  // 动态获取第一个选项
                on: function (val) {
                    
                }
            });


            var xmGroupsList = xmSelect.render({
                el: '#xmGroupsList',
                model: { label: { type: 'text' } },
                prop: {
                    name: 'title',
                    value: 'id',
                },
                minWidth: 350,
                height: 350,
                radio: true,
                filterable: true,
                clickClose: true,
                tree: {
                    show: true,
");
                WriteLiteral(@"
                    strict: false,
                    expandedKeys: [-1],
                },
                data: data,
                initValue: [data[0]],  // 动态获取第一个选项
                on: function (val) {
                    if (val.arr && val.arr.length > 0) {
                        getFormList(val.arr[0].id);
                    }
                }
            });
            // 手动触发 getFormList 函数
            if (data && data.length > 0) {
                getFormList(data[0].id); // 使用数据源中的第一个选项的 id 调用函数
            }

            var xmSelectFormList = xmSelect.render({
                el: '#xmSelectFormList',
                autoRow: true,
                radio: true,
                prop: {
                    name: 'name',
                    value: 'id',
                },
                minWidth: 350,
                height: 350,
                filterable: true,
                tips: '请选择CRF表单',
                on: function (val) {
                    $.ajax({
");
                WriteLiteral(@"                        url: '/Demo/BatchInspectExtractionTask/GetTableColmns?FormID=' + val.arr[0].id,
                        type: ""get"",
                        datatype: 'json',
                        success: function (result) {
                            tableColumns = [result.columns];
                            tableResult = table.render({
                                elem: '#pathologyResult'
                                , id: 'pathologyResult'
                                , minHeight: 100 // 设置表格的最小高度
                                , limit: Number.MAX_VALUE // 数据表格默认全部显示
                                , page: false
                                , cols: tableColumns
                                , data: []
                                , done: function (res, curr, count, origin) {
                                    // 设置 .layui-table-box 的 overflow-y 属性为 scroll
                                    //$('[lay-id=""pathologyResult""]').find('.layui-table-box').css('overf");
                WriteLiteral(@"low-x', 'scroll');
                                    // 设置 .layui-table-header 的 overflow-y 属性为 scroll
                                    $('[lay-id=""pathologyResult""]').find('.layui-table-header').css('overflow-x', 'scroll');
                                }
                            });

                            //InsertTable();

                        }, error: function () {
                            layer.msg(""获取失败！"");
                        }
                    });
                },
                done: function (res) {
                }
            })
            function getFormList(val) {
                $.ajax({
                    url: '/Demo/BatchInspectExtractionTask/GetFormList?val=' + val,
                    type: ""get"",
                    datatype: 'json',
                    success: function (result) {
                        xmSelectFormList.update({
                            data: result.data
                        });
                    }, err");
                WriteLiteral(@"or: function () {
                        layer.msg(""获取失败！"");
                    }
                });
            }

            table.render({
                elem: '#tablelist'
                , id: 'tablelist'
                , page: true
               , limit:  30
                , height: 'full-145'
                , cols: [[
                    { field: 'No', title: '序号', type: 'numbers', fixed: 'left' }
                    , { field: 'Id', title: 'Id', sort: true, width: '6%', hide: true }
                    , { field: 'PatientId', title: '患者Id', width: 100 }
                    , { field: 'PatientName', title: '患者姓名', width: 100 }
                    , { field: 'PatientSource', title: '患者来源', width: 100 }
                    , { field: 'DocDetailedNo', title: '医嘱号', width: 100 }
                    , { field: 'ProjectNo', title: '项目编码', width: 100 }
                    , { field: 'ProjectName', title: '项目名称', width: 100 }
                    , { field: 'SystemId', title: '医技标识");
                WriteLiteral(@"', width: 100 }
                    , { field: 'CRFormId', title: '表单id', width: 100 }
                    , { field: 'FormName', title: '表单名称', minWidth: 200 }
                    , { field: 'KeyWords', title: '检索关键字', width: 100 }
                    , { field: 'ExtractBatch', title: '提取批次', width: 100 }
                    , {
                        field: 'IsExec', title: '状态', sort: true, width: 90, templet: function (d) {
                            if (d.IsExec === 1) {
                                return '<span style=""color: green"">成功</span>';
                            } else if (d.IsExec === -1) {
                                return '<span style=""color: red"">失败</span>';
                            } else {
                                return '<span>未执行</span>';
                            }
                        }
                    },
                    , { field: 'ExecErrorCount', title: '错误次数', width: 80 }
                    , {
                        field: 'Ex");
                WriteLiteral(@"ecMsg', title: '错误信息', width: 200, templet: function (d) {
                            if (d.IsExec === -1) {
                                return '<span style=""color: red"">' + d.ExecMsg == null || d.ExecMsg == undefined ? """" : d.ExecMsg + '</span>';
                            } else {
                                return '<span>' + d.ExecMsg == null || d.ExecMsg == undefined ? """" : d.ExecMsg + '</span>';
                            }
                        }
                    }
                    , { field: 'ExecTime', title: '执行时间', width: 150 }
                    , { title: '操作', toolbar: '#tableBar1', width: 100, fixed: 'right' }
                ]]
                , done: function (res) {
                    
                }

            });

            //监听tablelist工具条
            table.on('tool(tablelist)', function (obj) {
                var data = obj.data;
                if (obj.event === 'show') {
                  

                    var patientid = 0;//  enc");
                WriteLiteral(@"odeURIComponent(data.PatientId);
                    var jsonstr = encodeURIComponent(data.AIExtractJsonValue) ;
                    var formId = encodeURIComponent(data.CRFormId);
                    var Fid = encodeURIComponent(data.CRFId);
                    //console.log(jsonstr);
                    var m = ""/ReportingManage/PatientManage/detail?json="" + jsonstr + ""&formId="" + formId + ""&ResearchPatientId="" + patientid + ""&Fid="" + Fid;
                    parent.layui.index.openTabsPage(""/ReportingManage/PatientManage/detail?json="" + jsonstr + ""&formId="" + formId + ""&ResearchPatientId="" + patientid + ""&Fid="" + Fid, ""CRF表单详情"");
                   
                }
            });

            function SearchData() {

                table.reload('tablelist', {
                    page: {
                        curr: 1
                    },
                    url: '/Demo/BatchInspectExtractionTask/TaskCollectsList'
                    , where: {
                        'SystemId': x");
                WriteLiteral(@"mGroupsList.getValue('valueStr'),
                        'CRFormId': xmSelectFormList.getValue('valueStr'),
                        'keyword': $.trim($(""#keyWord"").val()),
                        'IsExec': xmIsExec.getValue('valueStr'),
                    }
                });
            };
            $(document).ready(function () {

                SearchData();
                $(document).on('click', '#Search', function () {
                    SearchData();
                })

                $(document).on('click', '#enable', function () {
                    operate(""Start"");
                });
                $(document).on('click', '#pause', function () {
                    operate(""Pause"");
                });
                $(document).on('click', '#run', function () {
                    operate(""Run"");
                });
                $(document).on('click', '#state', function () {
                    getStatus();
                });

            });
          ");
                WriteLiteral(@"  function operate(type) {
                var indexs = layer.load();
                $.ajax({
                    url: ""/Demo/BatchInspectExtractionTask/"" + type,
                    type: ""post"",
                    data: { ""Id"": $(""#Id"").val() },
                    success: function (res) {
                        if (res.okMsg) {
                            layer.msg(res.okMsg);
                        }
                        else {
                            layer.msg(res.errorMsg);
                        }
                        layer.close(indexs);
                    }
                });
            }



            function getStatus() {
                $.ajax({
                    url: ""/Demo/BatchInspectExtractionTask/GetOnlineJobById"",
                    type: ""post"",
                    data: { taskName: $(""#TaskName"").val() },
                    success: function (res) {
                        if (res.code == 0) {
                            layer.msg(res.dat");
                WriteLiteral(@"a);
                        } else {
                            layer.msg(res.errorMsg);
                        }
                        layer.close(indexs);
                    }
                });
            }
            function setTableH() {
                var winH = $(window).height();
                var navH = $("".layui-tab-brief"").height();
                var searchH = $("".search_wrap"").height();
                var editAreaH = $("".edit_area"").height();
                var tableH = winH - (navH + searchH + editAreaH) - 55 + ""px"";
                console.log(tableH)
                $("".table_wrap"").css(""height"", tableH);

            };
            setTableH();
            $(window).resize(function () {
                setTableH()
            });
        });
    </script>

");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<AngelwinResearch.Models.TaskInfo> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
