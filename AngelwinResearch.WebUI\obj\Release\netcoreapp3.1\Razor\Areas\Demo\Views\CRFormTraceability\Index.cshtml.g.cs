#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\CRFormTraceability\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "4fb2188d7ae6a002af7058c83d0df3c65044becbdc19af0e891936ca48e5b675"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_Demo_Views_CRFormTraceability_Index), @"mvc.1.0.view", @"/Areas/Demo/Views/CRFormTraceability/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"4fb2188d7ae6a002af7058c83d0df3c65044becbdc19af0e891936ca48e5b675", @"/Areas/Demo/Views/CRFormTraceability/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_Demo_Views_CRFormTraceability_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/touchslider/touchslider.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin\\layui\\font\\web_font\\iconfont.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/columnDrag/column_drag.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("type", new global::Microsoft.AspNetCore.Html.HtmlString("text/javascript"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/columnDrag/column_drag.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\CRFormTraceability\Index.cshtml"
  
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4fb2188d7ae6a002af7058c83d0df3c65044becbdc19af0e891936ca48e5b6757094", async() => {
                WriteLiteral("\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>CRF溯源</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "4fb2188d7ae6a002af7058c83d0df3c65044becbdc19af0e891936ca48e5b6757522", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "4fb2188d7ae6a002af7058c83d0df3c65044becbdc19af0e891936ca48e5b6758724", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "4fb2188d7ae6a002af7058c83d0df3c65044becbdc19af0e891936ca48e5b6759926", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "4fb2188d7ae6a002af7058c83d0df3c65044becbdc19af0e891936ca48e5b67511128", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"

    <style>
        .layui-tab-title {
            border-bottom-width: 0;
        }

        .layui-tab-brief > .layui-tab-more li.layui-this:after, .layui-tab-brief > .layui-tab-title .layui-this:after {
            border-bottom: 2px solid #50314F;
        }

        .panel-header {
            border-bottom: 2px solid #50314F;
        }

        .panel-content {
            overflow-y: auto;
            overflow-x: hidden;
        }

        .top_right {
            display: flex;
            flex-direction: row;
            align-items: baseline;
        }

        .project-info {
            font-size: 18px;
            padding-left: 10px;
            font-weight: bold;
            color: #1E9FFF;
        }

        .user-name {
            padding-right: 10px;
        }

        .unselectable {
            -webkit-user-select: none; /* Safari */
            -moz-user-select: none; /* Firefox */
            -ms-user-select: none; /* Internet Explorer/Edge */
  ");
                WriteLiteral("          user-select: none; /* 标准语法 */\r\n        }\r\n\r\n\r\n    </style>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4fb2188d7ae6a002af7058c83d0df3c65044becbdc19af0e891936ca48e5b67514171", async() => {
                WriteLiteral(@"
    <div class=""container"">
        <div class=""top-nav"">
            <div class=""layui-tab layui-tab-brief"" lay-filter=""docDemoTabBrief"">
                <ul class=""layui-tab-title"">
                    <li class=""layui-this"">CRF资料核查</li>
                    <li>修改历史</li>
                    <li>检査历史</li>
                </ul>
            </div>
            <div class=""top_right"">
                <div class=""user-info"">
                    <div class=""user-avatar"">郭春芳</div>
                    <div class=""user-name"">55岁</div>
                    <div>男</div>
                </div>
                <div class=""project-info"">
                    <span>数据采集点1</span>
                </div>
            </div>
        </div>
        <div class=""main-content"">
            <div class=""left-panel"">

                <div class=""layui-tab-item layui-show"">
                    <div class=""panel-header"">CRF资料核查</div>
                    <div class=""panel-content"">
                        <div c");
                WriteLiteral(@"lass=""record-section"">
                            <p>
                                <strong>既往史：</strong>“乙肝小三阳”病史20余年，未诊治。否认结核、疟疾病史 ，否认高血压、心脏病史，否认糖尿病、脑血管疾病、精神疾病史，否认手术、外伤史，否认输血史，否认食物过敏史，预防接种史不详。
                            </p>
                        </div>
                        <div class=""record-section"">
                            <p>
                                <strong>个人史：</strong>生于福建省莆田市涵江区，久居本地，无疫区、疫情、疫水接触史，吸烟40余年，平均1包／日，未戒烟。无饮酒史。
                            </p>
                        </div>
                        <div class=""record-section"">
                            <p>
                                <strong>现病史：</strong>缘于入院前15天无明显诱因出现持续性中上腹闷胀，进食后加重，活动后可稍缓解，伴皮肤瘙痒、尿黄、嗳气，无恶心、呕吐，无腹痛、腹泻，无呕血、排黑便，无腰痛、尿频、尿急、尿痛，无皮疹、双下肢水肿等不适。遂就诊当地诊所，予胃药治疗后（具体不详），症状无明显好转。2天前无明显诱因出现左上腹刺痛，阵发性，疼痛无放射他处，排便排气后可缓解，2-3次/天，持续时间约1-2分钟/次，无恶心、呕吐，无乏力、纳差等不适。现为进一步诊治就诊我院门诊，门诊拟“肝功能异常待查”收入院。自发病以来，精神、睡眠尚可，食欲一般，大便正常，小便如上述，体重减轻约2kg。
                            </p>
                        </div>
                  ");
                WriteLiteral(@"      <div class=""record-section"">
                            <p>
                                <strong>婚姻史：</strong>适龄结婚,育有2女,配偶及女儿体健。
                            </p>
                        </div>
                        <div class=""record-section"">
                            <p><strong>检查及诊治：</strong></p>
                            <div class=""subsection-header"">体格检查：</div>
                            <p>
                                T:36.5℃;P:66次／分;R:19次／分;BP:103/67mmHg。神志清楚，查体合作。全身皮肤粘膜黄染，无紫绀，无皮疹、皮下出血。毛发分布正常，可见肝掌，皮下无水肿，无蜘蛛痣。全身浅表淋巴结未触及肿大。眼结膜黄染。双肺呼吸音清晰，未闻及干湿性啰音，无胸膜摩擦音。心率66次/分，律齐，A2>P2，各瓣膜听诊区未闻及杂音，无心包摩擦音。腹平坦，未见胃形、肠形，无腹壁静脉曲张，腹壁柔软，全腹无压痛、反跳痛，未触及包块，肝脾脏肋下未触及，墨菲氏征阴性。肠鸣音4次/分。
                            </p>
                            <div class=""divider""></div>
                            <div class=""subsection-header"">辅助检查：</div>
                            <p>肝脏MRI平扫+增强+MRCP:1、肝门区浸润性病变，考虑胆管细胞癌可能，并多发肝内胆管梗阻性扩张;肝门区多发肿大淋巴结，部分为MT待除。2、肝Ⅱ段包膜下富血供病变，考虑为血管瘤;余肝内多发小囊肿可能。3、肝硬化伴再生结节。4、腹腔少量积液。5、扫及下腰椎椎体上缘");
                WriteLiteral(@"异常强化灶，首先考虑终板骨软骨可能，MT待除。</p>
                            <p>全腹部CT平扫+增强:1、肝门区不规则浸润性病变，考虑为恶性肿瘤，胆管癌可能性大，伴肝内胆管多发扩张，肝门区多发肿大淋巴结，建议结合MRI检查及复查。2、余肝内多发小肿，肝右叶小钙化灶;肝Ⅱ段血管瘤;肝左叶动脉期强化灶，考虑为异常灌注。3、慢性胆.炎可能。4、右肾小结石。5、前列腺增生。6、所摄入双肺肺气肿，双侧少量胸腔积液，建议结合相关检查。</p>
                        </div>
                        <div class=""record-section"">
                            <p><strong>初诊诊断：</strong>1.肝功能异常待查;2.慢性HBV携带；</p>
                        </div>
                        <div class=""record-section"">
                            <p><strong>治疗方案：</strong>科内讨论或MDT会诊，经讨论后制定如下治疗方案:肝门部胆管根治术。</p>
                        </div>

                    </div>
                </div>

            </div>
            <div class=""resizer "">
                <div class=""resize_btn unselectable"">⋮</div>
            </div>
            <div class=""right-panel"">
                <div class=""panel-header"">CRF 项目</div>
                <div class=""crf_btn_group"">
                    <div class=""status-bar"">
                        <div c");
                WriteLiteral(@"lass=""status-item active"">传统筛查</div>
                        <div class=""status-item"">已核查确认</div>
                        <div class=""status-item"">待核查确认</div>
                        <div class=""status-item"">存在逻辑错误</div>
                        <button class=""submit-button"">提交</button>
                    </div>
                    <div class=""button-group"">
                        <button class=""layui-btn "">确认全部项目</button>
                        <button class=""layui-btn layui-btn-normal"">仅确认待核查项目</button>
                        <button class=""layui-btn layui-btn-danger"">取消确认全部项目</button>
                        <button class=""layui-btn layui-btn-danger"">取消确认已核查项目</button>
                    </div>
                </div>

                <div class=""iframe_content"">
                    <iframe id=""crformframe""");
                BeginWriteAttribute("src", " src=\"", 6553, "\"", 6559, 0);
                EndWriteAttribute();
                WriteLiteral(" width=\"99%\" height=\"98%\">\r\n                    </iframe>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n    </div>\r\n\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4fb2188d7ae6a002af7058c83d0df3c65044becbdc19af0e891936ca48e5b67519927", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4fb2188d7ae6a002af7058c83d0df3c65044becbdc19af0e891936ca48e5b67521138", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4fb2188d7ae6a002af7058c83d0df3c65044becbdc19af0e891936ca48e5b67522349", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script type=""text/javascript"">
        layui.use(['jquery', 'layer', 'form', 'element', 'code', 'laytpl', 'table'], function () {
            var layer = layui.layer,
                $ = layui.$,
                form = layui.form,
                laytpl = layui.laytpl,
                element = layui.element,
                table = layui.table;

            var JsonObj = {
                ""variables"": [
                    {
                        ""variable_name"": ""ALCOHOLHISTORY"",
                        ""变量描述"": ""饮酒史"",
                        ""value"": ""无"",
                        ""source"": ""无饮酒史。""
                    },
                    {
                        ""variable_name"": ""SURGERYHISTORYDESC"",
                        ""变量描述"": ""手术史具体描述"",
                        ""value"": """",
                        ""source"": ""否认手术、外伤史，否认输血史，否认食物过敏史，预防接种史不详。""
                    },
                    {
                        ""variable_name"": ""SURGERYHISTORY"",
                        ");
                WriteLiteral(@"""变量描述"": ""手术史"",
                        ""value"": ""无"",
                        ""source"": ""否认手术、外伤史，否认输血史，否认食物过敏史，预防接种史不详。""
                    },
                    {
                        ""variable_name"": ""SMOKINGYEARS"",
                        ""变量描述"": ""吸烟时长/年"",
                        ""value"": ""40"",
                        ""source"": ""吸烟40余年，平均1包／日，未戒烟。""
                    },
                    {
                        ""variable_name"": ""SMOKINGHISTORY"",
                        ""变量描述"": ""吸烟史"",
                        ""value"": ""持续吸烟"",
                        ""source"": ""吸烟40余年，平均1包／日，未戒烟。""
                    },
                    {
                        ""variable_name"": ""OTHER_PHYSICAL_EXAMINATION"",
                        ""变量描述"": ""其他体格检查描述"",
                        ""value"": ""神志清楚，查体合作。全身皮肤粘膜黄染，无紫绀，无皮疹、皮下出血。毛发分布正常，可见肝掌，皮下无水肿，无蜘蛛痣。全身浅表淋巴结未触及肿大。眼结膜黄染。双肺呼吸音清晰，未闻及干湿性啰音，无胸膜摩擦音。心率66次/分，律齐，A2>P2，各瓣膜听诊区未闻及杂音，无心包摩擦音。腹平坦，未见胃形、肠形，无腹壁静脉曲张，腹壁柔软，全腹无压痛、反跳痛，未触及包块，肝脾脏肋下未触及，墨菲氏征阴性。肠鸣音4次/分。"",
          ");
                WriteLiteral(@"              ""source"": ""神志清楚，查体合作。全身皮肤粘膜黄染，无紫绀，无皮疹、皮下出血。毛发分布正常，可见肝掌，皮下无水肿，无蜘蛛痣。全身浅表淋巴结未触及肿大。眼结膜黄染。双肺呼吸音清晰，未闻及干湿性啰音，无胸膜摩擦音。心率66次/分，律齐，A2&gt;P2，各瓣膜听诊区未闻及杂音，无心包摩擦音。腹平坦，未见胃形、肠形，无腹壁静脉曲张，腹壁柔软，全腹无压痛、反跳痛，未触及包块，肝脾脏肋下未触及，墨菲氏征阴性。肠鸣音4次/分。""
                    },
                    {
                        ""variable_name"": ""HISTORYDESC"",
                        ""变量描述"": ""既往病史具体描述"",
                        ""value"": ""“乙肝小三阳”病史20余年，未诊治。"",
                        ""source"": ""“乙肝小三阳”病史20余年，未诊治。""
                    },
                    {
                        ""variable_name"": ""HISTORY"",
                        ""变量描述"": ""既往病史"",
                        ""value"": ""有"",
                        ""source"": ""“乙肝小三阳”病史20余年，未诊治。""
                    },
                    {
                        ""variable_name"": ""OTHERHISTORY"",
                        ""变量描述"": ""过敏史"",
                        ""value"": ""无"",
                        ""source"": ""否认食物过敏史""
                    },
                    {
                     ");
                WriteLiteral(@"   ""variable_name"": ""NEUROPSYCHDISDESC"",
                        ""变量描述"": ""神经精神疾病描述"",
                        ""value"": """",
                        ""source"": ""否认糖尿病、脑血管疾病、精神疾病史，否认手术、外伤史，否认输血史，否认食物过敏史，预防接种史不详。""
                    },
                    {
                        ""variable_name"": ""NEUROPSYCHDIS"",
                        ""变量描述"": ""神经精神疾病"",
                        ""value"": ""无"",
                        ""source"": ""否认糖尿病、脑血管疾病、精神疾病史，否认手术、外伤史，否认输血史，否认食物过敏史，预防接种史不详。""
                    },
                    {
                        ""variable_name"": ""MARITALSTATUS"",
                        ""变量描述"": ""婚姻状况"",
                        ""value"": ""maritalStatusM"",
                        ""source"": ""适龄结婚,育有2女,配偶及女儿体健。""
                    },
                    {
                        ""variable_name"": ""INITIAL_DIAGNOSIS"",
                        ""变量描述"": ""初步诊断"",
                        ""value"": ""1.肝功能异常待查;2.慢性HBV携带；"",
                        ""source"": ""1.肝功能异常待查;2.慢性HBV携带；""
               ");
                WriteLiteral(@"     },
                    {
                        ""variable_name"": ""SYSTOLIC_BLOOD_PRESSURE"",
                        ""变量描述"": ""收缩压"",
                        ""value"": ""103"",
                        ""source"": ""BP:103/67mmHg。""
                    },
                    {
                        ""variable_name"": ""IMMUNEDISDESC"",
                        ""变量描述"": ""免疫系统疾病具体描述"",
                        ""value"": """",
                        ""source"": ""“乙肝小三阳”病史20余年，未诊治。""
                    },
                    {
                        ""variable_name"": ""HEART_RATE"",
                        ""变量描述"": ""心率"",
                        ""value"": ""66"",
                        ""source"": ""P:66次／分;""
                    },
                    {
                        ""variable_name"": ""DIASTOLIC_BLOOD_PRESSURE"",
                        ""变量描述"": ""舒张压"",
                        ""value"": ""67"",
                        ""source"": ""BP:103/67mmHg。""
                    },
                    {
                   ");
                WriteLiteral(@"     ""variable_name"": ""DESCRIPTION_CONDITION"",
                        ""变量描述"": ""病情描述"",
                        ""value"": ""缘于入院前15天无明显诱因出现持续性中上腹闷胀，进食后加重，活动后可稍缓解，伴皮肤瘙痒、尿黄、嗳气，无恶心、呕吐，无腹痛、腹泻，无呕血、排黑便，无腰痛、尿频、尿急、尿痛，无皮疹、双下肢水肿等不适。"",
                        ""source"": ""缘于入院前15天无明显诱因出现持续性中上腹闷胀，进食后加重，活动后可稍缓解，伴皮肤瘙痒、尿黄、嗳气，无恶心、呕吐，无腹痛、腹泻，无呕血、排黑便，无腰痛、尿频、尿急、尿痛，无皮疹、双下肢水肿等不适。""
                    },
                    {
                        ""variable_name"": ""CIGARETTESPERDAY"",
                        ""变量描述"": ""吸烟包数/日"",
                        ""value"": ""1"",
                        ""source"": ""吸烟40余年，平均1包／日，未戒烟。""
                    },
                    {
                        ""variable_name"": ""CHECK_RESULTS2"",
                        ""变量描述"": ""检查结果2"",
                        ""value"": ""全腹部CT平扫+增强:1、肝门区不规则浸润性病变，考虑为恶性肿瘤，胆管癌可能性大，伴肝内胆管多发扩张，肝门区多发肿大淋巴结，建议结合MRI检查及复查。2、余肝内多发小肿，肝右叶小钙化灶;肝Ⅱ段血管瘤;肝左叶动脉期强化灶，考虑为异常灌注。3、慢性胆.炎可能。4、右肾小结石。5、前列腺增生。6、所摄入双肺肺气肿，双侧少量胸腔积液，建议结合相关检查。"",
                        ""source"": ""全腹部CT平扫+增强:1、肝门区不规则浸润");
                WriteLiteral(@"性病变，考虑为恶性肿瘤，胆管癌可能性大，伴肝内胆管多发扩张，肝门区多发肿大淋巴结，建议结合MRI检查及复查。2、余肝内多发小肿，肝右叶小钙化灶;肝Ⅱ段血管瘤;肝左叶动脉期强化灶，考虑为异常灌注。3、慢性胆.炎可能。4、右肾小结石。5、前列腺增生。6、所摄入双肺肺气肿，双侧少量胸腔积液，建议结合相关检查。""
                    },
                    {
                        ""variable_name"": ""CHECK_RESULTS1"",
                        ""变量描述"": ""检查结果1"",
                        ""value"": ""肝脏MRI平扫+增强+MRCP:1、肝门区浸润性病变，考虑胆管细胞癌可能，并多发肝内胆管梗阻性扩张;肝门区多发肿大淋巴结，部分为MT待除。2、肝Ⅱ段包膜下富血供病变，考虑为血管瘤;余肝内多发小囊肿可能。3、肝硬化伴再生结节。4、腹腔少量积液。5、扫及下腰椎椎体上缘异常强化灶，首先考虑终板骨软骨可能，MT待除。"",
                        ""source"": ""肝脏MRI平扫+增强+MRCP:1、肝门区浸润性病变，考虑胆管细胞癌可能，并多发肝内胆管梗阻性扩张;肝门区多发肿大淋巴结，部分为MT待除。2、肝Ⅱ段包膜下富血供病变，考虑为血管瘤;余肝内多发小囊肿可能。3、肝硬化伴再生结节。4、腹腔少量积液。5、扫及下腰椎椎体上缘异常强化灶，首先考虑终板骨软骨可能，MT待除。""
                    },
                    {
                        ""variable_name"": ""CHECK_NAME2"",
                        ""变量描述"": ""检查名称2"",
                        ""value"": ""全腹部CT平扫+增强"",
                        ""source"": ""全腹部CT平扫+增强:1、肝门区不规则浸润性病变，考虑为恶性肿瘤，胆管癌可能性大，伴肝内胆管多发扩张，肝门区多发肿大淋巴结，建议结合MRI检查及复查。""
                    },
   ");
                WriteLiteral(@"                 {
                        ""variable_name"": ""CHECK_NAME1"",
                        ""变量描述"": ""检查名称1"",
                        ""value"": ""肝脏MRI平扫+增强+MRCP"",
                        ""source"": ""肝脏MRI平扫+增强+MRCP:1、肝门区浸润性病变，考虑胆管细胞癌可能，并多发肝内胆管梗阻性扩张;肝门区多发肿大淋巴结，部分为MT待除。""
                    },
                    {
                        ""variable_name"": ""CARDIODISDESC"",
                        ""变量描述"": ""心血管系统疾病具体描述"",
                        ""value"": """",
                        ""source"": ""否认高血压、心脏病史，否认糖尿病、脑血管疾病、精神疾病史，否认手术、外伤史，否认输血史，否认食物过敏史，预防接种史不详。""
                    },
                    {
                        ""variable_name"": ""CARDIODIS"",
                        ""变量描述"": ""心血管系统疾病"",
                        ""value"": ""无"",
                        ""source"": ""否认高血压、心脏病史，否认糖尿病、脑血管疾病、精神疾病史，否认手术、外伤史，否认输血史，否认食物过敏史，预防接种史不详。""
                    },
                    {
                        ""variable_name"": ""BODY_TEMPERATURE"",
                        ""变量描述"": ""体温"",
                        ");
                WriteLiteral(@"""value"": ""36.5℃"",
                        ""source"": ""T:36.5℃;P:66次／分;R:19次／分;BP:103/67mmHg。""
                    },
                    {
                        ""variable_name"": ""IMMUNEDIS"",
                        ""变量描述"": ""免疫系统疾病"",
                        ""value"": ""无"",
                        ""source"": ""否认高血压、心脏病史，否认糖尿病、脑血管疾病、精神疾病史，否认手术、外伤史，否认输血史，否认食物过敏史，预防接种史不详。""
                    },
                    {
                        ""variable_name"": ""TREATMENT_OPTIONS"",
                        ""变量描述"": ""治疗方案"",
                        ""value"": ""肝门部胆管根治术"",
                        ""source"": ""科内讨论或MDT会诊，经讨论后制定如下治疗方案:肝门部胆管根治术。""
                    }
                ]
            };

            //患者列表的高度
            function setPanelH() {
                var winH = $(window).height();
                var topNavH = $("".top-nav"").height();
                var titleH = $("".panel-header"").height();
                var panel_H = winH - (topNavH + titleH) - 70 + ""px"";
                $("".panel-co");
                WriteLiteral(@"ntent"").css(""height"", panel_H);

                var CRFBtn = $("".crf_btn_group"").height();
                var iframe_H = winH - (topNavH + titleH + CRFBtn) - 70 + ""px"";
                $("".iframe_content"").css(""height"", iframe_H);
            };
            setPanelH();

            $(document).ready(function () {
                LoadCRForm();
            });

            function LoadCRForm() {
                 var token = """);
                Write(
#nullable restore
#line 364 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\CRFormTraceability\Index.cshtml"
                               ViewBag.token

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\";\r\n                var url = \"");
                Write(
#nullable restore
#line 365 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\CRFormTraceability\Index.cshtml"
                             ViewBag.formUrl

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"cbdac379b5bf41eab44042290dea7326.form?token=""+token;
                 setTimeout(function () {
                     $(""#crformframe"").attr(""src"", url);
                     $('#crformframe').attr('src', $('#crformframe').attr('src'));
                 }, 500);


                setTimeout(function () {
                    // 发送消息到子页面
                    var iframe = document.getElementById('crformframe');
                    iframe.contentWindow.postMessage({ data: JSON.stringify(JsonObj) }, ""*"");
                }, 2500);
            }

            $(window).resize(function () {
                setPanelH();
            });


            // 定义一个函数，接受variable_name作为参数
            function getSourceByVariableName(variableName) {
                var node = JsonObj.variables.find(function (item) {
                    return item.variable_name === variableName;
                });
                return node ? node.source : ""NotFound"";
            }

             // 父窗口
            wind");
                WriteLiteral(@"ow.addEventListener('message', function (event) {
                if (event.data.action === 'show') {
                    var fieldname = event.data.fieldname;
                    var searchString = getSourceByVariableName(fieldname);
                    var sourceList = $("".record-section"");
                    sourceList.each(function (index, element) {
                        var textContent = $(this).html();
                        if (textContent.includes('<span style=""color:red; font-weight:bold;"">')) {
                            textContent = textContent.replace('<span style=""color:red; font-weight:bold;"">', """")
                                        .replace(""</span>"", """");
                        }
                        // 检查textContent是否包含特定字符串
                        if (searchString != ""NotFound""&& textContent.includes(searchString)) {
                            textContent = textContent.replace(searchString, '<span style=""color:red; font-weight:bold;"">' + searchString + '</span>");
                WriteLiteral("\');\r\n                        }\r\n                        $(this).html(textContent);\r\n\r\n                    });\r\n                }\r\n\r\n            }, false);\r\n\r\n\r\n\r\n\r\n        });\r\n    </script>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
