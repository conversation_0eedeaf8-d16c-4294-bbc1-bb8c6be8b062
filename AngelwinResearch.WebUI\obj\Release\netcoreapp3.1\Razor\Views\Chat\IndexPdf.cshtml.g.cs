#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Chat\IndexPdf.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_Chat_IndexPdf), @"mvc.1.0.view", @"/Views/Chat/IndexPdf.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI

#nullable disable
    ;
#nullable restore
#line 2 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e", @"/Views/Chat/IndexPdf.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"a5bcd83cf27c8ffb8baf597d24314352bf7b52b5a9bb5ee83e83e9d0089a628e", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_Chat_IndexPdf : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/chatGTPIndex.css?v=v21"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/lib/codemirror.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/theme/pastel-on-dark.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "ChatGLM3-6B-32k", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "Llama3", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "Qwen1.5-7B-Chat", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("layui-form flex_row"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("lay-filter", new global::Microsoft.AspNetCore.Html.HtmlString("formModel"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("formModel"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("action", new global::Microsoft.AspNetCore.Html.HtmlString(""), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_13 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/lib/codemirror.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_14 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/mode/python/python.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_15 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/mode/sql/sql.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_16 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_17 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_18 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/images/chat_icon_Q.jpg"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_19 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("alt", new global::Microsoft.AspNetCore.Html.HtmlString(""), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_20 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/images/chat_icon_A1.png"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_21 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("layui-form"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_22 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("lay-filter", new global::Microsoft.AspNetCore.Html.HtmlString("fm"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_23 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("fm"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_24 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("lay-filter", new global::Microsoft.AspNetCore.Html.HtmlString("fmsql"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_25 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("fmsql"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Chat\IndexPdf.cshtml"
  
    ViewBag.Title = "ChatGPTStream";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"zh\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e13488", async() => {
                WriteLiteral("\r\n    <meta charset=\"UTF-8\">\r\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>chatGTP</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e13984", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e15187", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n    <!-- 代码高亮 -->\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e16415", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e17619", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e18822", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        body {
            background-color: #fafafa;
            overflow: hidden;
        }


        .code_wrap {
            background-color: #3f3f3f;
            border-radius: 6px;
            overflow: hidden;
        }

        .code_top {
            padding: 4px 6px 0;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            background-color: #000;
            color: #c2be9e;
        }

        .code_content {
            padding: 10px;
            border: 10px solid #000;
            color: #c2be9e;
            text-align: left;
            resize: none;
        }

        .layui-btn-xs {
            padding: 0 6px;
        }

        .layui-btn-primary {
            border-color: transparent;
            background-color: transparent;
            color: #fff;
        }

            .layui-btn-primary:hover {
                color: #eee;
                border-color: #eee;
          ");
                WriteLiteral(@"  }

        .message_content {
            flex: 1;
        }

            .message_content p,
            .result_text {
                white-space: pre-line;
            }

        .result_text {
            padding: 10px;
            height: 80px;
            background-color: #3f3f3f;
            font-size: 14px;
            word-wrap: break-word;
            overflow-x: hidden;
            overflow-y: auto;
            border: 10px solid #000;
            border-top: 2px solid #000;
            border-radius: 0;
        }

            .result_text:hover {
                border: 10px solid #000 !important;
                border-top: 2px solid #000 !important;
            }

            .result_text:focus {
                padding: 10px;
                border: 10px solid #000 !important;
                border-top: 2px solid #000 !important;
                color: #c2be9e;
                border-radius: 0;
            }

        .layui-table-view {
            mar");
                WriteLiteral(@"gin: 0 10px;
        }

        .result_bj {
            display: flex;
            flex-direction: row;
            align-items: center;
            background-color: #009688;
            color: #fff;
        }

        .result_icon {
            width: 34px;
            height: 34px;
            border-radius: 10px;
            border: 2px solid #0bbe56;
            overflow: hidden;
            margin-right: 10px;
        }

            .result_icon img {
                vertical-align: top;
                width: 100%;
            }
            .flex_row{
                display:flex;
                flex-direction:row;
                justify-content:space-between;
            }

        .right_title{
            display:block;
        }

        .layui-btn{
            padding:0 20px;

        }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e23632", async() => {
                WriteLiteral("\r\n    <div");
                BeginWriteAttribute("class", " class=\"", 3602, "\"", 3610, 0);
                EndWriteAttribute();
                WriteLiteral(">\r\n\r\n\r\n\r\n\r\n\r\n        <div class=\"chat layui-row\">\r\n\r\n            <div class=\"layui-col-xs12 layui-col-md12\">\r\n                \r\n                    <div class=\"right_title\" style=\"margin-top:10px;\">\r\n");
                WriteLiteral("\r\n                        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e24370", async() => {
                    WriteLiteral(@"
                            <div class=""layui-form right_title_select"">
                                <div class=""layui-inline"">
                                   
                                    <div class=""layui-input-inline"">
                                        <select id=""modelType"">
                                            ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e25017", async() => {
                        WriteLiteral("ChatGLM3-6B-32k");
                    }
                    );
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                    __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_6.Value;
                    __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_6);
                    BeginWriteTagHelperAttribute();
                    __tagHelperStringValueBuffer = EndWriteTagHelperAttribute();
                    __tagHelperExecutionContext.AddHtmlAttribute("selected", Html.Raw(__tagHelperStringValueBuffer), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.Minimized);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                                            ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e26717", async() => {
                        WriteLiteral("Llama3");
                    }
                    );
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                    __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_7.Value;
                    __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_7);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                                            ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e28073", async() => {
                        WriteLiteral("Qwen1.5-7B-Chat");
                    }
                    );
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                    __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_8.Value;
                    __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_8);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral(@"
                                        </select>
                                    </div>
                                </div>
                                <div class=""layui-inline"">
                                    <div class=""layui-input-inline"">
                                        <button type=""button"" class=""layui-btn layui-btn-normal"" id=""btnPdf"">查看PDF</button>
                                        <input type=""hidden"" id=""pdf""");
                    BeginWriteAttribute("value", " value=\"", 5043, "\"", 5063, 1);
                    WriteAttributeValue("", 5051, 
#nullable restore
#line 170 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Chat\IndexPdf.cshtml"
                                                                              ViewBag.url

#line default
#line hidden
#nullable disable
                    , 5051, 12, false);
                    EndWriteAttribute();
                    WriteLiteral(@" />
                                    </div>
                                    <div class=""layui-input-inline"">
                                        <button type=""button"" class=""layui-btn layui-btn-normal"" id=""btnGetPDFValue"">提取特征变量</button>
                                    </div>
                                </div>
                          
                                
                            </div>
                            <div>
                                <button id=""popbtn"" type=""button"" class=""layui-btn layui-bg-blue"">
                                    <i class=""layui-icon layui-icon-survey""></i>
                                </button>
                            </div>
                        ");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n");
                WriteLiteral(@"                </div>
              


                <div class=""layui-row left_content"">
                    <div class=""content_inner layui-col-md12 layui-col-lg12"">
                        <div class=""chat_main middle_main"" style=""width:98%;"">
                            <div class=""chat-history"" id=""chat-history"">
                                <ul id=""messgeList"">
                                </ul>
                            </div>

                            <div class=""chat-message"">
                                <textarea name=""message-to-send"" id=""message-to-send""");
                BeginWriteAttribute("placeholder", " placeholder=\"", 6465, "\"", 6479, 0);
                EndWriteAttribute();
                WriteLiteral(@"
                                          rows=""2""></textarea>
                                <button class=""layui-btn sub_btn"" id=""submitBtn"">
                                    <i class=""layui-icon layui-icon-release""
                                       style=""font-size: 30px;""></i>
                                </button>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>



    <div id=""popwrap"" style=""display:none;height:100%;overflow:hidden;"">
        <div class=""layui-row layui-col-space30"" style=""height:100%"">
            <div class=""layui-col-md12"" style=""height:100%"">
                <div style=""background-color: #fff;height: 100%; padding:15px 15px;"">
                    <textarea id=""model_wrapR"" class=""layui-textarea"" style="" height:100%;width:100%;line-height:40px;font-size:20px;resize:none"">
使用 <data></data> 标记中的内容作为你的知识:   <data>{text}</data> 回答要求：
- 如果");
                WriteLiteral(@"你不清楚答案，你需要澄清。
- 避免提及你是从 <data></data> 获取的知识。
- 保持答案 与 <data></data>中描述的一致。
- 使用与问题相同的语言回答。
- {
  ""说明"": ""从报告所见中提取以下特征变量，并记录对应的原文依据。"",
  ""特征提取"": [
    {
      ""变量名"": ""纤维腺体组织含量(FGT)"",
      ""指令"": ""分类提取：脂肪型，散在纤维腺体型，不均质纤维腺体型，致密型"",
      ""原文依据"": ""记录FGT描述文本""
    },
    {
      ""变量名"": ""背景强化程度"",
      ""指令"": ""提取：轻微强化至明显强化，包括无法归类"",
      ""原文依据"": ""记录背景强化描述文本""
    },
    {
      ""变量名"": ""腺体分布基本对称"",
      ""指令"": ""提取：对称，欠对称，不对称"",
      ""原文依据"": ""记录腺体分布描述文本""
    },
    {
      ""变量名"": ""边界"",
      ""指令"": ""提取：清楚，不清，待评定"",
      ""原文依据"": ""记录边界描述文本""
    },
    {
      ""变量名"": ""TIC曲线"",
      ""指令"": ""提取：流入型，平台型，流出型，环形强化"",
      ""原文依据"": ""记录TIC曲线描述文本""
    },
    {
      ""变量名"": ""第一个病变位置"",
      ""指令"": ""记录具体位置信息"",
      ""原文依据"": ""记录病变位置描述文本""
    },
    {
      ""变量名"": ""第一个病变距离乳头"",
      ""指令"": ""提取具体数值"",
      ""原文依据"": ""记录病变距离描述文本""
    },
    {
      ""变量名"": ""第一个病变大小或范围"",
      ""指令"": ""注意单位和尺寸格式"",
      ""原文依据"": ""记录病变大小描述文本""
    }
  ]
}

- 按照如下格式输出：
{
  ""variables"": [
    {
      ""variable_name"": """"");
                WriteLiteral(",\r\n      \"value\": \"\",\r\n      \"source\": \"\"\r\n    }\r\n  ]\r\n}\r\n- 问题:{prompt}\r\n</textarea>\r\n                </div>\r\n            </div>\r\n");
                WriteLiteral("        </div>\r\n\r\n\r\n    </div>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e35855", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e36980", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_14);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e38105", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_15);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e39230", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_16);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e40355", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_17);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
        layui.use(['jquery', 'layer', 'form', 'element', 'code', 'laytpl', 'table'], function () {
            var layer = layui.layer,
                $ = layui.$,
                form = layui.form,
                laytpl = layui.laytpl,
                element = layui.element,
                table = layui.table;

            //查询结果表格用的高度  height:""full-"" + table_top_H,
            var table_top_H;

            layui.code();

            $(window).resize(function () {

                arrangement();
            })
            arrangement();
            function arrangement() {
                var winH = $(window).height(),
                    right_title_H = $("".right_title"").outerHeight(true),
                    messageH = $("".chat-message"").outerHeight(true);

                var _middle_main = winH - right_title_H - messageH - 100 + ""px"";

                $(""#chat-history"").css(""height"", _middle_main);
            }

            $(""#addBtn"").on(""click"", functio");
                WriteLiteral(@"n () {
                $(""#messgeList"").children().remove();
                $(""#message-to-send"").focus();


                hidden();

              

                $(""#message-to-send"").val("""");
            })

            $('#message-to-send').focus(function () {
                $(this).css(""border"", ""2px solid #009688"");
            });

            $('#message-to-send').blur(function () {
                $(this).css(""border"", ""2px solid #f0f0f0"");
            })

            $(document).ready(function () {

                $(document).on('click', '#submitBtn', function () {
                    submitBtn();
                });

                $(document).on('click', '#btnPdf', function () {
                  
                    layer.open({
                        type: 2,
                        title: 'PDF预览',
                        shadeClose: true, // 点击遮罩关闭层
                        area: ['80%', '80%'], // 设置弹窗大小
                        content:  $(""#pdf"").val()");
                WriteLiteral(@"
                    });
                });

                $(document).on('click', ""#btnGetPDFValue"", function () {
                    $(""#message-to-send"").val(""提取特征变量"");
                    $('#submitBtn').trigger('click');
                });

                $(""#popbtn"").on(""click"", function () {
                    layer.open({
                        type: 1,
                        area: ['60%', '80%'],
                        resize: false,
                        shadeClose: true,
                        title: '帮助',
                        content: $(""#popwrap""),
                        success: function () {
                           // $(""#model_wrapL"").val(modelText);
                        }
                    })
                })
                // 监听文本框中的按键事件
                $(""#message-to-send"").keypress(function (event) {
                    if (event.which == 13) {
                        submitBtn();
                    }
                });
            ");
                WriteLiteral(@"   // getWiki();
            });


            function submitBtn() {
                var prompt = $.trim($(""#message-to-send"").val());
                if (prompt == """") {
                    layer.msg(""请输入问题"");
                    $(""#message-to-send"").focus();
                    return;
                }

                var getTplreq = RequestModel.innerHTML;
                laytpl(getTplreq).render(prompt, function (html) {
                    $('#messgeList').append(html);
                });

                resetHistoryscrollTop();

                var i = 0;
                var url = $(""#pdf"").val();
                var source = new EventSource('/Chat/GetChatStreamAnswerPDF?prompt=' + encodeURIComponent(prompt) + ""&modelType="" + $(""#modelType"").val() + ""&url="" + url + ""&tips="" + encodeURIComponent($(""#model_wrapR"").val()));
                source.onmessage = function (event) {

                    var result = JSON.parse(event.data);
                    console.log(result);
");
                WriteLiteral(@"                    if (result.okMsg) {
                        if (i == 0) {
                            var getTplreq = ResponseModelStream.innerHTML;
                            laytpl(getTplreq).render(prompt, function (html) {
                                $('#messgeList').append(html);
                            });
                        }

                        $("".clearfix[responseCurrent='1'] p"").append(result.data);
                        i = i + 1;
                    }
                    else {
                        layer.msg(result.errorMsg);
                        $("".clearfix[responseCurrent='1']"").removeAttr(""responseCurrent"");
                        source.close();
                    }
                    resetHistoryscrollTop();
                };

                source.addEventListener('end', function (event) {
                    debugger
                    var result = JSON.parse(event.data);
                    if (result.okMsg) {


              ");
                WriteLiteral(@"          var getTplreq = ResponseModel.innerHTML;
                        laytpl(getTplreq).render(result.newContent, function (html) {
                            $("".clearfix[responseCurrent='1']"").html(html);
                        });
                        layui.each(result.newContent, function (idx, item) {
                            if (item.SectionType == ""code"" && (item.LanguageType == ""sql"" || item.LanguageType == ""code"")) {
                                var anserMirror = codeEditorSql(""answer-input"" + item.guid);  // 生成CodeMirror编辑器
                                anserMirror.setOption(""readOnly"", true);

                            }
                            else if (item.SectionType == ""code"") {

                                var anserMirror = codeEditor(""answer-input"" + item.guid);  // 生成CodeMirror编辑器
                                anserMirror.setOption(""readOnly"", true);
                            }



                        });
                        $("".clear");
                WriteLiteral(@"fix[responseCurrent='1']"").removeAttr(""responseCurrent"");
                        $(""#message-to-send"").val('');

                    }
                    else {
                        layer.msg(result.errorMsg);
                    }

                    // 结束事件源连接
                    source.close();
                }, false);

                source.onerror = function (event) {
                    $("".clearfix[responseCurrent='1']"").removeAttr(""responseCurrent"");
                    source.close();
                };
            }


            function codeEditor(Id) {
                return CodeMirror.fromTextArea(document.getElementById(Id), {
                    mode: {
                        name: ""text/x-cython"",
                        version: 3,
                        singleLineStringErrors: false,
                    },
                    theme: 'pastel-on-dark',
                    lineNumbers: true,
                    indentUnit: 4,
                    matchBra");
                WriteLiteral(@"ckets: true
                })
            }
            function codeEditorSql(Id) {
                return CodeMirror.fromTextArea(document.getElementById(Id), {
                    mode: {
                        name: ""text/x-pgsql"",
                        version: 3,
                        singleLineStringErrors: false,
                    },
                    theme: 'pastel-on-dark',
                    lineNumbers: true,
                    indentUnit: 4,
                    matchBrackets: true
                })
            }

            //设置Y轴位置
            function resetHistoryscrollTop() {
                var ele = document.getElementById(""chat-history"");
                if (ele.scrollHeight > ele.clientHeight) {
                    setTimeout(function () {
                        //设置滚动条到最底部
                        ele.scrollTop = ele.scrollHeight;
                    }, 500);
                }
            }

            function hidden() {
                $("".men");
                WriteLiteral(@"u_wrap"").css(""display"", ""none"")
                $("".menu_list"").removeClass(""show_menu_list"")
            }

            var btn_type = 0;
            var title_text = """";


        });
    </script>

    <!-- 提问模板 -->
    <script id=""RequestModel"" type=""text/html"">
        <li class=""clearfix"" requestDel=""1"">
            <div class=""message other-message"">
                <div class=""message-data"">");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("img", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e50504", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_18);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_19);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"</div>
                <p>{{d}}</p>
            </div>
        </li>
    </script>

    <!-- 回复模板(stream) -->
    <script id=""ResponseModelStream"" type=""text/html"">
        <li class=""clearfix"" responseCurrent=""1"">
            <div class=""message my-message"">
                <div class=""message-data"">");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("img", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e52026", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_20);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_19);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"</div>
                <div class=""message_content"">
                    <p></p>
                </div>
            </div>
        </li>
    </script>

    <!-- 回复模板 -->
    <script id=""ResponseModel"" type=""text/html"">
        <div class=""message my-message"">
            <div class=""message-data a_bg"">");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("img", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e53548", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_20);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_19);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"</div>
            <div class=""message_content"">
                {{# if(d.length > 0) {}}
                {{#  layui.each(d, function(index, item){ }}
                {{# if(item.SectionType==""string"") {}}
                <p>{{item.SectionContent}}</p>
                {{#  } else { }}
                <div class=""code_wrap"">
                    <div class=""code_top"">
                        <p class=""code_name"">{{item.LanguageType}}</p>
                        <div class=""code_btn_group"">
                            <button type=""button"" onclick=""javascript:var copyTarget = $(this).parent('div').parent('div').next('textarea'); var tempTextArea = $('<textarea>');tempTextArea.val(copyTarget.html());layer.msg(copyTarget.html());$('body').append(tempTextArea); tempTextArea.select();document.execCommand('copy'); tempTextArea.remove();layer.msg('复制成功');""
                                    class=""layui-btn layui-btn-xs layui-btn-primary"">
                                <i class=""layui-icon layui-icon-s");
                WriteLiteral(@"enior""></i> 复制
                            </button>
                        </div>
                    </div>
                    <textarea id=""answer-input{{item.guid}}"" class=""layui-textarea code_content result_text"" readonly>{{item.SectionContent}}</textarea>
                </div>
                {{#  } }}
                {{#  }); }}
                {{#  } }}
            </div>
        </div>
    </script>

    <!-- //主题栏模板 -->
    <script id=""ChatThemeListModel"" type=""text/html"">
        {{#  if(d.data.length > 0){ }}
        {{#  layui.each(d.data, function(index, item){ }}
        <li class=""topic_item"" themeid=""{{item.id}}"" model=""{{item.modelName}}"">
            <div class=""topic_item_text"">
                <i class=""layui-icon layui-icon-heart""></i>
                <div class=""title"">
                    <div class=""layui-input-inline"">
                        <input type=""text"" name=""title"" autocomplete=""off"" value=""{{ item.theme }}""
                               class=""lay");
                WriteLiteral(@"ui-input topic_theme"" readonly>
                    </div>
                </div>
            </div>
            <!-- 编辑和删除 -->
            <div class=""operating_btn"">
                <button type=""button"" class=""layui-btn layui-btn-primary layui-btn-sm"">
                    <i class=""layui-icon layui-icon-edit""></i>
                </button>
                <button type=""button"" class=""layui-btn layui-btn-primary layui-btn-sm"" style=""margin-left:0"">
                    <i class=""layui-icon layui-icon-delete""></i>
                </button>
            </div>
        </li>
        {{#  }); }}
        {{#  } }}
    </script>

    <!-- //消息栏模板 -->
    <script id=""ChatMsgListModel"" type=""text/html"">
        {{#  if(d.data.length > 0){ }}
        {{#  layui.each(d.data, function(index, item){ }}
        <li class=""clearfix"">
            {{# if(item.chatType==""request""){  }}
            <div class=""message other-message"">
                <div class=""message-data"">
                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("img", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e57965", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_18);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_19);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n                </div>\r\n                <p>{{item.content}}</p>\r\n            </div>\r\n            {{#  } else { }}\r\n            <div class=\"message my-message\">\r\n                <div class=\"message-data a_bg\">");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("img", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e59388", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_20);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_19);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"</div>
                <div class=""message_content"">
                    {{# if(item.newContent.length > 0) {}}
                    {{# layui.each(item.newContent, function(index, item2){ }}
                    {{# if(item2.sectionType==""string"") {}}
                    <p>{{item2.sectionContent}}</p>
                    {{#  } else { }}
                    <div class=""code_wrap"">
                        <div class=""code_top"">
                            <p class=""code_name"">{{item2.languageType}}</p>
                            <div class=""code_btn_group"">
                                <button type=""button"" onclick=""javascript:var copyTarget = $(this).parent('div').parent('div').next('textarea'); var tempTextArea = $('<textarea>');tempTextArea.val(copyTarget.html());$('body').append(tempTextArea); tempTextArea.select();document.execCommand('copy'); tempTextArea.remove();layer.msg('复制成功');""
                                        class=""layui-btn layui-btn-xs layui-btn-primary"">
               ");
                WriteLiteral(@"                     <i class=""layui-icon layui-icon-senior""></i> 复制
                                </button>
                            </div>
                        </div>
                        <textarea id=""answer-input{{item2.guid}}"" class=""layui-textarea code_content result_text"" readonly>{{item2.sectionContent}}</textarea>
                    </div>
                    {{#  } }}
                    {{#  }); }}
                    {{#  } }}
                </div>
            </div>
            {{#  } }}
        </li>
        {{#  }); }}
        {{#  } }}
    </script>

    <!-- 展示SQL执行结果模版 -->
    <script id=""ShowResultModel"" type=""text/html"">
        <div class=""layui-tab layui-tab-card"" lay-filter=""demo-filter-tab-sql"">
            <ul class=""layui-tab-title"">
                {{#  if(d.tableCount > 0){ }}
                {{#  for (var i=0;i<d.tableCount;i++) {}}
                <li");
                BeginWriteAttribute("{{i", " {{i=", 24048, "", 24053, 0);
                EndWriteAttribute();
                WriteLiteral("=0?\"class=\'layui-this\'\":\"\"}}>执行结果{{i+1}}</li>\r\n                {{# } }}\r\n                {{# } }}\r\n                {{# if(d.errors.length>0){ }}\r\n                {{#  for (var i=0;i<d.errors.length;i++) {}}\r\n                <li");
                BeginWriteAttribute("{{d.tableCount", " {{d.tableCount=", 24280, "", 24296, 0);
                EndWriteAttribute();
                WriteLiteral(@"=0&&i==0?""class='layui-this'"":""""}}>执行异常{{i+1}}</li>
                {{# } }}
                {{# } }}
            </ul>
            <div class=""layui-tab-content"">
                {{#  if(d.datas.length > 0){ }}
                {{# layui.each(d.datas, function(index, item){ }}
                <div class=""layui-tab-item {{index==0?""layui-show"":""""}}"">
                    <div class=""return_code_wrap"">
                        <div class=""code_top"">
                            <p class=""code_name"">sql</p>
                            <div class=""code_btn_group"">
                                <button type=""button"" indexId=""{{index}}"" class=""layui-btn layui-btn-sm layui-btn-primary editsql-button"">
                                    <i class=""layui-icon layui-icon-edit""></i> 修改
                                </button>
                                <button type=""button"" indexId=""{{index}}"" class=""layui-btn layui-btn-sm layui-btn-primary executesql-button"">
                                    <i ");
                WriteLiteral(@"class=""layui-icon layui-icon-triangle-r""></i> 执行
                                </button>
                            </div>
                        </div>
                        <textarea id=""sql-input{{index}}"" class=""layui-textarea code_content result_text"">{{item.sqlStr}}</textarea>



                    </div>
                    <table id=""tableresult_{{index+1}}"" lay-filter=""tableresult_{{index+1}}""></table>
                </div>
                {{# }); }}
                {{# } }}
                {{# if(d.errors.length>0){ }}
                {{# layui.each(d.errors, function(index, item){ }}
                <div class=""layui-tab-item {{d.tableCount==0&&index==0?""layui-show"":""""}}"">
                    <div class=""return_code_wrap"">
                        <div class=""code_top"">
                            <p class=""code_name"">sql</p>
                            <div class=""code_btn_group"">
                                <button type=""button"" indexId=""{{index}}"" class=""layui-btn ");
                WriteLiteral(@"layui-btn-sm layui-btn-primary editsql-button"">
                                    <i class=""layui-icon layui-icon-edit""></i> 修改
                                </button>
                                <button type=""button"" indexId=""{{index}}"" class=""layui-btn layui-btn-sm layui-btn-primary executesql-button"">
                                    <i class=""layui-icon layui-icon-triangle-r""></i> 执行
                                </button>
                            </div>
                        </div>
                        <textarea id=""sql-input{{index}}"" class=""layui-textarea code_content result_text"">{{item.sqlStr}}</textarea>



                    </div>
                    <table class=""layui-table"">
                        <colgroup><col width=""60""><col><col></colgroup>
                        <thead> <tr><th>序号</th><th>错误提示</th></tr></thead>
                        <tbody>
                            <tr><td>{{index+1}}</td><td>{{item.ErrorMsg}}</td></tr>
                       ");
                WriteLiteral(@" </tbody>
                    </table>
                </div>
                {{# }); }}
                {{# } }}
            </div>
        </div>
    </script>


    <!-- 展示python执行结果模版 -->
    <script id=""ShowPythonResultModel"" type=""text/html"">
        <div class=""layui-tab layui-tab-card"" lay-filter=""demo-filter-tab"">
            <ul class=""layui-tab-title"">
                {{#  if(d.dataCount > 0){ }}
                {{#  for (var i=0;i<d.dataCount;i++) {}}
                <li");
                BeginWriteAttribute("{{i", " {{i=", 27871, "", 27876, 0);
                EndWriteAttribute();
                WriteLiteral(@"=0?""class='layui-this'"":""""}}>执行结果{{i+1}}</li>
                {{# } }}
                {{# } }}
            </ul>
            <div class=""layui-tab-content"">
                {{#  if(d.datas.length > 0){ }}
                {{# layui.each(d.datas, function(index, item){ }}
                <div class=""layui-tab-item {{index==0?""layui-show"":""""}}"" indexId=""{{index}}"">
                    <div class=""return_code_wrap"">
                        <div class=""code_top"">
                            <p class=""code_name"">python</p>
                            <div class=""code_btn_group"">
                                <button type=""button"" indexId=""{{index}}"" class=""layui-btn layui-btn-sm layui-btn-primary edit-button"">
                                    <i class=""layui-icon layui-icon-edit""></i> 修改
                                </button>
                                <button type=""button"" indexId=""{{index}}"" class=""layui-btn layui-btn-sm layui-btn-primary execute-button"">
                            ");
                WriteLiteral(@"        <i class=""layui-icon layui-icon-triangle-r""></i> 执行
                                </button>
                            </div>
                        </div>
                        <textarea id=""code-input{{index}}"" class=""layui-textarea code_content result_text"">{{item.codeStr}}</textarea>



                    </div>
                    <div class=""exhibition"">
                        <pre id=""output{{index}}"">
                        {{# if(item.code==0){ }}
                        {{# if(item.image.length>0){ }}
                        {{# layui.each(item.image, function(index2, item2){ }}
                        <img src=""data:image/png;base64,{{ item2 }}"" />
                        <br />
                        {{# }); }}
                        {{# } }}
                        {{item.text}}
                        {{# } else{ }}
                        {{item.errorMsg}}
                        {{# } }}
                        </pre>
                    </div>
      ");
                WriteLiteral(@"          </div>
                {{# }); }}
                {{# } }}
            </div>
        </div>
    </script>

    <!-- 选择确定或取消 -->
    <script id=""okClose"" type=""text/x-handlebars-template"">
        <div class=""ok_close"">
            <!-- 确认和取消 -->
            <button type=""button"" class=""layui-btn layui-btn-primary layui-btn-sm"" id=""ok"">
                <i class=""layui-icon layui-icon-ok""></i>
            </button>
            <button type=""button"" class=""layui-btn layui-btn-primary layui-btn-sm"" id=""close"" style=""margin-left:0"">
                <i class=""layui-icon layui-icon-close""></i>
            </button>
        </div>
    </script>

");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n<div class=\"window_wrap\" id=\"form_window\" style=\"display: none\">\r\n    ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e70754", async() => {
                WriteLiteral("\r\n        <div class=\"form-control cm-s-pastel-on-dark changecode\">\r\n            <textarea class=\"layui-textarea code_content result_text \" id=\"code-python\"\r\n                      name=\"code-python\"></textarea>\r\n        </div>\r\n\r\n    ");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_21);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_22);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_23);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
    <script>
        var codeEidterPython = CodeMirror.fromTextArea(document.getElementById(""code-python""), {
            mode: {
                name: ""text/x-cython"",
                version: 3,
                singleLineStringErrors: false,
            },
            theme: 'pastel-on-dark',
            lineNumbers: true,
            indentUnit: 4,
            matchBrackets: true
        });
    </script>
</div>

<div class=""window_wrap"" id=""form_window_sql"" style=""display: none"">
    ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7071d5b5bc9326bd19d807b45346f4221af0ff75e1b0a69a6a3f2fb79445422e73114", async() => {
                WriteLiteral("\r\n        <div class=\"form-control cm-s-pastel-on-dark changecode\">\r\n            <textarea class=\"layui-textarea code_content result_text \" id=\"code-sql\"\r\n                      name=\"code-python\"></textarea>\r\n        </div>\r\n\r\n    ");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_21);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_24);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_25);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
    <script>
        var codeEidterSql = CodeMirror.fromTextArea(document.getElementById(""code-sql""), {
            mode: {
                name: ""text/x-pgsql"",
                version: 3,
                singleLineStringErrors: false,
            },
            theme: 'pastel-on-dark',
            lineNumbers: true,
            indentUnit: 4,
            matchBrackets: true
        });
    </script>
</div>
</html>

");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
