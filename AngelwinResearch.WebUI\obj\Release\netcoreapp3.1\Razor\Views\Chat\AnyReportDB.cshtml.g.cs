#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Chat\AnyReportDB.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "be7cb24e9ccc10a96624cc239ceba5b18dd82801ffc1f701d0487d7c31ed0b9e"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_Chat_AnyReportDB), @"mvc.1.0.view", @"/Views/Chat/AnyReportDB.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI

#nullable disable
    ;
#nullable restore
#line 2 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"be7cb24e9ccc10a96624cc239ceba5b18dd82801ffc1f701d0487d7c31ed0b9e", @"/Views/Chat/AnyReportDB.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"a5bcd83cf27c8ffb8baf597d24314352bf7b52b5a9bb5ee83e83e9d0089a628e", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_Chat_AnyReportDB : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/chatGTPIndex.css?v=v21"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/lib/codemirror.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/theme/pastel-on-dark.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("layui-form flex_row"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("lay-filter", new global::Microsoft.AspNetCore.Html.HtmlString("formModel"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("formModel"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("action", new global::Microsoft.AspNetCore.Html.HtmlString(""), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/lib/codemirror.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/mode/python/python.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/mode/sql/sql.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_13 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_14 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_15 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("layui-form"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_16 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("lay-filter", new global::Microsoft.AspNetCore.Html.HtmlString("fm"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_17 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("fm"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_18 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("lay-filter", new global::Microsoft.AspNetCore.Html.HtmlString("fmsql"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_19 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("fmsql"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Chat\AnyReportDB.cshtml"
  
    ViewBag.Title = "ChatGPTStream";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"zh\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "be7cb24e9ccc10a96624cc239ceba5b18dd82801ffc1f701d0487d7c31ed0b9e11399", async() => {
                WriteLiteral("\r\n    <meta charset=\"UTF-8\">\r\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>chatGTP</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "be7cb24e9ccc10a96624cc239ceba5b18dd82801ffc1f701d0487d7c31ed0b9e11895", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "be7cb24e9ccc10a96624cc239ceba5b18dd82801ffc1f701d0487d7c31ed0b9e13098", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n    <!-- 代码高亮 -->\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "be7cb24e9ccc10a96624cc239ceba5b18dd82801ffc1f701d0487d7c31ed0b9e14326", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "be7cb24e9ccc10a96624cc239ceba5b18dd82801ffc1f701d0487d7c31ed0b9e15530", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "be7cb24e9ccc10a96624cc239ceba5b18dd82801ffc1f701d0487d7c31ed0b9e16733", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        body {
            background-color: #fafafa;
            overflow: hidden;
        }


        .code_wrap {
            background-color: #3f3f3f;
            border-radius: 6px;
            overflow: hidden;
        }

        .code_top {
            padding: 4px 6px 0;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            background-color: #000;
            color: #c2be9e;
        }

        .code_content {
            padding: 10px;
            border: 10px solid #000;
            color: #c2be9e;
            text-align: left;
            resize: none;
        }

        .layui-btn-xs {
            padding: 0 6px;
        }

        .layui-btn-primary {
            border-color: transparent;
            background-color: transparent;
            color: #fff;
        }

            .layui-btn-primary:hover {
                color: #eee;
                border-color: #eee;
          ");
                WriteLiteral(@"  }

        .message_content {
            flex: 1;
        }

            .message_content p,
            .result_text {
                white-space: pre-line;
            }

        .result_text {
            padding: 10px;
            height: 80px;
            background-color: #3f3f3f;
            font-size: 14px;
            word-wrap: break-word;
            overflow-x: hidden;
            overflow-y: auto;
            border: 10px solid #000;
            border-top: 2px solid #000;
            border-radius: 0;
        }

            .result_text:hover {
                border: 10px solid #000 !important;
                border-top: 2px solid #000 !important;
            }

            .result_text:focus {
                padding: 10px;
                border: 10px solid #000 !important;
                border-top: 2px solid #000 !important;
                color: #c2be9e;
                border-radius: 0;
            }

        .layui-table-view {
            mar");
                WriteLiteral(@"gin: 0 10px;
        }

        .result_bj {
            display: flex;
            flex-direction: row;
            align-items: center;
            background-color: #009688;
            color: #fff;
        }

        .result_icon {
            width: 34px;
            height: 34px;
            border-radius: 10px;
            border: 2px solid #0bbe56;
            overflow: hidden;
            margin-right: 10px;
        }

            .result_icon img {
                vertical-align: top;
                width: 100%;
            }

        .flex_row {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .right_title {
            display: block;
        }

        .layui-btn {
            padding: 0 20px;
        }

        .right_content {
            margin: 10px;
            margin-left: 0;
            background-color: #fff;
            border: 1px solid #eee;
            border-radius: ");
                WriteLiteral("10px;\r\n            overflow: hidden;\r\n        }\r\n    </style>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "be7cb24e9ccc10a96624cc239ceba5b18dd82801ffc1f701d0487d7c31ed0b9e21808", async() => {
                WriteLiteral("\r\n    <div");
                BeginWriteAttribute("class", " class=\"", 3824, "\"", 3832, 0);
                EndWriteAttribute();
                WriteLiteral(">\r\n\r\n\r\n\r\n\r\n\r\n        <div class=\"chat layui-row\">\r\n\r\n            <div class=\"layui-col-xs12 layui-col-md12\">\r\n\r\n                <div class=\"right_title\" style=\"margin-top:10px; \">\r\n");
                WriteLiteral("\r\n                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "be7cb24e9ccc10a96624cc239ceba5b18dd82801ffc1f701d0487d7c31ed0b9e22523", async() => {
                    WriteLiteral(@"
                        <div class=""layui-form right_title_select"">
                           
                            <div class=""layui-inline"">
                                <div class=""layui-input-inline"">
                                  <button type=""button"" class=""layui-btn layui-btn-normal"" id=""btnLoad"">加载数据</button>
                                    <input type=""hidden"" id=""pdf""");
                    BeginWriteAttribute("value", " value=\"", 4581, "\"", 4601, 1);
                    WriteAttributeValue("", 4589, 
#nullable restore
#line 170 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Chat\AnyReportDB.cshtml"
                                                                          ViewBag.url

#line default
#line hidden
#nullable disable
                    , 4589, 12, false);
                    EndWriteAttribute();
                    WriteLiteral(" />\r\n                                    <input type=\"hidden\" id=\"projectName\"");
                    BeginWriteAttribute("value", " value=\"", 4680, "\"", 4708, 1);
                    WriteAttributeValue("", 4688, 
#nullable restore
#line 171 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Chat\AnyReportDB.cshtml"
                                                                                  ViewBag.projectName

#line default
#line hidden
#nullable disable
                    , 4688, 20, false);
                    EndWriteAttribute();
                    WriteLiteral(" />\r\n                                    <input type=\"hidden\" id=\"projectNo\"");
                    BeginWriteAttribute("value", " value=\"", 4785, "\"", 4811, 1);
                    WriteAttributeValue("", 4793, 
#nullable restore
#line 172 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Chat\AnyReportDB.cshtml"
                                                                                ViewBag.projectNo

#line default
#line hidden
#nullable disable
                    , 4793, 18, false);
                    EndWriteAttribute();
                    WriteLiteral(" />\r\n                                    <input type=\"hidden\" id=\"patientId\"");
                    BeginWriteAttribute("value", " value=\"", 4888, "\"", 4914, 1);
                    WriteAttributeValue("", 4896, 
#nullable restore
#line 173 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Chat\AnyReportDB.cshtml"
                                                                                ViewBag.patientId

#line default
#line hidden
#nullable disable
                    , 4896, 18, false);
                    EndWriteAttribute();
                    WriteLiteral(" />\r\n                                    <input type=\"hidden\" id=\"docDetailedNo\"");
                    BeginWriteAttribute("value", " value=\"", 4995, "\"", 5025, 1);
                    WriteAttributeValue("", 5003, 
#nullable restore
#line 174 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Chat\AnyReportDB.cshtml"
                                                                                    ViewBag.docDetailedNo

#line default
#line hidden
#nullable disable
                    , 5003, 22, false);
                    EndWriteAttribute();
                    WriteLiteral(" />\r\n                                </div>\r\n                               \r\n                            </div>\r\n\r\n\r\n                        </div>\r\n                       \r\n                    ");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n");
                WriteLiteral(@"                </div>

              
                <div class=""layui-row layui-col-space10"">
                  
                    <div class=""layui-col-xs12 layui-col-md12"">
                        <div class=""right_content"">
                            <iframe id=""rptframe"" width=""100%"" height=""100%""></iframe>
                        </div>
                    </div>
                </div>
            </div>



        </div>
    </div>
    </div>



    <div id=""popwrap"" style=""display:none;height:100%;overflow:hidden;"">
        <div class=""layui-row layui-col-space30"" style=""height:100%"">
            <div class=""layui-col-md12"" style=""height:100%"">
                <div style=""background-color: #fff;height: 100%; padding:15px 15px;"">
                    <textarea id=""model_wrapR"" class=""layui-textarea"" style="" height:100%;width:100%;line-height:40px;font-size:20px;resize:none"">
使用 <data></data> 标记中的内容作为你的知识:   <data>{text}</data> 回答要求：
- 如果你不清楚答案，你需要澄清。
- 避免提及你是从 <data></da");
                WriteLiteral(@"ta> 获取的知识。
- 保持答案 与 <data></data>中描述的一致。
- 使用与问题相同的语言回答。
- {
  ""说明"": ""从报告所见中提取以下特征变量，并记录对应的原文依据。"",
  ""特征提取"": [
    {
      ""变量名"": ""纤维腺体组织含量(FGT)"",
      ""指令"": ""分类提取：脂肪型，散在纤维腺体型，不均质纤维腺体型，致密型"",
      ""原文依据"": ""记录FGT描述文本""
    },
    {
      ""变量名"": ""背景强化程度"",
      ""指令"": ""提取：轻微强化至明显强化，包括无法归类"",
      ""原文依据"": ""记录背景强化描述文本""
    },
    {
      ""变量名"": ""腺体分布基本对称"",
      ""指令"": ""提取：对称，欠对称，不对称"",
      ""原文依据"": ""记录腺体分布描述文本""
    },
    {
      ""变量名"": ""边界"",
      ""指令"": ""提取：清楚，不清，待评定"",
      ""原文依据"": ""记录边界描述文本""
    },
    {
      ""变量名"": ""TIC曲线"",
      ""指令"": ""提取：流入型，平台型，流出型，环形强化"",
      ""原文依据"": ""记录TIC曲线描述文本""
    },
    {
      ""变量名"": ""第一个病变位置"",
      ""指令"": ""记录具体位置信息"",
      ""原文依据"": ""记录病变位置描述文本""
    },
    {
      ""变量名"": ""第一个病变距离乳头"",
      ""指令"": ""提取具体数值"",
      ""原文依据"": ""记录病变距离描述文本""
    },
    {
      ""变量名"": ""第一个病变大小或范围"",
      ""指令"": ""注意单位和尺寸格式"",
      ""原文依据"": ""记录病变大小描述文本""
    }
  ]
}

- 按照如下格式输出：
{
  ""variables"": [
    {
      ""variable_name"": """",
      ""value"": """",
      ""sourc");
                WriteLiteral("e\": \"\"\r\n    }\r\n  ]\r\n}\r\n- 问题:{prompt}\r\n</textarea>\r\n                </div>\r\n            </div>\r\n");
                WriteLiteral("        </div>\r\n\r\n\r\n    </div>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "be7cb24e9ccc10a96624cc239ceba5b18dd82801ffc1f701d0487d7c31ed0b9e30288", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "be7cb24e9ccc10a96624cc239ceba5b18dd82801ffc1f701d0487d7c31ed0b9e31413", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "be7cb24e9ccc10a96624cc239ceba5b18dd82801ffc1f701d0487d7c31ed0b9e32538", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "be7cb24e9ccc10a96624cc239ceba5b18dd82801ffc1f701d0487d7c31ed0b9e33663", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "be7cb24e9ccc10a96624cc239ceba5b18dd82801ffc1f701d0487d7c31ed0b9e34788", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_14);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
        layui.use(['jquery', 'layer', 'form', 'element', 'code', 'laytpl', 'table'], function () {
            var layer = layui.layer,
                $ = layui.$,
                form = layui.form,
                laytpl = layui.laytpl,
                element = layui.element,
                table = layui.table;

            //查询结果表格用的高度  height:""full-"" + table_top_H,
            var table_top_H;

            layui.code();

            $(window).resize(function () {

                arrangement();
            })
            arrangement();
            function arrangement() {
                var winH = $(window).height();
                //     right_title_H = $("".right_title"").outerHeight(true),
                //     messageH = $("".chat-message"").outerHeight(true);

                // var _middle_main = winH - right_title_H - messageH - 100 + ""px"";

                // $(""#chat-history"").css(""height"", _middle_main);


                // var leftH = $("".left_conte");
                WriteLiteral(@"nt"").height();
                $("".right_content"").css(""height"", (winH-100) + ""px"");

            }



            $(document).ready(function () {
                var projectName = $(""#projectName"").val();
                
                 $(""#btnLoad"").on(""click"", function () {
               loadData();
            })
              

             
                var url = """";
                if (projectName == ""血常规检查"") {
                    url = """);
                Write(
#nullable restore
#line 337 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Chat\AnyReportDB.cshtml"
                             $"{ViewBag.formUrl}c40ca6b274fd4a62bc6ead6149400550.form?token={ViewBag.token}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\";\r\n                }\r\n                else if (projectName.indexOf(\"肝胆MRCP\") != -1) {\r\n                    url = \"");
                Write(
#nullable restore
#line 340 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Chat\AnyReportDB.cshtml"
                             $"{ViewBag.formUrl}a4913929f22147dbada8ac5eb2ef0fa7.form?token={ViewBag.token}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\";\r\n                }\r\n                else if (projectName.indexOf(\"全腹部平扫\") != -1) {\r\n                    url = \"");
                Write(
#nullable restore
#line 343 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Chat\AnyReportDB.cshtml"
                             $"{ViewBag.formUrl}49b55bcc950f4b58895be5a1e852929f.form?token={ViewBag.token}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\";\r\n                }\r\n                else if (projectName == \"彩色多普勒检查\") {\r\n                    url = \"");
                Write(
#nullable restore
#line 346 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Chat\AnyReportDB.cshtml"
                             $"{ViewBag.formUrl}47cde710098c4cfcba15fc300d291252.form?token={ViewBag.token}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@""";
                }
                if (url)
                    $(""#rptframe"").attr(""src"", url);
               // setTimeout(loadData, 1000);
                
                    
            });
          


           function loadData()
           {
                var docDetailedNo = $(""#docDetailedNo"").val();
               $.get('/Chat/getItemList?docDetailedNo=' + docDetailedNo, function (res) { 
               
                   if (res.code == 0) {
                        var iframe = document.getElementById('rptframe');
                        // 发送消息到子页面
                        iframe.contentWindow.postMessage({ data: res.data }, ""*"");
                   }
               })
           }

            //设置Y轴位置
            function resetHistoryscrollTop() {
                var ele = document.getElementById(""chat-history"");
                if (ele.scrollHeight > ele.clientHeight) {
                    setTimeout(function () {
                        //设置滚动条到最底部
       ");
                WriteLiteral(@"                 ele.scrollTop = ele.scrollHeight;
                    }, 500);
                }
            }

            function hidden() {
                $("".menu_wrap"").css(""display"", ""none"")
                $("".menu_list"").removeClass(""show_menu_list"")
            }

            var btn_type = 0;
            var title_text = """";

            // 父窗口
            window.addEventListener('message', function (event) {
                console.log(event.data.data);
                // alert(event.data.data);
                // if (event.origin !== 'https://example.com') { // 检查来源是否可信
                //     return; // 不是预期源，则忽略
                // }

                if (event.data.action === 'save') {

                    console.log(event.data);
                    var projectName = $(""#projectName"").val();
                    var projectNo = $(""#projectNo"").val();
                    var patientId = $(""#patientId"").val();
                    var docDetailedNo = $(""#docDetailedNo"").");
                WriteLiteral(@"val();
                    var formId = event.data.formId;
                    var formData = event.data.data;
                    var obj = { projectName: projectName, projectNo: projectNo, patientId: patientId, docDetailedNo: docDetailedNo, formId: formId, formData: formData };
                    $.post('/Chat/SaveForm', obj, function (res) {
                        if (res.code == 0) {
                            layer.msg(""操作成功"");
                        }
                        else
                            layer.msg(""操作失败"");

                    })

                }

            }, false);
        });
    </script>

  

");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n<div class=\"window_wrap\" id=\"form_window\" style=\"display: none\">\r\n    ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "be7cb24e9ccc10a96624cc239ceba5b18dd82801ffc1f701d0487d7c31ed0b9e42876", async() => {
                WriteLiteral("\r\n        <div class=\"form-control cm-s-pastel-on-dark changecode\">\r\n            <textarea class=\"layui-textarea code_content result_text \" id=\"code-python\"\r\n                      name=\"code-python\"></textarea>\r\n        </div>\r\n\r\n    ");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_15);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_16);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_17);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</div>\r\n\r\n<div class=\"window_wrap\" id=\"form_window_sql\" style=\"display: none\">\r\n    ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "be7cb24e9ccc10a96624cc239ceba5b18dd82801ffc1f701d0487d7c31ed0b9e44813", async() => {
                WriteLiteral("\r\n        <div class=\"form-control cm-s-pastel-on-dark changecode\">\r\n            <textarea class=\"layui-textarea code_content result_text \" id=\"code-sql\"\r\n                      name=\"code-python\"></textarea>\r\n        </div>\r\n\r\n    ");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_15);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_18);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_19);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n  \r\n</div>\r\n</html>\r\n\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
