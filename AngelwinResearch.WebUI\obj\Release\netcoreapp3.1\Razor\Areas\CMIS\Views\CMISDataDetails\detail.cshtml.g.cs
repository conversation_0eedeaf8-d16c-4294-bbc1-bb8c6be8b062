#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\CMISDataDetails\detail.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "c827f0775f6dca5aac31be3131b15e3f4ac4c7f0594f8b5f7db08eabda086830"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_CMIS_Views_CMISDataDetails_detail), @"mvc.1.0.view", @"/Areas/CMIS/Views/CMISDataDetails/detail.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"c827f0775f6dca5aac31be3131b15e3f4ac4c7f0594f8b5f7db08eabda086830", @"/Areas/CMIS/Views/CMISDataDetails/detail.cshtml")]
    #nullable restore
    internal sealed class Areas_CMIS_Views_CMISDataDetails_detail : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/chatGTPIndex.css?v=v21"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/lib/codemirror.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/theme/pastel-on-dark.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/lib/codemirror.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/mode/python/python.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/mode/sql/sql.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\CMISDataDetails\detail.cshtml"
  
    ViewBag.Title = "ChatGPTStream";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"zh\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "c827f0775f6dca5aac31be3131b15e3f4ac4c7f0594f8b5f7db08eabda0868307541", async() => {
                WriteLiteral("\r\n    <meta charset=\"UTF-8\">\r\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>chatGTP</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "c827f0775f6dca5aac31be3131b15e3f4ac4c7f0594f8b5f7db08eabda0868308036", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "c827f0775f6dca5aac31be3131b15e3f4ac4c7f0594f8b5f7db08eabda0868309238", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n    <!-- 代码高亮 -->\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "c827f0775f6dca5aac31be3131b15e3f4ac4c7f0594f8b5f7db08eabda08683010465", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "c827f0775f6dca5aac31be3131b15e3f4ac4c7f0594f8b5f7db08eabda08683011669", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "c827f0775f6dca5aac31be3131b15e3f4ac4c7f0594f8b5f7db08eabda08683012872", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        body {
            background-color: #fafafa;
            overflow: hidden;
        }


        .code_wrap {
            background-color: #3f3f3f;
            border-radius: 6px;
            overflow: hidden;
        }

        .code_top {
            padding: 4px 6px 0;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            background-color: #000;
            color: #c2be9e;
        }

        .code_content {
            padding: 10px;
            border: 10px solid #000;
            color: #c2be9e;
            text-align: left;
            resize: none;
        }

        .layui-btn-xs {
            padding: 0 6px;
        }

        .layui-btn-primary {
            border-color: transparent;
            background-color: transparent;
            color: #fff;
        }

            .layui-btn-primary:hover {
                color: #eee;
                border-color: #eee;
          ");
                WriteLiteral(@"  }

        .message_content {
            flex: 1;
        }

            .message_content p,
            .result_text {
                white-space: pre-line;
            }

        .result_text {
            padding: 10px;
            height: 80px;
            background-color: #3f3f3f;
            font-size: 14px;
            word-wrap: break-word;
            overflow-x: hidden;
            overflow-y: auto;
            border: 10px solid #000;
            border-top: 2px solid #000;
            border-radius: 0;
        }

            .result_text:hover {
                border: 10px solid #000 !important;
                border-top: 2px solid #000 !important;
            }

            .result_text:focus {
                padding: 10px;
                border: 10px solid #000 !important;
                border-top: 2px solid #000 !important;
                color: #c2be9e;
                border-radius: 0;
            }

        .layui-table-view {
            mar");
                WriteLiteral(@"gin: 0 10px;
        }

        .result_bj {
            display: flex;
            flex-direction: row;
            align-items: center;
            background-color: #009688;
            color: #fff;
        }

        .result_icon {
            width: 34px;
            height: 34px;
            border-radius: 10px;
            border: 2px solid #0bbe56;
            overflow: hidden;
            margin-right: 10px;
        }

            .result_icon img {
                vertical-align: top;
                width: 100%;
            }

        .flex_row {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .right_title {
            display: block;
        }

        .layui-btn {
            padding: 0 20px;
        }

        .right_content {
            margin: 10px;
            margin-left: 0;
            background-color: #fff;
            border: 1px solid #eee;
            border-radius: ");
                WriteLiteral("10px;\r\n            overflow: hidden;\r\n        }\r\n    </style>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "c827f0775f6dca5aac31be3131b15e3f4ac4c7f0594f8b5f7db08eabda08683017947", async() => {
                WriteLiteral("\r\n    <div");
                BeginWriteAttribute("class", " class=\"", 3824, "\"", 3832, 0);
                EndWriteAttribute();
                WriteLiteral(">\r\n\r\n\r\n\r\n\r\n\r\n        <div class=\"chat layui-row\">\r\n\r\n            <div class=\"layui-col-xs12 layui-col-md12\">\r\n\r\n                <div class=\"right_title\" style=\"margin-top:10px; display:none \">\r\n");
                WriteLiteral("\r\n                    <form class=\"layui-form flex_row\" lay-filter=\"formModel\" id=\"formModel\"");
                BeginWriteAttribute("action", " action=\"", 4177, "\"", 4186, 0);
                EndWriteAttribute();
                WriteLiteral(@">
                        <div class=""layui-form right_title_select"">

                            <div class=""layui-inline"">
                                <div class=""layui-input-inline"">

                                    <input type=""hidden"" id=""json""");
                BeginWriteAttribute("value", " value=\"", 4451, "\"", 4472, 1);
                WriteAttributeValue("", 4459, 
#nullable restore
#line 170 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\CMISDataDetails\detail.cshtml"
                                                                           ViewBag.json

#line default
#line hidden
#nullable disable
                , 4459, 13, false);
                EndWriteAttribute();
                WriteLiteral(" />\r\n                                    <input type=\"hidden\" id=\"ResearchPatientId\"");
                BeginWriteAttribute("value", " value=\"", 4557, "\"", 4591, 1);
                WriteAttributeValue("", 4565, 
#nullable restore
#line 171 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\CMISDataDetails\detail.cshtml"
                                                                                        ViewBag.ResearchPatientId

#line default
#line hidden
#nullable disable
                , 4565, 26, false);
                EndWriteAttribute();
                WriteLiteral(" />\r\n                                    <input type=\"hidden\" id=\"Fid\"");
                BeginWriteAttribute("value", " value=\"", 4662, "\"", 4682, 1);
                WriteAttributeValue("", 4670, 
#nullable restore
#line 172 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\CMISDataDetails\detail.cshtml"
                                                                          ViewBag.Fid

#line default
#line hidden
#nullable disable
                , 4670, 12, false);
                EndWriteAttribute();
                WriteLiteral(" />\r\n                                    <input type=\"hidden\" id=\"PDFURL\"");
                BeginWriteAttribute("value", " value=\"", 4756, "\"", 4779, 1);
                WriteAttributeValue("", 4764, 
#nullable restore
#line 173 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\CMISDataDetails\detail.cshtml"
                                                                             ViewBag.pdfurl

#line default
#line hidden
#nullable disable
                , 4764, 15, false);
                EndWriteAttribute();
                WriteLiteral(@" />

                                </div>

                            </div>


                        </div>
                        <div>
                            <button id=""popbtn"" type=""button"" class=""layui-btn layui-bg-blue"">
                                <i class=""layui-icon layui-icon-survey""></i>
                            </button>
                        </div>
                    </form>
");
                WriteLiteral(@"                </div>


                <div class=""layui-row layui-col-space10"">

                    <div class=""layui-col-xs6 layui-col-md6"">
                        <div class=""right_content"">
                            <iframe id=""rptframe"" width=""100%"" height=""100%""></iframe>
                        </div>
                    </div>
                    <div class=""layui-col-xs6 layui-col-md6"">
                        <div class=""right_content"">
                            <iframe id=""ftpframe""");
                BeginWriteAttribute("src", " src=\"", 5758, "\"", 5833, 1);
                WriteAttributeValue("", 5764, 
#nullable restore
#line 200 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\CMISDataDetails\detail.cshtml"
                                                        Url.Content("~/CMIS/CMISDataDetails/GetPdf?pdfurl="+@ViewBag.pdfurl)

#line default
#line hidden
#nullable disable
                , 5764, 69, false);
                EndWriteAttribute();
                WriteLiteral(" width=\"100%\" height=\"100%\"></iframe>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n\r\n\r\n        </div>\r\n    </div>\r\n    </div>\r\n\r\n\r\n\r\n  \r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "c827f0775f6dca5aac31be3131b15e3f4ac4c7f0594f8b5f7db08eabda08683023308", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "c827f0775f6dca5aac31be3131b15e3f4ac4c7f0594f8b5f7db08eabda08683024432", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "c827f0775f6dca5aac31be3131b15e3f4ac4c7f0594f8b5f7db08eabda08683025556", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "c827f0775f6dca5aac31be3131b15e3f4ac4c7f0594f8b5f7db08eabda08683026680", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "c827f0775f6dca5aac31be3131b15e3f4ac4c7f0594f8b5f7db08eabda08683027804", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
        layui.use(['jquery', 'layer', 'form', 'element', 'code', 'laytpl', 'table'], function () {
            var layer = layui.layer,
                $ = layui.$,
                form = layui.form,
                laytpl = layui.laytpl,
                element = layui.element,
                table = layui.table;

            //查询结果表格用的高度  height:""full-"" + table_top_H,
            var table_top_H;

            layui.code();

            $(window).resize(function () {

                arrangement();
            })
            arrangement();
            function arrangement() {
                var winH = $(window).height();
                $("".right_content"").css(""height"", (winH - 50) + ""px"");

            }

            $(document).ready(function () {
                var url = """);
                Write(
#nullable restore
#line 246 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\CMISDataDetails\detail.cshtml"
                             $"{ViewBag.formUrl}{ViewBag.formId}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\" + \"");
                Write(
#nullable restore
#line 246 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\CMISDataDetails\detail.cshtml"
                                                                         $".form?token={ViewBag.token}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@""";               
                $(""#rptframe"").attr(""src"", url);
                setTimeout(loadData, 1500);

            });



            function loadData() {
                var json = $(""#json"").val();
                var iframe = document.getElementById('rptframe');
                var ftpframe = document.getElementById(""ftpframe"");

                var pdfUrl = $(""#PDFURL"").val();
                // 动态设置内容
                if (pdfUrl) {
                } else {
                    // 如果地址为空，显示提示语
                    ftpframe.srcdoc = `
      <html>
        <head>
          <style>
            body {
              display: flex;
              justify-content: center;
              align-items: center;
              height: 100vh;
              margin: 0;
              background: #f5f5f5;
              font-family: Arial, sans-serif;
              color: #666;
            }
            .message {
              text-align: center;
              padding: 20px;
        ");
                WriteLiteral(@"      border: 2px dashed #ccc;
              border-radius: 8px;
            }
          </style>
        </head>
        <body>
          <div class=""message"">
            <h2>📄 PDF 文件未找到</h2>
            <p>请联系管理员提供有效文档地址</p>
          </div>
        </body>
      </html>
    `;
                }
                
                // 发送消息到子页面
                iframe.contentWindow.postMessage({ data: json }, ""*"");
            }

            //设置Y轴位置
            function resetHistoryscrollTop() {
                var ele = document.getElementById(""chat-history"");
                if (ele.scrollHeight > ele.clientHeight) {
                    setTimeout(function () {
                        //设置滚动条到最底部
                        ele.scrollTop = ele.scrollHeight;
                    }, 500);
                }
            }

            function hidden() {
                $("".menu_wrap"").css(""display"", ""none"")
                $("".menu_list"").removeClass(""show_menu_list"")
            }
");
                WriteLiteral(@"
            var btn_type = 0;
            var title_text = """";

            // 父窗口
            window.addEventListener('message', function (event) {
                console.log(event.data.data);

                if (event.data.action === 'save') {
                   layer.msg(""此处不能进行提交操作！"");
                }
                else {
                    layer.msg(""此处不能进行该操作！"");
                }

            }, false);
        });
    </script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>\r\n\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
