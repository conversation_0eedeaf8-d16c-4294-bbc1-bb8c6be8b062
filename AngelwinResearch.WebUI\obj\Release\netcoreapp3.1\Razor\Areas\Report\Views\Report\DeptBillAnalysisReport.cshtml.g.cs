#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Report\Views\Report\DeptBillAnalysisReport.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "38c96a76a72fb3eeceb83a0f6f6952ee14a0231c0bbf43c8c93b5555a99f23c5"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_Report_Views_Report_DeptBillAnalysisReport), @"mvc.1.0.view", @"/Areas/Report/Views/Report/DeptBillAnalysisReport.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"38c96a76a72fb3eeceb83a0f6f6952ee14a0231c0bbf43c8c93b5555a99f23c5", @"/Areas/Report/Views/Report/DeptBillAnalysisReport.cshtml")]
    #nullable restore
    internal sealed class Areas_Report_Views_Report_DeptBillAnalysisReport : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
            WriteLiteral("<!DOCTYPE html>\r\n<html lang=\"zh-CN\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "38c96a76a72fb3eeceb83a0f6f6952ee14a0231c0bbf43c8c93b5555a99f23c53117", async() => {
                WriteLiteral(@"
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <title>科室流量分析报表</title>
    <style>
        body {
            background: #f4f6fa;
            font-family: 'Segoe UI', 'PingFang SC', 'Hiragino Sans', Arial, sans-serif;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        .container {
            width: 100%;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        .title {
            text-align: center;
            font-size: 2.2rem;
            font-weight: bold;
            margin: 0 0 8px 0;
            padding: 8px 0;
            color: #2563eb;
            letter-spacing: 2px;
        }
        .main-content {
            display: flex;
            gap: 8px;
            margin: 0 0 8px 0;
            padding: 0;
            width: 100%;
            box-sizing: border-box;
        }
        .panel {
            background: ");
                WriteLiteral(@"#fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            padding: 8px;
            flex: 1 1 0;
            min-width: 0;
            min-height: 600px;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            overflow: hidden;
        }
        .sankey-filter {
            background: #f8fafc;
            padding: 12px 0 12px 30px;
            margin-bottom: 8px;
            display: flex;
            gap: 12px;
            align-items: center;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        }
        .sankey-filter label {
            color: #2563eb;
            font-size: 16px;
            font-weight: 500;
            margin-right: 0.5rem;
        }
        .sankey-filter select, .sankey-filter button {
            min-width: 120px;
            max-width: 220px;
            height: 38px;
            padding: 0 18px;
            bor");
                WriteLiteral(@"der-radius: 8px;
            border: 1px solid #cbd5e1;
            font-size: 15px;
            background: #fff;
            outline: none;
            transition: border 0.2s;
        }
        .sankey-filter button {
            background: #2563eb;
            color: #fff;
            border: none;
            cursor: pointer;
            transition: background 0.2s;
            font-weight: 500;
        }
        .sankey-filter button:hover {
            background: #1d4ed8;
        }
        #sankey {
            width: 100%;
            height: 100%;
            min-height: 500px;
            background: #fff;
            border-radius: 0;
            box-shadow: none;
            flex: 1 1 0;
            padding: 8px 0;
        }
        .table-toolbar {
            display: flex;
            justify-content: flex-end;
            gap: 8px;
            margin-bottom: 8px;
        }
        .icon-btn {
            background: #f1f5f9;
            border: none;
    ");
                WriteLiteral(@"        border-radius: 0;
            width: 38px;
            height: 38px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.2s;
            box-shadow: 0 1px 3px rgba(0,0,0,0.04);
            position: relative;
        }
        .icon-btn:hover {
            background: #dbeafe;
        }
        .icon-btn svg {
            width: 20px;
            height: 20px;
            color: #2563eb;
        }
        .icon-btn .tooltip {
            display: none;
            position: absolute;
            top: -32px;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: #fff;
            padding: 4px 10px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            pointer-events: none;
        }
        .icon-btn:hover .tooltip {
            display: block;
      ");
                WriteLiteral(@"  }
        .table-scroll {
            flex: 1 1 0;
            overflow-y: auto;
            overflow-x: hidden;
            min-height: 500px;
            width: 100%;
            box-sizing: border-box;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 1rem;
            table-layout: fixed;
        }
        th, td {
            padding: 12px 16px;
            text-align: left;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        th {
            background: #f1f5f9;
            font-weight: 600;
            color: #1e293b;
            border-bottom: 2px solid #e5e7eb;
        }
        tr:not(:last-child) td {
            border-bottom: 1px solid #e5e7eb;
        }
        tr:hover td {
            background: #f0f9ff;
        }
        .percent {
            color: #2563eb;
            font-weight: bold;
        }
        .analysis-box {
 ");
                WriteLiteral(@"           background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            padding: 8px;
            margin: 0 0 8px 0;
            width: 100%;
            box-sizing: border-box;
        }
        .analysis-title {
            font-size: 1.1rem;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 8px;
        }
        .analysis-content {
            color: #334155;
            font-size: 1rem;
            line-height: 1.8;
        }
        ");
                WriteLiteral(@"@media (max-width: 900px) {
            .main-content {
                flex-direction: column;
                gap: 8px;
                width: 100%;
            }
            .panel {
                padding: 16px;
                min-height: 480px;
                width: 100%;
            }
            .container {
                padding: 0;
                width: 100%;
            }
            #sankey {
                min-height: 380px;
                width: 100%;
            }
            .table-scroll {
                min-height: 380px;
                width: 100%;
            }
        }
        ");
                WriteLiteral(@"@media (max-width: 480px) {
            .panel {
                padding: 12px;
                min-height: 400px;
                width: 100%;
            }
            #sankey {
                min-height: 320px;
                width: 100%;
            }
            .table-scroll {
                min-height: 320px;
                width: 100%;
            }
            .container {
                padding: 0;
                width: 100%;
            }
            .title {
                font-size: 1.8rem;
                margin-bottom: 16px;
                padding: 16px 0;
            }
        }
    </style>
    <script src=""https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js""></script>
    <script src=""https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js""></script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "38c96a76a72fb3eeceb83a0f6f6952ee14a0231c0bbf43c8c93b5555a99f23c511508", async() => {
                WriteLiteral(@"
    <div class=""container"">
        <div class=""title"">科室流量分析报表</div>
        <div class=""main-content"">
            <div class=""panel"">
                <form class=""sankey-filter"" id=""sankeyFilter"" onsubmit=""return false;"">
                    <label for=""examType"">检查类型</label>
                    <select id=""examType"">
                        <option");
                BeginWriteAttribute("value", " value=\"", 7566, "\"", 7574, 0);
                EndWriteAttribute();
                WriteLiteral(@">全部</option>
                        <option>CT</option>
                        <option>超声</option>
                        <option>普放</option>
                        <option>MR</option>
                    </select>
                    <button type=""button"" id=""sankeySearch"">检索</button>
                </form>
                <div id=""sankey""></div>
            </div>
            <div class=""panel"">
                <div class=""table-toolbar"">
                    <button class=""icon-btn"" id=""excelExport"" title=""导出为Excel"">
                        <span class=""tooltip"">导出为Excel</span>
                        <svg viewBox=""0 0 24 24"" fill=""none"" stroke=""currentColor"" stroke-width=""2"" stroke-linecap=""round"" stroke-linejoin=""round""><path d=""M16 16v2a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v2"" /><polyline points=""15 10 20 10 20 15"" /><line x1=""10"" y1=""14"" x2=""20"" y2=""4"" /></svg>
                    </button>
                    <button class=""icon-btn"" id=""printTable"" title=""打");
                WriteLiteral(@"印"">
                        <span class=""tooltip"">打印</span>
                        <svg viewBox=""0 0 24 24"" fill=""none"" stroke=""currentColor"" stroke-width=""2"" stroke-linecap=""round"" stroke-linejoin=""round""><rect x=""3"" y=""4"" width=""18"" height=""13"" rx=""2"" /><polyline points=""8 17 8 21 16 21 16 17"" /><line x1=""12"" y1=""12"" x2=""12"" y2=""12"" /></svg>
                    </button>
                </div>
                <div class=""table-scroll"">
                    <table id=""dataTable"">
                        <thead>
                            <tr>
                                <th>来源科室</th>
                                <th>目的科室</th>
                                <th>检查类型</th>
                                <th>数量</th>
                                <th>占比</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr><td>呼吸与危重症医学科门诊</td><td>CT</td><td>CT</td><td>2012</td><td class=""percent"">7.19%</td></tr>
       ");
                WriteLiteral(@"                     <tr><td>乳腺疾病门诊</td><td>超声诊断科</td><td>超声</td><td>1900</td><td class=""percent"">6.79%</td></tr>
                            <tr><td>内分泌科门诊</td><td>超声诊断科</td><td>超声</td><td>1871</td><td class=""percent"">6.69%</td></tr>
                            <tr><td>骨科门诊</td><td>普放</td><td>普放</td><td>1770</td><td class=""percent"">6.33%</td></tr>
                            <tr><td>普外科门诊</td><td>超声诊断科</td><td>超声</td><td>1291</td><td class=""percent"">4.61%</td></tr>
                            <tr><td>超声医学科门诊</td><td>超声诊断科</td><td>超声</td><td>1199</td><td class=""percent"">4.29%</td></tr>
                            <tr><td>神经内科门诊</td><td>MR</td><td>MR</td><td>1136</td><td class=""percent"">4.06%</td></tr>
                            <tr><td>胸外科门诊</td><td>CT</td><td>CT</td><td>1081</td><td class=""percent"">3.86%</td></tr>
                            <tr><td>肿瘤科门诊</td><td>CT</td><td>CT</td><td>791</td><td class=""percent"">2.83%</td></tr>
                            <tr><td>骨科门诊</td><td>MR</td><td>MR</td><td>7");
                WriteLiteral(@"65</td><td class=""percent"">2.73%</td></tr>
                            <tr><td>消化内科门诊</td><td>超声诊断科</td><td>超声</td><td>718</td><td class=""percent"">2.57%</td></tr>
                            <tr><td>心血管内科门诊</td><td>CT</td><td>CT</td><td>685</td><td class=""percent"">2.45%</td></tr>
                            <tr><td>乳腺疾病门诊</td><td>普放</td><td>普放</td><td>682</td><td class=""percent"">2.44%</td></tr>
                            <tr><td>呼吸与危重症医学科门诊</td><td>普放</td><td>普放</td><td>674</td><td class=""percent"">2.41%</td></tr>
                            <tr><td>消化内科门诊</td><td>CT</td><td>CT</td><td>556</td><td class=""percent"">1.99%</td></tr>
                            <tr><td>肿瘤科门诊</td><td>CT</td><td>CT</td><td>480</td><td class=""percent"">1.72%</td></tr>
                            <tr><td>肾科门诊</td><td>超声诊断科</td><td>超声</td><td>450</td><td class=""percent"">1.61%</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class=""a");
                WriteLiteral(@"nalysis-box"">
            <div class=""analysis-title"">基于以上数据的分析及提醒建议</div>
            <div class=""analysis-content"">
                1. CT与超声诊断科为主要检查流向，建议关注高流量科室的设备利用率和预约排队情况。<br>
                2. 呼吸与危重症医学科、乳腺疾病门诊、内分泌科门诊等为主要开单来源，建议加强与相关科室的沟通协作，优化检查流程。<br>
                3. 普放、MR等检查类型占比虽低，但也需关注其增长趋势，合理配置资源。<br>
                4. 建议定期复盘流量数据，及时发现异常波动，辅助管理决策。
            </div>
        </div>
    </div>
    <script>
        var sankeyData = {
            nodes: [
                {name: '消化内科门诊'}, {name: '消化内镜门诊'}, {name: '肝病科门诊'}, {name: '泌尿外科门诊'},
                {name: '呼吸与危重症医学科门诊'}, {name: '耳鼻咽喉科门诊'}, {name: '心血管内科门诊'},
                {name: '胸外科门诊'}, {name: '内分泌科门诊'}, {name: '普外科门诊'}, {name: '肿瘤科门诊'},
                {name: '乳腺疾病门诊'}, {name: '超声医学科门诊'}, {name: '神经内科门诊'}, {name: '骨科门诊'},
                {name: '肾科门诊'},
                {name: '内镜中心'}, {name: 'CT'}, {name: '超声诊断科'}, {name: '普放'}, {name: 'MR'}
            ],
            links: [
                {source: '消化内科门诊', targe");
                WriteLiteral(@"t: '内镜中心', value: 800, type: ''},
                {source: '消化内镜门诊', target: '内镜中心', value: 600, type: ''},
                {source: '肝病科门诊', target: '内镜中心', value: 400, type: ''},
                {source: '泌尿外科门诊', target: 'CT', value: 300, type: 'CT'},
                {source: '呼吸与危重症医学科门诊', target: 'CT', value: 2012, type: 'CT'},
                {source: '呼吸与危重症医学科门诊', target: '普放', value: 674, type: '普放'},
                {source: '乳腺疾病门诊', target: '超声诊断科', value: 1900, type: '超声'},
                {source: '乳腺疾病门诊', target: '普放', value: 682, type: '普放'},
                {source: '内分泌科门诊', target: '超声诊断科', value: 1871, type: '超声'},
                {source: '骨科门诊', target: '普放', value: 1770, type: '普放'},
                {source: '骨科门诊', target: 'MR', value: 765, type: 'MR'},
                {source: '普外科门诊', target: '超声诊断科', value: 1291, type: '超声'},
                {source: '超声医学科门诊', target: '超声诊断科', value: 1199, type: '超声'},
                {source: '神经内科门诊', target: 'MR', value: 1136, type");
                WriteLiteral(@": 'MR'},
                {source: '胸外科门诊', target: 'CT', value: 1081, type: 'CT'},
                {source: '肿瘤科门诊', target: 'CT', value: 791, type: 'CT'},
                {source: '肿瘤科门诊', target: 'CT', value: 480, type: 'CT'},
                {source: '消化内科门诊', target: '超声诊断科', value: 718, type: '超声'},
                {source: '消化内科门诊', target: 'CT', value: 556, type: 'CT'},
                {source: '心血管内科门诊', target: 'CT', value: 685, type: 'CT'},
                {source: '肾科门诊', target: '超声诊断科', value: 450, type: '超声'}
            ]
        };
        var sankeyChart = echarts.init(document.getElementById('sankey'));
        function renderSankey(filter) {
            var links = sankeyData.links.filter(function(link) {
                var match = true;
                if (filter.type && link.type !== filter.type) match = false;
                return match;
            });
            var nodesSet = new Set();
            links.forEach(l => { nodesSet.add(l.source); nodesSet.add(l.targe");
                WriteLiteral(@"t); });
            var nodes = sankeyData.nodes.filter(n => nodesSet.has(n.name));
            // 桑基图配色
            var nodeColors = {
                // 右侧节点
                '内镜中心': '#c6ecc6',
                'CT': '#e6f5c9',
                '超声诊断科': '#dbeafe',
                '普放': '#fde2e2',
                'MR': '#e0e7ff',
            };
            nodes.forEach(function(n) {
                if (!nodeColors[n.name]) {
                    nodeColors[n.name] = '#e0f2f1'; // 左侧统一淡绿色
                }
            });
            // 定义不同检查类型的主色
            var typeColors = {
                'CT': ['#60a5fa', '#e6f5c9'],
                '超声': ['#38bdf8', '#dbeafe'],
                '普放': ['#fca5a5', '#fde2e2'],
                'MR': ['#a78bfa', '#e0e7ff'],
                '': ['#34d399', '#c6ecc6'] // 其他/未分类
            };
            sankeyChart.setOption({
                series: [{
                    type: 'sankey',
                    data: nodes.map(function(n) {
            ");
                WriteLiteral(@"            return {
                            ...n,
                            itemStyle: {
                                color: nodeColors[n.name],
                                borderColor: '#b0b0b0',
                                borderWidth: 2,
                                borderRadius: 12,
                                shadowColor: 'rgba(60,120,180,0.10)',
                                shadowBlur: 10
                            },
                            label: {
                                color: '#1e293b',
                                fontWeight: 'normal',
                                fontSize: 15
                            }
                        }
                    }),
                    links: links.map(function(l) {
                        var colors = typeColors[l.type] || typeColors[''];
                        // 生成带透明度的渐变色
                        var colorFrom = colors[0].replace(')', ', 0.38)').replace('rgb', 'rgba');
                  ");
                WriteLiteral(@"      var colorTo = colors[1].replace(')', ', 0.55)').replace('rgb', 'rgba');
                        return {
                            ...l,
                            lineStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 1,
                                    y2: 0,
                                    colorStops: [
                                        { offset: 0, color: colorFrom },
                                        { offset: 1, color: colorTo }
                                    ]
                                },
                                opacity: 1,
                                curveness: 0.5,
                                width: 22
                            }
                        }
                    }),
                    emphasis: {
                        focus: 'adjacency");
                WriteLiteral(@"',
                        itemStyle: {
                            shadowColor: 'rgba(37,99,235,0.18)',
                            shadowBlur: 18,
                            borderColor: '#2563eb',
                            borderWidth: 3
                        },
                        lineStyle: {
                            opacity: 1,
                            width: 32,
                            color: '#2563eb',
                            shadowColor: 'rgba(37,99,235,0.18)',
                            shadowBlur: 10
                        },
                        label: {
                            color: '#2563eb',
                            fontWeight: 'bold',
                            fontSize: 16
                        }
                    },
                    nodeAlign: 'left',
                    nodeWidth: 40,
                    nodeGap: 14,
                    layoutIterations: 32,
                    lineStyle: { curveness: 0.5 },
              ");
                WriteLiteral(@"      label: {
                        fontSize: 15,
                        color: '#1e293b',
                        fontWeight: 'normal',
                        padding: [4, 8],
                        formatter: function(params) {
                            // 右侧目标科室字体更大更粗
                            var targets = ['内镜中心','CT','超声诊断科','普放','MR'];
                            if(targets.includes(params.name)) {
                                return '{targetStyle|' + params.name + '}';
                            }
                            return params.name;
                        },
                        rich: {
                            targetStyle: {
                                fontSize: 18,
                                fontWeight: 'normal',
                                color: '#1e293b',
                                padding: [4, 8]
                            }
                        }
                    },
                    tooltip: {
                  ");
                WriteLiteral(@"      show: true,
                        trigger: 'item',
                        formatter: function(params) {
                            if(params.dataType === 'edge') {
                                return params.data.source + ' → ' + params.data.target + '<br>数量：' + params.data.value;
                            } else {
                                return params.data.name;
                            }
                        }
                    }
                }]
            });
        }
        renderSankey({});
        document.getElementById('sankeySearch').onclick = function() {
            renderSankey({
                type: document.getElementById('examType').value
            });
        };
        // Excel导出
        document.getElementById('excelExport').onclick = function() {
            var wb = XLSX.utils.table_to_book(document.getElementById('dataTable'), {sheet: ""Sheet1""});
            XLSX.writeFile(wb, '科室流量分析明细表.xlsx');
        };
        // 打印表格
   ");
                WriteLiteral(@"     document.getElementById('printTable').onclick = function() {
            var printContents = document.querySelector('.table-scroll').innerHTML;
            var win = window.open('', '', 'height=700,width=900');
            win.document.write('<html><head><title>打印</title>');
            win.document.write('<style>table{width:100%;border-collapse:collapse;font-size:1rem;}th,td{padding:12px 16px;text-align:left;}th{background:#f1f5f9;font-weight:600;color:#1e293b;border-bottom:2px solid #e5e7eb;}tr:not(:last-child) td{border-bottom:1px solid #e5e7eb;}tr:hover td{background:#f0f9ff;} .percent{color:#2563eb;font-weight:bold;}</style>');
            win.document.write('</head><body>');
            win.document.write(printContents);
            win.document.write('</body></html>');
            win.document.close();
            win.print();
        };
    </script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html> ");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
