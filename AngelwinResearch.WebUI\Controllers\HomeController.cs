﻿using AngelwinResearch.ModelExtends;
using AngelwinResearch.Models;
using AngelwinResearch.Services;
using AngelwinResearch.WebUI.Filters;
using AngelwinResearch.WebUI.Models;
using AngelwinResearch.WebUI.Unity;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Spire.Pdf;
using Spire.Pdf.Texts;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using static AngelwinResearch.WebUI.Unity.Common;

namespace AngelwinResearch.WebUI.Controllers
{
    [Authorize]
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;
        private IConfiguration Configuration { get; }
        private readonly IWebHostEnvironment env;
        private readonly AngelwinResearchDbContext db;

        public HomeController(ILogger<HomeController> logger, IConfiguration configuration, IWebHostEnvironment _env, AngelwinResearchDbContext _db)
        {
            _logger = logger;
            Configuration = configuration;
            env = _env;
            db = _db;
        }

        public async Task<IActionResult> Index()
        {
            string currentUserName = User.Identity.Name;
            var user = db.Users.Include(i => i.Roles).FirstOrDefault(i => i.UserName == currentUserName);
            ViewBag.trueName = user.TrueName;
            ViewBag.SiteTitle = Configuration["AppSettings:SiteTitle"];

            #region 根据配置是否获取anyreport的ticket
            ViewBag.Ticket = "";
            var IsLoginAnyReport = "0";
            if (!string.IsNullOrEmpty(Configuration["AppSettings:IsLoginAnyReport"]))
            {
                IsLoginAnyReport = Configuration["AppSettings:IsLoginAnyReport"];
            }
            if (IsLoginAnyReport == "1")
            {
                ViewBag.Ticket = GetAnyreportTicket()?.Result;
            }
            #endregion

            var username = user.UserName;
            ViewBag.UserName = currentUserName;
            var uml = new UserMenuList();
            var ump = new UserMenuList();
            List<MenuTreeDTO> MenuTreeDTOList = null;
            if (username == "admin")          //如果是管理员，则加载所有菜单
            {
                uml.Menus = await db.Menus.ToListAsync();
            }
            else
            {
                var user_roles = user.Roles.ToList();
                var rolesid = user_roles.Select(c => c.RoleId).ToList();
                List<RoleInfo> roles = db.Roles.Include(i => i.Menus).ThenInclude(i => i.Menu).Where(i => rolesid.Contains(i.Id)).ToList();

                foreach (var r in roles)
                {
                    if (uml.Menus == null)
                    {
                        uml.Menus = r.Menus.Select(c => c.Menu).ToList();
                    }
                    else
                    {
                        if (uml.Menus != null && r.Menus != null)
                        {
                            uml.Menus = uml.Menus.Union(r.Menus.Select(c => c.Menu).ToList());
                        }
                        else if (uml.Menus == null && r.Menus != null)
                        {
                            uml.Menus = r.Menus.Select(c => c.Menu).ToList();
                        }
                    }
                }
            }

            if (uml.Menus != null)
            {
                var rootMenu = db.Menus.FirstOrDefault(i => i.menuType == "根模块");
                if (rootMenu != null)
                {
                    var rootMenuId = rootMenu.Id;
                    var catalogMenu = uml.Menus.Where(i => i.parentId == rootMenuId && i.isMenu == "是").OrderBy(i => i.parentId).ThenBy(i => i.menuOrder).ToList(); //非菜单模块不加载
                    MenuTreeDTOList = (from r in catalogMenu
                                       select new MenuTreeDTO
                                       {
                                           MenuID = r.Id,
                                           MenuName = r.menuName,
                                           ParentID = r.parentId,
                                           MenuOrder = r.menuOrder,
                                           MenuIcon = r.menuIcon,
                                           MenuController = string.IsNullOrWhiteSpace(r.menuController) ? "" : r.menuController,
                                           MenuArea = string.IsNullOrWhiteSpace(r.menuArea) ? "" : r.menuArea,
                                           MenuAction = string.IsNullOrWhiteSpace(r.menuAction) ? "" : r.menuAction,
                                           Children = GetChildTree(uml.Menus.ToList(), r.Id),
                                           MenuNote = string.IsNullOrEmpty(r.menuNote) ? "" : r.menuNote,
                                       }).OrderBy(c => c.MenuOrder).ToList();
                }
            }
            else
            {
                return Redirect("/Account/LogOff");
            }
            LoginLog login = CommonFunction.GetLoginInfo(db, HttpContext.User.Identity.Name);
            ump.LastLoginIp = login.LastLoginIp == null ? "" : login.LastLoginIp;
            ump.LastLoginTime = login.LastLoginTime == null ? "" : login.LastLoginTime.ToString();
            return View(MenuTreeDTOList);
        }

        public IActionResult update()
        {
            try
            {
                var cfrdata1 = db.CRFDatas.Where(a => a.CRFormId == 15).ToList();
                foreach (var data in cfrdata1)
                {
                    var json1 = data.CRFJsonValue.Replace("detailArray_row17", "detailArray_row18")
                    .Replace("detailArray_row21", "detailArray_row22");
                    data.CRFJsonValue = json1;

                }
                db.SaveChanges();
                var cfrdata2 = db.CRFDatas.Where(a => a.CRFormId == 16).ToList();
                foreach (var data in cfrdata2)
                {
                    var json1 = data.CRFJsonValue.Replace("detailArray_row15", "detailArray_row16")
                    .Replace("detailArray_row18", "detailArray_row19");
                    data.CRFJsonValue = json1;

                }
                db.SaveChanges();
                return Content("成功");
            }
            catch (Exception)
            {

                return Content("失败");
            }
        }

        public IActionResult ipadIndex()
        {
            return View();
        }

        public IActionResult NullPage()
        {
            return View();
        }
        public static List<MenuTreeDTO> GetChildTree(List<Menu> list, int Id)
        {

            List<MenuTreeDTO> tree = new List<MenuTreeDTO>();
            List<Menu> ChildList = GetChildList(list, Id);
            foreach (var r in ChildList)
            {
                MenuTreeDTO treeB = new MenuTreeDTO();
                treeB.MenuID = r.Id;
                treeB.MenuName = r.menuName;
                treeB.MenuIcon = r.menuIcon;
                treeB.MenuOrder = r.menuOrder;
                treeB.MenuController = string.IsNullOrWhiteSpace(r.menuController) ? "" : r.menuController;
                treeB.MenuArea = string.IsNullOrWhiteSpace(r.menuArea) ? "" : r.menuArea;
                treeB.MenuAction = string.IsNullOrWhiteSpace(r.menuAction) ? "" : r.menuAction;
                treeB.Children = GetChildTree(list, r.Id);
                treeB.MenuNote = string.IsNullOrEmpty(r.menuNote) ? "" : r.menuNote;
                tree.Add(treeB);
            }
            return tree;
        }

        public static List<Menu> GetChildList(List<Menu> list, int Id)
        {
            var childList = list.Where(x => x.parentId == Id).OrderBy(o => o.menuOrder).ToList();
            return childList;
        }

        public IActionResult IndexDemo()
        {
            var token = TokenGenerator.getToken();
            ViewBag.token = WebUtility.UrlEncode(token);
            ViewBag.SiteTitle = Configuration["AppSettings:SiteTitle"];
            return View();
        }

        public IActionResult welcome()
        {
            return View();
        }

        public IActionResult NewXBS()
        {
            var token = TokenGenerator.getToken();
            ViewBag.token = WebUtility.UrlEncode(token);
            ViewBag.formUrl = Configuration["AppSettings:AnyReportUrl"];
            return View();
        }

        public IActionResult CRF(string id)
        {
            var token = TokenGenerator.getToken();
            ViewBag.token = WebUtility.UrlEncode(token);
            ViewBag.formUrl = Configuration["AppSettings:AnyReportUrl"];
            ViewBag.id = id;
            return View();
        }

        public IActionResult NewTGJC()
        {

            var token = TokenGenerator.getToken();
            ViewBag.token = WebUtility.UrlEncode(token);
            ViewBag.formUrl = Configuration["AppSettings:AnyReportUrl"];
            return View();
        }
        public IActionResult NewZLPG()
        {
            var token = TokenGenerator.getToken();
            ViewBag.token = WebUtility.UrlEncode(token);
            ViewBag.formUrl = Configuration["AppSettings:AnyReportUrl"];
            return View();
        }

        public IActionResult AanyReportTGJC()
        {
            var token = TokenGenerator.getToken();
            ViewBag.token = WebUtility.UrlEncode(token);
            ViewBag.formUrl = Configuration["AppSettings:AnyReportUrl"];
            return View();
        }

        public IActionResult AanyReportHYS()
        {
            var token = TokenGenerator.getToken();
            ViewBag.token = WebUtility.UrlEncode(token);
            ViewBag.formUrl = Configuration["AppSettings:AnyReportUrl"];
            return View();
        }

        public IActionResult AanyReportGMS()
        {
            var token = TokenGenerator.getToken();
            ViewBag.token = WebUtility.UrlEncode(token);
            ViewBag.formUrl = Configuration["AppSettings:AnyReportUrl"];
            return View();
        }
        [HttpPost]
        public IActionResult Save([FromBody] temp t)
        {
            var a = Request;
            return Content("OK");
        }
        [HttpPost]
        public IActionResult Save([FromBody] dynamic d)
        {
            return Content("OK");
        }
        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }

        [SSE]
        public async Task<ActionResult> getNewResult(string type, string str, string tips)
        {

            var prompt = "";
            if (type == "xbs")
                prompt = $"{str},根据我提供的JSON格式的现病史数据，我需要您生成一个完全连贯的单一段落，该段落应包含患者病程的所有关键细节。请确保段落内的" +
                       $"信息完整、无遗漏，并保持文本连贯、专业，类似于经验丰富的医疗专业人员所书写的临床病历。请避免使用任何分段或子标题，" +
                       $"确保整个文本是一个统一的、连续的整体，并且不引入任何假设或模拟数据,避免在输出中添加不相关的前导语，并使用中文回答。";
            else if (type == "tgjc")
                prompt = $"{str},根据我提供的JSON格式的体格检查数据，我需要您生成一个完全连贯的单一段落，该段落应包含体格检查的所有关键细节。" +
                    $"请确保段落内的信息完整、无遗漏，并保持文本连贯、专业，类似于经验丰富的医疗专业人员所书写的临床病历。请避免使用任何分段或" +
                    $"子标题，确保整个文本是一个统一的、连续的整体。请避免冗余描述，只需简洁地列出每个部位的主要检查结果，并且不引入任何假设" +
                    $"或模拟数据,避免在输出中添加不相关的前导语，并使用中文回答。";
            else if (type == "zlpg")
                prompt = $"{str},根据我提供的JSON格式的肿瘤评估数据，我需要您生成一个完全连贯的单一段落，该段落应包含肿瘤评估的所有关键细节。" +
                    $"请确保段落内的信息完整、无遗漏，并保持文本连贯、专业，类似于经验丰富的医疗专业人员所书写的临床病历。请避免使用任何分段或" +
                    $"子标题，确保整个文本是一个统一的、连续的整体，并且不引入任何额外的假设或模拟数据,避免在输出中添加不相关的前导语，并使用中文回答。";
            else if (type == "zjp")
            {
                if (string.IsNullOrEmpty(tips))
                    prompt = $"{str},根据我提供的JSON格式的椎间盘数据，我需要您生成一份放射检查报告。" +
                          $"返回内容需要包含检查所见和诊断。参考以下格式" +
                          $"【检查所见:" +
                          //$"腰椎顺列，生理曲度变直，诸椎体边角缘骨质增生。L4－5椎间盘向后及双侧后突出，" +
                          //$"约0.5cm，双侧侧隐窝变窄，局部硬膜囊受压。L5－S1椎间盘向后突出，约0.3cm，局部硬膜" +
                          //$"囊稍受压。双侧椎小关节无明显骨质增生。双侧黄韧带无肥厚。双侧腰大肌影无肿大。" +
                          $"诊断 " +
                         // $"腰椎退行性变，部分椎间盘轻度突出，建议必要时MRI进一步检查。
                         $" 】，并使用中文回答。";
                else
                    prompt = string.Format(tips, str);
            }
            //怀孕史
            else if (type == "hys")
                prompt = $"{str},根据我提供的JSON格式的初诊-月经史与既往孕产史数据，我需要您生成一个完全连贯的单一段落，该段落应包含月经史与既往孕产史的所有关键细节。" +
                    $"请确保段落内的信息完整、无遗漏，并保持文本连贯、专业，类似于经验丰富的医疗专业人员所书写的临床病历。请避免使用任何分段或" +
                    $"子标题，确保整个文本是一个统一的、连续的整体，并且不引入任何额外的假设或模拟数据,避免在输出中添加不相关的前导语，并使用中文回答。";
            //过敏史
            else if (type == "gms")
                prompt = $"{str},根据我提供的JSON格式的初诊-家族病史-FH-过敏史的数据，我需要您生成一个完全连贯的单一段落，该段落应包含家族病史-FH-过敏史的所有关键细节。" +
                    $"请确保段落内的信息完整、无遗漏，并保持文本连贯、专业，类似于经验丰富的医疗专业人员所书写的临床病历。请避免使用任何分段或" +
                    $"子标题，确保整个文本是一个统一的、连续的整体，并且不引入任何额外的假设或模拟数据,避免在输出中添加不相关的前导语，并使用中文回答。";

            var response = Response;
            response.Headers.Add("Cache-Control", "no-cache");
            response.Headers.Add("Connection", "keep-alive");
            try
            {
                var modelType = Configuration["AppSettings:modelType"];
                #region 读取配置文件
                //   string APIUrl = Configuration["AppSettings:APIUrl"];
                #endregion

                string endpoint = "chat/completions";
                dynamic history = new List<dynamic>();
                var ResponseContent = "";
                var requestMsgTime = System.DateTime.Now;

                var contentStr = "";
                #region 读取配置文件
                string apiKey = Configuration[$"GPTSetting:{modelType}:ApiKey"];
                string model = Configuration[$"GPTSetting:{modelType}:Model"];
                string apiUrl = Configuration[$"GPTSetting:{modelType}:apiUrl"];
                string maxTokenStr = Configuration[$"GPTSetting:{modelType}:MaxTokens"];
                int maxTokens = 4000;
                if (!string.IsNullOrEmpty(maxTokenStr))
                {
                    int.TryParse(maxTokenStr, out maxTokens);
                }
                HttpClientHandler handler = null;
                string webProxy = Configuration[$"GPTSetting:{modelType}:WebProxy"];
                if (!string.IsNullOrWhiteSpace(webProxy))
                {
                    handler = new HttpClientHandler()
                    {
                        Proxy = new WebProxy(webProxy)
                    };
                }
                else
                {
                    handler = new HttpClientHandler() { };
                }
                #endregion
                handler.ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator;
                //  HttpClientHandler handler = new HttpClientHandler() { };
                var httpClient = new HttpClient(handler);
                httpClient.BaseAddress = new Uri(apiUrl);
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                history.Add(new { role = "user", content = $"{prompt}" });

                //var model = Configuration["AppSettings:Model"];
                //if (string.IsNullOrEmpty(model))
                //    model = "ChatGLM3-6B-32k";
                dynamic requstDTO = new ExpandoObject();
                requstDTO.model = model;
                requstDTO.messages = history;
                requstDTO.stream = true;
                var requestBody = JsonConvert.SerializeObject(requstDTO);
                var request = new HttpRequestMessage(HttpMethod.Post, apiUrl + endpoint)
                {
                    Content = new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
                };

                using var APIResponse = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);
                {
                    if (APIResponse.IsSuccessStatusCode)
                    {
                        var stream = await APIResponse.Content.ReadAsStreamAsync();
                        var streamReader = new StreamReader(stream);
                        var ddd = await streamReader.ReadLineAsync();
                        while (!streamReader.EndOfStream)
                        {
                            var line = await streamReader.ReadLineAsync();
                            if (!string.IsNullOrWhiteSpace(line))
                            {
                                if (line != "data: [DONE]")
                                {
                                    if (line.StartsWith("data:"))
                                        line = line.Substring(5, line.Length - 5);
                                    var delta = JObject.Parse(line).SelectToken("choices").First().SelectToken("delta");
                                    var finish_reason = JObject.Parse(line).SelectToken("choices").First().SelectToken("finish_reason");
                                    if (delta != null && ((JContainer)delta).Count > 0)
                                    {
                                        //var resultStream = delta.First() as JProperty;
                                        //if (resultStream.Name == "content")
                                        //{
                                        //    contentStr = resultStream.Value.ToString();
                                        //    ResponseContent += contentStr;
                                        //    await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { okMsg = "成功", data = contentStr })}\n\n");
                                        //    await response.Body.FlushAsync();
                                        //}
                                        if (delta["content"] != null) //在不同的api下第一个未必是content，调整支持不同的api
                                        {
                                            contentStr = delta["content"].ToString();
                                            ResponseContent += contentStr;
                                            await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { okMsg = "成功", data = contentStr })}\n\n");
                                            await response.Body.FlushAsync();
                                        }
                                    }
                                    if (finish_reason != null)
                                    {
                                        var resultfinish = finish_reason as JProperty;
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        var errorMsg = "Failed to connect to API.";
                        await response.WriteAsync($"event: end\ndata:{JsonConvert.SerializeObject(new { errorMsg = $"{errorMsg}" })}\n\n");
                        await response.Body.FlushAsync();
                        return new EmptyResult();

                    }
                }

                var resultAll = new
                {
                    okMsg = $"成功",
                    role = "assistant",
                    content = ResponseContent,
                    //newContent = GetSectionList(ResponseContent, prompt)
                };
                await Response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(resultAll)}\n\n");
                await Response.Body.FlushAsync();
                return new EmptyResult();
            }
            catch (Exception ex)
            {
                await Response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(new { errorMsg = $"报错:{ex.Message}-{ex.InnerException?.Message}" })}\n\n");
                await Response.Body.FlushAsync();
                return new EmptyResult();
                //return Json(new { errorMsg = $"报错：{ex.Message}" });
            }
        }

        public IActionResult GetPdfContent(string name)
        {
            var fileAllPath = @$"{env.WebRootPath}/test/{name}";
            PdfDocument pdf = new PdfDocument();
            var text = "";

            // 使用FileStream来读取本地文件
            using (var stream = new FileStream(fileAllPath, FileMode.Open, FileAccess.Read))
            {
                pdf.LoadFromStream(stream);
                // 遍历PDF的每一页  
                for (int i = 0; i < pdf.Pages.Count; i++)
                {
                    // 获取当前页  
                    PdfPageBase page = pdf.Pages[i];

                    // 提取文本内容  
                    PdfTextExtractor textExtractor = new PdfTextExtractor(page);
                    var content = textExtractor.ExtractText(new PdfTextExtractOptions { IsExtractAllText = true });
                    var newcontent = content.Replace("Evaluation Warning : The document was created with Spire.PDF for .NET", "");

                    text += newcontent + "/r/n";

                }
            }

            return Json(new { url = Url, content = text });
        }

        public IActionResult changelog()
        {
            return View();
        }

        public async Task<string> GetAnyreportTicket()
        {
            var tiket = "";
            try
            {
                var user = "admin";
                var pwd = EncryptTools.GetMD5Hash("123456");
                //http://localhost:9905/dmp/index/ssoc.htm?userName=demo&password=xx
                var baseUrl = Configuration["AppSettings:AnyReportUrl"];
                var apiUrl = $"{baseUrl}index/ssoc.htm?userName={user}&password={pwd}";
                var httpClient = new HttpClient();
                httpClient.BaseAddress = new Uri(apiUrl);
                using var APIResponse = await httpClient.GetAsync(apiUrl);
                {
                    var stream = await APIResponse.Content.ReadAsStreamAsync();
                    using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
                    {
                        tiket = reader.ReadToEnd();
                    }
                    return tiket;
                }
            }
            catch (Exception ex)
            {
                return tiket;
            }
        }

    }

    public class temp
    {
        public string patientName { get; set; }
        public string idcard { get; set; }
    }
}
