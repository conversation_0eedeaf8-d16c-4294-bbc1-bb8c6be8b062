﻿using AngelwinResearch.WebUI.Unity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AngelwinResearch.WebUI.Controllers
{
    [Area("API")]
    public class SignalRController : Controller
    {
        private readonly IHubContext<ChatHub> hub;
        private readonly ConnService connService;

        public SignalRController(IHubContext<ChatHub> _hubContext, ConnService _connService)
        {
            hub = _hubContext;
            connService = _connService;
        }

        public async Task<IActionResult> Broadcast(string message)
        {
            //向所有客户端广播消息
            await hub.Clients.All.SendAsync("ReceiveMessage", $"服务端广播: {message}");
            return Json(new { code = "0", msg = $"已广播: {message}"});
        }

        public async Task<IActionResult> SendPrivate(string connectionId, string message)
        {
            //向特定客户端发送消息
            await hub.Clients.Client(connectionId).SendAsync("ReceivePrivate", $"私密消息: {message}");
            return Json(new { code = "0", msg = $"已私发[{connectionId}]: {message}" });
        }

        public async Task<IActionResult> SendPrivateWithIP(string IP, string message)
        {
            //向特定客户端发送消息
            var connectionId = connService.GetConnectionId(IP);
            if (connectionId != null)
            {
                await hub.Clients.Client(connectionId).SendAsync("ReceivePrivate", $"私密消息: {message}");
                return Json(new { code = "0", msg = $"已私发[{IP}-{connectionId}]: {message}" });

            }
            else
            {
                return Json(new { code = "-100", msg = $"{IP}不存在" });

            }
        }
    }
}
