﻿@{
	ViewBag.Title = "前结构化数据";
	Layout = null;
}
@model AngelwinResearch.WebUI.Areas.CMIS.Models.TempCMIS
<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>前结构化数据</title>
	<link href="~/layuiadmin/layui/css/layui.css" rel="stylesheet" />
	<link href="~/layuiadmin/style/admin.css" rel="stylesheet" />
	<script src="~/layuiadmin/layui/layui.js"></script>
	<script src="~/lib/jquery/dist/jquery.js"></script>
	<script src="~/js/common.js"></script>
	<script src="~/js/marked.min.js"></script>
	<style>
		html, body {
			height: 100%;
			margin: 0;
			padding: 0;
		}

		.container {
			height: 100%;
			padding: 15px;
			box-sizing: border-box;
		}

		.block {
			background: #f8f8f8;
			border-radius: 4px;
			padding: 15px;
			box-sizing: border-box;
			height: 96%;
		}
		/* 自定义表单间距 */
		.custom-form .layui-form-item {
			margin-bottom: 12px;
		}
		/* iframe样式 */
		.full-iframe {
			width: 100%;
			height: 100%;
			border: none;
			border-radius: 4px;
		}
		/* 备注框样式 */
		.full-textarea {
			width: 100%;
			height: 100%;
			padding: 10px;
			font-size: 14px;
			border: 1px solid #e6e6e6;
			border-radius: 4px;
			resize: none; /* 禁止调整大小 */
		}

		.block_header {
			display: flex;
			flex-direction: row;
			justify-content: flex-end;
			align-items: center;
			padding: 5px;
		}

		.infowrap .layui-input-inline {
			width: 70%;
			margin-bottom: 10px;
		}

			.infowrap .layui-input-inline .layui-input {
				line-height: 38px;
			}

		.formSel_wrap {
			width: 810px;
			margin: 0 auto;
		}

		.crf_wrap {
			height: 100%;
		}

		#txtMedical p {
			white-space: pre-wrap;
		}
	</style>
</head>
<body>
	<div class="container layui-fluid">
		<!-- 顶部：患者信息 -->
		<div class="layui-row" style="height: 20%; margin-bottom: 15px;display:none">
			<div class="layui-col-md12">
				<div class="block" style="background: #F0F9EB;border: 1px solid #ccc">
					<form class="layui-form custom-form">
						<div class="layui-row infowrap">
							<!-- 每行显示3个表单项 -->

							<div class="layui-form-item layui-row">
								<div class="layui-col-xs4">
									<label class="layui-form-label">患者姓名</label>
									<div class="layui-input-inline">
										<div class="layui-input">@Model?.PatientName</div>
									</div>
								</div>

								<div class="layui-col-xs4">
									<label class="layui-form-label">患者性别</label>
									<div class="layui-input-inline">
										<div class="layui-input">@Model?.SEX</div>
									</div>
								</div>
								<div class="layui-col-xs4">
									<label class="layui-form-label">患者卡号</label>
									<div class="layui-input-inline">
										<div class="layui-input">@Model?.BRKH</div>
									</div>
								</div>
								<div class="layui-col-xs4">
									<label class="layui-form-label">患者来源</label>
									<div class="layui-input-inline">
										<div class="layui-input">@Model?.PatientSource</div>
									</div>
								</div>
								<div class="layui-col-xs4">
									<label class="layui-form-label">检查项目</label>
									<div class="layui-input-inline">
										<div class="layui-input">@Model?.ProjectName</div>
									</div>
								</div>
								<div class="layui-col-xs4">
									<label class="layui-form-label">检查部位</label>
									<div class="layui-input-inline">
										<div class="layui-input">@Model?.JCBW</div>
									</div>
								</div>

							</div>
						</div>
					</form>
				</div>
			</div>
		</div>

		<!-- 下部布局 -->
		<div class="layui-row" style="height: calc(99% - 1px);">
			<!-- 左侧iframe -->
			<div class="@(Model?.HideBtn==true? "layui-col-md12":"layui-col-md9")" style="height: 100%; padding-right: 7px;">
				<div class="formSel_wrap" style="height:45px;display:none;">
					<div class="layui-form layui-form-item" style="height:4%;padding-bottom:10px;">
						<label class="layui-form-label">选择模板：</label>
						<div class="layui-input-inline">
							<select id="formSel" lay-filter="formSel">
							</select>
						</div>
					</div>
				</div>


				<div class="block crf_wrap" style="padding: 10px;">
					<iframe class="full-iframe" frameborder="0" scrolling="auto" id="reportFrame"></iframe>
				</div>
			</div>

			<!-- 右侧备注 -->
			<div class="layui-col-md3 block" style="height: 100%; padding-left: 7px;@(Model?.HideBtn==true?"display:none":"")">
				<div class="block_header">
					<p></p>
					<button type="button" class="layui-btn layui-btn-normal layui-btn-sm" id="btnCopy">一键复制</button>

					<button type="button" class="layui-btn layui-btn-warm layui-btn-sm" id="btnlook">查看表单数据</button>
				</div>
				<div class="block" style="padding: 10px;">
					<div id="txtMedical" style="height:100%;width:100%;resize:none;line-height:40px;font-size:20px; overflow:auto;border: 1px solid #ccc"></div>
				</div>

			</div>
		</div>
	</div>

	<script src="~/layuiadmin/layuiextend/xm-select.js"></script>
	<script>
		layui.config({
			base: '/layuiadmin/layuiextend/'
		}).use(['element', 'layer', 'form'], function () {
			var element = layui.element
				, layer = layui.layer
				, $ = layui.$
				, form = layui.form;


			var option = '';
			var selectData = @Html.Raw(Json.Serialize(ViewBag.select));
			selectData.forEach(function (item, index) {
				if (index == 0) {
					var url = getReportUrl(item.id);
					$("#reportFrame").attr("src", url);
					if(item.id=="245c13512e3342069b4dfa253200c92f")
					{
						 setTimeout(function() {
									FillCRForm(item.id);
								}, 3000);
					}
					else
					   setTimeout(function() {
									FillCRForm(item.id);
								}, 1000);

				}
				option += `<option value=${item.id}>${item.name}</option>`;
			});
			$("#formSel").html(option);
			form.render();

			form.on('select(formSel)', function (data) {
				var val = data.value;
				var url = getReportUrl(val);
				$("#reportFrame").attr("src", url);
				 setTimeout(function() {
									FillCRForm(val);
								}, 1000);

			});
			var source;
			// 父窗口
			window.addEventListener('message', function (event) {
				if (event.data.action === 'save') {
					 var formData = event.data.data;
					var obj = {};
					obj.formData = formData;
					obj.cmis = @Html.Raw(Json.Serialize(Model));
					obj.formId = $("#formSel").val();
					 $.post('/CMIS/BeforeStructured/SubmitData', { "request": obj }, function (res) {
							   if(res.code==0)
							   {
								   if (source != undefined) {
						source.close();
					}

					var json = JSON.stringify(obj);
				  //  url = '/CMIS/BeforeStructured/GetResult?words=' + encodeURIComponent(json);
					url = '/CMIS/BeforeStructured/GetResult?Id=' + res.data.id;
					console.log(formData);
					var loadIndex = layer.load(1);
					var i = 0;
					var allData = "";
					source = new EventSource(url);
					source.onmessage = function (event) {
						var result = JSON.parse(event.data);
						if (result.okMsg) {
							if(result.data)
							{
							layer.close(loadIndex);
							}
							allData += result.data;
							var htmlContent = marked.parse(allData);
							$("#txtMedical").html(htmlContent);

							var div = $('#txtMedical');
							div.scrollTop(div[0].scrollHeight - div.innerHeight());

						}
						else {
							  layer.close(loadIndex);
							layer.msg(result.errorMsg);
							source.close();
						}
					};

					source.addEventListener('end', function (event) {
						var result = JSON.parse(event.data);
						layer.close(loadIndex);
						console.log("end:" + event.data);
						var newObj = {};
						newObj.AIResult = result.content;
						newObj.id= res.data.id;
						if (result.okMsg) {
							//  var loading = layer.load(1);
							  $.post('/CMIS/BeforeStructured/Update',newObj, function (res) {
								 if(res.code!=0){
								   layer.msg(res.msg);
								 }
							  })

						}
						else {
							layer.msg(result.errorMsg);
						}
						source.close();
					}, false);

					source.onerror = function (event) {
						  layer.close(loadIndex);
						source.close();
					};
							   }
							   else
								layer.msg(res.msg);
							})

				}
				else if (event.data.action === 'pingti') {
					var formData = event.data.data;
					// 创建一个隐藏的 textarea 元素
					var textArea = document.createElement("textarea");

					// 将目标文本放入 textarea 中
					textArea.value = formData;

					// 避免出现滚动条，同时确保元素不在视口中可见
					textArea.style.position = 'fixed';
					textArea.style.top = '0';
					textArea.style.left = '-9999px';

					// 添加 textarea 到 DOM 中
					document.body.appendChild(textArea);

					// 选中 textarea 中的内容
					textArea.select();
					textArea.setSelectionRange(0, 99999); /* 为了兼容移动设备 */

					try {
						// 执行复制命令
						var successful = document.execCommand('copy');
						var msg = successful ? 'successful' : 'unsuccessful';
						console.log('Copying text command was ' + msg);
					} catch (err) {
						console.error('Oops, unable to copy', err);
					}

					// 移除临时的 textarea 元素
					document.body.removeChild(textArea);

					 var obj = {};
					obj.formData = formData;
					obj.cmis = @Html.Raw(Json.Serialize(Model));
					obj.formId = $("#formSel").val();
					  $.post('/CMIS/BeforeStructured/SubmitData', { "request": obj }, function (res) {

								layer.msg(res.msg);
							})
				}

			}, false);

			function getReportUrl(formId) {
				var randVersion = Math.random();
				var url = "@($"{ViewBag.formUrl}")" + formId + "@($".form?token={ViewBag.token}")&version=" + randVersion;
				return url;
			}

			$("#btnCopy").click(function () {
				//var textBox = document.getElementById('txtMedical');
				//textBox.select(); // 选择文本
				//document.execCommand('copy'); // 执行复制命令
				//textBox.blur();
				//layer.msg('病历文书已复制');

				// 创建一个临时的textarea来复制内容
				var $temp = $("<textarea>");
				$("body").append($temp);
				$temp.val($("#txtMedical").text()).select();
				try {
					var successful = document.execCommand('copy');
					var msg = successful ? '成功复制到剪贴板' : '复制失败';
					layer.msg(msg);
				} catch (err) {
					layer.msg('无法复制，浏览器不支持');
				}
				// 移除临时textarea
				$temp.remove();
			});

           $("#btnlook").click(function () {
	var obj = {};
	//obj.formId=formId;
	obj.cmis = @Html.Raw(Json.Serialize(Model));
	$.ajax({
		type: 'POST',
		url: "/CMIS/BeforeStructured/LookFormData",
		data: JSON.stringify(obj),
		dataType: 'json',
		contentType: "application/json; charset=utf-8",
		success: function (res) {
			if (res.code == 0) {
				layer.open({
					type: 1,
					area: ['60%', '80%'],
					resize: false,
					shadeClose: true,
					title: '帮助',
					content: $("#popwrap"),
					success: function () {
						$("#model_wrapR").val(res.data);
					}
				})
			}
		}
	});
})

			function FillCRForm(formId)
			{
				var obj ={};
				obj.formId=formId;
				obj.cmis = @Html.Raw(Json.Serialize(Model));
				$.ajax({
					  type: 'POST',
					  url: "/CMIS/BeforeStructured/FillCRForm",
					  data: JSON.stringify(obj),
					  dataType: 'json',
					  contentType: "application/json; charset=utf-8",
					  success: function (res) {
						  if(res.code==0){
						   var iframe = document.getElementById('reportFrame');
							 iframe.contentWindow.postMessage({ action: "show", data: res.data }, "*");
						  }
					  }
					 });
			}

		});
	</script>
</body>

<div id="popwrap" style="display:none;height:100%;overflow:hidden;">
	<div class="layui-row layui-col-space30" style="height:100%">
		<div class="layui-col-md12" style="height:100%">
			<div style="background-color: #fff;height: 100%; padding:15px 15px;">
				<textarea id="model_wrapR" style="height:100%;width:100%;resize:none;line-height:40px;font-size:20px; overflow:auto;"></textarea>
			</div>
		</div>
	</div>


</div>
</html>