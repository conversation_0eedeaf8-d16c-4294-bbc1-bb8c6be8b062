﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace AngelwinResearch.Models
{
    public class AngelwinResearchDbContext : IdentityDbContext<UserInfo, RoleInfo, int>
    {
        public AngelwinResearchDbContext(DbContextOptions<AngelwinResearchDbContext> options)
            : base(options)
        {
        }
       
        public virtual DbSet<Menu> Menus { get; set; }
        public virtual DbSet<RoleMenu> RoleMenus { get; set; }
        public virtual DbSet<HospitalDept> HospitalDepts { get; set; }
        public virtual DbSet<DiseaseSpecificGroup> DiseaseSpecificGroups { get; set; }
        public virtual DbSet<CRForm> CRForms { get; set; }
        public virtual DbSet<CRFData> CRFDatas { get; set; }
        public virtual DbSet<MedicalDocument> MedicalDocuments { get; set; }
        public virtual DbSet<CRFormFieldSet> CRFormFieldSets { get; set; }
        public virtual DbSet<ResearchPatient> ResearchPatients { get; set; }
        public virtual DbSet<Log> Logs { get; set; }
        public virtual DbSet<LoginLog> LoginLogs { get; set; }
        public virtual DbSet<WebConfig> WebConfigs { get; set; }
        public virtual DbSet<PatientFormHide> PatientFormHides { get; set; }
        public virtual DbSet<UserZBZSetting> UserZBZSettings { get; set; }

        public virtual DbSet<PdfInfo> PdfInfos { get; set; }

        /// <summary>
        /// addbyzolf 20240903
        /// </summary>
        public virtual DbSet<YY_KSBMK> YY_KSBMK { get; set; }
        public virtual DbSet<OperationHistory> OperationHistorys { get; set; }
        public virtual DbSet<DataDict> DataDicts { get; set; }



        public virtual DbSet<TaskInfo> TaskInfos { get; set; }
        public virtual DbSet<TaskAutoCollect> TaskAutoCollects { get; set; }
        public virtual DbSet<TaskAutoCollectCRF> TaskAutoCollectCRFs { get; set; }

        public virtual DbSet<ExternalReport> ExternalReports { get; set; }
        public virtual DbSet<MultiCenter> MultiCenters { get; set; }

        public virtual DbSet<AgentInfo> AgentInfos { get; set; }
        public virtual DbSet<PromptInfo> PromptInfos { get; set; }

        public virtual DbSet<CMISCRFSetting> CMISCRFSettings { get; set; }
        public virtual DbSet<CMISCRFData> CMISCRFDatas { get; set; }
        public virtual DbSet<TaskCMISCRFData> TaskCMISCRFDatas { get; set; }

        public virtual DbSet<FollowupTemplate> FollowupTemplates { get; set; }

        public virtual DbSet<FollowupTemplateDetail> FollowupTemplateDetails { get; set; }

        public virtual DbSet<FollowupPlan> FollowupPlans { get; set; }

        public virtual DbSet<FollowupRecord> FollowupRecords { get; set; }

        public virtual DbSet<FollowupRecordDetail> FollowupRecordDetails { get; set; }

        public virtual DbSet<ChatTheme> ChatThemes { get; set; }
        public virtual DbSet<ChatMessage> ChatMessages { get; set; }

        // addbyzolf 20250620
        public virtual DbSet<HospitalCRForm> HospitalCRForms { get; set; }
        public virtual DbSet<HospitalCRFData> HospitalCRFDatas { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            ///组合主键
            modelBuilder.Entity<RoleMenu>()
               .HasKey(o => new { o.RoleInfoId, o.MenuId });

            modelBuilder.Entity<UserInfo>().HasMany<IdentityUserRole<int>>((UserInfo e) => e.Roles)
                .WithOne().HasForeignKey((IdentityUserRole<int> e) => e.UserId).IsRequired(true);

            modelBuilder.Entity<RoleInfo>().HasMany<IdentityUserRole<int>>((RoleInfo e) => e.Users)
                .WithOne().HasForeignKey((IdentityUserRole<int> e) => e.RoleId).IsRequired(true);
            // addbyzolf 20240903
            modelBuilder.Entity<DataDict>().HasIndex(e => new { e.DataDictCode, e.ParentId }).IsUnique();

            modelBuilder.Entity<CRForm>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");
            modelBuilder.Entity<ResearchPatient>().Property("JoinDate").HasDefaultValueSql("GETDATE()");
            modelBuilder.Entity<ResearchPatient>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");
            modelBuilder.Entity<DiseaseSpecificGroup>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");
            modelBuilder.Entity<UserZBZSetting>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");
            // addbyzolf 20240903
            modelBuilder.Entity<OperationHistory>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<TaskAutoCollect>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<ExternalReport>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");
            modelBuilder.Entity<MultiCenter>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<AgentInfo>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");
            modelBuilder.Entity<PromptInfo>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<CMISCRFSetting>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");
            modelBuilder.Entity<CMISCRFData>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");
            modelBuilder.Entity<TaskCMISCRFData>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");

            // addbyzolf 20250620
            modelBuilder.Entity<HospitalCRFData>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");
            modelBuilder.Entity<HospitalCRForm>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");

            foreach (IMutableForeignKey item in modelBuilder.Model.GetEntityTypes().SelectMany(e => e.GetForeignKeys()))
            {
                item.DeleteBehavior = DeleteBehavior.Restrict;
            }

        }
    }
}
