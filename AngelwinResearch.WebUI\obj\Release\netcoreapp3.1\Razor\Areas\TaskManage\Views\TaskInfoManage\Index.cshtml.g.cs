#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\TaskManage\Views\TaskInfoManage\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "b24c011a4554dc0b953c3bf1b5c27d1d3d37c98a8b3fb88b687971a06a39a172"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_TaskManage_Views_TaskInfoManage_Index), @"mvc.1.0.view", @"/Areas/TaskManage/Views/TaskInfoManage/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"b24c011a4554dc0b953c3bf1b5c27d1d3d37c98a8b3fb88b687971a06a39a172", @"/Areas/TaskManage/Views/TaskInfoManage/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_TaskManage_Views_TaskInfoManage_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<
#nullable restore
#line 4 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\TaskManage\Views\TaskInfoManage\Index.cshtml"
       AngelwinResearch.Models.TaskInfo

#line default
#line hidden
#nullable disable
    >
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/jquery/dist/jquery.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\TaskManage\Views\TaskInfoManage\Index.cshtml"
  
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "b24c011a4554dc0b953c3bf1b5c27d1d3d37c98a8b3fb88b687971a06a39a1726936", async() => {
                WriteLiteral("\r\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\">\r\n    <title>任务管理</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "b24c011a4554dc0b953c3bf1b5c27d1d3d37c98a8b3fb88b687971a06a39a1727449", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "b24c011a4554dc0b953c3bf1b5c27d1d3d37c98a8b3fb88b687971a06a39a1728651", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "b24c011a4554dc0b953c3bf1b5c27d1d3d37c98a8b3fb88b687971a06a39a1729853", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        /*弹窗*/
        .window_wrap {
            padding: 15px;
        }

        .search_wrap {
            background-color: #f0f0f0;
        }

        .layui-tab-brief {
            background-color: #fff;
        }

        .line_wrap {
            padding: 10px 15px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .form_item {
            padding-top: 10px;
        }

        .btnwrap {
            padding-left: 10px;
        }

        .layui-show {
            display: flex !important;
            align-items: flex-end;
        }

        .form_wrap {
            flex: 1;
        }

        .table_wrap {
            overflow: hidden;
        }

        #detail_window .layui-form-label {
            width: 100px;
        }

        #detail_window .layui-form-val {
            padding: 9px 15px;
        }

        #detail_window .mindow_icon .title_icon {
        ");
                WriteLiteral(@"    display: inline-block;
        }

        #detail_window .mindow_icon {
            text-align: center;
            padding: 20px 0;
        }

        #detail_window .layui-form-item {
            margin-bottom: 0;
        }
        /* 定义表头样式 */
        .layui-table-header .layui-table-cell {
            font-weight: bold; /* 加粗 */
            font-size: 14px; /* 可选：调整字体大小以提升可读性 */
            color: #333; /* 可选：调整字体颜色 */
        }

        .layui-table-g {
            background-color: #f2f2f2; /* Light gray background color */
            cursor: pointer; /* Change cursor to pointer */
            transition: background-color 0.3s; /* Smooth transition for background color */
        }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "b24c011a4554dc0b953c3bf1b5c27d1d3d37c98a8b3fb88b687971a06a39a17213480", async() => {
                WriteLiteral(@"
    <div class=""layui-col-md12"">
        <div class=""layui-card"">
            <!--编辑区-->
            <div class=""layui-card-body layui-form"">
                <div class=""layui-inline"">
                    <label class=""layui-form-label"">作业名称</label>
                    <div class=""layui-input-inline"">
                        <input type=""text"" name=""Id"" id=""Id"" style=""display:none;""");
                BeginWriteAttribute("value", " value=\"", 2703, "\"", 2720, 1);
                WriteAttributeValue("", 2711, 
#nullable restore
#line 99 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\TaskManage\Views\TaskInfoManage\Index.cshtml"
                                                                                           Model.Id

#line default
#line hidden
#nullable disable
                , 2711, 9, false);
                EndWriteAttribute();
                WriteLiteral(" />\r\n                        <input type=\"text\" name=\"TaskName\" id=\"TaskName\" required lay-verify=\"required\" placeholder=\"作业名称\" autocomplete=\"off\" class=\"layui-input\"");
                BeginWriteAttribute("value", " value=\"", 2887, "\"", 2910, 1);
                WriteAttributeValue("", 2895, 
#nullable restore
#line 100 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\TaskManage\Views\TaskInfoManage\Index.cshtml"
                                                                                                                                                                          Model.TaskName

#line default
#line hidden
#nullable disable
                , 2895, 15, false);
                EndWriteAttribute();
                WriteLiteral(@" style=""width:150px;"" readonly>
                    </div>
                </div>
                <div class=""layui-inline"">
                    <label class=""layui-form-label"" style=""width:100px;"">下次执行时间</label>
                    <div class=""layui-input-block "" style=""margin-left:130px;"">
                        <input type=""text"" name=""Cron"" id=""Cron"" placeholder=""任务执行时间"" autocomplete=""off"" class=""layui-input""");
                BeginWriteAttribute("value", " value=\"", 3334, "\"", 3360, 1);
                WriteAttributeValue("", 3342, 
#nullable restore
#line 106 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\TaskManage\Views\TaskInfoManage\Index.cshtml"
                                                                                                                                     Model.NextRunTime

#line default
#line hidden
#nullable disable
                , 3342, 18, false);
                EndWriteAttribute();
                WriteLiteral(@" readonly style=""width:150px;"">
                    </div>
                </div>
                <div class=""layui-inline"">
                    <button id=""enable"" class=""layui-btn layui-btn-normal"" lay-filter=""enable"">
                        <i class=""layui-icon layui-icon-play""></i> 启用
                    </button>
                    <button id=""pause"" class=""layui-btn layui-btn-danger"" lay-filter=""pause"">
                        <i class=""layui-icon layui-icon-pause""></i> 暂停
                    </button>
                    <button id=""run"" class=""layui-btn layui-btn-warm"" lay-filter=""run"">
                        <i class=""layui-icon layui-icon-refresh""></i> 立即执行
                    </button>
                </div>
            </div>
            <!--搜索区-->
            <div class=""line_wrap search_wrap"">
                <div>
                    <div class=""layui-inline"">
                        <label class=""layui-form-label"">搜索条件</label>
                        <div class=""layui-in");
                WriteLiteral("put-inline\">\r\n                            <div id=\"xmDeptsList\" style=\"width:300px\"></div>\r\n                        </div>\r\n                        <div class=\"layui-inline\">\r\n                            <div id=\"xmGroupsList\" ");
                WriteLiteral(" style=\"width:250px\"></div>\r\n                        </div>\r\n                        <div class=\"layui-input-inline\">\r\n                                <input type=\"text\" class=\"layui-input\" name=\"keyWord\" placeholder=\"请输入患者ID或姓名\" id=\"keyWord\"");
                BeginWriteAttribute("value", " value=\"", 4880, "\"", 4916, 1);
                WriteAttributeValue("", 4888, 
#nullable restore
#line 133 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\TaskManage\Views\TaskInfoManage\Index.cshtml"
                                                                                                                                     ViewBag.ResearchPatientId

#line default
#line hidden
#nullable disable
                , 4888, 28, false);
                EndWriteAttribute();
                WriteLiteral(@" />
                            </div>
                        <button id=""Search"" class=""layui-btn layui-btn-primary layui-border-green""><i class=""layui-icon layui-icon-search""></i> </button>
                    </div>
                </div>
            </div>
            <div class=""layui-row"">
                <div class=""layui-col-xs6"">
                    <div class=""layui-card-body table_wrap"">
                        <table id=""tablelist"" lay-filter=""tablelist""></table>
                        <script type=""text/html"" id=""tableBar"">
                            <a class=""layui-btn layui-btn-normal layui-btn-xs"" lay-event=""view"" style=""text-decoration: none ""><i class=""layui-icon""></i>查看进度</a>
                        </script>
                    </div>
                </div>
                <div class=""layui-col-xs6"">
                    <div class=""layui-card-body table_wrap"">
                        <table id=""tableCRFlist"" lay-filter=""tableCRFlist""></table>
                        <s");
                WriteLiteral(@"cript type=""text/html"" id=""tableBar1"">
                            <a class=""layui-btn layui-btn-xs"" lay-event=""edit"">编辑</a>
                        </script>


                    </div>
                </div>

            </div>



        </div>
    </div>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "b24c011a4554dc0b953c3bf1b5c27d1d3d37c98a8b3fb88b687971a06a39a17220140", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "b24c011a4554dc0b953c3bf1b5c27d1d3d37c98a8b3fb88b687971a06a39a17221264", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "b24c011a4554dc0b953c3bf1b5c27d1d3d37c98a8b3fb88b687971a06a39a17222388", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "b24c011a4554dc0b953c3bf1b5c27d1d3d37c98a8b3fb88b687971a06a39a17223512", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"


    <script>
        layui.use(['element', 'layer', 'table', 'laydate', 'form'], function () {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , laydate = layui.laydate
                , $ = layui.$
                , form = layui.form;
            ;
            var url = '';
            var value = """";
            table.render({
                elem: '#tablelist'
                , id: 'tablelist'
                , page: true

                , limit: 10
                , height: 'full-185'
                , cols: [[
                    { field: 'No', title: '序号', type: 'numbers', fixed: 'left' }
                    , { field: 'Id', title: 'Id', sort: true, width: '6%', hide: true }
                    , { field: 'PatientName', title: '患者', width: 100 }
                    , { field: 'GroupName', title: '专病名称', minWidth: 150 }
                    , {
                        field: 'progress', t");
                WriteLiteral(@"itle: '采集进度', width: 200, templet: function (d) {
                            var total = d.CRFormCount || 0;
                            var completed = d.CollectedCount || 0;
                            var percentage = total > 0 ? (completed / total * 100).toFixed(2) : 0;
                            return `

                        <div style='margin-top: 5px;'><div class=""layui-progress layui-progress-big herfform"" lay-showPercent=""true"">
                            <div class=""layui-progress-bar"" lay-percent=""${completed}/${total}""></div>
                        </div></div>
                    `;
                        }
                    }
                    , { field: 'CreateUserName', title: '创建人', width: 100 }
                    , { field: 'CreatedTime', title: '创建时间', width: 200 }
                    , { title: '操作', toolbar: '#tableBar', width: 100, fixed: 'right' }
                ]]
                , done: function (res, curr, count) {
                    element.render('p");
                WriteLiteral(@"rogress'); // 渲染进度条
                    var id = 0;
                    if (res.data.length > 0) {
                        //默认第一行选中
                        $(""[lay-id='tablelist'] .layui-table tbody:eq(0)"").find(""tr:eq(0)"").removeClass('layui-bg-w').addClass('layui-table-g').siblings().removeClass('layui-table-g').addClass('layui-bg-w');
                        id = res.data[0].Id;
                    }
                    SearchCRFData(id);
                    table.on('row(tablelist)', function (obj) {
                        obj.tr.removeClass('layui-bg-w').addClass('layui-table-g').siblings().removeClass('layui-table-g').addClass('layui-bg-w');
                        //加载Detail表格
                        SearchCRFData(obj.data.Id);
                    });
                }
            });

            table.render({
                elem: '#tableCRFlist'
                , id: 'tableCRFlist'
                , page: false
                /* , limit: 10*/
                , height: 'full-");
                WriteLiteral(@"185'
                , cols: [[
                    { field: 'No', title: '序号', type: 'numbers', fixed: 'left' }
                    , { field: 'Id', title: 'Id', sort: true, width: '6%', hide: true }
                    , { field: 'CRFormId', title: '表单id', width: 80 }
                    , { field: 'CRFormName', title: '表单名称', minWidth: 200 }
                    , {
                        field: 'IsExec', title: '状态', sort: true, width: 90, templet: function (d) {
                            if (d.IsExec === 1) {
                                return '<span style=""color: green"">成功</span>';
                            } else if (d.IsExec === -1) {
                                return '<span style=""color: red"">失败</span>';
                            } else {
                                return '<span>未执行</span>';
                            }
                        }
                    },
                    , { field: 'ExecErrorCount', title: '错误次数', width: 80 }
                   ");
                WriteLiteral(@" , {
                        field: 'ExecMsg', title: '错误信息', width: 200, templet: function (d) {
                            if (d.IsExec === -1) {
                                return '<span style=""color: red"">' + d.ExecMsg + '</span>';
                            } else {
                                return '<span>' + d.ExecMsg + '</span>';
                            }
                        }
                    }
                    , { field: 'ExecTime', title: '执行时间', width: 150 }
                    , { title: '操作', toolbar: '#tableBar1', width: 100, fixed: 'right' }
                ]]
                , done: function (res) {
                    // 监听工具条
                    table.on('tool(tableCRFlist)', function (obj) {
                        var data = obj.data;
                        if (obj.event === 'edit') {
                            $(obj.tr).find('td[data-field=""ExecErrorCount""]').each(function () {
                                var td = $(this);
               ");
                WriteLiteral(@"                 var value = td.text();
                                td.html('<input type=""text"" style=""width:80px;"" class=""layui-input"" value=""' + value + '"">');
                            });
                            $(obj.tr).find('a[lay-event=""edit""]').attr('lay-event', 'save').text('保存');
                        } else if (obj.event === 'save') {
                            var newName = $(obj.tr).find('td[data-field=""ExecErrorCount""] input').val();
                            data.ExecErrorCount = newName;
                            $.ajax({
                                url: '/TaskManage/TaskInfoManage/Edit',
                                type: 'POST',
                                data: { 'id': data.Id, 'count': data.ExecErrorCount },
                                success: function (res) {
                                    if (res.code == 0) {
                                        layer.msg('保存成功');
                                        table.reload('tableCRFlist');");
                WriteLiteral(@"
                                    } else {
                                        layer.msg('保存失败');
                                    }
                                },
                                error: function () {
                                    layer.msg('保存失败');
                                }
                            });
                        }
                    });
                }

            });

            //监听tablelist工具条
            table.on('tool(tablelist)', function (obj) {
                var data = obj.data;
                if (obj.event === 'view') {
                    var pId = data.ResearchID;

                    if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
                        url = ""/ReportingManage/PatientManage/DataCollect?ResearchID="" + pId;
                        window.location.href = url;
");
                WriteLiteral(@"
                    }
                    else {
                        parent.layui.index.openTabsPage(""/ReportingManage/PatientManage/DataCollecPC?ResearchID="" + pId, ""数据采集"");
                    }
                }
            });


            //监听提交
            form.on('submit(submit)', function (data) {
                var indes = layer.load();
                data.field.TaskName = $(""#TaskName"").val();
                data.field.TaskType = $(""#TaskType"").val();
                data.field.Cron = $(""#Cron"").val();
                data.field.Id = $(""#Id"").val();
                //提交 Ajax 成功后，关闭当前弹层并重载表格
                $.ajax({
                    url: '/TaskManage/TaskInfoManage/Save',
                    type: ""post"",
                    data: { 'id': $(""#Id"").val(), 'taskName': $(""#TaskName"").val(), 'taskType': $(""#TaskType"").val(), 'cron': $(""#Cron"").val() },
                    datatype: 'json',
                    success: function (data) {
                        if (data.ok");
                WriteLiteral(@"Msg) {
                            layer.msg(data.okMsg);
                            table.reload('tablelist'); //重载表格
                        }
                        else {
                            layer.msg(data.errorMsg);
                        }
                        layer.close(indes);
                    }, error: function (res) {
                        layer.msg(""加载统计信息错误："" + res.responseText);
                        layer.close(indes);
                    }
                });
                return false;
            });

            var xmDeptsList = xmSelect.render({
                el: '#xmDeptsList',
                model: { label: { type: 'text' } },
                prop: {
                    name: 'title',
                    value: 'id',
                },
                minWidth: 200,
                radio: true,
                filterable: true,
                clickClose: true,
                //树
                tree: {
                    show: tr");
                WriteLiteral(@"ue,
                    //非严格模式
                    strict: false,
                    //默认展开节点
                    expandedKeys: [-1],
                },
                data: []
                , on: function (val) {
                    getGroupsList(1, val.arr[0].id, """");
                }
            });

            var xmGroupsList = xmSelect.render({
                el: '#xmGroupsList',
                model: { label: { type: 'text' } },
                prop: {
                    name: 'title',
                    value: 'id',
                },
                minWidth: 200,
                radio: true,
                filterable: true,
                clickClose: true,
                //树
                tree: {
                    show: true,
                    //非严格模式
                    strict: false,
                    //默认展开节点
                    expandedKeys: [-1],
                },
                data: []
                , on: function (val) {
            ");
                WriteLiteral(@"    }
            });

            function GetDeptsTree(value) {
                $.ajax({
                    url: '/CommAPI/GetOrgsTreeList',
                    type: ""post"",
                    datatype: 'json',
                    data: { 'Type': value },
                    success: function (result) {
                        xmDeptsList.update({
                            data: result
                        });
                        //if (result[0].id) {
                        //    var arr = new Array();
                        //    arr.push(result[0].id);
                        //    xmDeptsList.setValue(arr);
                        //    getGroupsList(1, result[0].id, """");
                        //}
                        setTimeout(function () {
                            SearchData();
                        }, 1000);

                    }, error: function () {
                        layer.msg(""获取失败！"");
                    }
                })
            };");
                WriteLiteral(@"

            function getGroupsList(type, val, defaultValue) {
                $.ajax({
                    url: '/CommAPI/GetGroupTreeList?hospitalDeptId=' + val,
                    // contentType: ""application/json"",
                    type: ""get"",
                    datatype: 'json',
                    success: function (result) {
                        if (type == 1) {
                            xmGroupsList.update({
                                data: result
                            });
                            if ($.trim(defaultValue) != '' && result[0].id) {
                                var arr = new Array();
                                arr.push(defaultValue);
                                xmGroupsList.setValue(arr);
                            }
                        }
                    }, error: function () {
                        layer.msg(""获取失败！"");
                    }
                });
            }

            function SearchData() {

 ");
                WriteLiteral(@"               table.reload('tablelist', {
                    page: {
                        curr: 1
                    },
                    url: '/TaskManage/TaskInfoManage/TaskCollectsList'
                    , where: {
                        'deptId': xmDeptsList.getValue('valueStr'),
                        'groupId': xmGroupsList.getValue('valueStr'),
                        'keyword': $.trim($(""#keyWord"").val())
                    }
                });
            };

            function SearchCRFData(collectId) {

                table.reload('tableCRFlist', {
                    //page: {
                    //    curr: 1
                    //},
                    url: '/TaskManage/TaskInfoManage/TaskAutoCollectCRFsList'
                    , where: {
                        'taskAutoCollectId': collectId
                    }
                });
            };

            $(document).ready(function () {
                GetDeptsTree(value);
                $(doc");
                WriteLiteral(@"ument).on('click', '#Search', function () {
                    SearchData();
                })

                $(document).on('click', '#enable', function () {
                    operate(""Start"");
                });
                $(document).on('click', '#pause', function () {
                    operate(""Pause"");
                });
                $(document).on('click', '#run', function () {
                    operate(""Run"");
                });
                $(document).on('click', '#state', function () {
                    getStatus();
                });

            });

            function operate(type) {
                var indexs = layer.load();
                $.ajax({
                    url: ""/TaskManage/TaskInfoManage/"" + type,
                    type: ""post"",
                    data: { ""Id"": $(""#Id"").val() },
                    success: function (res) {
                        if (res.okMsg) {
                            layer.msg(res.okMsg);
         ");
                WriteLiteral(@"               }
                        else {
                            layer.msg(res.errorMsg);
                        }
                        layer.close(indexs);
                    }
                });
            }

            function getStatus() {
                $.ajax({
                    url: ""/TaskManage/TaskInfoManage/GetOnlineJobById"",
                    type: ""post"",
                    data: { taskName: $(""#TaskName"").val() },
                    success: function (res) {
                        if (res.code == 0) {
                            layer.msg(res.data);
                        } else {
                            layer.msg(res.errorMsg);
                        }
                        layer.close(indexs);
                    }
                });
            }


            function setTableH() {
                var winH = $(window).height();
                var navH = $("".layui-tab-brief"").height();
                var searchH = $("".search_w");
                WriteLiteral(@"rap"").height();
                var editAreaH = $("".edit_area"").height();
                var tableH = winH - (navH + searchH + editAreaH) - 55 + ""px"";
                console.log(tableH)
                $("".table_wrap"").css(""height"", tableH);

            };
            setTableH();
            $(window).resize(function () {
                setTableH()
            });
        });
    </script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<AngelwinResearch.Models.TaskInfo> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
