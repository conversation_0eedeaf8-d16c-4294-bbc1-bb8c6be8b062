#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\MedicalRecordUpLoad\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "8a74f0d3df6cc0bb54b47e5e8f8ee261ae1cf0559bb9ac5ce7befa44ad858e9a"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_BasicConfig_Views_MedicalRecordUpLoad_Index), @"mvc.1.0.view", @"/Areas/BasicConfig/Views/MedicalRecordUpLoad/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"8a74f0d3df6cc0bb54b47e5e8f8ee261ae1cf0559bb9ac5ce7befa44ad858e9a", @"/Areas/BasicConfig/Views/MedicalRecordUpLoad/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_BasicConfig_Views_MedicalRecordUpLoad_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/jquery/dist/jquery.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\MedicalRecordUpLoad\Index.cshtml"
  
    ViewBag.Title = "模块管理";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"en\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "8a74f0d3df6cc0bb54b47e5e8f8ee261ae1cf0559bb9ac5ce7befa44ad858e9a6051", async() => {
                WriteLiteral(@"
    <meta charset=""UTF-8"">
    <meta name=""viewport""
          content=""width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"">
    <meta http-equiv=""X-UA-Compatible"" content=""ie=edge"">
    <title>病历文书上传管理</title>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "8a74f0d3df6cc0bb54b47e5e8f8ee261ae1cf0559bb9ac5ce7befa44ad858e9a6605", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "8a74f0d3df6cc0bb54b47e5e8f8ee261ae1cf0559bb9ac5ce7befa44ad858e9a7807", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "8a74f0d3df6cc0bb54b47e5e8f8ee261ae1cf0559bb9ac5ce7befa44ad858e9a9009", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "8a74f0d3df6cc0bb54b47e5e8f8ee261ae1cf0559bb9ac5ce7befa44ad858e9a10132", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        .window_wrap {
            padding: 15px;
        }


        .layui-form-label {
            width: 100px;
        }

        .layui-input-block {
            margin-left: 140px;
            padding-right: 15px;
        }

        .line_wrap {
            display: flex;
            flex-direction: row;
            padding-right: 15px;
        }


        .layui-form-item .layui-input-inline {
            width: 50%;
            margin-right: 0;
            margin-left: 10px;
        }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "8a74f0d3df6cc0bb54b47e5e8f8ee261ae1cf0559bb9ac5ce7befa44ad858e9a12519", async() => {
                WriteLiteral(@"
    <div class=""layui-col-md12"">
        <div class=""layui-card"">
            <div class=""layui-card-body layui-form"">
                <div class=""layui-inline"">
                    <div class=""layui-input-inline"" style=""width:200px;"">
                        <input type=""text"" class=""layui-input"" name=""keyWord"" placeholder=""请输入患者Id"" id=""keyWord"" />
                    </div>
                </div>
                <div class=""layui-inline"">
                    <button class=""layui-btn layui-btn-normal fr"" id=""Search"">查询</button>
                    <button class=""layui-btn"" id=""addFile"" data-type=""add"">新增</button>
                </div>
            </div>
            <div class=""layui-card-body"">
                <table class=""layui-table layui-form"" id=""docmentTreeTb""></table>
                <script type=""text/html"" id=""tableBar1"">
                    {{# if(d.DocmentType == ""文件夹""){ }}
                    <a class=""layui-btn  layui-btn-xs"" lay-event=""add""><i class=""layui-icon layui-icon-ad");
                WriteLiteral(@"d-circle""></i>新增</a>
                    {{# } }}
                    {{# if(d.DocmentType == ""文件""){ }}
                    <a class=""layui-btn layui-btn-normal layui-btn-xs"" lay-event=""see""><i class=""layui-icon layui-icon-eye""></i>查看文件</a>
                    {{# } }}
                    <a class=""layui-btn layui-btn-danger layui-btn-xs"" lay-event=""del""><i class=""layui-icon layui-icon-delete""></i>删除</a>
                </script>
            </div>
        </div>

        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "8a74f0d3df6cc0bb54b47e5e8f8ee261ae1cf0559bb9ac5ce7befa44ad858e9a14412", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
        <script>
            layui.config({
                base: '/layuiadmin/layuiextend/'
            }).use(['element', 'layer', 'treeTable', 'upload', 'form'], function () {
                var element = layui.element
                    , layer = layui.layer
                    , treeTable = layui.treeTable //表格
                    , upload = layui.upload
                    , $ = layui.$
                    , form = layui.form;
                ;
                var windowsIndex;
                var selectTableRowData = [];
                var url = '';

                //上传
                var uploadObj = upload.render({
                    elem: '#addFileBtn'
                    , url: '/BasicConfig/MedicalRecordUpload/UploadFile'
                    , multiple: false
                    , accept: 'file' //普通文件
                    , exts: 'pdf'
                    , before: function (obj) {
                        //预读本地文件示例，不支持ie8
                        this.data = {
     ");
                WriteLiteral(@"                       'path': $(""#FileUrl"").val(),
                        };
                        
                        obj.preview(function (index, file, result) {
                            $('#FileListDIV').html('');
                            $(""#fileName"").val(file.name);
                            $('#FileListDIV').append('<li><a style=""text - decoration: underline; color: blue;"" href=""' + result + '"" target=""_blank"">' + file.name + '</a></li>');
                        });
                    }
                    , done: function (res, index, upload) {
                        if (res.status) {

                        }
                        else {
                            layer.alert(res.msg, { icon: 5, title: ""警告"" });
                        }
                    }
                });

                // 渲染表格
                var docmentTreeTb = treeTable.render({
                    elem: '#docmentTreeTb',
                    url: ""/BasicConfig/MedicalRecordUpLo");
                WriteLiteral(@"ad/List"",
                    tree: {
                        idName: 'Id',
                        pidName: 'ParentId',
                        haveChildName: 'haveChildName',
                        iconIndex: 1,    // 折叠图标显示在第几列
                        isPidData: true,  // 是否是pid形式数据
                        arrowType: 'arrow2',
                        getIcon: 'ew-tree-icon-style3'
                    },
                    cols: [[
                        { type: 'numbers', fixed: 'left' },
                        { field: 'DocmentName', title: '名称', fixed: 'left', minWidth: 220 },
                        { field: 'PatientId', title: '患者Id' },
                        { field: 'DocmentType', title: '类型' },
                        { field: 'FileUrl', title: '地址', minWidth: 220 },
                        { fixed: 'right', align: 'center', title: '操作', width: 260, toolbar: '#tableBar1' }
                    ]],
                    done: function () {
                        docmentTreeTb.ex");
                WriteLiteral(@"pand(1);
                    }
                });

                //监听tablelist工具条
                treeTable.on('tool(docmentTreeTb)', function (obj) {
                    var data = obj.data;
                    if (obj.event === 'add') {
                        $(""#Id"").val(""0"");
                        $('#PatientId').val(data.PatientId);
                        $(""#DocmentName"").val("""");
                        $('#ParentId').val(data.Id);
                        $('#PatientId').attr(""readonly"", ""readonly"");
                        $('#DocmentType').removeAttr(""disabled"");
                        $(""#FileUrl"").val(data.FileUrl);
                        windowsIndex = layer.open({
                            type: 1,
                            title: '新增文件(夹)',
                            area: '600px',
                            resize: true,
                            content: $('#form_window')
                        });

                        url = '/BasicConfig/MedicalRec");
                WriteLiteral(@"ordUpload/Create';
                    }
                    else if (obj.event === 'see') {
                        layer.open({
                            type: 2,
                            title: 'PDF预览',
                            shadeClose: true, // 点击遮罩关闭层
                            area: ['80%', '80%'], // 设置弹窗大小
                            content: data.FileUrl
                        });
                    }
                    else if (obj.event === 'del') {
                        layer.confirm('确定要删除名为【' + data.DocmentName + '】的文件(夹)吗？将无法恢复。', {
                            title: '',
                            btn: ['确定', '取消'], //按钮
                            resize: false
                        }, function (index) {
                            $.post('/BasicConfig/MedicalRecordUpload/Del', { id: data.Id }, function (result) {
                                if (result.okMsg) {
                                    debugger;
                                    layer.ms");
                WriteLiteral(@"g(result.okMsg);
                                    if (data.ParentId === '' || data.ParentId == undefined || data.ParentId === 0) {
                                        docmentTreeTb.reload({ url: ""/BasicConfig/MedicalRecordUpLoad/List"", where: { 'pid': 0 } }); //重载表格
                                    }
                                    else {
                                        docmentTreeTb.refresh(data.ParentId); //重载表格
                                    }
                                } else {
                                    layer.msg(result.errorMsg);
                                }
                            }, 'json');
                            layer.close(index);
                        });
                    }
                });

                //监听提交
                form.on('submit(submit)', function (data) {
                    var indexs = layer.load(1);
                    //提交 Ajax 成功后，关闭当前弹层并重载表格
                    if ($(""#DocmentType"").val() == ");
                WriteLiteral(@"""文件"") {
                        data.field.FileUrl = $(""#FileUrl"").val() + '/' + $(""#fileName"").val()
                    }
                    $.ajax({
                        url: url,
                        type: ""post"",
                        data: { 'node': data.field },
                        datatype: 'json',
                        success: function (result) {
                            if (result.okMsg) {
                                debugger;
                                layer.msg(result.okMsg);
                                layer.close(windowsIndex);//关闭弹出层
                                if ($(""#ParentId"").attr(""reload"") == ""1"") {
                                    docmentTreeTb.refresh(parseInt(data.field.ParentId)); //重载表格
                                }
                                else {
                                    if ($(""#ParentId"").attr(""haveChildName"") == ""1"") {
                                        docmentTreeTb.refresh(parseInt(data.field.Pare");
                WriteLiteral(@"ntId)); //重载表格
                                    }
                                    else {
                                        docmentTreeTb.reload({ url: ""/BasicConfig/MedicalRecordUpLoad/List"", where: { 'pid': 0 } }); //重载表格
                                    }
                                }
                            }
                            else {
                                layer.msg(result.errorMsg);
                            }
                            layer.close(indexs);
                            $('#FileListDIV').html('');
                        }, error: function (res) {
                            layer.msg(""加载统计信息错误："" + res.responseText);
                            layer.close(indes);
                        }
                    });
                    return false;
                });

                $(document).ready(function () {
                    $(document).on('click', '#addFile', function () {
                        $(""#PatientId"").v");
                WriteLiteral(@"al("""");
                        $(""#DocmentName"").val("""");
                        $(""#ParentId"").val(""0"");
                        $('#PatientId').removeAttr(""readonly"");
                        $(""#Id"").val(""0"");
                        $('#DocmentType').attr(""disabled"", true);
                        $(""#FileUrl"").val('');
                        windowsIndex = layer.open({
                            type: 1,
                            title: '新增文件(夹)',
                            area: '600px',
                            resize: true,
                            content: $('#form_window')
                        });
                        url = '/BasicConfig/MedicalRecordUpload/Create';
                    });

                });

                form.on('select(DocmentTypeSelect)', function (data) {
                    var value = data.value; // 获得被选中的值
                    if (value == '文件夹') {
                        $(""#uploadDiv"").hide();
                    }
            ");
                WriteLiteral("        else {\r\n                        $(\"#uploadDiv\").show();\r\n                    }\r\n                });\r\n\r\n            });\r\n        </script>\r\n    </div>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n<!--新增/修改文件-->\r\n<div class=\"window_wrap\" id=\"form_window\" style=\"display: none;\">\r\n    <form class=\"layui-form\" lay-filter=\"fm\" id=\"fm\"");
            BeginWriteAttribute("action", " action=\"", 12274, "\"", 12283, 0);
            EndWriteAttribute();
            WriteLiteral(@">
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">患者Id:</label>
            <div class=""layui-input-block"">
                <input type=""text"" name=""Id"" id=""Id"" style=""display:none;"" />
                <input type=""text"" name=""ParentId"" id=""ParentId"" style=""display:none;"" />
                <input type=""text"" name=""PatientSource"" id=""PatientSource"" value=""O"" style=""display:none;"" />
                <input type=""text"" name=""FileUrl"" id=""FileUrl"" style=""display:none;"" />
                <input type=""text"" name=""PatientId"" id=""PatientId"" required lay-verify=""required"" autocomplete=""off"" class=""layui-input"">
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">文件(夹)名称:</label>
            <div class=""layui-input-block"">
                <input type=""text"" name=""DocmentName"" id=""DocmentName"" required lay-verify=""required"" autocomplete=""off"" class=""layui-input"">
            </div>
        </div>
        ");
            WriteLiteral(@"<div class=""layui-form-item"">
            <label class=""layui-form-label"">类型:</label>
            <div class=""layui-input-block"">
                <select id=""DocmentType"" name=""DocmentType"" lay-filter=""DocmentTypeSelect"">
                    <option value=""文件夹"" selected=""selected"">文件夹</option>
                    <option value=""文件"">文件</option>
                </select>
            </div>
        </div>
        <div class=""layui-form-item"" id=""uploadDiv"" style="" padding-top:10px; display:none;"">
            <label class=""layui-form-label"">上传附件</label>
            <div class=""layui-input-block line_wrap"">
                <a id=""addFileBtn"" href=""#"" data-type=""add"" title=""添加参数"" style=""display:inline-block;width:25px;height:25px;padding-top:4px;""><i class=""layui-icon layui-icon-add-circle"" style=""color: #ffa500; font-size: 25px;""></i></a>
                <ol id=""FileListDIV"" class=""file_list"" start=""1"">
                </ol>
                <input id=""fileId"" type=""hidden"" />
                <inpu");
            WriteLiteral(@"t id=""fileName"" type=""hidden"" />
            </div>
        </div>

        <div class=""layui-form-item"">
            <div class=""layui-input-block"">
                <button type=""submit"" class=""layui-btn"" lay-submit lay-filter=""submit"">提交</button>
                <button type=""reset"" class=""layui-btn layui-btn-primary"" id=""btn_reset"">重置</button>
            </div>
        </div>
    </form>
</div>

</html>
");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
