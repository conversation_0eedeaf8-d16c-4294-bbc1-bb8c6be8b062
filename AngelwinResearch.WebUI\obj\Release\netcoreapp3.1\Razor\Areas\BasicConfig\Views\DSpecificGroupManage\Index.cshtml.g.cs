#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\DSpecificGroupManage\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "2392a68efd92d462fbebd5010266f6e5eb57829d80db218a0fd34bcc17323adb"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_BasicConfig_Views_DSpecificGroupManage_Index), @"mvc.1.0.view", @"/Areas/BasicConfig/Views/DSpecificGroupManage/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"2392a68efd92d462fbebd5010266f6e5eb57829d80db218a0fd34bcc17323adb", @"/Areas/BasicConfig/Views/DSpecificGroupManage/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_BasicConfig_Views_DSpecificGroupManage_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\DSpecificGroupManage\Index.cshtml"
  
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2392a68efd92d462fbebd5010266f6e5eb57829d80db218a0fd34bcc17323adb6021", async() => {
                WriteLiteral("\r\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n    <meta charset=\"utf-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>专病管理</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "2392a68efd92d462fbebd5010266f6e5eb57829d80db218a0fd34bcc17323adb6531", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        /*弹窗*/
        .window_wrap {
            padding: 15px;
        }
        .search_wrap{
            background-color:#f0f0f0;
        }
        .layui-tab-brief {
            background-color: #fff;
        }

        .line_wrap {
            padding: 10px 15px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
          
        }

        .form_item {
            padding-top: 10px;
        }

        .btnwrap {
            padding-left: 10px;
        }

        .layui-show {
            display: flex !important;
            align-items: flex-end;
        }

        .form_wrap {
            flex: 1;
        }
        .table_wrap {
            overflow: hidden;
        }

        #detail_window .layui-form-label {
            width: 100px;
        }

        #detail_window .layui-form-val {
            padding: 9px 15px;
        }

        #detail_window .mindow_icon .title_icon {
    ");
                WriteLiteral(@"        display: inline-block;
        }

        #detail_window .mindow_icon {
            text-align: center;
            padding: 20px 0;
        }

        #detail_window .layui-form-item {
            margin-bottom: 0;
        }
        /* 定义表头样式 */
        .layui-table-header .layui-table-cell {
            font-weight: bold; /* 加粗 */
            font-size: 14px; /* 可选：调整字体大小以提升可读性 */
            color: #333; /* 可选：调整字体颜色 */
        }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2392a68efd92d462fbebd5010266f6e5eb57829d80db218a0fd34bcc17323adb9974", async() => {
                WriteLiteral(@"
    <div class=""layui-col-md12"">
        <div class=""layui-card"">
            <div class=""layui-tab-brief"">
                <ul class=""layui-tab-title"">
                    <li class=""layui-this"" data-value=""01"">医学中心</li>
                    <li data-value=""02"">医疗组</li>
                    <li data-value=""03"">科室</li>
                </ul>
            </div>
            <!--搜索区-->
            <div class=""line_wrap search_wrap"">
                <div>
                    <div class=""layui-inline"">
                        <label class=""layui-form-label"">搜索条件</label>
                        <div class=""layui-input-inline"">
                            <div id=""xmDeptsList"" style=""width:300px""></div>
                        </div>
                        <div class=""layui-input-inline"">
                            <input type=""text"" class=""layui-input"" name=""keyWord"" placeholder=""请输入关键字"" id=""keyWord""");
                BeginWriteAttribute("value", " value=\"", 2801, "\"", 2809, 0);
                EndWriteAttribute();
                WriteLiteral(@" />
                        </div>
                        <button id=""Search"" class=""layui-btn layui-btn-primary layui-border-green""><i class=""layui-icon layui-icon-search""></i> </button>
                    </div>
                </div>
                <div>
                    <button type=""button"" id=""addDSP"" data-type=""add"" class=""layui-btn layui-bg-blue""><i class=""layui-icon layui-icon-add-1""></i></button>
                </div>
            </div>
            <!--编辑区-->
            <div class="" edit_area"">
                <div class=""line_wrap layui-card layui-colla-content layui-show"">
                    <div class=""layui-row form_wrap layui-form"">
                        <div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"">
                            <div class=""form_item"">
                                <label class=""layui-form-label"" id=""labelContent"">医学中心</label>
                                <div class=""layui-input-block"">
                                    <div id=""xmDept");
                WriteLiteral(@"sList2""></div>
                                </div>
                            </div>
                        </div>
                        <div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"">
                            <div class=""form_item"">
                                <label class=""layui-form-label"">专病名称</label>
                                <div class=""layui-input-block "">
                                    <input type=""text"" name=""Id"" id=""Id"" style=""display:none;"" />
                                    <input type=""text"" name=""GroupName"" id=""GroupName"" required lay-verify=""required"" placeholder=""请输入专病组名称"" autocomplete=""off"" class=""layui-input"">
                                </div>
                            </div>
                        </div>
                        <div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"">
                            <div class=""form_item"">
                                <label class=""layui-form-label"">疾病分类</label>
                     ");
                WriteLiteral(@"           <div class=""layui-input-block "">
                                    <input type=""text"" name=""ICDCode"" id=""ICDCode"" required lay-verify=""required"" placeholder=""请输入疾病分类"" autocomplete=""off"" class=""layui-input"">
                                </div>
                            </div>
                        </div>

                        <div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"">
                            <div class=""form_item"">
                                <label class=""layui-form-label"">负责人</label>
                                <div class=""layui-input-block "">
                                    <input type=""text"" name=""ChargePerson"" id=""ChargePerson"" placeholder=""请输入负责人"" autocomplete=""off"" class=""layui-input"">
                                </div>
                            </div>
                        </div>
                        <div class=""layui-col-xs12 layui-col-sm12 layui-col-md12"">
                            <div class=""form_item"">
               ");
                WriteLiteral(@"                 <label class=""layui-form-label"" id=""labelContent"" style=""width:90px;"">所属参与机构</label>
                                <div class=""layui-input-block"" id=""xmCentersList""></div>
                            </div>
                        </div>
                            <div class=""layui-col-xs6 layui-col-sm6 layui-col-md6"">
                                <div class=""form_item"">
                                    <label class=""layui-form-label"">研究方向</label>
                                    <div class=""layui-input-block"">
                                        <textarea name=""ResearchDirection"" id=""ResearchDirection"" class=""layui-textarea"" style=""resize: none""></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class=""layui-col-xs6 layui-col-sm6 layui-col-md6"">
                                <div class=""form_item"">
                                    <label class=""lay");
                WriteLiteral(@"ui-form-label"">描述</label>
                                    <div class=""layui-input-block "">
                                        <textarea name=""GroupDesc"" id=""GroupDesc"" class=""layui-textarea"" style=""resize: none""></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class=""btnwrap"">
                            <button class=""layui-btn"" lay-submit lay-filter=""submit"">保存</button>
                        </div>
                    </div>
            </div>
            
            <div class=""layui-card-body table_wrap"">
                <table id=""tablelist"" lay-filter=""tablelist""></table>
                <script type=""text/html"" id=""tableBar"">
                    <a class=""layui-btn layui-btn-danger layui-btn-xs"" lay-event=""del"" style=""text-decoration:none""><i class=""layui-icon layui-icon-delete""></i>删除</a>
                </script>
            </div>
");
                WriteLiteral("        </div>\r\n    </div>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2392a68efd92d462fbebd5010266f6e5eb57829d80db218a0fd34bcc17323adb16885", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2392a68efd92d462fbebd5010266f6e5eb57829d80db218a0fd34bcc17323adb18009", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2392a68efd92d462fbebd5010266f6e5eb57829d80db218a0fd34bcc17323adb19133", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2392a68efd92d462fbebd5010266f6e5eb57829d80db218a0fd34bcc17323adb20257", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"

    <script>
        layui.use(['element', 'layer', 'table', 'laydate', 'form'], function () {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , laydate = layui.laydate
                , $ = layui.$
                , form = layui.form;
            ;
            var url = ''
            var value = '01';

            table.render({
                elem: '#tablelist'
                , id: 'tablelist'
                , page: true

                , limit: 10
                , height: 'full-325'
                , cols: [[
                    { field: 'No', title: '序号', type: 'numbers', fixed: 'left' }
                    , { field: 'GroupName', title: '专病名称', width: 250 }
                    , { field: 'ICDCode', title: '疾病分类' }
                    , { field: 'DeptName', title: '医学中心' }
                    , { field: 'CenterName', title: '所属中心' }
                    , { field: 'ChargePerson', title");
                WriteLiteral(@": '负责人' }
                    , { field: 'ResearchDirection', title: '研究方向' }
                    , { field: 'GroupDesc', title: '专病组描述' }
                    , { field: 'CreatedTime', title: '创建时间' }
                    , { title: '操作', toolbar: '#tableBar', width: 160, minWidth: 160, fixed: 'right' }
                ]]
                , done: function (res, curr, count) {
                }
            });

            //监听tablelist工具条
            table.on('tool(tablelist)', function (obj) {
                var data = obj.data;
                if (obj.event === 'del') {
                    layer.confirm('确定要删除名为【' + data.GroupName + '】的专病组吗？将无法恢复。', {
                        title: '',
                        btn: ['确定', '取消'], //按钮
                        resize: false
                    }, function (index) {
                        $.post('/BasicConfig/DSpecificGroupManage/Del', { id: data.Id }, function (result) {
                            if (result.okMsg) {
                      ");
                WriteLiteral(@"          layer.msg(result.okMsg);
                                currentIndex = -1;
                                table.reload('tablelist'); //重载表格
                                EmptyData();
                            } else {
                                layer.msg(result.errorMsg);
                            }
                        }, 'json');
                        layer.close(index);
                    });
                }
            });

            //触发行单击事件
            table.on('row(tablelist)', function (obj) {
                var data = obj.data;
                xmDeptsList2.update({
                    disabled: true
                });
                if (data.HospitalDeptId) {
                    var arr = new Array();
                    arr.push(data.HospitalDeptId);
                    xmDeptsList2.setValue(arr);
                }
                //xmCentersList.update({
                //    disabled: true
                //});
                if (dat");
                WriteLiteral(@"a.MultiCenterId) {
                    var arr = data.MultiCenterId.split(',');
                    xmCentersList.setValue(arr);
                }
                else {
                    xmCentersList.setValue([]);
                }
                $(""#GroupName"").val(data.GroupName);
                $(""#ICDCode"").val(data.ICDCode);
                $(""#ChargePerson"").val(data.ChargePerson);
                $(""#ResearchDirection"").val(data.ResearchDirection);
                $(""#GroupDesc"").val(data.GroupDesc);
                $(""#Id"").val(data.Id);
            });

            var xmDeptsList = xmSelect.render({
                el: '#xmDeptsList',
                model: { label: { type: 'text' } },
                prop: {
                    name: 'title',
                    value: 'id',
                },
                minWidth: 200,
                radio: true,
                filterable: true,
                clickClose: true,
                //树
                tree: {
  ");
                WriteLiteral(@"                  show: true,
                    //非严格模式
                    strict: false,
                    //默认展开节点
                    expandedKeys: [-1],
                },
                data: []
            });

            var xmDeptsList2 = xmSelect.render({
                el: '#xmDeptsList2',
                model: { label: { type: 'text' } },
                prop: {
                    name: 'title',
                    value: 'id',
                },
                minWidth: 200,
                radio: true,
                filterable: true,
                clickClose: true,
                //树
                tree: {
                    show: true,
                    //非严格模式
                    strict: false,
                    //默认展开节点
                    expandedKeys: [-1],
                },
                data: []
            });

            var xmCentersList = xmSelect.render({
                el: '#xmCentersList',
                model: { label: { ");
                WriteLiteral(@"type: 'text' } },
                prop: {
                    name: 'title',
                    value: 'id',
                },
                minWidth: 200,
                radio: false,
                filterable: true,
                clickClose: false,
                //树
                tree: {
                    show: true,
                    //非严格模式
                    strict: false,
                    //默认展开节点
                    expandedKeys: [-1],
                },
                data: []
            });

            function GetDeptsTree(value) {
                $.ajax({
                    url: '/CommAPI/GetOrgsTreeList',
                    type: ""post"",
                    datatype: 'json',
                    data: { 'Type': value },
                    success: function (result) {
                        xmDeptsList.update({
                            data: result
                        });
                        xmDeptsList2.update({
                  ");
                WriteLiteral(@"          data: result
                        });
                        if (result[0].id) {
                            var arr = new Array();
                            arr.push(result[0].id);
                            //xmDeptsList.setValue(arr);
                            setTimeout(function () {
                                SearchData();
                            }, 1000);

                        }

                    }, error: function () {
                        layer.msg(""获取失败！"");
                    }
                })
            };

            function GetCentersTree(value) {
                $.ajax({
                    url: '/CommAPI/GetCentersList',
                    type: ""post"",
                    datatype: 'json',
                    data: { 'Type': value },
                    success: function (result) {
                        xmCentersList.update({
                            data: result
                        });
                        if ");
                WriteLiteral(@"(result[0].id) {
                            var arr = new Array();
                            arr.push(result[0].id);
                            setTimeout(function () {
                            }, 1000);

                        }

                    }, error: function () {
                        layer.msg(""获取失败！"");
                    }
                })
            };

            function SearchData() {

                table.reload('tablelist', {
                    page: {
                        curr: 1
                    },
                    url: '/BasicConfig/DSpecificGroupManage/List'
                    , where: {
                        'deptId': xmDeptsList.getValue('valueStr'),
                        'keyWord': $.trim($(""#keyWord"").val()),
                        'deptType': value
                    }
                });
            };

            $(document).ready(function () {
                $('.layui-tab-title li').on('click', function () {
     ");
                WriteLiteral(@"               // 获取被点击列表项的 value
                    value = $(this).data('value');

                    var labelText = $(this).text();
                    $('#labelContent').text(labelText);
                    GetDeptsTree(value);
                    EmptyData();
                    SearchData();
                })
                GetDeptsTree(value);
                GetCentersTree(value);
                $(document).on('click', '#Search', function () {
                    EmptyData();
                    SearchData();
                })

                $(document).on('click', '#addDSP', function () {
                    EmptyData();
                });

            });
            function EmptyData() {
                $('#Id').val('');
                $(""#GroupName"").val('');
                $(""#ICDCode"").val('');
                $(""#ChargePerson"").val('');
                $(""#ResearchDirection"").val('');
                $(""#GroupDesc"").val('');
                xmDeptsList2.");
                WriteLiteral(@"update({
                    disabled: false
                });
                xmCentersList.setValue([]);
            }
            SearchData();

            //监听提交
            form.on('submit(submit)', function (data) {
                var indes = layer.load(1);
                var hospitalDeptId = xmDeptsList2.getValue('valueStr');
                var centerIds = xmCentersList.getValue('valueStr');
                data.field.hospitalDeptId = hospitalDeptId;
                data.field.multiCenterIds = centerIds;
                data.field.groupName = $(""#GroupName"").val();
                data.field.iCDCode = $(""#ICDCode"").val();
                data.field.chargePerson = $(""#ChargePerson"").val();
                data.field.researchDirection = $(""#ResearchDirection"").val();
                data.field.groupDesc = $(""#GroupDesc"").val();
                data.field.id = $(""#Id"").val();
                //提交 Ajax 成功后，关闭当前弹层并重载表格
                $.ajax({
                    url: '/BasicConf");
                WriteLiteral(@"ig/DSpecificGroupManage/Save',
                    type: ""post"",
                    data: { 'node': data.field },
                    datatype: 'json',
                    success: function (data) {
                        if (data.okMsg) {
                            layer.msg(data.okMsg);
                            table.reload('tablelist'); //重载表格
                        }
                        else {
                            layer.msg(data.errorMsg);
                        }
                        layer.close(indes);
                    }, error: function (res) {
                        layer.msg(""加载统计信息错误："" + res.responseText);
                        layer.close(indes);
                    }
                });
                return false;
            });

            function setTableH() {
                var winH = $(window).height();
                var navH = $("".layui-tab-brief"").height();
                var searchH = $("".search_wrap"").height();
                v");
                WriteLiteral(@"ar editAreaH = $("".edit_area"").height();
                var tableH = winH - (navH + searchH + editAreaH) - 55 + ""px"";
                console.log(tableH)
                $("".table_wrap"").css(""height"", tableH);

            };
            setTableH();
            $(window).resize(function () {
                setTableH()
            });
        });
    </script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
