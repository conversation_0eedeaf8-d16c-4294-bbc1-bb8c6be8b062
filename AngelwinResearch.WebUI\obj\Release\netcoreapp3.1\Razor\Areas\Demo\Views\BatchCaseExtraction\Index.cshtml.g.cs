#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\BatchCaseExtraction\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "67006f2e42348b0c353cd38cd9b6a7061dbb0690754f7bbc5d53353766e2a9d9"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_Demo_Views_BatchCaseExtraction_Index), @"mvc.1.0.view", @"/Areas/Demo/Views/BatchCaseExtraction/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"67006f2e42348b0c353cd38cd9b6a7061dbb0690754f7bbc5d53353766e2a9d9", @"/Areas/Demo/Views/BatchCaseExtraction/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_Demo_Views_BatchCaseExtraction_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\BatchCaseExtraction\Index.cshtml"
  
    ViewBag.Title = "BatchCaseExtraction";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"zh\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "67006f2e42348b0c353cd38cd9b6a7061dbb0690754f7bbc5d53353766e2a9d94588", async() => {
                WriteLiteral("\r\n    <meta charset=\"UTF-8\">\r\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>批量病理提取</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "67006f2e42348b0c353cd38cd9b6a7061dbb0690754f7bbc5d53353766e2a9d95082", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        .flex_row {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .select_wrap {
            margin: 10px 5px;
            background-color: #fff;
            border: 1px solid #eee;
        }

        .content_wrap {
            margin: 0 5px;
        }

        .pathology_list_wrap {
            user-select: none; /* 标准语法 */
            -webkit-user-select: none; /* Safari 和 Chrome */
            -moz-user-select: none; /* Firefox */
            -ms-user-select: none; /* Internet Explorer 和 Edge */
        }

            .pathology_list_wrap .layui-colla-title {
                display: flex;
                flex-direction: row;
                padding: 0 15px 0 15px;
            }

            .pathology_list_wrap .layui-colla-icon {
                left: 96% !important;
            }

        .pathology_title {
            padding: 0 15px;
            font-size: 16px;
           ");
                WriteLiteral(@" font-weight: blod;
        }

        .pathology_list_wrap, .pathology_result_wrap {
            overflow-y: auto;
        }

        .item_title {
            font-size: 16px;
            padding-right: 10px;
            color: #1E9FFF;
        }

        .pathology_inner_item {
            /* padding: 5px 0;*/
            /* border-bottom: 1px solid #f6f6f6;*/
        }

        .pathology-container {
            margin-bottom: 20px;
        }

        .report_bg {
            /*background-color: #f3f2ff;*/
            /*    padding: 10px;*/
            border-radius: 6px;
            /*    border: 1px dashed #ccc;*/
        }

        /*数据表格内容自动显示*/
        .layui-table, .layui-table-view {
            margin: 0;
        }

            .layui-table td, .layui-table th {
                white-space: normal !important; /* 允许内容换行 */
                word-break: break-all !important; /* 长单词或 URL 地址换行 */
                height: auto !important; /* 自动调整高度 */
            }

");
                WriteLiteral(@"
        .layui-table-cell {
            height: auto !important; /* 自动调整高度 */
        }

        .layui-table-cell, .layui-table-tool-panel li {
            white-space: normal !important; /* 允许内容换行 */
        }

        .sex {
            font-size: 14px;
            font-weight: normal;
        }

        .sex0 {
            color: #FF5722;
        }

        .sex1 {
            color: #1E9FFF;
        }

        .btn_wrap{
            text-align:center;
            margin-top:20px;
        }
        .currentMouse {
            color:red;
            font-weight:bold;
        }


        #addRow {
            margin-top: 15px;
        }
        #addRow .layui-form-item{
            margin-bottom:5px;
        }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "67006f2e42348b0c353cd38cd9b6a7061dbb0690754f7bbc5d53353766e2a9d99879", async() => {
                WriteLiteral(@"
    <div class=""wrap"" id=""content"">
        <div class=""select_wrap layui-form flex_row"">
            <div>
                <div class=""layui-inline"">
                    <label class=""layui-form-label"">类型</label>
                    <div class=""layui-input-inline"" style=""width:300px;"">
                        <select id=""selectForm"" lay-filter=""selectForm"">
                            <option value=""甲状腺"">甲状腺</option>
                            <option value=""胆囊"">胆囊</option>
                            <option value=""子宫"">子宫</option>
                        </select>
                    </div>
                </div>
                <div class=""layui-inline"">
                    <div class=""layui-input-inline"">
                        <input class=""layui-input"" type=""text"" name=""SFZH"" id=""SFZH"" lay-filter=""SFZH"" title=""身份证"" placeholder=""请输入身份证号"">
                    </div>
                </div>
                <div class=""layui-inline"">
                    <button class=""layui-btn layui-b");
                WriteLiteral(@"tn-normal"" id=""btnSearch"">查询</button>
                    <button class=""layui-btn "" id=""btnAnalysis"">批量提取</button>
                </div>
            </div>
            <div>
                <div class=""layui-inline"">
                    <button id=""popbtn"" type=""button"" class=""layui-btn layui-bg-blue"">
                        <i class=""layui-icon layui-icon-survey""></i>
                    </button>
                </div>
            </div>
        </div>
        <div class=""layui-row"">
            <div class=""layui-col-xs4 layui-col-md4"">
                <div class=""layui-card content_wrap"">
                    <div class=""layui-card-header"">病理报告</div>
                    <div class=""layui-card-body pathology_list_wrap"">
                        <div class=""layui-collapse"" id=""pathologyReports"">
                        </div>
                    </div>
                </div>
            </div>
            <div class=""layui-col-xs8 layui-col-md8"">
                <div class=""layui-card");
                WriteLiteral(@" content_wrap"">
                    <div class=""layui-card-header"">提取结果</div>
                    <div class=""layui-card-body pathology_result_wrap"">
                        <table id=""pathologyResult"" lay-filter=""pathologyResult""></table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 弹窗内容 -->
        <div id=""popwrap"" style=""display:none"">
            <table id=""dataTable"" lay-filter=""dataTable""></table>
            <!-- 新增数据的输入行 -->
            <div class=""layui-form layui-form-pane"" id=""addRow"">
                <div class=""layui-form-item"" pane style=""display:none"">
                    <label class=""layui-form-label"">序号</label>
                    <div class=""layui-input-block"">
                        <input type=""text"" id=""newId"" class=""layui-input"" placeholder=""请输入序号"">
                    </div>
                </div>
                <div class=""layui-form-item"" pane>
                    <label class=""layui-form-label"">变量</label>
");
                WriteLiteral(@"                    <div class=""layui-input-block"">
                        <input type=""text"" id=""newVariable"" class=""layui-input"" placeholder=""请输入变量"">
                    </div>
                </div>
                <div class=""layui-form-item"" pane>
                    <label class=""layui-form-label"">提取要求</label>
                    <div class=""layui-input-block"">
                        <input type=""text"" id=""newRequirement"" class=""layui-input"" placeholder=""请输入提取要求"">
                    </div>
                </div>
");
                WriteLiteral("                <div class=\"btn_wrap\"><button class=\"layui-btn layui-btn-normal\" id=\"addButton\">添加</button></div>\r\n            </div>\r\n        </div>\r\n\r\n    </div>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "67006f2e42348b0c353cd38cd9b6a7061dbb0690754f7bbc5d53353766e2a9d914239", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
        layui.use(['element', 'layer', 'table', 'laydate', 'form', 'element', 'laytpl'], function () {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , laydate = layui.laydate
                , $ = layui.$
                , form = layui.form
                , element = layui.element
                , laytpl = layui.laytpl;

            var isFirstLoad = true;

            // 表格渲染
            function reloadData() {
                table.render({
                    elem: '#dataTable',
                    url: '/Demo/BatchCaseExtraction/GetData', // 后端接口地址
                    height: 470,
                    cols: [[
                        { field: 'id', title: '序号', width: 80, sort: true }
                        , { field: 'variable', title: '变量', minWidth: 250 }
                        , { field: 'requirement', title: '提取要求', width: 200 }
                        , { field: 'traceab");
                WriteLiteral(@"ility', title: '是否溯源', width: 200 }
                        , {
                            field: 'operation', title: '操作', width: 100, fixed: 'right', templet: function (d) {
                                return '<button class=""layui-btn layui-btn-xs edit-btn"" data-id=""' + d.id + '""><i class=""layui-icon layui-icon-edit""></i></button> ' +
                                    '<button class=""layui-btn layui-btn-danger layui-btn-xs delete-btn"" data-id=""' + d.id + '""><i class=""layui-icon layui-icon-delete""></i></button>';
                            }
                        }
                    ]]
                    , done: function (res, curr, count) {
                        // 绑定编辑按钮点击事件
                        $('.edit-btn').off('click').on('click', function () {
                            var id = $(this).data('id');
                            console.log('编辑 ID: ' + id);
                            var tr = table.cache['dataTable'].find(function (item) { return item.id === id; });

  ");
                WriteLiteral(@"                          if (tr) {
                                // 将数据填充到表单中
                                document.getElementById('newId').value = tr.id;
                                document.getElementById('newVariable').value = tr.variable;
                                document.getElementById('newRequirement').value = tr.requirement;
                                // document.getElementById('newTraceability').checked = tr.traceability === 'true';
                            }
                        });

                        // 绑定删除按钮点击事件
                        $('.delete-btn').off('click').on('click', function () {
                            var id = $(this).data('id');
                            console.log('删除 ID: ' + id);
                            $.ajax({
                                url: '/Demo/BatchCaseExtraction/DeleteData?id=' + id, // 替换为实际的控制器和方法路径
                                type: 'POST',
                                success: function (response) {
");
                WriteLiteral(@"
                                    if (response.okMsg) {
                                        layer.msg(response.okMsg, { icon: 1 });
                                        // 重新渲染表格
                                        table.reload('dataTable', {
                                            url: '/Demo/BatchCaseExtraction/GetData'
                                        });
                                    } else if (response.errorMsg) {
                                        layer.msg(response.errorMsg, { icon: 2 });
                                    }
                                },
                                error: function (jqXHR, textStatus, errorThrown) {
                                    layer.msg('请求失败: ' + textStatus, { icon: 2 });
                                }
                            });
                        });
                    }
                });
            }
            //新增患者弹窗
            $(""#addButton"").on(""click"", function () {
   ");
                WriteLiteral(@"             var newId = $(""#newId"").val();
                var newVariable = $(""#newVariable"").val();
                var newRequirement = $(""#newRequirement"").val();
                // var newTraceability = $(""#newTraceability"").val();
                if (newId == """") {
                    newId = 0;
                }
                var data = {
                    Id: newId,
                    Variable: newVariable,
                    Requirement: newRequirement,
                    Traceability: ""true""
                };

                $.ajax({
                    url: '/Demo/BatchCaseExtraction/AddData', // 替换为实际的控制器和方法路径
                    type: 'POST',
                    data: data,
                    success: function (response) {
                        if (response.okMsg) {
                            layer.msg(response.okMsg, { icon: 1 });
                            // 重新渲染表格
                            table.reload('dataTable', {
                                url:");
                WriteLiteral(@" '/Demo/BatchCaseExtraction/GetData'
                            });
                            scrollToBottom();
                        } else if (response.errorMsg) {
                            layer.msg(response.errorMsg, { icon: 2 });
                        }
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        layer.msg('请求失败: ' + textStatus, { icon: 2 });
                    }
                });


            });

            $(window).resize(function () {
                setContentH();
            });
            $(document).ready(function () {
                setContentH();
                getPathologyReport();
            });
            // 绑定点击事件
            $(document).on('click', '#btnSearch', function () {
                getPathologyReport();
            });

            $(document).on('click', '#btnAnalysis', function () {
                var checkboxes = $('input[name=pathologyTitle]:checked');
 ");
                WriteLiteral(@"               var selectedData = [];
                // 使用 .each() 方法遍历这些复选框
                checkboxes.each(function () {
                    var data = JSON.parse($(this).attr('bldata')); // 获取当前复选框的 data 属性值
                    selectedData.push(data);
                });
                $(""#pathologyResult"").html("""");
                var indexs = layer.load(1);
                isFirstLoad = true;
                for (var i = 0; i < selectedData.length; i++) {
                    var json = JSON.stringify(selectedData[i]);
                    $.post('/Demo/BatchCaseExtraction/AnalysePdf', { ""json"": json }, function (res) {
                        if (res.code == 0) {
                            loadTable(res.data);
                        }
                        else
                            layer.msg(res.msg);
                        layer.close(indexs);
                    })
                }
            });

            $(""#popbtn"").on(""click"", function () {
               ");
                WriteLiteral(@" layer.open({
                    type: 1,
                    area: ['60%', '80%'],
                    resize: false,
                    shadeClose: true,
                    title: '帮助',
                    content: $(""#popwrap""),
                    success: function () {
                        reloadData();
                    }
                })
            });
            function getPathologyReport() {
                var selectedValue = $('#selectForm').val();
                var SFZH = $(""#SFZH"").val();
                $.post(""/Demo/BatchCaseExtraction/getPathologyReport"", { ""ID_NO"": SFZH, ""CLASS_NAME"": ""病理"", ""SAMPLE_NAME"": selectedValue }, function (res) {
                    if (res.code == 0) {
                        // 清空现有内容
                        $('#pathologyReports').empty();
                        var data = res.data;
                        var getTpl2 = NoStructurModel.innerHTML;
                        laytpl(getTpl2).render(data, function (html) {
           ");
                WriteLiteral(@"                 $('#pathologyReports').append(html);
                        });
                        // 遍历所有复选框，为每个复选框添加点击事件处理器
                        document.querySelectorAll('.layui-colla-title input[type=""checkbox""]').forEach(function (checkbox) {
                            checkbox.addEventListener('click', function (event) {
                                event.stopPropagation(); // 阻止事件冒泡
                                // 获取当前复选框的状态
                                const isChecked = this.checked;

                                // 获取当前复选框所在的 .layui-colla-item 元素
                                const collaItem = this.closest('.layui-colla-item');

                                // 获取当前复选框的 data 属性
                                const checkbox = $(this);
                                const data = checkbox.data();

                                // 获取当前 .layui-colla-item 下的所有报告内容
                                const reportContents = collaItem.querySelectorAll('.pathology_in");
                WriteLiteral(@"ner_item .report_bg');
                                // 根据复选框的状态决定是否显示报告内容
                                if (isChecked) {
                                    reportContents.forEach(reportContent => {
                                        //console.log(reportContent.innerText); // 输出报告内容
                                    });
                                } else {
                                    console.log('复选框未选中'); // 复选框未选中时的处理
                                }
                            });
                        });

                        // 监听复选框变化
                        form.on('checkbox(pathologyTitle)', function (data) {
                            var choice = data.elem.checked;
                            //console.log(data.elem.checked); // 输出复选框的状态
                        });
                        // 重新初始化 Layui 的折叠面板组件
                        element.init();

                    } else {
                        layer.msg(res.msg);
                    }
");
                WriteLiteral(@"                })
            };
            function setContentH() {
                var winH = $(window).height();
                var card_title_H = $("".layui-card-header"").outerHeight(true);
                var select_H = $("".select_wrap"").outerHeight(true);
                var contentH = winH - select_H - card_title_H - 30;
                $("".pathology_list_wrap"").css(""height"", contentH + ""px"");
                $("".pathology_result_wrap"").css(""height"", contentH + ""px"");
            };
            var data1 = [];
            var columns = [];
            function loadTable(obj) {
                try {
                    $(""#pathologyResult"").html("""");
                    var data = JSON.parse(obj);
                    //console.log(data);

                    if (Array.isArray(data)) {
                        var rowWidth = """";
                        if (isFirstLoad) {
                            data1 = [];
                            columns = [{ type: 'numbers', title: '序号', w");
                WriteLiteral(@"idth: '5%' }];

                            // 创建一个临时对象来存储所有键值对，包括 '原文依据'
                            const tempData = {};

                            // 遍历数据以生成列定义和临时数据对象
                            $.each(data, function (index, item) {
                                if (item[""项目""] !== ""原文依据"") {
                                    var rowHead = {};
                                    rowHead.field = item[""项目""];
                                    rowHead.title = item[""项目""];
                                    rowHead.width = ""120"";
                                    columns.push(rowHead);
                                }
                            });

                            // 创建一个对象来存储项目值和原文依据的映射
                            const projectToOriginalBasis = {};

                            // 遍历数据以填充项目值和原文依据
                            $.each(data, function (index, item) {
                                if (item[""项目""] !== ""原文依据"") {
                                    tempData[");
                WriteLiteral(@"item[""项目""]] = item[""项目值""];
                                    projectToOriginalBasis[item[""项目值""]] = item[""原文依据""];
                                }
                            });

                            // 将项目值和原文依据的映射添加到 tempData 中
                            tempData.projectToOriginalBasis = projectToOriginalBasis;

                            // 将临时数据对象推入 data1 数组
                            data1.push(tempData);
                            isFirstLoad = false;
                        } else {
                            // 创建一个临时对象来存储所有键值对，包括 '原文依据'
                            const tempData = {};

                            // 创建一个对象来存储项目值和原文依据的映射
                            const projectToOriginalBasis = {};

                            // 遍历数据以填充项目值和原文依据
                            $.each(data, function (index, item) {
                                if (item[""项目""] !== ""原文依据"") {
                                    tempData[item[""项目""]] = item[""项目值""];
                       ");
                WriteLiteral(@"             projectToOriginalBasis[item[""项目值""]] = item[""原文依据""];
                                }
                            });

                            // 将项目值和原文依据的映射添加到 tempData 中
                            tempData.projectToOriginalBasis = projectToOriginalBasis;

                            // 将临时数据对象推入 data1 数组
                            data1.push(tempData);
                        }
                        var pathologyResult = table.render({
                            elem: '#pathologyResult',
                            height: ""full-160"",
                            page: true,
                            cols: [columns],
                            data: data1,
                            done: function (res, curr, count) {
                                // 监听单元格点击事件
                                table.on('row(pathologyResult)', function (obj) {
                                    console.log('obj:', obj);
                                    console.log('obj.config");
                WriteLiteral(@":', obj.config);

                                    var tr = obj.tr; // 当前行的 DOM 元素
                                    var data = obj.data; // 当前行的数据
                                    var td = $(event.target).closest('td'); // 获取当前单元格的 DOM 元素
                                    var Cellvalue = td.text(); // 获取当前单元格的值

                                    // 显示选中的单元格数据
                                    console.log(data);
                                    var targetTitle = data.病理号;
                                    var targetTypeName = data.报告分类名称;
                                    var value = data.projectToOriginalBasis[Cellvalue];//获取提示词值
                                    console.log('值: ' + value);

                                    var sourceList = $("".layui-colla-content"");
                                    sourceList.each(function (index, element) {
                                        var $element = $(element);
                                        // 正确地移除 curren");
                WriteLiteral(@"tMouse 类
                                        $element.find(""span"").removeClass(""currentMouse"");
                                        $element.removeClass(""layui-show"");
                                       
                                        if ($element.attr(""title"") == targetTitle && $element.attr(""typeName"") == targetTypeName) {
                                            $element.addClass(""layui-show"");
                                            var textContent = $(this).html();
                                            if (textContent.includes(value)) {
                                                
                                                textContent = textContent.replace(value, '<span class=""currentMouse"">' + value + '</span>');

                                            } else {
                                                value = value.replace('：', ':</strong>').replace(/""/g, '');
                                                value = '<strong class=""item_");
                WriteLiteral(@"title"">' + value;
                                                if (textContent.includes(value)) {
                                                    textContent = textContent.replace(value, '<span class=""currentMouse"">' + value + '</span>');
                                                }
                                            }
                                        }
                                        $(this).html(textContent);
                                    });
                                    // addbyzolf 自动滚动到定位位置 2024-10-30
                                    var targetElement = document.querySelector('.currentMouse');
                                    // 滚动到该元素的位置
                                    if (targetElement) {
                                        targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                    }
                                });
                            }
                        });
");
                WriteLiteral(@"                    }
                } catch (error) {
                    console.error('JSON字符串格式错误:', error.message);
                    alert('JSON字符串格式错误:' + error.message);
                }
            }
            function scrollToBottom() {
                var div = $('.layui-table-view');
                div.scrollTop(div[0].scrollHeight - div.innerHeight());
            }

        });

    </script>

");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"

<script id=""NoStructurModel"" type=""text/html"">
    {{#  for (var i=0;i<d.length;i++) {  }}
    <div class=""layui-colla-item"">
        <div class=""layui-colla-title"">
            <input type=""checkbox"" name=""pathologyTitle"" bldata='{{JSON.stringify(d[i])}}' title=""{{ d[i][""病理申请ID""]}}"" lay-skin=""primary"" />

            <p><span class=""pathology_title"">{{ d[i][""患者姓名""]}}</span> <span class=""sex {{d[i][""");
            BeginWriteAttribute("患者性别\"]", "患者性别\"] =", 25762, "", 25770, 0);
            EndWriteAttribute();
            WriteLiteral(@"= '男' ? 'sex1' : 'sex0'}}""> {{ d[i][""患者性别""]}} </span>(<span class=""pathology_time"">{{ d[i][""报告日期""]}}</span>)</p>
        </div>
        <div class=""layui-colla-content"" title=""{{ d[i][""病理申请ID""]}}"" typeName=""{{ d[i][""报告分类名称""]}}"">
            <div class=""pathology_inner_list"">
                <div class=""pathology_inner_item"">
                    {{# for (let key in d[i]) {   }}
                    <p class=""report_bg""><strong class=""item_title"">{{key}}:</strong>{{d[i][key]}}</p>
                    {{# } }}
                </div>
            </div>

        </div>
    </div>
    {{# } }}
</script>

</html>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
