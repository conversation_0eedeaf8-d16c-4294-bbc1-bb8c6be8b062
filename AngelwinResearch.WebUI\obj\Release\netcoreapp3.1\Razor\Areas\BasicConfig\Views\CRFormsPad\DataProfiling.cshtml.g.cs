#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\CRFormsPad\DataProfiling.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "fa5c6988e829976600900cf9bddb95d8518ba9ee9b6e2a842d1fe7a2eca9911e"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_BasicConfig_Views_CRFormsPad_DataProfiling), @"mvc.1.0.view", @"/Areas/BasicConfig/Views/CRFormsPad/DataProfiling.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"fa5c6988e829976600900cf9bddb95d8518ba9ee9b6e2a842d1fe7a2eca9911e", @"/Areas/BasicConfig/Views/CRFormsPad/DataProfiling.cshtml")]
    #nullable restore
    internal sealed class Areas_BasicConfig_Views_CRFormsPad_DataProfiling : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin\\layui\\font\\web_font\\iconfont.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/paddemo_style.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/jquery-steps/css/ystep.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/limarquee/css/limarquee.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("type", new global::Microsoft.AspNetCore.Html.HtmlString("text/javascript"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/jquery-steps/js/setstep.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/limarquee/js/jquery.limarquee.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\CRFormsPad\DataProfiling.cshtml"
  
	Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "fa5c6988e829976600900cf9bddb95d8518ba9ee9b6e2a842d1fe7a2eca9911e8246", async() => {
                WriteLiteral("\r\n\t<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n\t<meta charset=\"utf-8\" />\r\n\t<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n\t<title>医学中心管理</title>\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "fa5c6988e829976600900cf9bddb95d8518ba9ee9b6e2a842d1fe7a2eca9911e8748", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "fa5c6988e829976600900cf9bddb95d8518ba9ee9b6e2a842d1fe7a2eca9911e9948", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "fa5c6988e829976600900cf9bddb95d8518ba9ee9b6e2a842d1fe7a2eca9911e11148", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "fa5c6988e829976600900cf9bddb95d8518ba9ee9b6e2a842d1fe7a2eca9911e12351", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "fa5c6988e829976600900cf9bddb95d8518ba9ee9b6e2a842d1fe7a2eca9911e13554", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
	<style>
		.layui-form-select .layui-input {
			background-color: transparent;
			border: none;
			color: #fff;
			padding-left: 25px;
		}

		.layui-form-select .layui-edge {
			border-top-color: #fff;
		}

		/*进度按钮*/
		.step-button{
			display:none;
		}

		/*今日事件*/
		.newswrap{
		overflow:hidden;
		position:relative;
		}
		.jq22 {
			height: 32vh;
		}

		.jq22 ul {
			margin: 0;
			line-height: 30px;
		}

		.jq22 a {
		
			text-decoration: none;
		}

		.jq22 a:hover {
			text-decoration: underline;
		}

		.str_wrap{
			background-color:#fff;
		}

	</style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "fa5c6988e829976600900cf9bddb95d8518ba9ee9b6e2a842d1fe7a2eca9911e16073", async() => {
                WriteLiteral("\r\n\t<div class=\"wrap\">\r\n\t\t<header class=\"aui-navBar aui-navBar-fixed\">\r\n\t\t\t<div class=\"toptools layui-form\">\r\n\t\t\t\t<div class=\"search_wrap\">\r\n\t\t\t\t\t<div class=\"input_group\">\r\n\t\t\t\t\t\t<div class=\"layui-inline\">\r\n\t\t\t\t\t\t\t<div");
                BeginWriteAttribute("class", " class=\"", 1468, "\"", 1476, 0);
                EndWriteAttribute();
                WriteLiteral(" style=\"width:68vw;\">\r\n");
                WriteLiteral("\t\t\t\t\t\t\t\t<select name=\"interest\" lay-filter=\"aihao\" style=\"background-color:transparent;\">\r\n\t\t\t\t\t\t\t\t\t<option value=\"0\"");
                BeginWriteAttribute("selected", " selected=\"", 1661, "\"", 1672, 0);
                EndWriteAttribute();
                WriteLiteral(@">妇产儿保医学中心</option>
									<option value=""2"">麻醉医学中心</option>
									<option value=""3"">急诊医学中心</option>
									<option value=""4"">老年医学中心</option>
									<option value=""5"">重症医学</option>
									<option value=""6"">血液科</option>
									<option value=""7"">呼吸内科</option>
									<option value=""8"">肠胃疾病医学中心</option>
									<option value=""1"">放射科</option>
								</select>
							</div>
						</div>
						<div class=""layui-input-inline"">
							<button class=""layui-btn layui-btn-primary"" id=""Search"">
								<i class=""layui-icon layui-icon-search""></i>
							</button>
						</div>
					</div>
				</div>

			</div>
		</header>
		<div style=""height:95px;""></div>
		<div class=""content_wrap"">
			<div class=""layui-card content_top "">
				<div class=""layui-card-header""><i class=""layui-icon icon-shu""></i><span class=""card_title""></span>妊娠相关免疫性疾病</div>
				<div");
                BeginWriteAttribute("class", " class=\"", 2562, "\"", 2570, 0);
                EndWriteAttribute();
                WriteLiteral(">\r\n");
                WriteLiteral(@"
					<div class=""stepCont stepCont2"">
						<div class='ystep-container ystep-lg ystep-blue' ></div>
					</div>

					<div class=""layui-card-body"">
						<div class=""layui-row"">

							<div class=""layui-col-xs4 layui-col-sm3 layui-col-md3"">
								<div class=""subject_item"">
									<div class=""subject_icon button_bj2""><i class=""layui-icon icon-difanglianqinliliang""></i></div>
									<p>入组人数(<span>37</span>)</p>
								</div>
							</div>
							<div class=""layui-col-xs4 layui-col-sm3 layui-col-md3"">
								<div class=""subject_item"">
									<div class=""subject_icon button_bj2"">
										<i class=""layui-icon icon-chuangjianCRFbiaodanicon""></i>
									</div>
									<p>CRF表(<span>21</span>)</p>
								</div>
							</div>
							<div class=""layui-col-xs4 layui-col-sm3 layui-col-md3"">
								<div class=""subject_item"">
									<div class=""subject_icon button_bj2""><i class=""layui-icon icon-bianliang""></i></div>
									<p>变量字段(<span>620</span>)</p>
								</div>
					");
                WriteLiteral(@"		</div>
							<div class=""layui-col-xs4 layui-col-sm3 layui-col-md3"">
								<div class=""subject_item"">
									<div class=""subject_icon button_bj2"">
										<i class=""layui-icon icon-shengwuyangbenku""></i>
									</div>
									<p>生物样本留存(<span>47</span>)</p>
								</div>
							</div>
							<div class=""layui-col-xs4 layui-col-sm3 layui-col-md3"">
								<div class=""subject_item"">
									<div class=""subject_icon button_bj2""><i class=""layui-icon icon-suifangjihua""></i></div>
									<p>随访量(<span>20</span>)</p>
								</div>
							</div>

						</div>

					</div>

				</div>
				
			</div>

			<div class=""layui-card"">
				<div class=""layui-card-header""><i class=""layui-icon icon-shu""></i><span class=""card_title""></span>今日事件</div>
				<div class=""layui-card-body newswrap"">
					<div class=""jq22"">
						<ul>
							<li>
								<div>刘班登录</div>
								<div>2024-08-0707:35</div>
							</li>
							<li>
								<div>表单填写</div>
								<div>2024-08-0709:11</div>
");
                WriteLiteral(@"							</li>
							<li>
								<div>修改表单</div>
								<div>2024-08-0714:15</div>
							</li>
							<li>
								<div>留存样本(19)</div>
								<div>2024-08-0715:40</div>
							</li>

							<li>
								<div>冯辉登录</div>
								<div>2024-08-0719:34</div>
							</li>
							<li>
								<div>表单填写</div>
								<div>2024-08-0718:32</div>
							</li>
							<li>
								<div>修改表单</div>
								<div>2024-08-0719:33</div>
							</li>
							<li>
								<div>留存样本(21)</div>
								<div>2024-08-0717:35</div>
							</li>

							<li>
								<div>刘伟登录</div>
								<div>2024-08-0708:34</div>
							</li>
							<li>
								<div>表单填写</div>
								<div>2024-08-0710:32</div>
							</li>
							<li>
								<div>修改表单</div>
								<div>2024-08-0712:33</div>
							</li>
							<li>
								<div>留存样本(37)</div>
								<div>2024-08-0713:35</div>
							</li>

						</ul>
					</div>

				</div>

			</div>

		</div>


		<div style=""height:60px;""></div>
		<foot");
                WriteLiteral(@"er class=""aui-footer aui-footer-fixed"">
			<a href=""javascript:;"" class=""aui-tabBar-item "" id=""yxzx"">
				<span class=""aui-tabBar-item-icon"">
					<i class=""icon layui-icon icon-login_organization_icon""></i>
				</span>
				<span class=""aui-tabBar-item-text"">医学中心</span>
			</a>
			<a href=""javascript:;"" class=""aui-tabBar-item "" id=""bdgl"">
				<span class=""aui-tabBar-item-icon"">
					<i class=""icon layui-icon icon-biaodanguanli""></i>
				</span>
				<span class=""aui-tabBar-item-text"">eCRF</span>
			</a>
			<a href=""javascript:;"" class=""aui-tabBar-item aui-tabBar-item-active "" id=""sjcj"">
				<span class=""aui-tabBar-item-icon"">
					<i class=""icon layui-icon icon-shujucaiji""></i>
				</span>
				<span class=""aui-tabBar-item-text"">数据采集</span>
			</a>
			<div class=""no-style aui-tabBar-item "">
				<span class=""aui-tabBar-item-icon"">
					<i class=""icon layui-icon icon-wode-copy""></i>
				</span>
				<span class=""aui-tabBar-item-text"">我的</span>
			</div>
		</footer>




	</div>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "fa5c6988e829976600900cf9bddb95d8518ba9ee9b6e2a842d1fe7a2eca9911e22572", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "fa5c6988e829976600900cf9bddb95d8518ba9ee9b6e2a842d1fe7a2eca9911e23694", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "fa5c6988e829976600900cf9bddb95d8518ba9ee9b6e2a842d1fe7a2eca9911e24903", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "fa5c6988e829976600900cf9bddb95d8518ba9ee9b6e2a842d1fe7a2eca9911e26031", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "fa5c6988e829976600900cf9bddb95d8518ba9ee9b6e2a842d1fe7a2eca9911e27156", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"


	<script type=""text/javascript"">
		layui.use(['jquery', 'layer', 'form', 'element', 'code', 'laytpl', 'table'], function () {
			var layer = layui.layer,
				$ = layui.$,
				form = layui.form,
				laytpl = layui.laytpl,
				element = layui.element,
				table = layui.table;

			$(document).ready(function () {
				$(""#yxzx"").on(""click"", function () {
					window.location.href = ""/BasicConfig/CRFormsPad/MedicalCenterManage"";
				})

				$(""#bdgl"").on(""click"", function () {
					window.location.href = ""/BasicConfig/CRFormsPad/Index"";
				})

				$(""#sjcj"").on(""click"", function () {
					window.location.href = ""/BasicConfig/CRFormsPad/DataProfiling"";
				})

				var step2 = new SetStep({
					content: '.stepCont2'
					,clickAble: false
					, steps: ['方向选定', '表单设计', '入组患者', '数据采集', '审核质控', '生物样本留存', '随访']
					,stepCounts: 7//总共的步骤数
					,curStep: 5//当前显示第几页
				})
			
				var $jq22 = $('.jq22').liMarquee({
					direction: 'up',
					runshort: false
				});
			
				// $jq");
                WriteLiteral(@"22.liMarquee('pause');
				$("".ystep-container-steps"").on(""click"", ""li"", function () {
					var index = $(this).index();
						index += 1;
					var _title = $(this).html();
					if (index == 2 || index == 3 || index == 4 || index == 7) {
						var imageUrl = '../../images/CRFormsPad/' + index + '.jpg';
						// 使用 layer.open 方法创建一个空的弹窗
                        layer.open({
                            type: 1, // 类型 1 表示普通的弹窗
                            title: _title,
                            area: ['90%', '90%'], // 设置弹窗大小
                            content: '<img id=""popupImage"" />',
                            success: function (layero) {
                                // 当弹窗成功打开后，设置图片的 src
                                $('#popupImage').attr('src', imageUrl);
                                // 调整图片样式使其自适应容器
                                $('#popupImage').css({
                                    ""max-width"": ""90%"",
                                    ""max-height"": ""90%"",
                     ");
                WriteLiteral(@"               ""display"": ""block""
                                });
                                // 调整弹窗样式使其居中显示
                                $("".layui-layer-content"").css({
                                    ""display"": ""flex"",
                                    ""justify-content"": ""center"",
                                    ""align-items"": ""center"",
                                    ""overflow"": ""hidden""
                                });
                            }
                        });
					};
				})



				setNewsWrapH();
			//今日事件的高度
			function setNewsWrapH() {
				var winH = $(window).height();
				var headH = $("".head_wrap"").height();
				var footerH = $("".aui-footer"").height();
				var contentTopH = $("".content_top"").height();
				var news_H = winH - (headH + footerH + contentTopH) + ""px"";
				$("".newswrap"").css(""height"", news_H);
			}
		

			$(window).resize(function () {
				setNewsWrapH();
			});

			});

		})

	</script>


");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
