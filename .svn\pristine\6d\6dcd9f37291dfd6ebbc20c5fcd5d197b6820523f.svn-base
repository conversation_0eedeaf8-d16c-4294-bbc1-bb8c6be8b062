﻿//AnyReport CRF表单保存数据和展示数据方法
let HideList = [];
let ShowList = [];
let DisableList = [];
let EnableList = [];
window.addEventListener('message', function (event) {
    console.log(event);
    // 处理接收到的消息
    var res = event.data.data;
    var action = event.data.action;
    console.log(action);
    if (action == "show") {
        $("[name='save']").show();
    }
    //父页面主动触发表单的保存事件
   else if (action == "save")
    {
        $("[name='SAVE']").trigger("click");
        return false;
    }
    if (res) {
        try {
            var obj = JSON.parse(res);

        } catch (error) {
            console.error('JSON字符串格式错误:', error.message);
            alert('JSON字符串格式错误:' + error.message);
        }

        obj.variables.forEach(function (variable) {
            var variableName = variable.variable_name;
            var value = variable.value;
            if (variableName.startsWith("detailArray_")) {
                const variableNameArray = variableName.split("_");
                var rows = variableNameArray[1];
                var match = rows.match(/\d+/);
                var XH = match ? parseInt(match[0], 10) : null;
                XH = XH + 1;
                var detailArray = JSON.parse(value);
                console.log('detailArray个数：', detailArray.length);
                //注册新增列
                for (var i = 0; i < detailArray.length; i++) {
                    var $rows = $('tr.' + rows + '[IsBindData!="1"]:not(:hidden)');
                    // 使用for...in循环遍历对象
                    for (var key in detailArray[i]) {

                        var thisValue = detailArray[i][key];
                        key = key.toUpperCase();
                        var controls = $rows.find('[name^="' + key + '"]');
                        if (controls.length > 0) {
                            if (controls.is(':radio')) {
                                var radio = controls.filter('[value="' + thisValue + '"]');
                                radio.prop('checked', true);
                            }
                            else if (controls.is(':checkbox')) {
                                controls.prop("checked", false);
                                // 使用字符串的slice方法来截取字符串
                                if (thisValue) {
                                    var valueArr = thisValue.split("&&&###");
                                    valueArr.forEach(function (fruit) {
                                        var checkbox = controls.filter('[value="' + fruit + '"]');
                                        checkbox.prop('checked', true);
                                    });
                                }
                            }
                            else if (controls.is('select')) {
                                // var previousButton = control.parent('div').find('button');
                                if (thisValue) {
                                    var arrs = thisValue.split('&&&###');
                                    $(controls).selectpicker('val', arrs);
                                }
                            }
                            else {
                                controls.each(function () {
                                    $(this).prop('value', thisValue); // 设置每个input元素的value属性为value1
                                });
                            }
                        }
                    }
                    $rows.attr("IsBindData", "1");
                    if (i + 1 < detailArray.length) {
                        $('a.exticon[exttype="0"][cellref="A' + XH + '"]').click();
                    }
                }
            }
            else {
                variableName = variableName.toUpperCase();
                var control = $('[name="' + variableName + '"]');
                if (control.is(':radio')) {
                    var radio = $('[name="' + variableName + '"][value="' + value + '"]');
                    radio.prop('checked', true);

                }
                else if (control.is(':checkbox')) {

                    var CheckBoxList = $('input[name="' + variableName + '"][type="checkbox"]');
                    if (CheckBoxList.length > 0) {
                        CheckBoxList.prop("checked", false);
                        CheckBoxList.removeAttr("checked");
                        if (value) {
                            var valueArr = value.split("&&&###");
                            valueArr.forEach(function (fruit) {
                                var checkbox = $('[name="' + variableName + '"][value="' + fruit + '"]');
                                checkbox.prop('checked', true);
                            });
                        }
                    }
                }
                else if (control.is('select')) {
                    //var previousButton = control.parent('div').find('button');
                    if (value) {
                        var arrs = value.split('&&&###');
                        $(control).selectpicker('val', arrs);
                    }
                }
                else {
                    //20241107解决绘图 图片显示问题
                    if (variableName.indexOf("PICTURES_") > -1) {
                        var td = variableName.split("PICTURES_")[1];
                        var A9Td = anyrt.getFormCellByPos(td);
                        var htm = "<input type='hidden' value='" + value + "' name='" + variableName + "'><img class='img' src='" + value + "' style='height:126px;width:100px'>";
                        A9Td.html(htm);
                    }
                    else
                        control.val(value);
                }

                //解决CMIS默认隐藏问题
                if (ShowList.length > 0 && (control.is(':radio') || control.is(':checkbox')))
                {
                  
                    // 使用 find 方法来找到匹配的对象
                    var arrList = value.split("&&&###");
                  
                    $.each(arrList, function(index,arr){
                        let foundItem = ShowList.find(item => item.id === variableName && item.value==arr);
                        let showList = foundItem ? foundItem.ShowList : null;
                        if(showList)
                        {
                            ShowRowList(showList);
                        }
                    })

                   
                }

                if (HideList.length > 0 && (control.is(':radio') || control.is(':checkbox'))) {
                    // 使用 find 方法来找到匹配的对象
                    let foundItem = HideList.find(item => item.id === variableName && item.value === value);

                    let hideList = foundItem ? foundItem.HideList : null;
                    if (hideList) {
                        HideRowList(hideList);
                    }
                }

                if (DisableList.length > 0 && (control.is(':radio') || control.is(':checkbox')))
                {
                    // 使用 find 方法来找到匹配的对象
                    let foundItem = DisableList.find(item => item.id === variableName && item.value === value);

                    let disableList = foundItem ? foundItem.DisableList : null;
                    if (disableList)
                    {
                        DisableRowList(disableList);
                    }
                }

                if (EnableList.length > 0 && (control.is(':radio') || control.is(':checkbox')))
                {
                    // 使用 find 方法来找到匹配的对象
                    let foundItem = EnableList.find(item => item.id === variableName && item.value === value);

                    let enableList = foundItem ? foundItem.EnableList : null;
                    if (enableList)
                    {
                        EnableRowList(enableList);
                    }
                }
            }

        });
    }

}, false)


//args:如果有动态行，则传动态行(class)的数组 没有则传[]
function saveData(args) {
    //获取查询按钮对象
    var btn1 = $("[name='SAVE']")
    //删除按钮默认的onclick事件
    btn1.attr("onclick", "");
    //注册查询按钮click事件
    btn1.click(function () {
        var form = document.getElementById('qryForm');
        var json = {};
        // 获取所有<tr>元素
        var trList = form.getElementsByTagName("tr");

        if (args.length > 0) {
            for (var i = 0; i < args.length; i++) {
                const rowsArray = Array.from(trList);
                var detailRows = rowsArray.filter(row => row.classList.contains(args[i]));
                var detailRowsArry = [];
                for (var j = 0; j < detailRows.length; j++) {
                    // 获取当前<tr>元素
                    var tr = detailRows[j];
                    var detailRowsData = {};
                    // 检查当前<tr>元素是否隐藏
                    var isHidden = tr.style.display === "none";

                    // 如果当前<tr>元素不隐藏，则获取其下所有input元素的值
                    if (!isHidden) {
                        // 获取当前<tr>元素下的所有<input>元素
                        var inputList = tr.getElementsByTagName("input");
                        // 遍历<input>元素列表
                        for (var k = 0; k < inputList.length; k++) {
                            // console.log(classs + "开始循环");
                            var input = inputList[k];
                            var key = input.name;
                            var value = input.value;
                            var type = input.type;
                            if (type == "button") {
                                // console.log("button", key);
                                continue;
                            }

                            if (type == "checkbox" || type == "radio") {
                                //33固定hash值32+分隔符
                                key = key.substring(0, key.length - 33);
                                if (!input.checked) {
                                    value = "";
                                }
                            }
                            if (key != 'formId') {
                                if (detailRowsData[key]) {
                                    if (value) {
                                        detailRowsData[key] = detailRowsData[key] + "&&&###" + value;
                                    }
                                } else {
                                    detailRowsData[key] = value;
                                }
                            }

                        }

                        var selectList = tr.getElementsByTagName("select");
                        // console.log("查找select", selectList.length);
                        for (var k = 0; k < selectList.length; k++) {
                            var input = selectList[k];
                            var key = input.name;
                            var selectedIndex = input.selectedIndex;
                            var selectedOption = "";
                            var types = input.type;
                            if (types == "select-multiple") {
                                selectedOption = $(input).selectpicker('val').join('&&&###');
                            }
                            else {
                                selectedOption = $(input).selectpicker('val')
                            }
                            detailRowsData[key] = selectedOption;
                        }

                        var textareaList = tr.getElementsByTagName("textarea");
                        // console.log("查找textarea", textareaList.length);
                        for (var k = 0; k < textareaList.length; k++) {
                            var input = textareaList[k];
                            var key = input.name;
                            var value = input.value;
                            detailRowsData[key] = value;
                        }
                        detailRowsArry.push(detailRowsData);
                    }
                }

                json["detailArray_" + args[i]] = detailRowsArry;

                //把动态行的从trList移除
                trList = rowsArray.filter(row => !row.classList.contains(args[i]));

            }
        }

        // 循环遍历每个<tr>元素
        for (var i = 0; i < trList.length; i++) {
            // 获取当前<tr>元素
            var tr = trList[i];

            // 检查当前<tr>元素是否隐藏
            var isHidden = tr.style.display === "none";

            // 如果当前<tr>元素不隐藏，则获取其下所有input元素的值
            if (!isHidden) {
                // 获取当前<tr>元素下的所有<input>元素
                var inputList = tr.getElementsByTagName("input");
                // 遍历<input>元素列表
                for (var j = 0; j < inputList.length; j++) {
                    // console.log(classs + "开始循环");
                    var input = inputList[j];
                    var key = input.name;
                    var value = input.value;
                    var type = input.type;
                    if (type == "button") {
                        // console.log("button", key);
                        continue;
                    }

                    if (type == "checkbox" || type == "radio") {
                        //33固定hash值32+分隔符
                        //   key = key.substring(0, key.length - 33);
                        if (!input.checked) {
                            value = "";
                        }
                    }
                   
                    if (key != 'formId') {
                        if (json[key]) {
                            if (value) {
                                json[key] = json[key] + "&&&###" + value;
                            }
                        } else {

                            json[key] = value;
                        }
                    }

                }

                var selectList = tr.getElementsByTagName("select");
                // console.log("查找select", selectList.length);
                for (var j = 0; j < selectList.length; j++) {
                    var input = selectList[j];
                    var key = input.name;
                    var selectedIndex = input.selectedIndex;
                    var selectedOption = "";
                    var types = input.type;
                    if (types == "select-multiple") {
                        selectedOption = $(input).selectpicker('val').join('&&&###');
                    }
                    else {
                        selectedOption = $(input).selectpicker('val')
                    }
                    json[key] = selectedOption;
                }

                var textareaList = tr.getElementsByTagName("textarea");
                // console.log("查找textarea", textareaList.length);
                for (var j = 0; j < textareaList.length; j++) {
                    var input = textareaList[j];
                    var key = input.name;
                    var value = input.value;
                    json[key] = value;
                }
            }

        }
        var res = anyrt.formValid();
        json["formId"] = $("#formId").val();
        var jsonString = JSON.stringify(json);
        console.log(jsonString);
        // 向父窗口发送消息
        if (res) {
            window.parent.postMessage({ action: 'save', url: this.href, data: jsonString }, '*');
        }
    })
};


$('input').click(function () {
    var $name = $(this).attr('name');
    window.parent.postMessage({ action: 'show', url: this.href, fieldname: $name }, '*');
});

$('textarea').click(function () {
    var $name = $(this).attr('name');
    window.parent.postMessage({ action: 'show', url: this.href, fieldname: $name }, '*');
});

$('select').click(function () {
    var $name = $(this).attr('name');
    window.parent.postMessage({ action: 'show', url: this.href, fieldname: $name }, '*');
});


function HideRow(btnName, btnValue, HideListRow) {

    $("input[name='" + btnName + "']").on('change', function () {
        var value = this.value;
        if (this.checked && value == btnValue) {
            // Checkbox被选中时的操作
            console.log('Checkbox is checked');
            $.each(HideListRow, function (index, item) {
                $("[name='" + item + "']").closest('tr').hide();
            })


        } else {
            // Checkbox未被选中时的操作
            console.log('Checkbox is not checked');
            $.each(HideListRow, function (index, item) {
                $("[name='" + item + "']").closest('tr').show();
            })

        }
    });
}


//大于两个选项的
function HideRow2(btnName, btnValue, HideListRow) {

    $("input[name='" + btnName + "'][value='" + btnValue + "']").on('change', function () {
        if (this.checked) {
            // Checkbox被选中时的操作
            console.log('Checkbox is checked');
            $.each(HideListRow, function (index, item) {
                $("[name='" + item + "']").closest('tr').hide();
            })


        } else {
            // Checkbox未被选中时的操作
            console.log('Checkbox is not checked');
            $.each(HideListRow, function (index, item) {
                $("[name='" + item + "']").closest('tr').show();
            })

        }
    });
}


//大于两个选项的
function ShowRow(btnName, btnValue, HideListRow) {

    $("input[name='" + btnName + "'][value='" + btnValue + "']").on('change', function () {
        if (this.checked) {
            // Checkbox被选中时的操作
            console.log('Checkbox is checked');
            $.each(HideListRow, function (index, item) {
                $("[name='" + item + "']").closest('tr').show();
            })


        } else {
            // Checkbox未被选中时的操作
            console.log('Checkbox is not checked');
            $.each(HideListRow, function (index, item) {
                $("[name='" + item + "']").closest('tr').hide();
            })

        }
    });
}

function HideRowList(HideListRow) {
    $.each(HideListRow, function (index, item) {
        $("[name='" + item + "']").closest('tr').hide();
    })
}

function ShowRowList(HideListRow) {
    $.each(HideListRow, function (index, item) {
        $("[name='" + item + "']").closest('tr').show();
    })
}

//radio 可单选可点击取消选中
let lastChecked = null;

$('input[type="radio"]').click(function ()
{
    if (lastChecked === this)
    {
        $(this).prop('checked', false);
        lastChecked = null;
    } else
    {
        lastChecked = this;
    }
});

function DisableRowList(ListRow)
{
    $.each(ListRow, function (index, item)
    {
        $("[name='" + item + "']").closest('tr').find("input,textarea,select").prop('disabled', true)
    })
}

function EnableRow(btnName, btnValue, ListRow)
{

    $("input[name='" + btnName + "'][value='" + btnValue + "']").on('change', function ()
    {
        if (this.checked)
        {
            // Checkbox被选中时的操作
            console.log('Checkbox is checked');
            $.each(ListRow, function (index, item)
            {
                $("[name='" + item + "']").closest('tr').find("input,textarea,select").prop('disabled', true);
            })


        } else
        {
            // Checkbox未被选中时的操作
            console.log('Checkbox is not checked');
            $.each(HideListRow, function (index, item)
            {
                $("[name='" + item + "']").closest('tr').find("input,textarea,select").prop('disabled', false);
            })

        }
    });
}

function EnableRowList(ListRow)
{
    $.each(ListRow, function (index, item)
    {
        $("[name='" + item + "']").closest('tr').find("input,textarea,select").prop('disabled', false)
    })
}