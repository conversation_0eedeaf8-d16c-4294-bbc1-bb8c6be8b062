#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\CMISConfig\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "46fcdc95dd8c087631c63b47aa625a288921716eec97f36f75c882c6cccf27c4"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_CMIS_Views_CMISConfig_Index), @"mvc.1.0.view", @"/Areas/CMIS/Views/CMISConfig/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"46fcdc95dd8c087631c63b47aa625a288921716eec97f36f75c882c6cccf27c4", @"/Areas/CMIS/Views/CMISConfig/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_CMIS_Views_CMISConfig_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\CMISConfig\Index.cshtml"
  
	Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "46fcdc95dd8c087631c63b47aa625a288921716eec97f36f75c882c6cccf27c45916", async() => {
                WriteLiteral("\r\n\t<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n\t<meta charset=\"utf-8\" />\r\n\t<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n\t<title>临床科室管理</title>\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "46fcdc95dd8c087631c63b47aa625a288921716eec97f36f75c882c6cccf27c46418", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
	<style>
		/*弹窗*/
		.window_wrap {
			padding: 15px;
		}

		.search_wrap {
			background-color: #f0f0f0;
		}

		.layui-tab-brief {
			background-color: #fff;
		}

		.line_wrap {
			padding: 10px 15px;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
		}

		.form_item {
			padding-top: 10px;
		}

		.btnwrap {
			padding-left: 10px;
		}

		.layui-show {
			display: flex !important;
			align-items: flex-end;
		}

		.form_wrap {
			flex: 1;
		}

		.table_wrap {
			overflow: hidden;
		}

		#detail_window .layui-form-label {
			width: 100px;
		}

		#detail_window .layui-form-val {
			padding: 9px 15px;
		}

		#detail_window .mindow_icon .title_icon {
			display: inline-block;
		}

		#detail_window .mindow_icon {
			text-align: center;
			padding: 20px 0;
		}

		#detail_window .layui-form-item {
			margin-bottom: 0;
		}
		/* 定义表头样式 */
		.layui-table-header .layui-table-cell {
			font-weight: bold; /* 加粗 */
			font-size: 14");
                WriteLiteral("px; /* 可选：调整字体大小以提升可读性 */\r\n\t\t\tcolor: #333; /* 可选：调整字体颜色 */\r\n\t\t}\r\n\t</style>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "46fcdc95dd8c087631c63b47aa625a288921716eec97f36f75c882c6cccf27c49474", async() => {
                WriteLiteral("\r\n\t<div class=\"layui-col-md12\">\r\n\t\t<div class=\"layui-card\">\r\n\t\t\t<!--搜索区-->\r\n\t\t\t<div class=\"line_wrap search_wrap\">\r\n\t\t\t\t<div>\r\n\t\t\t\t\t<div class=\"layui-inline\">\r\n");
                WriteLiteral("\t\t\t\t\t\t<div class=\"layui-input-inline\">\r\n\t\t\t\t\t\t\t<input type=\"text\" class=\"layui-input\" name=\"keyWord\" placeholder=\"请输入关键字\" id=\"keyWord\"");
                BeginWriteAttribute("value", " value=\"", 1923, "\"", 1931, 0);
                EndWriteAttribute();
                WriteLiteral(@" />
						</div>
						<button id=""Search"" class=""layui-btn layui-btn-primary layui-border-green""><i class=""layui-icon layui-icon-search""></i> </button>
					</div>
				</div>
				<div>
					<button type=""button"" id=""addDSP"" data-type=""add"" class=""layui-btn layui-bg-blue""><i class=""layui-icon layui-icon-add-1""></i></button>
				</div>
			</div>
			<!--编辑区-->
			<div class="" edit_area"">
				<form class=""layui-form"" lay-filter=""fm"" id=""fm""");
                BeginWriteAttribute("action", " action=\"", 2384, "\"", 2393, 0);
                EndWriteAttribute();
                WriteLiteral(@">
					<div class=""line_wrap layui-card layui-colla-content layui-show"">
						<div class=""layui-row form_wrap layui-form"" lay-filter=""DeptInfo"" id=""DeptInfo"">
							<div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"">
								<div class=""form_item"">
									<label class=""layui-form-label"" id=""labelContent"">结构化名称</label>
									<div class=""layui-input-block "">
										<input type=""text"" name=""Id"" id=""Id"" style=""display:none;"" />
										<input type=""text"" name=""SettingName"" id=""SettingName"" required lay-verify=""required"" placeholder=""请输入结构化名称"" autocomplete=""off"" class=""layui-input"">
									</div>
								</div>
							</div>
							<div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"">
								<div class=""form_item"">
									<label class=""layui-form-label"">表单名称</label>
									<div class=""layui-input-block "">
										<input type=""text"" name=""FormName"" id=""FormName"" placeholder=""请输入表单名称"" autocomplete=""off"" class=""layui-input"">
									</div>
								</div>
							</div>
	");
                WriteLiteral(@"						<div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"">
								<div class=""form_item"">
									<label class=""layui-form-label"">表单Id</label>
									<div class=""layui-input-block "">
										<input type=""text"" name=""FormId"" id=""FormId"" placeholder=""请输入表单Id"" autocomplete=""off"" class=""layui-input"">
									</div>
								</div>
							</div>
							<div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"">
								<div class=""form_item"">
									<label class=""layui-form-label"">CMIS业务域</label>
									<div class=""layui-input-block "">
										<input type=""text"" name=""CMISDomain"" id=""CMISDomain"" placeholder=""请输入CMIS业务域"" autocomplete=""off"" class=""layui-input"">
									</div>
								</div>
							</div>
							<div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"">
								<div class=""form_item"">
									<label class=""layui-form-label"">检查部位</label>
									<div class=""layui-input-block "">
										<input type=""text"" name=""JCBW"" id=""JCBW"" placeholder=""请输入检查部位"" autocomplete=""off"" cl");
                WriteLiteral(@"ass=""layui-input"">
									</div>
								</div>
							</div>

							<div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"" style=""display:none"">
								<div class=""form_item"">
									<label class=""layui-form-label"">
										结构化类型
									</label>
									<div class=""layui-input-block "">
										<select name=""CRFType"" id=""CRFType""");
                BeginWriteAttribute("lay-search", " lay-search=\"", 4797, "\"", 4810, 0);
                EndWriteAttribute();
                WriteLiteral(@">
											<option value=""前结构化"" selected>前结构化</option>
											<option value=""后结构化"">后结构化</option>
										</select>
									</div>
								</div>
							</div>
							<div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"">
								<div class=""form_item"">
									<label class=""layui-form-label"" id=""labelContent"">疾病名称</label>
									<div class=""layui-input-block"">
										<select name=""Disease"" lay-filter=""Disease"" id=""Disease"">
											<option");
                BeginWriteAttribute("value", " value=\"", 5285, "\"", 5293, 0);
                EndWriteAttribute();
                WriteLiteral(@">请选择</option>
										</select>
									</div>
								</div>
							</div>

							<div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"">
								<div class=""form_item"">
									<label class=""layui-form-label"">医技标识</label>
									<div class=""layui-input-block "">
										<input type=""text"" name=""SystemId"" id=""SystemId"" required lay-verify=""required"" placeholder=""请输入医技标识"" autocomplete=""off"" class=""layui-input"">
									</div>
								</div>
							</div>
							<div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"">
								<div class=""form_item"">

									<label class=""layui-form-label"" title=""若启用，表单中空选项不会给AI"">不包含空值</label>
									<div class=""layui-input-block"">
										<input type=""checkbox"" name=""NotContainNull"" id=""NotContainNull"" lay-skin=""switch"" lay-text=""ON|OFF"">
									</div>

								</div>

							</div>
							<div class=""layui-col-xs6 layui-col-sm6 layui-col-md6"">
								<div class=""form_item"">
									<label class=""layui-form-label"">提示词</label>
					");
                WriteLiteral("\t\t\t\t<div class=\"layui-input-block \">\r\n");
                WriteLiteral(@"										<textarea name=""Tips"" id=""Tips"" class=""layui-textarea"" style=""height:260px;resize:none""></textarea>
									</div>
								</div>
							</div>

							<div class=""layui-col-xs6 layui-col-sm6 layui-col-md6"">
								<div class=""form_item"">
									<label class=""layui-form-label"" style=""padding-left:1px;width:90px;"">表单字段解释</label>
									<div class=""layui-input-block"">
");
                WriteLiteral(@"										<textarea name=""FormField"" id=""FormField"" class=""layui-textarea"" style=""height:260px;resize:none"" readonly></textarea>
									</div>
								</div>
							</div>

							<div class=""layui-col-xs6 layui-col-sm6 layui-col-md6"">
								<div class=""layui-form-item"">
									<div class=""layui-inline"">
										<label class=""layui-form-label"">启用状态</label>
										<div class=""layui-input-inline"">
											<input type=""checkbox"" name=""IsUsed"" id=""IsUsed"" lay-skin=""switch"" lay-text=""ON|OFF"">
										</div>
									</div>
								</div>

							</div>

							<div class=""layui-col-xs6 layui-col-sm6 layui-col-md6"">
								<div class=""layui-form-item"">
									<div style=""font-size:16px;font-weight:bold;color:red"">提示：{{formContent}}表示表单填写内容，结构如下{""组件名称"":""填写值""}</div>

								</div>
							</div>
						</div>
						<div class=""btnwrap"">
							<button class=""layui-btn"" lay-submit lay-filter=""submit"">保存</button>
						</div>
					</div>
				</form>
			</div>

			<div cl");
                WriteLiteral("ass=\"layui-card-body table_wrap\">\r\n\t\t\t\t<table id=\"tablelist\" lay-filter=\"tablelist\"></table>\r\n\t\t\t\t<script type=\"text/html\" id=\"tableBar\">\r\n");
                WriteLiteral("\t\t\t\t\t<a class=\"layui-btn layui-btn-danger layui-btn-xs\" lay-event=\"del\" style=\"text-decoration:none\"><i class=\"layui-icon layui-icon-delete\"></i>删除</a>\r\n\t\t\t\t</script>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "46fcdc95dd8c087631c63b47aa625a288921716eec97f36f75c882c6cccf27c417530", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "46fcdc95dd8c087631c63b47aa625a288921716eec97f36f75c882c6cccf27c418652", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "46fcdc95dd8c087631c63b47aa625a288921716eec97f36f75c882c6cccf27c419774", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "46fcdc95dd8c087631c63b47aa625a288921716eec97f36f75c882c6cccf27c420896", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"

	<script>
		layui.use(['element', 'layer', 'table', 'laydate', 'form'], function ()
		{
			var element = layui.element
				, layer = layui.layer
				, table = layui.table//表格
				, laydate = layui.laydate
				, $ = layui.$
				, form = layui.form;
			;
			var url = ''
			var value = '01';

			table.render({
				elem: '#tablelist'
				, id: 'tablelist'
				, page: true

				, limit: 10
				, height: 'full-325'
				, cols: [[
					{ field: 'No', title: '序号', type: 'numbers', fixed: 'left' }
					, { field: 'SettingName', title: '结构化名称', width: 250 }
					, { field: 'FormName', title: '表单名称', width: 250 }
					, { field: 'CMISDomain', title: 'CMIS业务域' }
					, { field: 'JCBW', title: '检查部位', width: 100 }
					, { field: 'Disease', title: '疾病名称', width: 100 }
					// , { field: 'CRFType', title: '结构化类型' }
					, { field: 'Tips', title: '提示词' }
					, { field: 'SystemId', title: '医技标识', width: 100 }
					, {
						field: 'IsUsed', title: '是否启用', width: 100, templet: function (d)
");
                WriteLiteral(@"
						{
							return '<input type=""checkbox"" name=""center"" lay-filter=""centerSwitch"" data-id=""' + d.Id + '"" title=""是|否"" lay-skin=""switch"" ' + (d.IsUsed ? 'checked' : '') + '>';
						}
					}
					, {
						field: 'NotContainNull', title: '不包含空值', width: 100, templet: function (d)
						{
							return '<input type=""checkbox"" name=""center"" lay-filter=""Switch2"" data-id=""' + d.Id + '"" title=""是|否"" lay-skin=""switch"" ' + (d.NotContainNull ? 'checked' : '') + '>';
						}
					}
					, { title: '操作', toolbar: '#tableBar', width: 200, minWidth: 200, fixed: 'right' }
				]]
				, done: function (res, curr, count)
				{
				}
			});

			$(document).ready(function ()
			{
				GetSelectList();

				$(document).on('click', '#Search', function ()
				{
					EmptyData();
					SearchData();
				})

				$(document).on('click', '#addDSP', function ()
				{
					EmptyData();
				});

				$(document).on('blur', '#FormId', function ()
				{
					GetFormField($(""#FormId"").val());
				})

			");
                WriteLiteral(@"});

			function GetFormField(FormId)
			{
				$.get('/CMIS/CMISConfig/GetFormField?formId=' + FormId, function (res)
				{

					if (res.code == 0)
					{
						$(""#FormField"").val(res.data);

					}
					else
					{
						$(""#FormField"").val("""");
						layer.msg(res.msg);
					}
				})
			}
			form.on('switch(centerSwitch)', function (data)
			{
				var id = data.elem.getAttribute('data-id'); // 获取唯一ID
				var checked = data.elem.checked;
				$.ajax({
					url: ""/CMIS/CMISConfig/UpdateStatus"",
					type: ""post"",
					data: { 'id': id, 'status': checked },
					datatype: 'json',
					success: function (data)
					{
						if (data.okMsg)
						{
							EmptyData();
							layer.msg(data.okMsg);
							table.reload('tablelist'); //重载表格
						}
						else
						{
							layer.msg(data.errorMsg);
						}
						layer.close(indes);
					}, error: function (res)
					{
						layer.msg(""加载统计信息错误："" + res.responseText);
						layer.close(indes);
					}
				});

			});
	");
                WriteLiteral(@"		form.on('switch(Switch2)', function (data)
			{
				var id = data.elem.getAttribute('data-id'); // 获取唯一ID
				var checked = data.elem.checked;
				$.ajax({
					url: ""/CMIS/CMISConfig/UpdateStatus2"",
					type: ""post"",
					data: { 'id': id, 'status': checked },
					datatype: 'json',
					success: function (data)
					{
						if (data.okMsg)
						{
							EmptyData();
							layer.msg(data.okMsg);
							table.reload('tablelist'); //重载表格
						}
						else
						{
							layer.msg(data.errorMsg);
						}
						layer.close(indes);
					}, error: function (res)
					{
						layer.msg(""加载统计信息错误："" + res.responseText);
						layer.close(indes);
					}
				});

			});
			//监听tablelist工具条
			table.on('tool(tablelist)', function (obj)
			{
				var data = obj.data;
				if (obj.event === 'del')
				{
					layer.confirm('确定要删除名为【' + data.FormName + '】的数据吗？将无法恢复。', {
						title: '',
						btn: ['确定', '取消'], //按钮
						resize: false
					}, function (index)
					{
						$.pos");
                WriteLiteral(@"t('/CMIS/CMISConfig/Del', { id: data.Id }, function (result)
						{
							if (result.okMsg)
							{
								layer.msg(result.okMsg);
								currentIndex = -1;
								table.reload('tablelist'); //重载表格
								EmptyData();
							} else
							{
								layer.msg(result.errorMsg);
							}
						}, 'json');
						layer.close(index);
					});
				}
				else if (obj.event === 'tips')
				{
					$(""#Tips"").val(data.Tips);
					$(""#SettingId"").val(data.Id);
					$.get('/CMIS/CMISConfig/GetFormField?formId=' + data.FormId, function (res)
					{

						if (res.code == 0)
						{
							$(""#FormField"").val(res.data);
							windowsIndex = layer.open({
								type: 1,
								title: '配置【' + data.FormName + '】的提示词',
								area: ['900px', '750px'],
								resize: true,
								content: $('#form_window'),
								btn: ['保存', '取消'],
								yes: function (index, layero)
								{
									$(""#btnSubmit"").click();
								},
								btn2: function (index, layero)
								{
			");
                WriteLiteral(@"						layer.close(windowsIndex);
								}

							});
						}
						else
							layer.msg(res.msg);
					})

				}
			});

			//触发行单击事件
			table.on('row(tablelist)', function (obj)
			{
				var data = obj.data;
				$(""#FormField"").val("""");
				form.val('DeptInfo', data);
				GetFormField(data.FormId);
				//$('#YJKSCode option[value=""' + data.YJKSCode + '""]').text(data.YJKSName);
				form.render('checkbox', 'DeptInfo');
				$(""#Disease"").val(data.DiseaseCode);
				form.render('select');
			});

			function GetSelectList()
			{

				$.ajax({
					url: ""/CMIS/CMISConfig/GetSelectList"",
					type: ""Get"",
					DataType: ""json"",
					success: function (res)
					{

						for (var i in res.data)
						{
							var str = '<option value=""' + res.data[i].code + '"">' + res.data[i].name + '</option>';
							$(""#Disease"").append(str);
						}
						form.render();
					},
					error: function (res) { layer.msg('失败，服务器内部错误', { icon: 2, }); }
				})
			}

			function SearchDat");
                WriteLiteral(@"a()
			{

				table.reload('tablelist', {
					page: {
						curr: 1
					},
					url: '/CMIS/CMISConfig/GetList'
					, where: {
						// 'code': xmDeptsList.getValue('valueStr'),
						'keyWord': $.trim($(""#keyWord"").val())
					}
				});
			};

			function EmptyData()
			{
				$('#Id').val('');
				$(""#SettingName"").val('');
				$(""#FormId"").val('');
				$(""#FormName"").val('');
				$(""#CMISDomain"").val('');
				$(""#JCBW"").val('');
				$(""#DiseaseCode"").val('');
				$(""#Disease"").val("""");
				$(""#CRFType"").val(""前结构化"");
				$(""#ExtraRequire"").val("""");
				$(""#SystemId"").val("""");
				$(""#Remark"").val("""");
				$(""#Tips"").val("""");
				$(""#FormField"").val("""");
				$('#IsUsed').prop('checked', false);
				$('#NotContainNull').prop('checked', false);
				form.render();
			}
			SearchData();

			//监听提交
			form.on('submit(submit)', function (data)
			{
				var indes = layer.load(1);
				if (data.field.IsUsed == ""on"")
					data.field.IsUsed = true;
				else
					data.field.");
                WriteLiteral(@"IsUsed = false;

				if (data.field.NotContainNull == ""on"")
					data.field.NotContainNull = true;
				else
					data.field.NotContainNull = false;
				data.field.DiseaseCode = $('#Disease').val();
				data.field.Disease = $('#Disease').find(':selected').text(),
					//data.field.isUsed = $('input[name=""IsEnable""]:checked').val();
					//提交 Ajax 成功后，关闭当前弹层并重载表格
					$.ajax({
						url: '/CMIS/CMISConfig/Save',
						type: ""post"",
						data: { 'node': data.field },
						datatype: 'json',
						success: function (data)
						{
							if (data.okMsg)
							{
								layer.msg(data.okMsg);
								table.reload('tablelist'); //重载表格
								EmptyData();
							}
							else
							{
								layer.msg(data.errorMsg);
							}
							layer.close(indes);
						}, error: function (res)
						{
							layer.msg(""加载统计信息错误："" + res.responseText);
							layer.close(indes);
						}
					});
				return false;
			});

			form.on('submit(demo2)', function (data)
			{
				var field = da");
                WriteLiteral(@"ta.field; // 获取表单字段值
				$.ajax({
					type: 'POST',
					url: ""/CMIS/CMISConfig/UpdateTips"",
					data: JSON.stringify(field),
					dataType: 'json',
					contentType: ""application/json; charset=utf-8"",
					success: function (result)
					{

						if (result.code == 0)
						{

							layer.msg(result.msg);
							//  layer.close(windowsIndex);//关闭弹出层
							layer.closeAll();
							table.reload('tablelist'); //重载表格
							$(""#fm"")[0].reset();
						}
						else
							layer.msg(result.msg);
					},
					error: function (data)
					{
						//layer.close(loading);
					}
				});
				// 此处可执行 Ajax 等操作
				// …
				return false; // 阻止默认 form 跳转
			});

			function setTableH()
			{
				var winH = $(window).height();
				var navH = $("".layui-tab-brief"").height();
				var searchH = $("".search_wrap"").height();
				var editAreaH = $("".edit_area"").height();
				var tableH = winH - (navH + searchH + editAreaH) - 55 + ""px"";
				$("".table_wrap"").css(""height"", tableH);

			};
		");
                WriteLiteral(@"	setTableH();
			$(window).resize(function ()
			{
				setTableH()
			});
		});
	</script>
	<script type=""text/html"" id=""switchTpl"">
		<!-- 这里的 checked 的状态只是演示 -->
		<input type=""checkbox"" id=""switch"" name=""{{d.Id}}"" value=""{{d.IsUsed}}"" lay-skin=""switch"" lay-text=""停用|启用"" lay-filter=""sexDemo"" {{");
                BeginWriteAttribute("d.IsUsed", " d.IsUsed =", 18215, "", 18226, 0);
                EndWriteAttribute();
                WriteLiteral("= \'on\' ? \'checked\' : \'\' }}>\r\n\t</script>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n<div class=\"window_wrap\" id=\"form_window\" style=\"display: none\">\r\n\t<form class=\"layui-form\" lay-filter=\"fm\" id=\"fm\"");
            BeginWriteAttribute("action", " action=\"", 18393, "\"", 18402, 0);
            EndWriteAttribute();
            WriteLiteral(">\r\n\t\t<div class=\"layui-form-item\">\r\n\t\t\t<input type=\"hidden\" id=\"SettingId\" name=\"SettingId\" />\r\n\t\t\t<label class=\"layui-form-label\" style=\"width:100px;\">表单字段解释：</label>\r\n\t\t\t<div class=\"layui-input-block\" style=\"margin-left:130px\">\r\n");
            WriteLiteral("\t\t\t</div>\r\n\t\t</div>\r\n\t\t<div class=\"layui-form-item\">\r\n\r\n\t\t\t<label class=\"layui-form-label\" style=\"width:100px;\">提示词：</label>\r\n\t\t\t<div class=\"layui-input-block\" style=\"margin-left:130px\">\r\n");
            WriteLiteral(@"
			</div>
		</div>
		<div class=""layui-form-item"">

			<div style=""font-size:16px;font-weight:bold;color:red"">提示：{{formContent}}表示表单填写内容，结构如下{""组件名称"":""填写值""}，{{formField}}表示表单字段解释，结构如下{""组件名称"":""组件描述""}</div>

		</div>

		<div class=""layui-form-item"" style=""display:none"">
			<button class=""layui-btn"" id=""btnSubmit"" lay-submit lay-filter=""demo2"">确认</button>

		</div>
	</form>
</div>
</html>
");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
