﻿@{
    Layout = null;
}
@model AngelwinResearch.Models.ResearchPatient
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="screen-orientation" content="landscape">

    <title>数据采集</title>
    <link href="~/layuiadmin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/lib/muihk/css/mui.min.css?v=1.0" rel="stylesheet" />
    <link href="~/layuiadmin/layui/font/eyes_icon/iconfont.css" rel="stylesheet" />
    <link href="~/lib/columnDrag/column_drag.css" rel="stylesheet" />

    <!-- 代码高亮 -->
    <link rel="stylesheet" href="~/lib/CodeMirror/lib/codemirror.css">
    <link rel="stylesheet" href="~/lib/CodeMirror/theme/pastel-on-dark.css" />
    <link rel="stylesheet" href="~/lib/dialog/style.css">
    <script src="~/js/jquery-3.5.1.min.js"></script>
    <script src="~/KeDaXunFei/hmac-sha256.js"></script>
    <script src="~/KeDaXunFei/HmacSHA1.js"></script>
    <script src="~/KeDaXunFei/md5.js"></script>
    <script src="~/KeDaXunFei/enc-base64-min.js"></script>
    <script src="~/KeDaXunFei/dist/index.umd.js"></script>
    <script type="text/javascript" src="~/KeDaXunFei/HZRecorder.js?v=6"></script>
   @*  <script src="~/KeDaXunFei/rtasr/XunFeiRecord.js?v=10" id="kdxf"></script> *@
    <script src="~/js/marked.min.js"></script>
    <style>
        body {
            background-color: #fafafa;
            overflow: hidden;
        }

        .layui-card-header {
            line-height: 20px;
            font-weight: bold;
        }


        /* 移动端头部 */

        .headrow {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            background-color: #f6f6f6;
            height: 38px;
        }

            .headrow .layui-btn-primary {
                background-color: transparent;
                border: none;
                color: #333;
            }

        .patient_information {
            align-items: center;
        }

        .middle {
            text-align: center;
        }

        .left, .right {
            min-width: 58px;
            height: 38px;
        }
        /* 移动端头部 */
        .space-between {
            justify-content: space-between;
        }

        .user_info {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            background-color: #fff;
            padding: 10px 15px;
        }

        .info_middle, .info_right {
            display: flex;
            flex-direction: row;
        }


        .user_info_item {
            margin-right: 20px;
        }

        .content_wrap {
            display: flex;
            flex-direction: row;
            padding: 10px 5px;
        }

        .flex_one {
            flex: 1;
        }

        .side_nav {
            width: 40px;
        }

        .nav_btn {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            width: 100%;
            padding: 20px 0;
            margin-bottom: 2px;
            writing-mode: vertical-rl;
            text-orientation: mixed;
            background: #e6e6e6;
            border-radius: 20px;
        }

        .nav_btn_active {
            color: #fff;
            background: rgb(122, 77, 123);
        }

        .content_inner {
            position: relative;
            padding: 10px;
            margin: 5px;
            margin-top: 0;
            background-color: #fff;
        }

        .btn_bottom {
            width: 96%;
            padding: 4px;
            padding-top: 5px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            position: absolute;
            left: 0;
            bottom: 15px;
            /* background-color:#f6f6f6; */
        }

        .nav_btn_group {
            display: flex;
            flex-direction: row;
        }

            .nav_btn_group .active {
                border: 1px solid #1e9fff;
                color: #1e9fff;
            }

        .nav_btn_item {
            text-align: center;
            padding: 0 10px;
            line-height: 30px;
            background-color: #fff;
            color: #666;
            border-radius: 2px;
            border: 1px solid #e6e6e6;
            cursor: pointer;
        }

        .left_content {
            height: 90%;
        }

        .left_inner {
            width: 100%;
            height: 100%;
        }


        .inner_user_info {
            justify-content: flex-start;
            background-color: transparent;
            padding: 0;
        }

        .writ {
            position: relative;
            margin: 10px 0;
            border: 1px solid #e6e6e6;
            border-radius: 2px;
        }

        .writ_bottom {
            width: 96%;
            position: absolute;
            bottom: 20px;
            text-align: right;
        }

        .empty {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 200px;
            height: 100px;
            margin-left: -100px;
            margin-top: -50px;
            text-align: center;
        }

            .empty i {
                font-size: 80px;
                color: #20222A;
            }

        .more_btn {
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            background-color: #fff;
        }

        .audio_item {
            padding-bottom: 10px;
            flex:1;
            padding-right:20px;
        }

        .audio_item audio {
            width: 100%;
        }

        .audio_list button {
            margin-top:20px;
        }

        .audio_title {
            padding-left: 5px;
            line-height: 34px;
        }

        .history_wrap {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            border: 1px solid #f6f6f6;
            border-radius: 2px;
        }

            .history_wrap .layui-input {
                border-width: 0;
            }

        .transfer_btn {
            width: 38px;
            height: 38px;
            margin-right:15px;
            text-align: center;
            cursor: pointer;
        }

            .transfer_btn i {
                font-size: 20px;
                line-height: 38px;
            }

        .alert {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            text-align: center;
            padding-top: 20%;
            z-index: 1000;
        }

            .alert i {
                font-size: 40px;
            }

            .alert p {
                margin-top: 20px;
            }

        .row_wrap {
            display: flex;
            flex-direction: row;
            align-content: center;
        }

        .layui-border-blue {
            border-color: #1E9FFF;
            color: #1E9FFF;
        }

        .translation_btn {
            background: rgb(122, 77, 123);
        }

        .btn_bg {
            width: 38px;
            height: 38px;
            margin-left: 10px;
            background-color: #f6f6f6;
            border-radius: 2px;
            cursor: pointer;
        }

        .right_btn_group {
            align-items: center;
        }

        .ws_btn {
            padding: 4px;
            background-color: #f6f6f6;
            border-radius: 2px;
            color: #1E9FFF;
            cursor: pointer;
        }

        .Menu_side {
            width: 220px;
            overflow-y: auto;
            overflow-x: hidden;
            transition: width 0.5s ease;
            flex: none;
        }

            .Menu_side.folded {
                width: 0;
            }

        .nav_item {
            position: relative;
            background-color: #fff;
            border: 1px solid #eee;
            padding: 10px 15px;
            border-radius: 4px;
            margin: 0 5px 10px;
        }

        .active {
            background: linear-gradient(70deg, #dcffff, #62ffff);
            border-color: transparent;
            color: #1E9FFF;
        }

            .active .layui-progress-text {
                color: #1E9FFF;
            }

        .layui-colla-content {
            padding: 0;
        }
        /* 折叠状态下左侧菜单的宽度 */
        .sider_btn {
            position: absolute;
            display: block;
            left: -5px;
            bottom: 10%;
            z-index: 99;
            line-height: 0;
            padding: 18px 0px 18px 0px;
            border: none;
            color: #fff;
            border-radius: 0 50% 50% 0;
            background: linear-gradient(to right, #56d1f9, #1eaddc);
        }

            .sider_btn i {
                font-size: 22px !important;
                padding-right: 10px;
            }

        .side_menu_title {
            font-size: 18px;
            color: #000;
            text-align: center;
            line-height: 30px;
            padding: 10px 0;
        }

        .form_name {
            margin-bottom: 15px;
        }

        .layui-colla-content .layui-card-body {
            padding: 10px 0;
        }

        .flex_row {
            display: flex;
            flex-direction: row;
        }

        .align_center {
            align-items: center;
        }

        .handle_R {
            padding-left: 10px;
            flex: 1;
        }

        .title_wrap {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .tools_wrap {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .tools_btn {
            height: 30px;
            padding: 2px 5px;
            border: 1px solid #1E9FFF;
            border-radius: 2px;
            margin: 0 1px;
            cursor: pointer;
        }

        .tools_wrap .layui-icon {
            font-size: 18px;
        }

        .form_name {
            margin-bottom: 15px;
            flex: 1;
        }


        .hide_report_list {
            padding: 15px;
        }

        .null_wrap {
            text-align: center;
            margin-top: 10%;
        }

            .null_wrap img {
                width: 200px;
            }

        .audio_list {
            max-height: 210px;
            overflow-y: auto;
        }
        .audio_list li{
            display:flex;
            flex-direction:row;
            justify-content:space-between;
            align-items:center;
        }

       .layui-form-switch{
           margin:0;

        }
    </style>
</head>
<body>

    <div class="wrap" id="content">
        <div class="headrow patient_information" style="display:none;">
            <div class="left">
                <button id="previous" class="layui-btn layui-btn-primary layui-border-green" style="margin-right:15px;">
                    <i class="layui-icon layui-icon-close"></i>
                </button>
            </div>
            <div class="middle">
                <h5>数据采集</h5>
            </div>
            <div class="right"></div>
        </div>

        <div class="user_info">
            <div class="info_left" style="min-width:100px;">
            </div>

            <div class="info_middle">
                <div class="user_info_item">患者:  <strong>@Model?.PatientName</strong></div>
                <div class="user_info_item">患者卡号:  <strong>@Model?.BRKH</strong></div>
            </div>

            <div class="info_right">
                <div class="user_info_item">科室:  <strong>@Model?.HospitalDept?.DeptName</strong></div>
                <div class="user_info_item">当前用户:  <strong>@ViewBag.userName</strong></div>
                <textarea id="model_wrapR" class="layui-textarea" style=" height:100%;width:100%;line-height:40px;
font-size:20px;resize:none;display:none"></textarea>
                <input type="hidden" id="AIExtractJsonValue" />
                <input type="hidden" id="reportId" />
            </div>
        </div>

        <div class="content_wrap">
            <div class="side_nav">
                <div class="nav_btn nav_btn_active" id="baselineBtn">记录表</div>
                <div class="nav_btn" id="recordsBtn">病历文书</div>
            </div>
            <div class="layui-row flex_one row_wrap">

                <!-- 左侧折叠菜单 -->
                <div id="sideMenu" class="Menu_side content_L layui-card mui-table-view baseline">
                    <div class="content_inner" style="padding:0;margin:0;">
                        <div class="layui-collapse" id="OA_task_1" style="height:100%;">
                            <div class="collapse_list"></div>
                        </div>
                    </div>
                </div>





                <div class="layui-col-xs5 layui-col-md5 flex_one">

                    <div class="content_inner">
                        <!--菜单折叠-->
                        <button type="button" id="toggleSidebar" class="layui-btn layui-btn-sm layui-btn-primary sider_btn">
                            <i class="layui-icon layui-icon-left" style="font-size:22px;"></i>
                        </button>
                        <div class="layui-card-header row_wrap space-between">
                            <div class="">
                                结构化内容提取
                            </div>
                            <div class="right_btn_group row_wrap">
                                <div class="ws_btn ">
                                    <button type="button" id="btnMedicalWrite" class="layui-btn layui-btn-normal layui-btn-sm">病历文书转写</button>
                                </div>
                                <div class="ws_btn ">
                                    <button type="button" id="btnAI" class="layui-btn layui-btn-normal layui-btn-sm">AI报告提取</button>
                                </div>
                                <div class="btn_bg" id="divImg"><img src="/images/img_icon.png" alt="Alternate Text" /></div>
                                <div class="btn_bg" id="divPdf"><img src="/images/pdf_icon.png" alt="Alternate Text" /></div>
                            </div>
                        </div>
                        <div class="writ">
                            <div id="crForm" style="height:100%">
                                <iframe id="reportingFrom" frameborder="0" width="100%" height="100%"></iframe>
                            </div>
                            <div class="" style="display:none;height:100%;width:100%;" id="divMedical">
                                @*<textarea id="txtMedical" class="layui-textarea" style="height:100%;width:100%;resize:none;line-height:40px;font-size:20px;"></textarea>*@
                                <div id="txtMedical" style="height:100%;width:100%;resize:none;line-height:40px;font-size:20px; overflow:auto;"></div>
                            </div>

                        </div>
                        <div class="writ_bottom" style="display:none">
                            <button type="button" class="layui-btn layui-btn-normal layui-btn-sm" id="btnUse">病历文书引用</button>
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs4 layui-col-md4 ">
                    <div class="content_inner ">
                        <div class="layui-card-header row_wrap space-between layui-form">
                            <div>医疗对话框</div>
                            <div>
                                <div class="layui-btn layui-btn-primary layui-btn-xs layui-border-blue " id="btnFill" style="margin-right:10px;"
                                    <i class="layui-icon layui-icon-time"></i>  实时填写
                                </div>

                                <input type="checkbox" name="close" lay-skin="switch" lay-text="云知声|科大讯飞" lay-filter="switchTest">
                            </div>
                        
                  
                        </div>
                        <div class="chat">
                            <div class="chat-history">
                                <ul id="ulList" style="height:100%;overflow-y:auto;">
                                </ul>
                            </div> <!-- end chat-history -->
                            <div class="more_btn ">
                                <div class="nav_btn_item"> 更多>>></div>
                            </div>
                        </div>
                        <div class="right_bottom">
                            <ul class="audio_list" id="audioList">
                            </ul>
                            <div class="layui-row" style="display: flex; align-items: center;">
                                <div class="layui-col-xs10 layui-col-sm10 layui-col-md10">
                                    <div class="history_wrap" style="align-items: center;">
                                        <div class="layui-input-inline" style="width:100%">
                                            <textarea id="result" message="" class="layui-textarea" placeholder="语音转文字区域" autocomplete="off" style="width:95%"></textarea>
                                            <textarea id="result2" message="" class="layui-textarea" placeholder="语音转文字区域" autocomplete="off" style="width:95%;display:none;"></textarea>

                                        </div>
                                        <div class="transfer_btn" id="btnSend" style='display:none'>
                                            <i class="layui-icon layui-icon-release"></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-col-xs2 layui-col-sm2 layui-col-md2" style="text-align:right;margin-top:5px;">
                                    <button type="button" id="btnRecord" class="layui-btn layui-btn-sm translation_btn">
                                        <i class="layui-icon layui-icon-mike"></i>开始录音
                                    </button>
                                </div>
                            </div>

                        </div>

                    </div>
                </div>
            </div>

        </div>

    </div>


    <div class="alert" id="orientationAlert">
        <i class="layui-icon layui-icon-refresh"></i>
        <p>请将您的设备旋转至横屏模式以继续。</p>
    </div>





    <!--隐藏报表列表-->
    <div id="hideReport" style="display:none">
        <div class="hide_report_list">
            <ul id="ulHideList">
            </ul>
        </div>
    </div>
    <div style="display: none;" id="isShow">
        <div class="mui-popup mui-popup-in">
            <div class="mui-popup-inner">
                <div class="mui-popup-title">提示</div>
                <div class="mui-popup-text">是否展示当前报表？</div>
            </div>
            <div class="mui-popup-buttons">
                <span class="mui-popup-button Confirmation_btn">确认</span>
                <span class="mui-popup-button mui-popup-button-bold">取消</span>
            </div>
        </div>
        <div class="mui-popup-backdrop mui-active"></div>
    </div>

    <!--对话框start-->
    <script src="~/js/jquery-3.5.1.min.js"></script>
    <script src='~/lib/dialog/handlebars.min.js'></script>
    @*   <script src='~/lib/dialog/list.min.js'></script>
    <script src="~/lib/dialog/script.js"></script> *@
    <!--对话框end-->


    <script src="~/layuiadmin/layui/layui.js"></script>
    <!--滑动删除插件-->
    <script src="~/lib/muihk/js/mui.min.js"></script>
    @*    <script type="text/javascript" src="~/lib/columnDrag/column_drag.js"></script> *@
<script>
        var APPID = "@ViewBag.XuFeiAPPID";
        var API_KEY = "@ViewBag.XuFeiAPI_KEY";
        var setIntervalTime = "@ViewBag.setIntervalTime";
	var autoMedicalIntervalTime = "@ViewBag.autoMedicalIntervalTime";
        const btnControl = document.getElementById("btnRecord");
        var audio = document.querySelector('audio');
        layui.use(['jquery', 'layer', 'form', 'element', 'code', 'laytpl', 'table', 'upload', 'carousel'], function() {
            var layer = layui.layer,
                $ = layui.$,
                form = layui.form,
                laytpl = layui.laytpl,
                element = layui.element,
                table = layui.table,
                upload = layui.upload,
                carousel = layui.carousel;

            var pageIndex = 1;
            var pageSize = 2;
            var loadIndex;
            if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
                $("#previous").show();
            }
            else {
                $("#previous").hide();
            }
            $("#previous").click(function() {
                // window.location.href = "/ReportingManage/PatientManage/Index";
            });



            // 点击折叠按钮时触发的函数
            document.getElementById('toggleSidebar').addEventListener('click', function() {

                var sideMenu = document.getElementById('sideMenu');

                var spreadIcon = document.getElementById('toggleSidebar');

                // 切换folded类来实现折叠效果
                sideMenu.classList.toggle('folded');

                // 根据菜单的状态切换图标显示
                if (sideMenu.classList.contains('folded')) {
                    spreadIcon.children[0].classList.remove('layui-icon-left'); // 显示展开图标
                    spreadIcon.children[0].classList.add('layui-icon-right'); // 隐藏展开图标
                } else {
                    spreadIcon.children[0].classList.remove('layui-icon-right'); // 显示展开图标
                    spreadIcon.children[0].classList.add('layui-icon-left'); // 隐藏展开图标
                }

                // 如果需要，可以通过Layui重新初始化相关元素，以保证样式和事件正常工作
                element.init();



            });

            $(".side_nav").on("click", ".nav_btn", function() {
                $(".nav_btn").removeClass("nav_btn_active");
                $(this).addClass("nav_btn_active");

                var navid = $(this).attr("id");
                if (navid == "baselineBtn") {
                    //基线
                    $(".baseline").css("display", "block");
                    $("#toggleSidebar").css("display", "block");
                    $("#divMedical").hide();
                    $("#crForm").show();
                    $(".writ_bottom").hide();
                     var iframe = document.getElementById('reportingFrom');
                    // iframe.src = iframe.src;
                    // 选择data-id为的li元素
                       var randVersion = Math.random();

                       var url = "@($"{ViewBag.formUrl}")" + formId + "@($".form?token={ViewBag.token}")&version=" + randVersion;
                      $("#reportingFrom").attr("src", url);


                    //解决当在病历文书tab页时，点击表单，再切换到记录表的时候 iframe的表单不显示问题
                  //  var targetLi = $('div[data-id="' + id + '"]');
                    // 模拟点击事件
                   // targetLi.trigger('click');

                    setWrit();

                } else if (navid == "recordsBtn") {
                    //病历文书
                    // $(".baseline").css("display", "none");
                    // $("#toggleSidebar").css("display", "none");
                    $("#divMedical").show();
                    $("#crForm").hide();
                    $(".writ_bottom").show();
                    setWrit();
                }




            });

            function checkOrientation() {
                if (window.innerHeight > window.innerWidth) { // 如果是竖屏
                    document.getElementById('orientationAlert').style.display = 'block';
                    document.getElementById('content').style.display = 'none';
                } else { // 如果是横屏
                    document.getElementById('orientationAlert').style.display = 'none';
                    document.getElementById('content').style.display = 'block';
                }
            };

            // 监听屏幕方向变化
            window.addEventListener('resize', checkOrientation);
            var IsCheck = false;
            $(document).ready(function() {



                $(document).on('click', '#btnMedicalWrite', function() {
                    var $this = $(this);
                    if ($this.hasClass('disabled'))
                        return;
                    //禁用按钮并添加样式或类以显示它现在不可用
                    $this.addClass('disabled');
                    $("#btnFill").addClass('disabled');
                    MedicalWrite("");
                });


                $(document).on('click', '#divImg', function() {
                    $.post('/ReportingManage/RehabilitationDept/GetImgList', { ResearchPatientId: '@Model.Id', CRFormId: id }, function(res) {
                        if (res.code == 0) {
                            $("#fileList").html("");
                            $.each(res.data, function(index, item) {
                                var htm = "<div><img src='" + item.reportURL + "' style='width:100%'></div>";
                                $("#fileList").append(htm);
                            })
                            windowsIndex = layer.open({
                                type: 1,
                                title: '文件预览',
                                area: ['800px', '600px'],
                                resize: true,
                                content: $('#form_window_Img'),

                            });
                        }
                        else {
                            layer.msg(res.msg);
                        }
                    })
                })

                $(document).on('click', '#divPdf', function() {
                    $.post('/ReportingManage/RehabilitationDept/GetPdfList', { ResearchPatientId: '@Model.Id', CRFormId: id }, function(res) {
                        if (res.code == 0) {
                            $("#fileList").html("");
                            $.each(res.data, function(index, item) {
                                var htm = "<div><iframe src='" + item.reportURL + "' style='width:100%;height:80vh'></iframe></div>";
                                $("#fileList").append(htm);
                            })
                            windowsIndex = layer.open({
                                type: 1,
                                title: '文件预览',
                                area: ['95%', '90%'],
                                resize: true,
                                content: $('#form_window_Img'),

                            });
                        }
                        else {
                            layer.msg(res.msg);
                        }
                    })
                })

                $("#btnAI").click(function() {
                    $("#baselineBtn").click();
                    AIExtract();
                });

                $("#btnSend").click(function () {
                    //$("#ulList").html("");
                    if (btnStatus === "CONNECTING" || btnStatus === "OPEN")
                        btnControl.click();
                    uploadVoice();

                });

                $(".nav_btn_item").click(function () {

                    pageIndex++;
                    $.post("/ReportingManage/RehabilitationDept/getMsgAndVoice", {
                        RId: '@Model.Id', CRFormId: id, pageIndex: pageIndex, pageSize: pageSize
                    },
                        function (res) {
                            if (res.code == 0) {
                                fillVoice(res.data);
                                setChatH();
                            }
                        });
                });

                $("#btnUse").click(function () {
                    //var textBox = document.getElementById('txtMedical');
                    //textBox.select(); // 选择文本
                    //document.execCommand('copy'); // 执行复制命令
                    //textBox.blur();
                    //layer.msg('病历文书已复制');

                    // 创建一个临时的textarea来复制内容
                    var $temp = $("<textarea>");
                    $("body").append($temp);
                    $temp.val($("#txtMedical").text()).select();
                    try {
                        var successful = document.execCommand('copy');
                        var msg = successful ? '成功复制到剪贴板' : '复制失败';
                        layer.msg(msg);
                    } catch (err) {
                        layer.msg('无法复制，浏览器不支持');
                    }
                    // 移除临时textarea
                    $temp.remove();
                });

                $("#btnFill").click(function () {
                    var btnId = $(".nav_btn_active").attr("id");
                    // if (btnStatus === "CONNECTING" || btnStatus === "OPEN")
                    //     btnControl.click();
                    // else {
                    //     SendLastMsg();
                    //     uploadVoice();
                    // }
                    if (btnStatus === "CONNECTING" || btnStatus === "OPEN") {
                        layer.msg("请先关闭录音！");
                        return;
                    }
                    if (!$("#result2").val()) {
                        layer.msg("请先选择录音文件！");
                        return;
                    }
                    if (btnId == "recordsBtn") {
                        //转写病历文书
                        var $this = $(this);
                        if ($this.hasClass('disabled'))
                            return;
                        //禁用按钮并添加样式或类以显示它现在不可用
                        $this.addClass('disabled');
                        $("#btnMedicalWrite").addClass('disabled');
                        MedicalWrite("audio")
                    }
                    else if (btnId == "baselineBtn") {
                        //根据语音填写表单
                        AIExtractByAudio();
                    }
                    else
                        layer.msg("请选择左侧记录表或者病历文书！");

                });
                // 使用原生JavaScript添加事件监听器
                var audios = document.querySelectorAll('#audioList audio');
                // 遍历所有音频元素并添加事件监听器
                audios.forEach(function(audio) {
                    audio.addEventListener('play', function(e) {

                        // 遍历所有音频元素
                        Array.from(audios).forEach(function(otherAudio) {
                            if (otherAudio !== e.target) {
                                console.log('暂停其他音频 (原生JS):', otherAudio.id);
                                otherAudio.pause();
                                otherAudio.currentTime = 0; // 可选：将时间重置为0
                            }
                        });
                    });

                });

                // 页面加载时检查一次
                checkOrientation();
                setContentH();
                setWrit();
                setChatH();


                loadJs(IsCheck);

            });

            function setContentH() {
                var winH = $(window).height(),
                    userInfoH = $(".user_info").height();
                var conentH = winH - userInfoH - 40;
                $(".content_inner").css("height", conentH + "px");
                return conentH;

            };

            function setWrit() {
                var conentH = setContentH();
                var thead = $(".layui-card-header").height();

                var isshow = $(".writ_bottom").css("display");
                var writBottom;
                var xx = 73;
                if (isshow == "none") {
                    writBottom = 0;
                    xx = 50;
                } else {
                    writBottom = $(".writ_bottom").height();
                }



                console.log("writBottom", writBottom);
                var writH = conentH - thead - writBottom - xx;

                $(".writ").css("height", writH + "px");
            };

            function setChatH() {
                var conentH = setContentH();
                var theadH = $(".layui-card-header").height();
                var bottomH = $(".right_bottom").height();
                var chatH = conentH - theadH - bottomH - 98;
                $(".chat-history").css("height", chatH + "px");
            };

            function getFormLit() {
                var patientId = '@Model?.Id';

                $.get({
                    url: '/ReportingManage/RehabilitationDept/GetCRFList?ResearchPatientId=' + patientId,
                    async: false, // 设置为false以使请求同步执行
                    success: function(res) {
                        if (res.code == 0) {
                            var htm = "";
                            $(".layui-collapse").html("");
                            $.each(res.data, function(index, item) {

                                htm += `<div class="layui-colla-item">
                                                                        <h2 class="layui-colla-title">`+ item.title + `</h2>
                                                                        <div class="layui-colla-content layui-show">
                                                                         <ul class="navMenu layui-card-body" lay-filter="navMenu">`;
                                var childHtm = "";

                                $.each(item.children, function(index1, child) {
                                    // console.log(index1);
                                    if (index1 == 0 && index == 0) {
                                        firstId = child.id;
                                    }
                                    childHtm += `<li class="nav_item mui-table-view-cell">
                                                      <div class="mui-slider-handle ">
                                                          <div class="flex_row align_center" >
                                                              <div class="handle_L">
                                                                  <input type="checkbox" class="crfChk" name="" data-id="` + child.id + `" data-formId="` + child.formId + `" />
                                                                </div>
                                                              <div class="handle_R"  data-businessDomain="` + child.businessDomain + `" data-id="` + child.id +
                                        `" data-formId="` + child.formId + `" data-formName="` + child.title + `">
                                                                   <div class="title_wrap">
                                                                         <p class="form_name">`+ child.title + `</p>
                                                                         <div class="tools_wrap" style="display:none;">
                                                                            <div class="tools_btn" id="btnUpload" title="上传文件"><i class="layui-icon layui-icon-upload-drag"></i></div>
                                                                            <div class="tools_btn" id="btnOperationLog" title="查看操作记录"><i class="layui-icon layui-icon-log"></i></div>
                                                                          </div>
                                                                    </div>
                                                                    <div class="layui-progress" lay-showPercent="true">
                                                                          <div class="layui-progress-bar layui-bg-blue" lay-percent="`+ child.per + `"></div>
                                                                     </div>
                                                                </div>
                                                           </div>
                                                         </div>
                                                         <div class="mui-slider-right mui-disabled">
                                                               <a class="mui-btn mui-btn-red"><i class="layui-icon icon-yingcangxinxi"></i></a>
                                                          </div>
                                                      </li>`;
                                });

                                htm += childHtm + `</ul></div></div>`;

                            })

                            htm = ` <div class="side_menu_title">
                                                                   <div>基线</div>
                                                                   <div class="search_hidden" id="ShowHidden">
                                                                    <button type="button" class="layui-btn layui-btn-xs layui-btn-primary">
                                                                    <i class="layui-icon icon-shuyi_yanjing-xianshi"></i>查看隐藏表单
                                                                    </button>
                                                                     </div></div>` + htm;

                            $(".layui-collapse").append(htm);
                            element.render('collapse');
                            // 渲染进度条组件
                            element.render('progress');

                            //报表切换事件
                            $(".navMenu").on("click", ".handle_R", function() {
                                $(".nav_item").removeClass("active");
                                $(this).parent().parent().parent().addClass("active");
                                $(".tools_wrap").css("display", "none");
                                // $(this).parent().addClass("active");
                                $(this).find(".tools_wrap").css("display", "flex");

                                var randVersion = Math.random();

                                formId = $(this).attr("data-formId");
                                id = $(this).attr("data-id");

                                var url = "@($"{ViewBag.formUrl}")" + formId + "@($".form?token={ViewBag.token}")&version=" + randVersion;

                                $("#reportingFrom").attr("src", url);
                                  $("#model_wrapR").val("");
                                  $("#txtMedical").html("");
                                   if(source!=undefined){
                           source.close();
                       }
                                $("#AIExtractJsonValue").val("");
                                $("#reportId").val("");
                                /*$("#txtMedical").val("");*/
                                $("#txtMedical").html("");
                                 $("#reportId").val("");
                                $("#result").val("");
                                $("#result2").val("");
                                audioId = 0;

                                var patientId = '@Model.Id';
                                $("#ulList").html("");
                                $("#audioList").html("");
                                getMsgAndVoice();
                                 pageIndex = 1;
                                 pageSize = 2;
                                var DelayTime = parseInt('@ViewBag.CRFDelayMinTime', 10);

                                setTimeout(function() {
                                    loadData(patientId, id);
                                }, DelayTime);
                            })

                            $(".navMenu").on('click', '#btnUpload', function(event) {
                                // event.preventDefault();
                                event.stopPropagation();
                                $("#fileName").val("");
                                $("#newFileName").val("");
                                $("#guid").val("");
                                windowsIndex = layer.open({
                                    type: 1,
                                    title: '上传文件',
                                    area: '600px',
                                    resize: true,
                                    content: $('#form_window')
                                });

                            });
                            $(".navMenu").on('click', '#btnOperationLog', function(event) {
                                event.stopPropagation();
                                var RId = '@Model.ResearchID';
                                var deptId = '@Model.HospitalDeptId';
                                var groupId = '@Model.DiseaseSpecificGroupId';
                                var url = "/OperationHistory/Index?researchId=" + RId + "&deptId=" + deptId + "&groupId=" + groupId + "&formIds=" + id;
                                parent.layui.index.openTabsPage(url, "查看操作日志");
                            });

                            $("#ShowHidden").on("click", function() {
                                layer.open({
                                    type: 1
                                    , title: '已隐藏的表单'
                                    , area: ['70%', '80%']
                                    , shade: 0.3
                                    , content: $('#hideReport')
                                    , success: function(layero, index) {
                                        var id = 0;
                                        $("#ulHideList").on("click", ".show_report", function() {
                                            $('#isShow').show();
                                            id = $(this).attr("data-id");
                                            var elem = this;
                                            $("#layui-layer-shade1").css("z-index", "9999998");
                                        })
                                        $(".Confirmation_btn").on("click", function(event) {
                                            event.stopImmediatePropagation();
                                            var obj = { id: id };
                                            $.post('/ReportingManage/RehabilitationDept/RemoveHide', obj, function(res) {
                                                if (res.code == 0) {
                                                    getHideList();
                                                    getFormLit();
                                                }
                                                else {
                                                    layer.msg("操作失败");
                                                }
                                            })
                                            $('#isShow').hide();
                                        })
                                        $(".mui-popup-button-bold").on("click", function() {
                                            $('#isShow').hide();
                                        })
                                    }
                                });

                            })
                            $(".navMenu").on('change', '.crfChk', function(event) {
                                event.stopPropagation();
                                var id = $(this).attr("data-id");

                                if (this.checked) {
                                    crformIds += "," + id;
                                } else {
                                    crformIds = crformIds.replace("," + id, "");
                                }
                            });
                            // $('.crfChk').change(function() {

                            // });

                        }
                    }
                });
            };

            getFormLit();

            //加载表单数据
            function loadData(ResearchPatientId, formId) {
                var url = "/ReportingManage/RehabilitationDept/LoadFormData?ResearchPatientId=" + ResearchPatientId + "&CRFormId=" + formId;
                $.get(url, function(res) {
                    var iframe = document.getElementById('reportingFrom');

                    if (res.code == 0) {
                        //  var iframe = document.getElementById('reportingFrom');
                        // 发送消息到子页面

                        iframe.contentWindow.postMessage({ action: "show", data: res.data }, "*");


                    }
                    else {
                        // 发送消息到子页面
                        iframe.contentWindow.postMessage({ action: "show" }, "*");
                    }
                    $("#model_wrapR").val(res.tips);

                })

            };

            //隐藏列表
            function getHideList() {
                var patientId = '@Model.Id';
                $.get('/ReportingManage/RehabilitationDept/getHideList?ResearchPatientId=' + patientId, function(res) {

                    if (res.code == 0) {
                        $("#ulHideList").html("");

                        if (res.data.length > 0) {
                            $.each(res.data, function(index, item) {
                                var htm = `<li class="nav_item flex_row" data-id="` + item.id + `"><div class="stretch" >
                                                                                                <p class="form_name">`+ item.formName + `</p>
                                                                                                <div class="layui-progress" lay-showPercent="true" style='display:none'>
                                                                                                    <div class="layui-progress-bar layui-bg-blue" lay-percent="5/10"></div>
                                                                                                </div>
                                                                                            </div >
                                                                                                    <a class="mui-btn mui-btn-red show_report" data-id="` + item.id + `">
                                                                                                                <i class="layui-icon icon-shuyi_yanjing-xianshi" data-id="` + item.id + `"></i>
                                                                                                    </a>
                                                                                        </li >`;
                                $("#ulHideList").append(htm);

                            })

                        } else {
                            var nullDataHtml = `<div class="null_wrap">
                                                                                                                    <img src="/images/datanull.png" />
                                                                                                                    <p>暂无隐藏表单</p>
                                                                                                                </div>`;
                            $("#ulHideList").append(nullDataHtml);
                        }
                    }
                })
            };
            getHideList();
            // 父窗口
            window.addEventListener('message', function(event) {
                if (event.data.action === 'save') {
                    var ResearchPatientId = '@Model.Id'
                    var formData = event.data.data;
                    var AIExtractJsonValue = $("#AIExtractJsonValue").val();
                    var reportId = $("#reportId").val();
                    var obj = {
                        ResearchPatientId: ResearchPatientId, CRFormId: id, CRFJsonValue: formData,
                        AIExtractJsonValue: AIExtractJsonValue, reportId: reportId
                    };
                    $.post('/ReportingManage/RehabilitationDept/SaveForm', obj, function(res) {
                        if (res.code == 0) {
                            getFormLit();
                            // 选择data-id为的li元素
                            var targetLi = $('div[data-id="' + id + '"]');

                            // 模拟点击事件
                            targetLi.trigger('click');

                            layer.msg("操作成功");
                        }
                        else
                            layer.msg("操作失败");

                    })

                }

            }, false);

            //右滑影藏start
            mui.init();
            $('#OA_task_1').on('tap', '.mui-btn', function(event) {
                var elem = this;
                var li = elem.parentNode.parentNode;
                // 在这个例子中，我们只关心确认按钮的回调
                mui.confirm('是否隐藏当前填报表单？', '提示', btnArray, function(e) {
                    // 这里是点击确认按钮后的回调函数
                    console.log('用户点击了确认按钮');
                    // 在这里执行你想要在点击确认后进行的操作
                    if (e.index == 0) {
                        var crformId = li.getAttribute("data-id");
                        var patid = '@Model.Id'
                        var obj = { CRFormId: crformId, ResearchPatientId: patid };
                        $.post('/ReportingManage/RehabilitationDept/HideForm', obj, function(res) {
                            if (res.code == 0) {
                                getHideList();
                                li.parentNode.removeChild(li);
                            }
                            else {
                                layer.msg(res.msg);
                            }
                        })
                    }
                }, function() {
                    // 这里是点击取消按钮后的回调函数
                    console.log('用户点击了取消按钮');
                });
            });
            var btnArray = ['确认', '取消'];
            //右滑影藏end




            //第一次默认选中第一个;
            // 选择data-id为的li元素
            var targetLi = $('div[data-id="' + firstId + '"]');
            // 模拟点击事件
            targetLi.trigger('click');
            var crformIds = "";

            var source;
            //病历文书转写
            function MedicalWrite(type) {
                var url = '/ReportingManage/RehabilitationDept/GetBLTextResult?ResearchID=@(Model.Id)&crFormIds=' + crformIds;
                if (type == "audio")
                {
                    //之前是文件语音上传后再转写，现在不上传语音也转写，改成通过聊天文字生成
                    var json = $("#result2").val();

                   // url = '/ReportingManage/RehabilitationDept/GetBLTextResultByReportId?reportId=' + audioId ;
                    url = '/ReportingManage/RehabilitationDept/GetBLTextResultByReportId?reportId=0&words=' + encodeURIComponent(json);
                }
                var i = 0;
                var allData = "";
                source = new EventSource(url);
                source.onmessage = function(event) {
                    var result = JSON.parse(event.data);
                    if (result.okMsg) {
                        $("#recordsBtn").click();
                        //var btnloading = document.getElementById("txtMedical");
                        //if (i == 0) {
                        //    var Info = result.data;
                        //    btnloading.value = Info;
                        //}
                        //else {
                        //    var Info = result.data;
                        //    btnloading.value += Info;
                        //}
                        //i = i + 1;
                        allData += result.data;
                        var htmlContent = marked.parse(allData);
                        $("#txtMedical").html(htmlContent);

                        var div = $('#txtMedical');
                        div.scrollTop(div[0].scrollHeight - div.innerHeight());

                    }
                    else {
                        layer.msg(result.errorMsg);
                        source.close();
                    }
                };

                source.addEventListener('end', function(event) {
                    var result = JSON.parse(event.data);
                    $("#btnMedicalWrite").removeClass('disabled');
                    $("#btnFill").removeClass('disabled');
                    if (result.okMsg) {


                    }
                    else {
                        layer.msg(result.errorMsg);
                    }
                    source.close();
                }, false);

                source.onerror = function(event) {
                    $("#btnMedicalWrite").removeClass('disabled');
                    $("#btnFill").removeClass('disabled');
                    source.close();
                };
            };

            upload.render({
                elem: '#UploadFile'
                , url: '/ReportingManage/RehabilitationDept/UploadFile' // 实际使用时改成您自己的上传接口即可。
                , accept: 'file' // 限制为图片类型
                , acceptMime: 'image/jpg,image/png,image/jpeg,application/pdf' // 限制图片扩展名
                , done: function(res) {
                    layer.msg('上传成功');

                    // console.log(res)
                    if (res.code == 0) {
                        $("#fileName").val(res.data.fileName);
                        $("#guid").val(res.data.guid);
                        $("#newFileName").val(res.data.newFileName);

                    }
                    else
                        layer.msg(res.msg);
                }
            });

            //监听提交
            form.on('submit(submit)', function(data) {
                var indexs = layer.load(1);
                data.field.newFileName = $("#newFileName").val();
                data.field.guid = $("#guid").val();
                data.field.CRFormId = id;
                data.field.RId = '@Model.Id';
                $.ajax({
                    url: "/ReportingManage/RehabilitationDept/SaveFile",
                    type: "post",
                    data: data.field,
                    datatype: 'json',
                    success: function(result) {
                        layer.close(indexs);
                        if (result.code == 0) {

                            layer.close(windowsIndex);
                            loadTree();
                        }
                        else
                            layer.msg(result.msg);

                    }, error: function(res) {
                        layer.msg("加载统计信息错误：" + res.responseText);
                        layer.close(indexs);
                    }
                });
                return false;
            });

            //根据上传的报告文档解析
            var timer;
            function AIExtract() {
                 loadIndex = layer.open({
                    type: 1,
                    area: ['600px', '49px'],
                    shade: 0.1,
                    closeBtn: 0,
                    resize: false,
                    title: false,
                    content: $("#form_window_load"),
                    success: function() {
                        // $("#model_wrapL").val(modelText);
                        element.progress('demo-filter-progress', '0%');
                        load();
                    }
                });
                var patientId = '@Model.Id';
                $.post('/ReportingManage/RehabilitationDept/NewAIExtract', { ResearchPatientId: '@Model.Id', CRFormId: id, "tips": $("#model_wrapR").val() }, function(res) {

                    element.progress('demo-filter-progress', '100%');
                    clearInterval(timer);
                    if (res.code == 0) {

                        $("#AIExtractJsonValue").val(res.data);
                        $("#reportId").val(res.reportId);

                        var iframe = document.getElementById('reportingFrom');
                        // 发送消息到子页面
                        iframe.contentWindow.postMessage({ data: res.data }, "*");
                    }
                    else
                        layer.msg(res.msg);
                    layer.close(loadIndex);
                    element.progress('demo-filter-progress', '0%');
                })
            };

            //
            function AIExtractByAudio() {
                //之前是文件语音上传后再转写，现在不上传语音也转写，改成通过聊天文字生成
                var json = $("#result2").val();
                // url = '/ReportingManage/RehabilitationDept/GetBLTextResultByReportId?reportId=' + audioId ;
                loadIndex = layer.open({
                    type: 1,
                    area: ['600px', '49px'],
                    shade: 0.1,
                    closeBtn: 0,
                    resize: false,
                    title: false,
                    content: $("#form_window_load"),
                    success: function () {
                        // $("#model_wrapL").val(modelText);
                        element.progress('demo-filter-progress', '0%');
                        load();
                    }
                });
                var patientId = '@Model.Id';
                $.post('/ReportingManage/RehabilitationDept/AIExtractByAudio', { "ResearchPatientId": '@Model.Id', "CRFormId": id, "words": json, "tips": $("#model_wrapR").val() }, function (res) {

                    element.progress('demo-filter-progress', '100%');
                    clearInterval(timer);
                    if (res.code == 0) {

                        $("#AIExtractJsonValue").val(res.data);
                        $("#reportId").val(res.reportId);

                        var iframe = document.getElementById('reportingFrom');
                        // 发送消息到子页面
                        iframe.contentWindow.postMessage({ data: res.data }, "*");
                    }
                    else
                        layer.msg(res.msg);
                    layer.close(loadIndex);
                    element.progress('demo-filter-progress', '0%');
                })
            };

            function load() {
                var n = 0;
                timer = setInterval(function() {
                    n = n + Math.random() * 10 | 0;
                    if (n > 99) {
                        n = 99;
                        clearInterval(timer);

                    }
                    element.progress('demo-filter-progress', n + '%');
                }, 300 + Math.random() * 4000);
            };

           // let timerId;
           // let wenshutimerId;
           let intervalIds = [];
            function Record() {
                btnControl.onclick = function() {
                    if (btnStatus === "UNDEFINED" || btnStatus === "CLOSED") {
                        $("#ulList").html("");
                        if (IsCheck) {
                            var sign = "";
                            var timestamp = Math.floor(new Date().getTime() / 1000);
                            $.ajax({
                                url: "/ReportingManage/RehabilitationDept/GetSHA256Hash?appkey=" + API_KEY + "&timestamp=" + timestamp + "&secret=" + APPID,
                                type: "get",
                                async: false,
                                success: function(res) {
                                    console.log(res);
                                    sign = res;
                                }
                            });
                            connectWebSocket("result", sign, timestamp);
                        }
                        else
                            connectWebSocket("result");
                        startSoundRecording();
                        //定时把语音交给AI区分语音
                       // timerId = setInterval(resultSplitByAI, parseInt(setIntervalTime) * 1000);
                                        intervalIds.push(setInterval(() => {
          resultSplitByAI();
        }, parseInt(setIntervalTime) * 1000));

                        //定时转写病历文书
                       // wenshutimerId = setInterval(AutoMedicalWrite,30 * 1000);
                       //  intervalIds.push(AutoMedicalWrite,30 * 1000);
                         intervalIds.push(setInterval(() => {
                             AutoMedicalWrite();
						 }, parseInt(autoMedicalIntervalTime) * 1000));

                    } else if (btnStatus === "CONNECTING" || btnStatus === "OPEN") {
                        // 结束录音
                      //  clearInterval(timerId);
                      //  clearInterval(wenshutimerId);
                      intervalIds.forEach(id => clearInterval(id)); // 清除所有定时器
                       if(source!=undefined){
                           source.close();
                       }
                        RecordStop();
                        SoundRecorder.stop();
                         AutoMedicalWrite();
                       // SendLastMsg();
                       //大模型调用成功再上传
                        var words = $("#result").val();
                        if (words) {

                            $.ajax({
                                url: '/ReportingManage/RehabilitationDept/resultSplitByAI',
                                type: 'POST',
                                async: false, // 设置为同步请求
                                data: { "words": $("#result2").val() },
                                success: function (res) {
                                    // 请求成功后的回调函数
                                    if (res.code == 0) {
                                        $("#ulList").html("");
                                        if (res.data) {
                                            $("#result2").attr("data-AIResult", res.data);
                                            var arr = res.data.split("\n");
                                            $.each(arr, function (index, item) {
                                                if (item.indexOf("医生：") > -1)
                                                    $("#ulList").append('<li class="clearfix"> <div class="message other-message float-right">' + item + '</div></li>');
                                                else
                                                    $("#ulList").append('<div class="message my-message">' + item + '</div>');
                                            });
                                        };
                                        uploadVoice();
                                        scrollToBottom();
                                    }
                                    else
                                      layer.msg(res.msg);

                                },
                                error: function (xhr, status, error) {
                                    // 请求失败后的回调函数
                                }
                            });
                            $("#result").val("");
                        }
                        else
                            uploadVoice();


                        // SoundstopRecord("Home/Upload", "Record.wav")
                    }
                };
            };

            Record();


            function uploadVoice() {
                var result = $("#result2").val();
                var AIResult = $("#result2").attr("data-AIResult");
                if (result) {


                    var formData = new FormData();

                    formData.append('file', SoundRecorder.getBlob());
                    formData.append('txt', $("#result2").val());
                    formData.append('AIResult', AIResult);
                    formData.append('RId', '@Model.Id');
                    formData.append('CRFormId', id);
                    formData.append('pageSize', pageSize * pageIndex);
                    fetch("/ReportingManage/RehabilitationDept/UploadAudio", {
                        method: 'POST',
                        body: formData
                    }).then(response => response.json())
                        .then(data => {
                            //$("#ulList").html("");
                            $("#audioList").html("");
                           // fillMsg(data.data);
                            fillVoice(data.reportList);
                            audioId = data.reportId;
                            setChatH();
                            $("#result").val("");
                            $("#result2").attr("data-AIResult", "");
                        }
                            )
                        .catch(error => console.error(error));


                }
                else {
                    layer.msg("对话文字不能为空！");
                    $("#result").focus();
                }


            };


            function getMsgAndVoice() {

                $.post("/ReportingManage/RehabilitationDept/getMsgAndVoice", {
                    RId: '@Model.Id', CRFormId: id, pageIndex:pageIndex,pageSize: pageSize
                },
                    function(res) {
                        if (res.code == 0) {
                            if (res.data.length > 0) {
                                fillMsg(res.data[0].reportContent);
                                fillVoice(res.data);

                                //切换表单时 默认选中第一个
                               $('#audioList li:first-child button[class*="look"]').click();
                            }
                            setChatH();
                        }
                    });
            };

            function fillMsg(data)
            {
                // $("#ulList").html("");
                // $("#audioList").html("");
               // var arr = data.split("\n").filter(Boolean);
                if (data&&data!="null") {
                    var arr = data.split("\n");
                    $.each(arr, function (index, item) {
                        if (item.indexOf("医生：") > -1)
                            $("#ulList").append('<li class="clearfix"> <div class="message other-message float-right">' + item + '</div></li>');
                        else
                            $("#ulList").append('<div class="message my-message">' + item + '</div>');
                    });
                    setChatH();
                    scrollToBottom();
                }



            };

            function fillVoice(list)
            {
                var audioHtm = "";
                $.each(list, function (index, item) {

                    audioHtm += ` <li >
                                    <div class="audio_item">
                                        <div class="audio_title">`+ item.createdTime + `</div>
                                          <audio id="audio2" controls>
                                              <source src="`+ item.reportURL + `" type="audio/mpeg" />设置不支持音频文件
                                           </audio>
                                   </div>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-primary look" title="查看" data-id='` + item.id + `' data-msg='` + item.reportContent + `'>
                                          <i class="layui-icon icon-shuyi_yanjing-xianshi"></i>
                                   </button>
                                  <button type="button" class="layui-btn layui-btn-sm layui-btn-primary del" title="删除语音" data-id='` + item.id + `'>
                                                  <i class="layui-icon layui-icon-delete"></i>
                                  </button>
                                </li>`;
                })
                $("#audioList").append(audioHtm);


            };

            var audioId = 0;
            $("#audioList").on("click", "button", function (event) {
                event.stopPropagation();
                var aid = parseInt($(this).attr("data-id"));
                if ($(this).hasClass('look')) {
                    $("#audioList").find("li").css("background-color", "#fff");
                    $(this).parent().css("background-color", "#ddd");
                    var msg = $(this).attr("data-msg");
                    $("#ulList").html("");
                    fillMsg(msg);
                    $("#result2").val(msg);
                    //20250221 .当切换语音列表时，切换完毕后自动执行语音转写病历或crf表单填充功能；
                      var btnId = $(".nav_btn_active").attr("id");
                       if(source!=undefined){
                           source.close();
                       }
                    if (btnId == "recordsBtn") {
                        //转写病历文书
                      //  var $this = $(this);
                       // if ($this.hasClass('disabled'))
                         //   return;
                        //禁用按钮并添加样式或类以显示它现在不可用
                       // $this.addClass('disabled');
                        $("#btnMedicalWrite").addClass('disabled');
                        MedicalWrite("audio")
                    }
                    else if (btnId == "baselineBtn") {
                        //根据语音填写表单
                        AIExtractByAudio();
                    }
                    else
                        layer.msg("请选择左侧记录表或者病历文书！");
                } else if ($(this).hasClass('del')) {
                    // 处理删除逻辑


                    layer.confirm('确定要删除此语音文件吗？', {
                        title: '',
                        btn: ['确定', '取消'], //按钮
                        resize: false
                    }, function(index) {
                        $.post('/ReportingManage/RehabilitationDept/DelVoice', { "Id": aid }, function (res) {
                            if (res.code == 0) {
                                if (aid == audioId) {
                                    $("#result2").val("");
                                }
                                $("#ulList").html("");
                                $("#audioList").html("");
                                getMsgAndVoice();
                                layer.msg("删除成功！");

                            }
                            else
                                layer.msg(res.msg);
                        })
                        layer.close(index);
                    });
                }
            });


            function getCurrentFormattedTime() {
                var now = new Date(); // 创建一个Date对象，表示当前时间
                var year = now.getFullYear(); // 获取当前年份
                var month = addZero(now.getMonth() + 1); // 获取当前月份，注意月份从0开始计数
                var day = addZero(now.getDate()); // 获取当前日
                var hour = addZero(now.getHours()); // 获取当前小时
                var minute = addZero(now.getMinutes()); // 获取当前分钟
                var second = addZero(now.getSeconds()); // 获取当前秒

                // 返回格式化的字符串
                return year + '/' + month + '/' + day + ' ' + hour + ':' + minute + ':' + second;
            };

            // 辅助函数，确保时间部分是两位数字
            function addZero(i) {
                if (i < 10) {
                    return '0' + i;
                }
                return i;
            };

            //把最后的语音填充到对话框
            function SendLastMsg() {
                var res = $("#result").val();
                if (res) {
                    // var arr = splitStringBySpeakers(res);
                    // arr.forEach((part, index) => {
                    //     if (part.indexOf('发言人2：') > -1) {
                    //         $("#ulList").append('<li class="clearfix"> <div class="message other-message float-right">' + part + '</div></li>');
                    //     }
                    //     else {
                    //         $("#ulList").append('<div class="message my-message">' + part + '</div>');
                    //     }

                    //     scrollToBottom();
                    // });
                    resultSplitByAI();
                    $("#result").val("");
                }

            };


            //切换语音
            form.on('switch(switchTest)', function(data) {
                //Loading层

                // var loadindex = layer.load(1, {
                //     shade: [0.1, '#fff'], //0.1透明度的白色背景,
                //     content:"语音正在切换中，请稍后！"
                // });
                IsCheck = this.checked;
                if (btnStatus === "CONNECTING" || btnStatus === "OPEN")
                {
                  //  clearInterval(timerId);
                  //  clearInterval(wenshutimerId);
                   intervalIds.forEach(id => clearInterval(id)); // 清除所有定时器
                    RecordStop();
                    SoundRecorder.stop();
                }


                var existingScript = document.getElementById('yuyin');
                if (existingScript) {
                    document.head.removeChild(existingScript);
                };
                $.post('/ReportingManage/RehabilitationDept/GetKey', { 'isCheck': IsCheck }, function(res) {
                    API_KEY = res.appkey;
                    APPID = res.appsecret;
                });
                $("#result").val("");
                $("#result2").val("");
                $("#ulList").html("");
                loadJs(this.checked);
              //  layer.close(loadindex);
            });


            function loadJs(IsCheck)
            {

                var kdxf = "/KeDaXunFei/rtasr/XunFeiRecord.js?v=" + Math.random();
                var yzs = "/YunZhiSheng/rtasr/yunzhisheng.js?v=" + Math.random();
                var script = document.createElement('script');
                if (IsCheck)
                    script.src = yzs;
                else
                    script.src = kdxf;
                script.type = 'text/javascript';
                script.id = "yuyin";

                document.head.appendChild(script);
            };

            function splitStringBySpeakers(inputString) {
                // 修改后的正则表达式，匹配发言人1到100及其后的内容，并捕获发言人标记
                // 使用非贪婪匹配 .*? 确保每个发言人后面的内容只匹配到下一个发言人标签之前
                const regex = /(?:发言人\d{1,3}：)(.*?)(?=发言人\d{1,3}：|$)/g;
                let match;
                const parts = [];
                // 循环匹配正则表达式，直到没有更多匹配项
                while ((match = regex.exec(inputString)) !== null) {
                    // 将匹配到的发言人标记和内容作为一个数组元素
                    // 注意这里使用 match[0] 而不是 match[1]，因为 match[0] 包含整个匹配的结果
                    parts.push(match[0]);
                }
                return parts;
            };

            function scrollToBottom() {
                var div = $('#ulList');
                div.scrollTop(div[0].scrollHeight - div.innerHeight());
            }

            //定时把文本框里面的值，丢给大模型处理再丢到对话框
            function resultSplitByAI()
            {
                var result2 = $("#result2").val();
                $("#result").val("");
                $.post('/ReportingManage/RehabilitationDept/resultSplitByAI', { "words": result2 }, function(res) {
                   if(res.code==0)
                   {
                       $("#ulList").html("");
                       if (res.data)
                       {
                           $("#result2").attr("data-AIResult", res.data);
                           var arr = res.data.split("\n");
                           $.each(arr, function (index, item) {
                                if (item.indexOf("医生：") > -1)
                                    $("#ulList").append('<li class="clearfix"> <div class="message other-message float-right">' + item + '</div></li>');
                                else
                                    $("#ulList").append('<div class="message my-message">' + item + '</div>');
                           });
                       }
                       scrollToBottom();
                   }
                })
            }

            // 20250217 增加语音定时自动转写病历文书  表单语音结束才自动填写
            function AutoMedicalWrite()
            {
                 var btnId = $(".nav_btn_active").attr("id");
                 if (btnId == "recordsBtn")
                 {
                        MedicalWrite("audio");
                       // $('#baselineBtn').css('pointer-events', 'auto');
                    }
                    else if (btnId == "baselineBtn"&&(btnStatus === "UNDEFINED" || btnStatus === "CLOSED")) {
                       AIExtractByAudio();
                    }
            }

        });
</script>

</body>
<div class="window_wrap" id="form_window" style="display: none;">
    <form class="layui-form" lay-filter="fm" id="fm" action="">

        <div class="layui-form-item" id="div10" style="margin-top:15px;">
            <label class="layui-form-label" style="width:124px;">上传附件：</label>
            <div class="layui-input-block line_wrap" style="margin-left:130px;">
                <div class="layui-upload-drag" id="UploadFile">
                    <i class="layui-icon"></i>
                    <p>点击上传，或将文件拖拽到此处</p>
                    <div class="layui-hide" id="uploadDemoView">
                        <hr>
                        <input type="hidden" id="newFileName" />
                        <input type="hidden" id="guid" />
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="width:124px;">文件名称：</label>
            <div class="layui-input-block" style="width:300px;margin-left:130px;">
                <input type="text" id="fileName" class="layui-input" name="fileName" />

            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left:130px;">
                <button type="submit" class="layui-btn" lay-submit lay-filter="submit">提交</button>
                <button type="reset" class="layui-btn layui-btn-primary" id="btn_reset">重置</button>
            </div>
        </div>
    </form>
</div>

<div class="window_wrap" id="form_window_Img" style="display: none;">
    <form class="layui-form">

        <div class="layui-form-item">
            <div id="fileList">
            </div>
        </div>

    </form>
</div>

<div class="window_wrap" id="form_window_load" style="display: none;background-color:transparent">
    <div class="layui-progress layui-progress-big" lay-showPercent="true" lay-filter="demo-filter-progress">
        <div class="layui-progress-bar" lay-percent="0%">
        </div>

    </div>
    <p style="text-align:center"> AI数据提取中...</p>
</div>
</html>
