#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\Pilot\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "4d9df921290ab2f495fbe16897cfdf64bd6cb9f13c4ecb422972cadb71e21727"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_Demo_Views_Pilot_Index), @"mvc.1.0.view", @"/Areas/Demo/Views/Pilot/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"4d9df921290ab2f495fbe16897cfdf64bd6cb9f13c4ecb422972cadb71e21727", @"/Areas/Demo/Views/Pilot/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_Demo_Views_Pilot_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/dialog/style.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("type", new global::Microsoft.AspNetCore.Html.HtmlString("text/javascript"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/echarts/echarts.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/echarts/theme/walden.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\Pilot\Index.cshtml"
  
    ViewBag.Title = "领航员";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"zh\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4d9df921290ab2f495fbe16897cfdf64bd6cb9f13c4ecb422972cadb71e217275928", async() => {
                WriteLiteral("\r\n    <meta charset=\"UTF-8\">\r\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>领航员</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "4d9df921290ab2f495fbe16897cfdf64bd6cb9f13c4ecb422972cadb71e217276419", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "4d9df921290ab2f495fbe16897cfdf64bd6cb9f13c4ecb422972cadb71e217277621", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4d9df921290ab2f495fbe16897cfdf64bd6cb9f13c4ecb422972cadb71e217278824", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4d9df921290ab2f495fbe16897cfdf64bd6cb9f13c4ecb422972cadb71e2172710034", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        body {
            background-color: #fafafa;
            overflow: hidden;
        }

        .userwrap {
            padding: 10px 15px;
            background-color: #7a4d7b;
            border-bottom: 1px solid #ccc;
            background-image: url(""/images/logo.png"");
            background-repeat: no-repeat;
            background-size: contain;
        }

        .row_wrap {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
        }

        .dotwrap {
            margin-right: 5px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            border: 1px solid #ccc;
            padding: 2px;
            background-color: transparent;
        }

        .doc {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background-color: #ccc;
        }

        .doc_check {
            ");
                WriteLiteral(@"color: #FFB800;
            font-weight: bold;
        }

        .type1 {
            margin-right: 10px;
            color: #7a4d7b;
        }

        .userinfo {
            display: flex;
            flex-direction: row;
            align-items: center;
            color:#fff;
        }

        .userinfo_val {
            padding: 0 5px;
        }

        .prompt {
            margin: 5px 15px;
            display: flex;
            flex-direction: row;
            align-items: center;
        }

        .operating {
            width: 100%;
            position: fixed;
            bottom: 0;
        }

        .input_wrap {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin: 5px 15px;
            background-color: #fff;
            border: 1px solid #7a4d7b;
            border-radius: 6px;
            overflow: hidden;
        }

            .input_wrap .layui-icon {
                color: #7a4d7b");
                WriteLiteral(@";
                padding: 12px;
            }

            .input_wrap .layui-input {
                border: none;
                padding-left: 0;
            }

                .input_wrap .layui-input:hover {
                    border: none;
                }

        .layui-icon-release {
            background-color: #7a4d7b;
            color: #fff !important;
            cursor: pointer;
        }
        /*对话去区*/
        .dialog_box {
            padding: 10px;
            overflow-y: auto;
         
            font-size: 20px;
        }

        .quizzer {
            padding: 10px 0;
            display: flex;
            justify-content: flex-end;
        }

        .text_wrap {
            width: 80%;
            background-color: #7a4d7b;
            color: #fff;
            padding: 10px;
            border-radius: 6px 0 6px 6px;
        }

        .answer {
            width: 100%;
        }

        .answer_inner {
            min-height: 100px;");
                WriteLiteral(@"
            padding: 10px;
            background-color: #fff;
            border-radius: 6px;
        }
        .layui-form-select dl dd.layui-this {
            background-color: #7a4d7b;
        }

        /*知识库*/
        .zrk {
            height: 0;
            background-color: #fff5ff;
            border-radius: 6px;
            overflow: hidden;
        }
            .zrk li {
                background-color: #fff;
                margin:10px;
                padding: 10px;
                border-radius: 6px;
            }
            .zrk p {
                padding: 10px;
            }
        .show_zsk {
            height: auto;
        }
        .hk{margin:10px 0 5px;}
        .fragment_head{
            display:flex;
            flex-direction:row;
            justify-content:space-between;
            align-items:center;
            padding-bottom:15px;
        }
        .vector{font-size:14px;}
        .vector i {
            color: #7a4d7b;
           ");
                WriteLiteral(" padding-right:5px;\r\n        }\r\n        .fragment_text {\r\n            font-family: \"Microsoft YaHei\", Arial, \"Helvetica Neue\", sans-serif !important;\r\n        }\r\n    </style>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4d9df921290ab2f495fbe16897cfdf64bd6cb9f13c4ecb422972cadb71e2172716379", async() => {
                WriteLiteral(@"
    <div class=""chat layui-form"">
        <div class=""userwrap row_wrap"">
            <div class=""userinfo"">
                <div style=""width:150px;""></div>
                <div class=""userinfo_val doc_check"">黄一波</div>
                <div class=""userinfo_val"">男</div>
                <div class=""userinfo_val"">70岁</div>
            </div>
            <div class=""dischargeSummary"">

                <label class=""layui-form-label"" style=""color:#fff;"">切换智能体</label>
                <div class=""layui-input-block"">
                    <select id=""Agent"" name=""Agent"" lay-filter=""Agent"" lay-verify=""required"">
                     
                      
                    </select>
                </div>
            </div>
        </div>
        <div class=""operating layui-form"">
            <div class=""prompt"">
                <div>你可以和我说：</div>
                <button type=""button"" class=""layui-btn layui-btn-primary layui-btn-xs"">今日待办事项</button>
                <button type=""button"" class=");
                WriteLiteral(@"""layui-btn layui-btn-primary layui-btn-xs"">需要重点关注的患者</button>
            </div>
            <div class=""input_wrap"">
                <div class=""layui-input-split layui-input-prefix"">
                    <i class=""layui-icon layui-icon-dialogue""></i>
                </div>
                <input type=""text"" placeholder=""请输入···"" class=""layui-input"" id=""Input"">
                <i class=""layui-icon layui-icon-release"" id=""InputBtn""></i>
            </div>
        </div>

        <div class=""dialog_box"">
            <!--提问区-->
");
                WriteLiteral("\r\n            <!--回答区-->\r\n");
                WriteLiteral("            \r\n        </div>\r\n\r\n    </div>\r\n\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4d9df921290ab2f495fbe16897cfdf64bd6cb9f13c4ecb422972cadb71e2172718484", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
        layui.use(['jquery', 'layer', 'tree', 'form', 'element', 'upload', 'laytpl', 'table'], function () {
            var layer = layui.layer,
                $ = layui.$,
                form = layui.form,
                laytpl = layui.laytpl,
                element = layui.element,
                tree = layui.tree,
                upload = layui.upload,
                table = layui.table;

            $(window).resize(function () {
                setDialogBoxH();
            });



            $(document).ready(function () {
                  $.ajax({
                      url: ""/Demo/Pilot/GetAgentInfo"",
                       type: ""get"",
                  
                      success: function (res) {
                          if (res.length > 0) {
                              $(""#Agent"").html('');
                          
                            for (var i = 0; i < res.length; i++) {
                                $(""#Agent"").append('<option va");
                WriteLiteral(@"lue=""' + res[i].appId + '"">' + res[i].agentName + '</option>');
                            }
                            form.render();
                        } else {
                            layer.msg(res.msg);
                        }
                    }
                });
            });

            form.on('select(Agent)', function (data) {

                $("".dialog_box"").html("""");


            });


            function setDialogBoxH() {
                var winH = $(window).height();
                var userwrapH = $("".userwrap"").height();
                var operatingH = $("".operating"").height();
                var DialogBox = winH - (userwrapH + operatingH)-40;
                $("".dialog_box"").css(""height"", DialogBox + ""px"");
            };
            setDialogBoxH();

            let input_p = document.getElementById(""Input"");


            document.onkeydown = function () {
                if (event.keyCode == 13) {
                    GetAgentSpeech();");
                WriteLiteral(@"
                }
            }

            function ShowAgent(result) {
                var nNode = document.getElementById('zsk_wrap' + result.Id);
                nNode.style.display = """";
                setTimeout(function () {
                    $('#ss' + result.Id).find("".isload"").removeClass(""layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"");
                    $('#ss' + result.Id).find("".isload"").addClass('layui-icon-ok-circle');
                }, 3000)

                $('#Azrk' + result.Id).append(AgentResult(result.AgentResult));
                var flage = 0;
                $('#ss' + result.Id).on('click', function (event) {
                    if (flage == 0) {
                        $(this).find("".isdown"").removeClass(""layui-icon-down"");
                        $(this).find("".isdown"").addClass(""layui-icon-up"");

                        flage = 1
                    } else {
                        $(this).find("".isdown"").removeClass(""layui-icon-up"");
");
                WriteLiteral(@"
                        $(this).find("".isdown"").addClass(""layui-icon-down"");
                        flage = 0;
                    }

                    $('#Azrk' + result.Id).toggleClass(""show_zsk"");

                });
            }
            function GetAgentSpeech1() {
                if ($.trim($(""#Input"").val()) == """") {
                    layer.msg(""请输入您要说的内容！"");
                    return;
                }


                var htm = ` <div class=""quizzer""><div class=""text_wrap"" id = ""answer"">`;


                htm += ` <p>` + $(""#Input"").val() + `</p> </div></div>`;
                $("".dialog_box"").append(htm);

                var indexs = layer.load();
                $.ajax({
                    url: '/Demo/Pilot/GetAgentSpeech',
                    type: ""post"",

                    data: {
                        Speech: $(""#Input"").val(),
                        xAppID: $(""#Agent"").val(),

                    },
                    success: function (eve");
                WriteLiteral(@"nt) {
                        var result = JSON.parse(event);
                        if (result.code == 100) {
                            if (result.chart && result.chart.data_value) {
                                mtype = result.chart.type;
                                if (mtype == ""chart"") {

                                    mdata_value = JSON.parse(result.chart.data_value);
                                    mstyle_value = JSON.parse(result.chart.style_value);
                                    var mChartDiv = ""<div class='answer_inner' style='width: 100%; height: 100%;' id='"" + result.chart.id + ""'></div>"";
                                    var htm1Result1 = `<div class=""answer""> ` + mChartDiv + ""  </div> "";
                                    $("".dialog_box"").append(htm1Result1);
                                    showFormComposition(result.chart.id, mdata_value, mstyle_value)
                                }


                            }
                            els");
                WriteLiteral(@"e {
                                var htm1Result = `<div class=""answer""><div class=""answer_inner"">`;
                                htm1Result += result.msg + "" </div></div> "";

                                $("".dialog_box"").append(htm1Result);
                            }


                        }
                        else {
                            var htm1Result = `<div class=""answer""><div class=""answer_inner"">`;
                            htm1Result += result.msg + "" </div></div> "";

                            $("".dialog_box"").append(htm1Result);
                        }

                        layer.close(indexs);
                    }, error: function () {
                        layer.close(indexs);
                        layer.msg(""获取失败！"");
                    }
                })
                $(""#Input"").val("""");
            }
            function GetAgentSpeech() {
                if ($.trim($(""#Input"").val()) == """") {
                    layer.msg(""请输");
                WriteLiteral(@"入您要说的内容！"");
                    return;
                }

                var htm = ` <div class=""quizzer""><div class=""text_wrap"" id = ""answer"">`;


                htm += ` <p>` + $(""#Input"").val() + `</p> </div></div>`;
                $("".dialog_box"").append(htm);
                var i = 0;
                source = new EventSource('/Demo/Pilot/GetAgentSpeech?Speech=' + $(""#Input"").val() + '&xAppID=' + $(""#Agent"").val());
                $(""#Input"").val("""");
                source.onmessage = function (event) {
                   // console.log(event.data);
                    var result = JSON.parse(event.data);

                    if (result.code == 100) {
                        var id = ""A"" + result.Id;
                        if (result.chart && result.chart.data_value) {
                            mtype = result.chart.type;
                            if (mtype == ""chart"") {

                                if (i > 0) {
                                    mdata_value = JSON.p");
                WriteLiteral(@"arse(result.chart.data_value);
                                    mstyle_value = JSON.parse(result.chart.style_value);
                                    var mChartDiv = ""<div class='answer_inner' style='width: 100%; min-height: 300px;' id='"" + result.chart.id + ""'></div>"";
                                    var htm1Result1 = `<div class=""answer""> ` + mChartDiv + ""  </div> "";
                                   // $("".dialog_box"").append(htm1Result1);
                                    var btnloading = document.getElementById(id);


                                    //$(""#"" + result.chart.id).css(""min-height"",""400px"");

                                    btnloading.innerHTML += htm1Result1;
                                    showFormComposition(result.chart.id, mdata_value, mstyle_value)
                                }
                                else {
                                    mdata_value = JSON.parse(result.chart.data_value);
                                    mstyle_");
                WriteLiteral(@"value = JSON.parse(result.chart.style_value);
                                    var mChartDiv = ""<div class='answer_inner' style='width: 100%; min-height: 300px;' id='"" + result.chart.id + ""'></div>"";
                                    var htm1Result1 = `<div class=""answer""> ` + mChartDiv + ""  </div> "";
                                  //  $(""#"" + result.chart.id).css(""min-height"",""400px"");
                                    $("".dialog_box"").append(htm1Result1);
                                    showFormComposition(result.chart.id, mdata_value, mstyle_value)
                                }

                            }


                        }
                        else {

                            if (i == 0) {
                                var htm1Result = `<div class='answer'><div class='answer_inner' >
                                                    <div class='zsk_wrap' style='display:none' id='zsk_wrap` + result.Id + `'>
                                           ");
                WriteLiteral(@"             <div class='layui-btn layui-btn-primary layui-btn-sm hk' id='ss` + result.Id + `' ><i class='isload layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop' ></i> 知识库知识  &nbsp; &nbsp; <i class='layui-icon layui-icon-down isdown'></i>  </div>
                                                        <ul class='zrk' id='Azrk` + result.Id + `'>

                                                        </ul>
                                                    </div>
                                                    <pre class='fragment_text' id='A` + result.Id + `'></pre>

                                                    </div></div> `;


                                /*  <p id='zsk` + result.Id + `'></p>*/
                                   //<li>
                                                            //    <div class='fragment_head'>
                                                            //         <h5>片段1</h5>
                                         ");
                WriteLiteral(@"                   //         <div><i class='layui-icon layui-icon-engine'></i>向量评分：<span>83.5</span></div>
                                                            //    </div>
                                                            //    <pre class='fragment_text'>互操作信息平台\r\n二、互操作平台概念与意义</pre>
                                                            //</li>
                                                            // <li>
                                                            //    <div class='fragment_head'>
                                                            //         <h5>片段2</h5>
                                                            //         <div><i class='layui-icon layui-icon-engine'></i>向量评分：<span>83.5</span></div>
                                                            //    </div>
                                                            //    <pre class='fragment_text'>aaaaaa</pre>
                                                            //</li>
");
                WriteLiteral(@"



                                $("".dialog_box"").append(htm1Result);

                                var btnloading = document.getElementById(id);
                              //  var zsk = document.getElementById('zsk' + result.Id);

                                //if (result.AgentResult) {
                                //    var nNode = document.getElementById('zsk_wrap' + result.Id);
                                //    nNode.style.display = """";

                                //    zsk.innerHTML = result.AgentResult;

                                //}

                                if (result.isAgentResult) {

                                    ShowAgent(result);
                                }
                                var Info = result.msg;

                                btnloading.innerHTML = Info;




                            }
                            else {
                                var btnloading = document.getElementById(id);
   ");
                WriteLiteral(@"                          //   var zsk = document.getElementById('zsk' + result.Id);
                                //if (result.AgentResult) {

                                //    var nNode = document.getElementById('zsk_wrap' + result.Id);
                                //    nNode.style.display = """";
                                //    zsk.innerHTML += result.AgentResult;
                                //}

                                if (result.isAgentResult) {
                                    ShowAgent(result);
                                }
                                var Info = result.msg;
                                btnloading.innerHTML += Info;


                            }
                            i = i + 1;

                        }


                    }
                    else {
                        layer.msg(result.msg);
                        source.close();
                    }
                };

                source.addEvent");
                WriteLiteral(@"Listener('end', function (event) {
                    var result = JSON.parse(event.data);
                    if (result.code=100) {
                        layui.each(result.msg, function (idx, item) {

                        });

                    }
                    else {
                        layer.msg(result.msg);
                    }
                    source.close();
                }, false);

                source.onerror = function (event) {
                    source.close();
                };
            }

            function AgentResult(slices) {



                var mResult = """";
                for (var i = 0; i < slices.length; i++) {
                    var mstr = ` <li>
                    <div class='fragment_head'>
                        <h5>片段${i+1}</h5>
                        <div class='vector'><i class='layui-icon layui-icon-engine'></i>${slices[i].source}:<span>${slices[i].score}</span></div>
                    </div>
                ");
                WriteLiteral(@"    <pre class='fragment_text'>${slices[i].text}</pre>
                     </li>`;
                    mResult = mResult + mstr

                }
                return mResult;

            }
            $(""#InputBtn"").on(""click"", function () {

                GetAgentSpeech();
            })

        })
        function showFormComposition(id, data_value, style_value ) {
            // 基于准备好的dom，初始化echarts实例

            var myChart = echarts.init(document.getElementById(id), 'walden');

            var xName = style_value.encode.x;
            var yName = style_value.encode.y;

            var xValue = [];
            var yValue = [];
            data_value.forEach(obj => {

                xValue.push(obj[xName]);
                yValue.push(obj[yName]);
            });
            // var data = genData(50);
            option = {
                xAxis: {
                    type: 'category',
                    data: xValue,
                    crosshair: true
      ");
                WriteLiteral(@"          },
                yAxis: {
                    type: 'value',
                      title: {
                          text: yName
                    }
                },
                series: [
                    {
                        data: yValue,
                        smooth: true,
                        type: style_value.type,
                        label: {
                            show: true // 显示数据标签
                        }
                    }
                ]
            };
            // 使用刚指定的配置项和数据显示图表。
            myChart.setOption(option);
        }
    </script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n \r\n</html>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
