#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\knowledgeBase\DS.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "9d9decbed6ade202c6180726d9150a23361eb1aa6cab78f46d2e6cc61e37f533"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_knowledgeBase_DS), @"mvc.1.0.view", @"/Views/knowledgeBase/DS.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI

#nullable disable
    ;
#nullable restore
#line 2 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"9d9decbed6ade202c6180726d9150a23361eb1aa6cab78f46d2e6cc61e37f533", @"/Views/knowledgeBase/DS.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"a5bcd83cf27c8ffb8baf597d24314352bf7b52b5a9bb5ee83e83e9d0089a628e", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_knowledgeBase_DS : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/knowledgeBase_style.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/perfect-scrollbar/perfect-scrollbar.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/images/base_icon.png"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("alt", new global::Microsoft.AspNetCore.Html.HtmlString("Alternate Text"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "0", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("selected", new global::Microsoft.AspNetCore.Html.HtmlString(""), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/perfect-scrollbar/perfect-scrollbar.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("layui-layout-body"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_13 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "1", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_14 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "2", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_15 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("layui-form"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\knowledgeBase\DS.cshtml"
  
    ViewBag.Title = "知识库配置";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "9d9decbed6ade202c6180726d9150a23361eb1aa6cab78f46d2e6cc61e37f5339901", async() => {
                WriteLiteral("\r\n    <meta charset=\"utf-8\">\r\n    <title> ");
                Write(
#nullable restore
#line 9 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\knowledgeBase\DS.cshtml"
             ViewBag.SiteTitle

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</title>
    <meta name=""renderer"" content=""webkit"">
    <meta http-equiv=""X-UA-Compatible"" content=""IE=edge,chrome=1"">
    <meta name=""viewport""
          content=""width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0"">
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "9d9decbed6ade202c6180726d9150a23361eb1aa6cab78f46d2e6cc61e37f53310788", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "9d9decbed6ade202c6180726d9150a23361eb1aa6cab78f46d2e6cc61e37f53311991", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "9d9decbed6ade202c6180726d9150a23361eb1aa6cab78f46d2e6cc61e37f53313194", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n    <!-- 自定义滚动条 -->\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "9d9decbed6ade202c6180726d9150a23361eb1aa6cab78f46d2e6cc61e37f53314424", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        body {
            background-color: #fff;
        }

            body .demo-class .layui-layer-btn0 {
                color: #fff;
                background-color: #FF5722;
                border-color: #FF5722;
            }

            body .myskin .layui-layer-content {
                overflow: visible;
            }

        .border_wrap {
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #eee;
        }

        .tab-content {
            display: none;
        }
    </style>

");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "9d9decbed6ade202c6180726d9150a23361eb1aa6cab78f46d2e6cc61e37f53316906", async() => {
                WriteLiteral(@"
    <div class=""layui-layout layui-layout-admin"">

        <div class=""layui-row"">
            <div class=""layui-col-xs2 layui-col-sm2 layui-col-md2"">
                <div class=""left_wrap"">
                    <div class=""base_name inner_wrap"">
                        <div class=""base_title"">
                            <input type=""hidden"" id=""wikiId""");
                BeginWriteAttribute("value", " value=\"", 1707, "\"", 1726, 1);
                WriteAttributeValue("", 1715, 
#nullable restore
#line 55 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\knowledgeBase\DS.cshtml"
                                                                     ViewBag.id

#line default
#line hidden
#nullable disable
                , 1715, 11, false);
                EndWriteAttribute();
                WriteLiteral(" />\r\n                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("img", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "9d9decbed6ade202c6180726d9150a23361eb1aa6cab78f46d2e6cc61e37f53318090", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n                            <span>");
                Write(
#nullable restore
#line 57 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\knowledgeBase\DS.cshtml"
                                   ViewBag.name

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</span>\r\n                        </div>\r\n                        <div class=\"base_type\" style=\"margin:15px 10px;\">\r\n                            <i class=\"layui-icon layui-icon-template-1\"></i>\r\n                            <span>");
                Write(
#nullable restore
#line 61 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\knowledgeBase\DS.cshtml"
                                   ViewBag.type

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</span>

                        </div>
                    </div>
                    <div class=""menu_list inner_wrap"">

                        <div class=""tool_menu_item active"" data-target=""#tab1"">
                            <i class=""layui-icon layui-icon-transfer""></i>
                            <span>数据集</span>
                        </div>

                        <div class=""tool_menu_item "" data-target=""#tab2"">
                            <i class=""layui-icon layui-icon-engine""></i>
                            <span>搜索测试</span>
                        </div>

                    </div>


                </div>
            </div>
            <div class=""layui-col-xs10 layui-col-sm10 layui-col-md10"" id=""tab_contents"">
                <div class=""tab_item"" id=""tab1"">

                    <div class=""right_wrap layui-form"">
                        <div class=""lay_t_head"">
                            <div class=""line_wrap"">
");
                WriteLiteral(@"                                <div class=""layui-form-item"">
                                    <div class=""line_wrap"">
                                        <input type=""text"" name=""keyWords"" id=""keyWords"" autocomplete=""off"" class=""layui-input"" placeholder=""请输入关键字"">

                                        <div class=""tool_menu "">
                                            <div class=""tool_btn"" style=""width:400px;padding-left:5px;"">
                                                <button class=""layui-btn"" id=""Search"">查询</button>
                                                <button id=""btnImport"" class=""layui-btn"" style=""margin-left:10px""><i class=""layui-icon layui-icon-export""></i> 导入文档</button>
                                            </div>


");
                WriteLiteral(@"                                        </div>

                                    </div>
                                </div>

                            </div>

                        </div>
                    </div>
                    <div class=""tablewrap"">
                        <table class=""layui-hide"" id=""test"" lay-filter=""test""></table>
                        <script type=""text/html"" id=""barDemo"">
                            <a class=""layui-btn layui-btn-normal layui-btn-xs"" lay-event=""info"">详情</a>
                            <a class=""layui-btn layui-btn-danger layui-btn-xs"" lay-event=""del"">删除</a>
                        </script>
                    </div>

                </div>
                <div class=""tab_item"" id=""tab2"" style=""display:none"">
                    <div class=""right_wrap layui-form"">
                        <div class=""layui-row"">
                            <div class=""layui-col-xs5 layui-col-sm5 layui-col-md5"">
                                <di");
                WriteLiteral(@"v class=""serach_inner_left"">
                                    <div class=""left_top"">
                                        <div class=""top_tool"">
                                            <select name=""city"" lay-verify=""required"">
                                                ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "9d9decbed6ade202c6180726d9150a23361eb1aa6cab78f46d2e6cc61e37f53323437", async() => {
                    WriteLiteral("单个文本测试");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_7.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_7);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
                                            </select>
                                        </div>
                                        <div class=""test_inner"">
                                            <textarea placeholder=""请输入内容"" id=""TextArea"" class=""layui-textarea"" style=""height:160px;margin-top:10px; resize:none;overflow-y:auto""></textarea>
                                            <div class=""btn_group"">
                                                <button type=""submit"" class=""layui-btn""");
                BeginWriteAttribute("lay-submit", " lay-submit=\"", 6937, "\"", 6950, 0);
                EndWriteAttribute();
                WriteLiteral(" lay-filter=\"demo1\" id=\"Query\" style=\"margin-top:10px;\">测试</button>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n");
                WriteLiteral(@"                                </div>
                            </div>
                            <div class=""layui-col-xs7 layui-col-sm7 layui-col-md7"">
                                <div class=""inner_right"">

                                    <div class=""test_result"">
                                        <!--列表为空-->
                                        <div class=""none_base"">
                                            <!--style=""display:none""-->
                                            <div class=""none_base_icon""><i class=""layui-icon layui-icon-release""></i></div>
                                            <p>测试结果将在这里展示</p>
                                        </div>
                                        <!--有值展示-->
                                        <div class=""result_list"" style=""display:none"">
                                            <h2><i class=""layui-icon layui-icon-tabs""></i>&nbsp;&nbsp;     测试结果</h2>
                                            <div clas");
                WriteLiteral(@"s=""result_inner"" id=""resultScroll"">

                                            </div>

                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>



    </div>







    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "9d9decbed6ade202c6180726d9150a23361eb1aa6cab78f46d2e6cc61e37f53327246", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "9d9decbed6ade202c6180726d9150a23361eb1aa6cab78f46d2e6cc61e37f53328370", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    <!-- 自定义滚动条 -->\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "9d9decbed6ade202c6180726d9150a23361eb1aa6cab78f46d2e6cc61e37f53329518", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
        layui.use(['element', 'layer', 'table', 'laydate', 'form', 'upload'], function () {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , laydate = layui.laydate
                , $ = layui.$
                , upload = layui.upload
                , form = layui.form;


            $("".menu_list"").on(""click"", "".tool_menu_item"", function () {

                $("".tool_menu_item"").removeClass(""active"");
                $(this).addClass(""active"");
                var showid = $(this).attr(""data-target"");
                $(""#tab_contents .tab_item"").hide();
                $(showid).show();
                menuHeight();
            })



            $(window).resize(function () {
                menuHeight();
            });


            menuHeight();
            function menuHeight() {
                var winH = $(window).height();
                var serH = $("".base_name"").o");
                WriteLiteral(@"uterHeight(true);
                var contentH = winH - serH - 20;
                var serachH = winH - 20;

                $("".border_wrap"").css(""height"", contentH + ""px"");
                $("".layui-tab-content"").css(""height"", contentH - 80 + ""px"");

                var textAreaH = (winH-130)+""px"";
                $(""#TextArea"").css(""height"",textAreaH);

                $("".return_val_list"").css(""height"", contentH - 100 + ""px"");
                $("".return_val_txt"").css(""height"", contentH - 100 + ""px"");
                $("".serach_inner_left"").css(""height"", serachH + ""px"");
                $("".result_inner"").css(""height"",textAreaH);





            }


            $(""#btnImport"").on('click', function () {
                $(""#addReset"").click();
                windowIndex = layer.open({
                    skin: ""myskin"",
                    type: 1,
                    title: '数据导入',
                    content: $('#addBase'),
                    area: ['100%', '100%'],
       ");
                WriteLiteral(@"             btn: ['确认导入', '关闭'],
                    maxmin: true,
                    yes: function () {
                        var path = $(""#filePath"").val();
                        if (!path)
                            layer.msg(""请先上传文件！"");
                        $.post('/knowledgeBase/Submit', {
                            path: path, HandleWay: $(""#HandleWay"").val(),
                            splitChar: $(""#splitChar"").val(), chunkLength: $(""#chunkLength"").val(),
                            fileName: $(""#fileName"").val(),wikiId:");
                Write(
#nullable restore
#line 302 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\knowledgeBase\DS.cshtml"
                                                                   ViewBag.id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"
                        }, function (res) {
                            if (res.code == 0) {
                                layer.msg(""数据已加入后台队列处理中，请稍后！"");
                                table.reload('test');
                                layer.close(windowIndex);
                            }

                        })
                    },
                    success: function (layero, index) {
                        $(""#divSplit"").hide();
                        $(""#divChunkLength"").hide();
                    }
                });

            })

            $(""#Query"").on(""click"", function () {
                    $("".result_inner"").html("""");
                    console.log(""DSDSDSDSDSDSDSDSDSDS"");
                    var wikiId = $(""#wikiId"").val();
                    $.post('/knowledgeBase/GetResult', { wikiId: wikiId, inputText: $(""#TextArea"").val()}, 
                    function (res) {
                    if (res.code == 0)
                        {
             ");
                WriteLiteral(@"               $("".none_base"").css(""display"",""none"");
                            $("".result_list"").css(""display"",""block"");
                            for (var i = 0; i < res.data.length; i++) {
                                val_html = '<div class=""item_wrap""><div class=""item_title""><span style=""color:#1E9FFF"">#' + (i+1) + '</span>丨语义检索<span> ' +
                                    res.data[i].distances.toFixed(4) + '</span></div><div class=""item_content""><pre style=""word-wrap: break-word; white-space: pre-wrap;"">' +
                                    res.data[i].documents + '</pre></div></div>';
                                $("".result_inner"").append(val_html)
                            }
                                          var _name = document.getElementById(""resultScroll"");
                                        leftContentScorll = new PerfectScrollbar(_name, {
                                            wheelspeed: 1,
                                            wheelPropagation: f");
                WriteLiteral(@"alse,
                                            minScrollbarLength: 2,
                                            scrollingThreshold: 1000
                                        });
        
        
        
        }
                        else if(res.code==""-100""){
                            $("".none_base"").css(""display"",""block"");
                            $("".result_list"").css(""display"",""none"");
                            layer.msg(res.msg);
                        }
                    })
                })

            $(""#Search"").on('click', function () {

                table.reload('test', {
                    page: {
                        curr: 1
                    },
                    where: { 'keyWord': $.trim($(""#keyWords"").val()) }
                });
            })
            var my_data = [{
                ""id"": ""10001""
                , ""username"": ""手动录入""
                , ""datanum"": ""1""
                , ""joinTime"": ""2016-10-14 08:08:15""
     ");
                WriteLiteral("           , \"type\": \"已就绪\"\r\n            }\r\n            ]\r\n\r\n            //表\r\n            table.render({\r\n                elem: \'#test\'\r\n                , url: \'/knowledgeBase/getDetailList?Id=\'+");
                Write(
#nullable restore
#line 376 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\knowledgeBase\DS.cshtml"
                                                           ViewBag.id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"

                , height: 'full-20'
                , cols: [[
                    { field: 'number', title: '#', width: 80, type: 'numbers' }
                    , { field: 'FileName', title: '名称', maxWidth: 120 }
                    , {
                        field: 'DataCount', title: '数据总量', width: 150,
                    }
                    , { field: 'CreateTime', title: '创建时间', width: 200, sort: true }
                    , {
                        field: 'StateName', title: '状态', width: 100, templet: function (res) {
                            return '<em>' + res.StateName + '</em>'
                        }
                    }
                    , { fixed: 'right', title: '操作', toolbar: '#barDemo', width: 150 }
                ]]
                , page: true
            });

            //监听tablelist工具条
            table.on('tool(test)', function (obj) {
                var data = obj.data;
                if (obj.event === 'del') {
                    layer.confirm");
                WriteLiteral(@"('确定要删除名为【' + data.FileName + '】的数据集吗？将无法恢复。', {
                        title: '',
                        btn: ['确定', '取消'], //按钮
                        resize: false
                    }, function (index) {
                        $.post('/knowledgeBase/DelWikiDetail', { id: data.Id }, function (result) {
                            if (result.code==0) {
                                layer.msg(result.msg);
                                table.reload('test'); //重载表格
                            } else {
                                layer.msg(result.msg);
                            }
                        }, 'json');
                        layer.close(index);
                    });
                }
                else if (obj.event === 'info') {
                    windowIndex = layer.open({
                        skin: ""myskin"",
                        type: 1,
                        title: '数据详情',
                        content: $('#detailInfo'),
                     ");
                WriteLiteral(@"   area: ['100%', '100%'],

                        maxmin: true,
                        yes: function () {

                        },
                        success: function (layero, index) {
                            var baH = layero.height();
                            $(""#divdetailInfo"").css(""height"", (baH - 100) + ""px"");




                            $.get('/knowledgeBase/getSplitList?Id=' + data.Id, function (res) {
                                if (res.code == 0) {
                                    $(""#divdetailInfo"").html("""");
                            
                                    $.each(res.data, function (index, item) {
                                        var item_num = index+1;


                                        var htm = `<div class=""layui-col-xs4 layui-col-sm4 layui-col-md4 "">
                                                        <div class=""split_item_wrap"" >
                                                            <div class=""split_i");
                WriteLiteral(@"tem_num"">
                                                                <h5>#`+ item_num + `</h5>
                                                            </div>
                                                            <div class=""split_item_txt"" id=""Sc` + index + `"">
                                                                <pre >` + item.Txt + `</pre>
                                                            </div>
                                                        </div>
                                                    </div>`;
                                        $(""#divdetailInfo"").append(htm);

                                        var txtID = ""Sc"" + index;

                                        var _name = document.getElementById(txtID);
                                        leftContentScorll = new PerfectScrollbar(_name, {
                                            wheelspeed: 1,
                                            wheelPropagation: false,
    ");
                WriteLiteral(@"                                        minScrollbarLength: 2,
                                            scrollingThreshold: 1000,
        suppressScrollX:true,
        swipePropagation:false
                                        });


                                    })



                                }

                            })
                        }
                    });

                }

            });

            upload.render({
                elem: '#ID-upload-demo-drag',
                url: '/knowledgeBase/uploadFile?HandleWay=' + $(""#HandleWay"").val() + ""&splitChar="" + $(""#splitChar"").val() + ""&chunkLength="" + $(""#chunkLength"").val(), // 实际使用时改成您自己的上传接口即可。
                accept: 'file', // 普通文件
                exts:'txt',
                acceptMime: 'text/plain'
                        , before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
                    layer.load(); //上传loading
                },
                done: function (re");
                WriteLiteral(@"s) {
                    layer.closeAll('loading'); //关闭loading
                    console.log(res);
                    layer.msg('上传成功');


                    $(""#files"").text(res.data.fileName);
                    $(""#fileName"").val(res.data.fileName);
                    $(""#filePath"").val(res.data.src);
                    var val_html;
                    var val_txt = '<pre style=""word-wrap: break-word; white-space: pre-wrap;"">' + res.data.txt+ '</pre>'





                    $("".return_val_list"").html("""");
                    for (var i = 0; i < res.data.list.length; i++) {
                        val_html = '<div class=""item_wrap""><div class=""item_title""><span style=""color:#1E9FFF"">#' + (i+1) + '</span>&nbsp;&nbsp;<i class=""layui-icon layui-icon-file""></i><span>' +
                            res.data.fileName + '</span></div><div class=""item_content""><pre style=""word-wrap: break-word; white-space: pre-wrap;"">' +
                            res.data.list[i] + '</pre></div></d");
                WriteLiteral(@"iv>';
                        $("".return_val_list"").append(val_html)
                    }

                    $("".return_val_txt"").append(val_txt)



                    $(""#files"").on('click', function () {

                      //  var fileName = $(this).attr(""href"");
                        // 这里使用弹窗来查看文档，你可以根据自己的需求使用合适的弹窗组件
                        layer.open({
                            type: 2,
                            title: '文件查看',
                            area: ['800px', '600px'],
                            content: res.data.src
                        });
                    })
                }
                , error: function (index, upload) {
                    layer.closeAll('loading'); //关闭loading
                }
            });

            form.on('select(HandleWay)', function (data) {
                console.log(data.value); // 打印选中的值
                if(data.value==2)
                {
                    $(""#divSplit"").show();
                    ");
                WriteLiteral(@"$(""#divChunkLength"").show();
                }
                else
                {
                    $(""#divSplit"").hide();
                    $(""#divChunkLength"").hide();
                }
            });

            $(""#btnPreview"").on(""click"",function(){

                var path = $(""#filePath"").val();
                if (!path)
                    layer.msg(""请先上传文件！"");
                $.post('/knowledgeBase/PreviewFile', { path: path, HandleWay: $(""#HandleWay"").val(),
                    splitChar: $(""#splitChar"").val(), chunkLength: $(""#chunkLength"").val()
                }, function (res) {
                    if (res.code == 0) {
                        $("".return_val_list"").html("""");
                        for (var i = 0; i < res.data.list.length; i++) {
                           var val_html = '<div class=""item_wrap""><div class=""item_title""><span style=""color:#1E9FFF"">#' + (i + 1) + '</span>&nbsp;&nbsp;<i class=""layui-icon layui-icon-file""></i><span>' +
                ");
                WriteLiteral(@"                $(""#fileName"").val() + '</span></div><div class=""item_content""><pre style=""word-wrap: break-word; white-space: pre-wrap;"">' +
                                res.data.list[i] + '</pre></div></div>';
                            $("".return_val_list"").append(val_html)
                        }

                    }

                })

            })

        })
    </script>


");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"

<div id=""addBase"" style=""display:none"">
    <div class=""set_base"" style=""padding:15px;"">

        <div class=""layui-row layui-col-space10"">
            <div class=""layui-col-xs6 layui-col-md6"">
                <div class=""border_wrap"">
                    ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "9d9decbed6ade202c6180726d9150a23361eb1aa6cab78f46d2e6cc61e37f53347582", async() => {
                WriteLiteral(@"
                        <div class=""layui-form-item"">
                            <label class=""layui-form-label"">上传文档</label>
                            <div class=""layui-input-block"">
                                <div class=""layui-upload-drag"" style=""display: block;"" id=""ID-upload-demo-drag"">
                                    <i class=""layui-icon layui-icon-upload""></i>
                                    <div>点击上传，或将文件拖拽到此处</div>

                                </div>
                                <div id=""files"" style="" text-decoration:underline;color:blue;cursor:pointer""></div>
                                <input type=""hidden"" id=""fileName"" />
                                <input type=""hidden"" id=""filePath"" />
                            </div>
                        </div>

                        <div class=""layui-form-item"">
                            <label class=""layui-form-label"">
                                处理方式
                            </label>
         ");
                WriteLiteral("                   <div class=\"layui-input-block\">\r\n                                <select name=\"HandleWay\" id=\"HandleWay\" lay-filter=\"HandleWay\">\r\n                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "9d9decbed6ade202c6180726d9150a23361eb1aa6cab78f46d2e6cc61e37f53349148", async() => {
                    WriteLiteral("自动");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_13.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_13);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n                                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "9d9decbed6ade202c6180726d9150a23361eb1aa6cab78f46d2e6cc61e37f53350430", async() => {
                    WriteLiteral("自定义规则");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_14.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_14);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
                                </select>
                            </div>
                        </div>
                        <div class=""layui-form-item"" id=""divSplit"" style=""display:none"">
                            <label class=""layui-form-label"" style=""width:85px"">自定义分隔符</label>
                            <div class=""layui-input-block"" style=""padding-left:5px;"">
                                <input type=""text"" name=""splitChar"" id=""splitChar"" placeholder=""\n;===;###"" autocomplete=""off"" class=""layui-input"">
                            </div>
                        </div>
                        <div class=""layui-form-item"" id=""divChunkLength"" style=""display:none"">
                            <label class=""layui-form-label"">分块长度</label>
                            <div class=""layui-input-block"">
                                <input type=""text"" name=""chunkLength"" id=""chunkLength"" placeholder=""请输入"" autocomplete=""off"" class=""layui-input"" value=""512"">
                            </div");
                WriteLiteral(@">
                        </div>
                        <div class=""layui-form-item"">

                            <button type=""button"" id=""btnPreview"" class=""layui-btn layui-btn-normal"" style=""float:right"">生成预览</button>

                        </div>
                        <button type=""submit"" id=""addSubmit"" class=""layui-btn""");
                BeginWriteAttribute("lay-submit", " lay-submit=\"", 29041, "\"", 29054, 0);
                EndWriteAttribute();
                WriteLiteral(" lay-filter=\"demo1\" style=\"display:none\">立即提交</button>\r\n                        <button type=\"reset\" id=\"addReset\" class=\"layui-btn layui-btn-primary\" style=\"display:none\">重置</button>\r\n                    ");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_15);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
                </div>
            </div>
            <div class=""layui-col-xs6 layui-col-md6"">
                <div class=""border_wrap"">
                    <div class=""layui-tab layui-tab-card"" lay-filter=""test-handle"">
                        <ul class=""layui-tab-title"">
                            <li class=""layui-this"" lay-id=""11"">分段预览</li>
                            <li lay-id=""22"">原文</li>
                        </ul>
                        <div class=""layui-tab-content"">
                            <div class=""layui-tab-item layui-show"">
                                <div class=""return_val_list"">
                                    <div class=""item_wrap"" style=""display:none"">
                                        <div class=""item_title""><i class=""layui-icon layui-icon-file""></i><span>标题名称</span></div>
                                        <div class=""item_content"">返回结果内容</div>
                                    </div>
                                </div>
                 ");
            WriteLiteral(@"           </div>
                            <div class=""layui-tab-item"">
                                <div class=""return_val_txt"">

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id=""detailInfo"" style=""display:none"">
    <div class=""set_base layui-row layui-col-space10"" style=""padding:15px;overflow-y:auto;"" id=""divdetailInfo"">

    </div>
</div>
</html>
");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
