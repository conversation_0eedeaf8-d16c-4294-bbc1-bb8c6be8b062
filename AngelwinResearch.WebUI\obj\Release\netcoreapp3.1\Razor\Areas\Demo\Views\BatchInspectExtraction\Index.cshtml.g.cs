#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\BatchInspectExtraction\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "0e47fbcb8dd99998159b70f67710535a292e60cf1f484bbdc2a2dab8a37516b8"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_Demo_Views_BatchInspectExtraction_Index), @"mvc.1.0.view", @"/Areas/Demo/Views/BatchInspectExtraction/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"0e47fbcb8dd99998159b70f67710535a292e60cf1f484bbdc2a2dab8a37516b8", @"/Areas/Demo/Views/BatchInspectExtraction/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_Demo_Views_BatchInspectExtraction_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/loading.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\BatchInspectExtraction\Index.cshtml"
  
    ViewBag.Title = "BatchInspectExtraction";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"zh\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e47fbcb8dd99998159b70f67710535a292e60cf1f484bbdc2a2dab8a37516b86054", async() => {
                WriteLiteral("\r\n    <meta charset=\"UTF-8\">\r\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>批量检查提取</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "0e47fbcb8dd99998159b70f67710535a292e60cf1f484bbdc2a2dab8a37516b86548", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "0e47fbcb8dd99998159b70f67710535a292e60cf1f484bbdc2a2dab8a37516b87750", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        .flex_row {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .select_wrap {
            margin: 10px 5px;
            background-color: #fff;
            border: 1px solid #eee;
        }

        .content_wrap {
            margin: 0 5px;
        }

        .pathology_list_wrap {
            position:relative;
            user-select: none; /* 标准语法 */
            -webkit-user-select: none; /* Safari 和 Chrome */
            -moz-user-select: none; /* Firefox */
            -ms-user-select: none; /* Internet Explorer 和 Edge */
        }
        .loading_show{
            position:absolute;
            top:50%;
            left:50%;
          
        }

        .pathology_list_wrap .layui-colla-title {
            display: flex;
            flex-direction: row;
            padding: 0 15px 0 15px;
        }

            .pathology_list_wrap .layui-colla-icon {
                lef");
                WriteLiteral(@"t: 96% !important;
            }

        .pathology_title {
            padding: 0 15px;
            font-size: 16px;
            font-weight: blod;
        }

        .pathology_list_wrap, .pathology_result_wrap {
            overflow-y: auto;
        }

        .item_title {
            font-size: 16px;
            padding-right: 10px;
            color: #1E9FFF;
        }

        .pathology_inner_item {
            /* padding: 5px 0;*/
            /* border-bottom: 1px solid #f6f6f6;*/
        }

        .pathology-container {
            margin-bottom: 20px;
        }

        .report_bg {
            /*background-color: #f3f2ff;*/
            /*    padding: 10px;*/
            border-radius: 6px;
            overflow-wrap: break-word;
            /*    border: 1px dashed #ccc;*/
        }

        /*数据表格内容自动显示*/
        .layui-table, .layui-table-view {
            margin: 0;
        }

            .layui-table td, .layui-table th {
                white-space:");
                WriteLiteral(@" normal !important; /* 允许内容换行 */
                word-break: break-all !important; /* 长单词或 URL 地址换行 */
                height: auto !important; /* 自动调整高度 */
            }

        .layui-table-cell {
            height: auto !important; /* 自动调整高度 */
        }

        .layui-table-cell, .layui-table-tool-panel li {
            white-space: normal !important; /* 允许内容换行 */
        }

        .sex {
            font-size: 14px;
            font-weight: normal;
        }

        .sex0 {
            color: #FF5722;
        }

        .sex1 {
            color: #1E9FFF;
        }

        .btn_wrap {
            text-align: center;
            margin-top: 20px;
        }

        .currentMouse {
            color: red;
            font-weight: bold;
        }


        #addRow {
            margin-top: 15px;
        }

            #addRow .layui-form-item {
                margin-bottom: 5px;
            }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e47fbcb8dd99998159b70f67710535a292e60cf1f484bbdc2a2dab8a37516b812746", async() => {
                WriteLiteral(@"
    <div class=""wrap"" id=""content"">
        <div class=""select_wrap layui-form flex_row"">
            <div>
                <div class=""layui-inline"">
                    <div id=""xmGroupsList"" class=""xm-select-demo"" style=""width:250px""></div>
                </div>
                <div class=""layui-inline"">
                    <div class=""layui-input-inline"">
                        <input class=""layui-input"" type=""text"" name=""KeyWord"" id=""KeyWord"" lay-filter=""KeyWord"" style=""width:250px"" title=""关键字"" placeholder=""请输入诊断代码|诊断名称|项目名称"">
                    </div>
                </div>
                <div class=""layui-inline"">
                    <div id=""xmSelectFormList"" class=""xm-select-demo"" style=""width:250px""></div>
                </div>
                <div class=""layui-inline"">
                    <button class=""layui-btn layui-btn-normal"" id=""btnSearch"">查询</button>
                    <button class=""layui-btn "" id=""btnAnalysis"">提取</button>
                    <button class=""layui-bt");
                WriteLiteral(@"n "" id=""btnALLAnalysis"">批量提取</button>
                </div>
            </div>
            <div>
                <div class=""layui-inline"">
                    <button id=""popbtn"" type=""button"" style=""visibility:hidden"" class=""layui-btn layui-bg-blue"">
                        <i class=""layui-icon layui-icon-survey""></i>
                    </button>
                </div>
            </div>
        </div>
        <div class=""layui-row"">
            <div class=""layui-col-xs4 layui-col-md4"">
                <div class=""layui-card content_wrap"">
                    <div class=""layui-card-header""><input type=""checkbox"" id=""pathologyAllTitle"" name=""pathologyAllTitle""  title=""全选"" lay-skin=""primary"" />检查报告</div>
                    <div class=""layui-card-body pathology_list_wrap"">
                        <div class=""layui-collapse"" id=""InspectReports"">
                        </div>
                        <div class=""loading_show"" style=""display:none""><i class=""layui-icon layui-icon-loading loadi");
                WriteLiteral(@"ng-animation""></i></div>
                    </div>
                    <!-- 添加分页容器 -->
                    <div id=""inspectPager"" style=""text-align: center;""></div>
                </div>
            </div>
            <div class=""layui-col-xs8 layui-col-md8"">
                <div class=""layui-card content_wrap"">
                    <div class=""layui-card-header"">提取结果</div>
                    <div class=""layui-card-body pathology_result_wrap"">
                        <table id=""pathologyResult"" lay-filter=""pathologyResult""></table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 弹窗内容 -->
        <div id=""popwrap"" style=""display:none"">
            <table id=""dataTable"" lay-filter=""dataTable""></table>
            <!-- 新增数据的输入行 -->
            <div class=""layui-form layui-form-pane"" id=""addRow"">
                <div class=""layui-form-item"" pane style=""display:none"">
                    <label class=""layui-form-label"">序号</label>
          ");
                WriteLiteral(@"          <div class=""layui-input-block"">
                        <input type=""text"" id=""newId"" class=""layui-input"" placeholder=""请输入序号"">
                    </div>
                </div>
                <div class=""layui-form-item"" pane>
                    <label class=""layui-form-label"">变量</label>
                    <div class=""layui-input-block"">
                        <input type=""text"" id=""newVariable"" class=""layui-input"" placeholder=""请输入变量"">
                    </div>
                </div>
                <div class=""layui-form-item"" pane>
                    <label class=""layui-form-label"">提取要求</label>
                    <div class=""layui-input-block"">
                        <input type=""text"" id=""newRequirement"" class=""layui-input"" placeholder=""请输入提取要求"">
                    </div>
                </div>
");
                WriteLiteral("                <div class=\"btn_wrap\"><button class=\"layui-btn layui-btn-normal\" id=\"addButton\">添加</button></div>\r\n            </div>\r\n        </div>\r\n\r\n    </div>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e47fbcb8dd99998159b70f67710535a292e60cf1f484bbdc2a2dab8a37516b817440", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e47fbcb8dd99998159b70f67710535a292e60cf1f484bbdc2a2dab8a37516b818564", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e47fbcb8dd99998159b70f67710535a292e60cf1f484bbdc2a2dab8a37516b819688", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e47fbcb8dd99998159b70f67710535a292e60cf1f484bbdc2a2dab8a37516b820812", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
        layui.use(['element', 'layer', 'table', 'laydate', 'form', 'element', 'laytpl', 'laypage'], function () {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , laydate = layui.laydate
                , $ = layui.$
                , form = layui.form
                , element = layui.element
                , laytpl = layui.laytpl
                , laypage = layui.laypage;

            var currentPage = 1; // 当前页码
            var pageSize = 30;   // 每页显示数量
            var isFirstLoad = true;
            var allkeywords = """";
            var data = [
                { title: '放射-C T', id: '放射-C T' },
                { title: '放射-DSA', id: '放射-DSA' },
                { title: '放射-ERCP', id: '放射-ERCP' },
                { title: '放射-MRI', id: '放射-MRI' },
                { title: '放射-PETCT', id: '放射-PETCT' },
                { title: '放射-X光', id: '放射-X光' },
                { title: '放射-");
                WriteLiteral(@"X光(DR)', id: '放射-X光(DR)' },
                { title: '放射-X光(造影)', id: '放射-X光(造影)' },
                { title: '放射-X线平片(CR)', id: '放射-X线平片(CR)' },
                { title: '放射-X线平片造影(CR)', id: '放射-X线平片造影(CR)' },
                { title: '放射-乳腺摄影', id: '放射-乳腺摄影' },
                { title: '放射-口腔放射(PX)', id: '放射-口腔放射(PX)' },
                { title: '放射-数字胃肠', id: '放射-数字胃肠' },
                { title: '放射-移动CT', id: '放射-移动CT' },
                { title: '超声-超声', id: '超声-超声' },
                { title: '消化内镜-13C检测', id: '消化内镜-13C检测' },
                { title: '消化内镜-ERCP', id: '消化内镜-ERCP' },
                { title: '消化内镜-ERCP造影', id: '消化内镜-ERCP造影' },
                { title: '消化内镜-Hp检查', id: '消化内镜-Hp检查' },
                { title: '消化内镜-小肠镜（经口进镜）', id: '消化内镜-小肠镜（经口进镜）' },
                { title: '消化内镜-小肠镜（经肛进镜）', id: '消化内镜-小肠镜（经肛进镜）' },
                { title: '消化内镜-急诊治疗', id: '消化内镜-急诊治疗' },
                { title: '消化内镜-急诊肠镜', id: '消化内镜-急诊肠镜' },
                { title: '消化内镜-急诊胃镜', id: '消化");
                WriteLiteral(@"内镜-急诊胃镜' },
                { title: '消化内镜-急诊麻醉治疗', id: '消化内镜-急诊麻醉治疗' },
                { title: '消化内镜-急诊麻醉肠镜', id: '消化内镜-急诊麻醉肠镜' },
                { title: '消化内镜-急诊麻醉胃镜', id: '消化内镜-急诊麻醉胃镜' },
                { title: '消化内镜-放大胃镜', id: '消化内镜-放大胃镜' },
                { title: '消化内镜-纵轴超声内镜', id: '消化内镜-纵轴超声内镜' },
                { title: '消化内镜-肠梗阻减压', id: '消化内镜-肠梗阻减压' },
                { title: '消化内镜-肠镜检查', id: '消化内镜-肠镜检查' },
                { title: '消化内镜-肠镜治疗', id: '消化内镜-肠镜治疗' },
                { title: '消化内镜-胃镜检查', id: '消化内镜-胃镜检查' },
                { title: '消化内镜-胃镜治疗', id: '消化内镜-胃镜治疗' },
                { title: '消化内镜-胰胆管内超声', id: '消化内镜-胰胆管内超声' },
                { title: '消化内镜-胶囊内镜', id: '消化内镜-胶囊内镜' },
                { title: '消化内镜-超声肠镜', id: '消化内镜-超声肠镜' },
                { title: '消化内镜-超声胃镜', id: '消化内镜-超声胃镜' },
                { title: '消化内镜-麻醉放大肠镜', id: '消化内镜-麻醉放大肠镜' },
                { title: '消化内镜-麻醉放大胃镜', id: '消化内镜-麻醉放大胃镜' },
                { title: '消化内镜-麻醉肠镜', id: '消化内镜-麻醉肠镜'");
                WriteLiteral(@" },
                { title: '消化内镜-麻醉肠镜治疗', id: '消化内镜-麻醉肠镜治疗' },
                { title: '消化内镜-麻醉胃镜', id: '消化内镜-麻醉胃镜' },
                { title: '消化内镜-麻醉胃镜治疗', id: '消化内镜-麻醉胃镜治疗' },
                { title: '消化内镜-麻醉超声内镜', id: '消化内镜-麻醉超声内镜' },
                { title: '消化内镜-麻醉超声内镜治疗', id: '消化内镜-麻醉超声内镜治疗' },
                { title: '消化内镜-麻醉超声肠镜', id: '消化内镜-麻醉超声肠镜' }
            ];

            var xmGroupsList = xmSelect.render({
                el: '#xmGroupsList',
                model: { label: { type: 'text' } },
                prop: {
                    name: 'title',
                    value: 'id',
                },
                minWidth: 350,
                height: 350,
                radio: true,
                filterable: true,
                clickClose: true,
                tree: {
                    show: true,
                    strict: false,
                    expandedKeys: [-1],
                },
                data: data,
                initValue: [");
                WriteLiteral(@"data[0]],  // 动态获取第一个选项
                on: function (val) {
                    if (val.arr && val.arr.length > 0) {
                        getFormList(val.arr[0].id);
                    }
                }
            });
            // 手动触发 getFormList 函数
            if (data && data.length > 0) {
                getFormList(data[0].id); // 使用数据源中的第一个选项的 id 调用函数
            }
            var xmSelectFormList = xmSelect.render({
                el: '#xmSelectFormList',
                autoRow: true,
                radio: true,
                prop: {
                    name: 'name',
                    value: 'id',
                },
                minWidth: 350,
                height: 350,
                filterable: true,
                clickClose: true,
                tips: '请选择CRF表单',
                on: function (val) {
                    $.ajax({
                        url: '/Demo/BatchInspectExtraction/GetTableColmns?FormID=' + val.arr[0].id,
                     ");
                WriteLiteral(@"   type: ""get"",
                        datatype: 'json',
                        success: function (result) {
                            tableColumns = [result.columns];
                            tableResult = table.render({
                                elem: '#pathologyResult'
                                , id: 'pathologyResult'
                                , minHeight: 100 // 设置表格的最小高度
                                , limit: Number.MAX_VALUE // 数据表格默认全部显示
                                , page: false
                                , cols: tableColumns
                                , data: []
                                , done: function (res, curr, count, origin) {
                                    // 设置 .layui-table-box 的 overflow-y 属性为 scroll
                                    //$('[lay-id=""pathologyResult""]').find('.layui-table-box').css('overflow-x', 'scroll');
                                    // 设置 .layui-table-header 的 overflow-y 属性为 scroll
                   ");
                WriteLiteral(@"                 $('[lay-id=""pathologyResult""]').find('.layui-table-header').css('overflow-x', 'scroll');
                                }
                            });

                            //InsertTable();

                        }, error: function () {
                            layer.msg(""获取失败！"");
                        }
                    });
                },
                done: function (res) {
                }
            })
            function getFormList(val) {
                $.ajax({
                    url: '/Demo/BatchInspectExtraction/GetFormList?val=' + val,
                    type: ""get"",
                    datatype: 'json',
                    success: function (result) {
                        xmSelectFormList.update({
                            data: result.data
                        });
                    }, error: function () {
                        layer.msg(""获取失败！"");
                    }
                });
            }
       ");
                WriteLiteral(@"     // 表格渲染
            function reloadData() {
                table.render({
                    elem: '#dataTable',
                    url: '/Demo/BatchInspectExtraction/GetData', // 后端接口地址
                    height: 470,
                    cols: [[
                        { field: 'id', title: '序号', width: 80, sort: true }
                        , { field: 'variable', title: '变量', minWidth: 250 }
                        , { field: 'requirement', title: '提取要求', width: 200 }
                        , { field: 'traceability', title: '是否溯源', width: 200 }
                        , {
                            field: 'operation', title: '操作', width: 100, fixed: 'right', templet: function (d) {
                                return '<button class=""layui-btn layui-btn-xs edit-btn"" data-id=""' + d.id + '""><i class=""layui-icon layui-icon-edit""></i></button> ' +
                                    '<button class=""layui-btn layui-btn-danger layui-btn-xs delete-btn"" data-id=""' + d.id + '""><i class=");
                WriteLiteral(@"""layui-icon layui-icon-delete""></i></button>';
                            }
                        }
                    ]]
                    , done: function (res, curr, count) {
                        // 绑定编辑按钮点击事件
                        $('.edit-btn').off('click').on('click', function () {
                            var id = $(this).data('id');
                            console.log('编辑 ID: ' + id);
                            var tr = table.cache['dataTable'].find(function (item) { return item.id === id; });

                            if (tr) {
                                // 将数据填充到表单中
                                document.getElementById('newId').value = tr.id;
                                document.getElementById('newVariable').value = tr.variable;
                                document.getElementById('newRequirement').value = tr.requirement;
                                // document.getElementById('newTraceability').checked = tr.traceability === 'true';
           ");
                WriteLiteral(@"                 }
                        });

                        // 绑定删除按钮点击事件
                        $('.delete-btn').off('click').on('click', function () {
                            var id = $(this).data('id');
                            console.log('删除 ID: ' + id);
                            $.ajax({
                                url: '/Demo/BatchInspectExtraction/DeleteData?id=' + id, // 替换为实际的控制器和方法路径
                                type: 'POST',
                                success: function (response) {
                                    if (response.okMsg) {
                                        layer.msg(response.okMsg, { icon: 1 });
                                        // 重新渲染表格
                                        table.reload('dataTable', {
                                            url: '/Demo/BatchInspectExtraction/GetData'
                                        });
                                    } else if (response.errorMsg) {
                 ");
                WriteLiteral(@"                       layer.msg(response.errorMsg, { icon: 2 });
                                    }
                                },
                                error: function (jqXHR, textStatus, errorThrown) {
                                    layer.msg('请求失败: ' + textStatus, { icon: 2 });
                                }
                            });
                        });
                    }
                });
            }
            //新增患者弹窗
            $(""#addButton"").on(""click"", function () {
                var newId = $(""#newId"").val();
                var newVariable = $(""#newVariable"").val();
                var newRequirement = $(""#newRequirement"").val();
                // var newTraceability = $(""#newTraceability"").val();
                if (newId == """") {
                    newId = 0;
                }
                var data = {
                    Id: newId,
                    Variable: newVariable,
                    Requirement: newRequire");
                WriteLiteral(@"ment,
                    Traceability: ""true""
                };

                $.ajax({
                    url: '/Demo/BatchInspectExtraction/AddData', // 替换为实际的控制器和方法路径
                    type: 'POST',
                    data: data,
                    success: function (response) {
                        if (response.okMsg) {
                            layer.msg(response.okMsg, { icon: 1 });
                            // 重新渲染表格
                            table.reload('dataTable', {
                                url: '/Demo/BatchInspectExtraction/GetData'
                            });
                            scrollToBottom();
                        } else if (response.errorMsg) {
                            layer.msg(response.errorMsg, { icon: 2 });
                        }
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        layer.msg('请求失败: ' + textStatus, { icon: 2 });
                    }
       ");
                WriteLiteral(@"         });


            });

            $(window).resize(function () {
                setContentH();
            });
            $(document).ready(function () {
                setContentH();
                getInspectReport(1);
            });
            // 绑定点击事件
            $(document).on('click', '#btnSearch', function () {
                getInspectReport(1);
            });

            var timer;
            var TCount = 0;
            var indexs;
            function setCloseIndex() {
                timer = setInterval(function () {
                    if (TCount <= 0) {
                        layer.close(indexs);
                        clearInterval(timer);
                    }

                }, 1000);
            }

            $(document).on('click', '#btnAnalysis', function () {
                var checkboxes = $('input[name=pathologyTitle]:checked');
                var CRForm = xmSelectFormList.getValue('valueStr');
                var selectedData = ");
                WriteLiteral(@"[];
                // 使用 .each() 方法遍历这些复选框
                checkboxes.each(function () {
                    var data = JSON.parse($(this).attr('bldata')); // 获取当前复选框的 data 属性值
                    selectedData.push(data);
                });
                $(""#pathologyResult"").html("""");
                indexs = layer.load(1);
                isFirstLoad = true;
                if (CRForm == """") {
                    layer.msg(""请选择CRF表单"");
                    layer.close(indexs);
                    return;
                }
                if (selectedData.length == 0) {
                    layer.msg(""请选择需要提取特征的报告"");
                    layer.close(indexs);
                    return;
                }
                if (selectedData.length >= 8) {
                    layer.msg(""选择的报告较多，请使用批量提取！"");
                    layer.close(indexs);
                    return;
                }
                TCount = selectedData.length;
                setCloseIndex();
                va");
                WriteLiteral(@"r errorMsg = """";
                for (var i = 0; i < selectedData.length; i++) {
                    var json = JSON.stringify(selectedData[i]);
                    $.post('/Demo/BatchInspectExtraction/AnalysePdf', { ""json"": json, ""CRForm"": CRForm }, function (res) {
                        TCount = TCount - 1;
                        if (res.code == 0) {
                            loadTable(res.data);
                        }
                        else {
                            errorMsg = errorMsg + res.msg + ""。"";
                        }
                        if (TCount == 0) {
                            if (errorMsg != """") {
                                layer.msg(""以下提取出错："" + errorMsg);
                            }
                            else {
                                layer.msg(""提取完成！"");
                            }
                        }
                    })
                }
            });

            $(document).on('click', '#btnALLAnalysis', fu");
                WriteLiteral(@"nction () {
                debugger;
                var checkboxes = $('input[name=pathologyTitle]:checked');
                var CRForm = xmSelectFormList.getValue('valueStr');
                var selectedData = [];
                // 使用 .each() 方法遍历这些复选框
                checkboxes.each(function () {
                    var data = $(this).attr('bldata'); // 获取当前复选框的 data 属性值
                    selectedData.push(data);
                });
                $(""#pathologyResult"").html("""");
                var indexsALL = layer.load(1);
                isFirstLoad = true;
                if (CRForm == """") {
                    layer.msg(""请选择CRF表单"");
                    layer.close(indexsALL);
                    return;
                }
                if (selectedData.length == 0) {
                    layer.msg(""请选择需要提取特征的报告"");
                    layer.close(indexsALL);
                    return;
                }
                debugger;
                TCount = selectedData.lengt");
                WriteLiteral(@"h; 
                $.post('/Demo/BatchInspectExtraction/AutoAnalyseTask', { ""JsonList"": selectedData, ""CRForm"": CRForm, ""KeyWords"": allkeywords }, function (res) {
                    if (res.code == 0) {
                        layer.msg(""批量提取任务创建成功：请去批量提取任务界面查看提取进度；"" );
                    }
                    else {
                        layer.msg(""批量提取任务创建失败："" + res.msg);
                    }
                });
                layer.close(indexsALL);
            });

            $(document).ready(function(){
                // 监听全选checkbox的状态变化
                $('#pathologyAllTitle').on('change', function () {
                    if ($(this).is(':checked')) {
                        $('input[name=""pathologyTitle""]').prop('checked', true);
                    } else {
                        $('input[name=""pathologyTitle""]').prop('checked', false);
                    }
                });

            });
           

            $(""#popbtn"").on(""click"", function () {
     ");
                WriteLiteral(@"           layer.open({
                    type: 1,
                    area: ['60%', '80%'],
                    resize: false,
                    shadeClose: true,
                    title: '帮助',
                    content: $(""#popwrap""),
                    success: function () {
                        reloadData();
                    }
                })
            });
            function getInspectReport(page) {
                // 清空现有内容
                $('#InspectReports').empty();
                $("".loading_show"").css(""display"",""block"")
                var Group = xmGroupsList.getValue('valueStr');
                var KeyWord = $.trim($(""#KeyWord"").val());
                allkeywords = KeyWord;
                $.post(""/Demo/BatchInspectExtraction/getInspectReport"", { ""Group"": Group, ""KeyWord"": KeyWord, ""page"": currentPage, ""pageSize"": pageSize }, function (res) {

                    if (res.code == 0) {

                    
                        var data = {};
    ");
                WriteLiteral(@"                    data.data = res.data;
                        data.xh = (currentPage - 1) * pageSize;
                        var getTpl2 = NoStructurModel.innerHTML;
                        laytpl(getTpl2).render(data, function (html) {
                            $('#InspectReports').append(html);
                        });
                        $("".loading_show"").css(""display"", ""none"")
                        // 初始化分页
                        layui.use('laypage', function () {
                            var laypage = layui.laypage;

                            // 执行一个laypage实例
                            laypage.render({
                                elem: 'inspectPager',
                                count: res.total, // 数据总数
                                curr: currentPage,
                                limit: pageSize,
                                layout: ['prev', 'page', 'next', 'limit'],
                                limits: [30, 50, 100],
                        ");
                WriteLiteral(@"        groups: 2,
                                jump: function (obj, first) {
                                    // 首次不执行
                                    if (!first) {
                                        currentPage = obj.curr;
                                        pageSize = obj.limit;
                                        getInspectReport(1);
                                    }
                                }
                            });
                        });
                        // 遍历所有复选框，为每个复选框添加点击事件处理器
                        document.querySelectorAll('.layui-colla-title input[type=""checkbox""]').forEach(function (checkbox) {
                            checkbox.addEventListener('click', function (event) {
                                event.stopPropagation(); // 阻止事件冒泡
                                // 获取当前复选框的状态
                                const isChecked = this.checked;

                                // 获取当前复选框所在的 .layui-colla-item 元素
       ");
                WriteLiteral(@"                         const collaItem = this.closest('.layui-colla-item');

                                // 获取当前复选框的 data 属性
                                const checkbox = $(this);
                                const data = checkbox.data();

                                // 获取当前 .layui-colla-item 下的所有报告内容
                                const reportContents = collaItem.querySelectorAll('.pathology_inner_item .report_bg');
                                // 根据复选框的状态决定是否显示报告内容
                                if (isChecked) {
                                    reportContents.forEach(reportContent => {
                                        //console.log(reportContent.innerText); // 输出报告内容
                                    });
                                } else {
                                    console.log('复选框未选中'); // 复选框未选中时的处理
                                }
                            });
                        });

                        // 监听复选框变化
           ");
                WriteLiteral(@"             form.on('checkbox(pathologyTitle)', function (data) {
                            var choice = data.elem.checked;
                            //console.log(data.elem.checked); // 输出复选框的状态
                        });
                        // 重新初始化 Layui 的折叠面板组件
                        element.init();

                    } else {
                        $("".loading_show"").css(""display"", ""none"")
                        layer.msg(res.msg);
                    }
                })
            };
            function setContentH() {
                var winH = $(window).height();
                var card_title_H = $("".layui-card-header"").outerHeight(true);
                var select_H = $("".select_wrap"").outerHeight(true);
                var content2H = winH - select_H - card_title_H - 45;
                var contentH = winH - select_H - card_title_H - 100;
                $("".pathology_list_wrap"").css(""height"", contentH + ""px"");
                $("".pathology_result_wrap"").css(""h");
                WriteLiteral(@"eight"", content2H + ""px"");
            };
            var data1 = [];
            var columns = [];
            function loadTable(obj) {
                try {
                    $(""#pathologyResult"").html("""");
                    var data = JSON.parse(obj);
                    //console.log(data);

                    if (Array.isArray(data)) {
                        var rowWidth = """";
                        if (isFirstLoad) {
                            data1 = [];
                            columns = [{ type: 'numbers', title: '序号', width: '5%' }];

                            // 创建一个临时对象来存储所有键值对，包括 '原文依据'
                            const tempData = {};

                            // 遍历数据以生成列定义和临时数据对象
                            $.each(data, function (index, item) {
                                if (item[""项目""] !== ""原文依据"") {
                                    var rowHead = {};
                                    rowHead.field = item[""项目""];
                                   ");
                WriteLiteral(@" rowHead.title = item[""项目""];
                                    rowHead.width = ""120"";
                                    columns.push(rowHead);
                                }
                            });

                            // 创建一个对象来存储项目值和原文依据的映射
                            const projectToOriginalBasis = {};

                            // 遍历数据以填充项目值和原文依据
                            $.each(data, function (index, item) {
                                if (item[""项目""] !== ""原文依据"") {
                                    tempData[item[""项目""]] = item[""项目值""];
                                    projectToOriginalBasis[item[""项目值""]] = item[""原文依据""];
                                }
                            });

                            // 将项目值和原文依据的映射添加到 tempData 中
                            tempData.projectToOriginalBasis = projectToOriginalBasis;

                            // 将临时数据对象推入 data1 数组
                            data1.push(tempData);
                          ");
                WriteLiteral(@"  isFirstLoad = false;
                        } else {
                            // 创建一个临时对象来存储所有键值对，包括 '原文依据'
                            const tempData = {};

                            // 创建一个对象来存储项目值和原文依据的映射
                            const projectToOriginalBasis = {};

                            // 遍历数据以填充项目值和原文依据
                            $.each(data, function (index, item) {
                                if (item[""项目""] !== ""原文依据"") {
                                    tempData[item[""项目""]] = item[""项目值""];
                                    projectToOriginalBasis[item[""项目值""]] = item[""原文依据""];
                                }
                            });

                            // 将项目值和原文依据的映射添加到 tempData 中
                            tempData.projectToOriginalBasis = projectToOriginalBasis;

                            // 将临时数据对象推入 data1 数组
                            data1.push(tempData);
                        }
                        var pathologyResult = tabl");
                WriteLiteral(@"e.render({
                            elem: '#pathologyResult',
                            height: ""full-160"",
                            page: true,
                            cols: [columns],
                            data: data1,
                            done: function (res, curr, count) {
                                // 监听单元格点击事件
                                table.on('row(pathologyResult)', function (obj) {
                                    console.log('obj:', obj);
                                    console.log('obj.config:', obj.config);

                                    var tr = obj.tr; // 当前行的 DOM 元素
                                    var data = obj.data; // 当前行的数据
                                    var td = $(event.target).closest('td'); // 获取当前单元格的 DOM 元素
                                    var Cellvalue = td.text(); // 获取当前单元格的值

                                    // 显示选中的单元格数据
                                    console.log(data);
                        ");
                WriteLiteral(@"            var targetTitle = data.患者姓名;
                                    var targetTypeName = data.项目名称;
                                    var value = data.projectToOriginalBasis[Cellvalue];//获取提示词值
                                    console.log('值: ' + value);

                                    var sourceList = $("".layui-colla-content"");
                                    sourceList.each(function (index, element) {
                                        var $element = $(element);
                                        // 正确地移除 currentMouse 类
                                        $element.find(""span"").removeClass(""currentMouse"");
                                        $element.removeClass(""layui-show"");

                                        if ($element.attr(""title"") == targetTitle && $element.attr(""typeName"") == targetTypeName) {
                                            $element.addClass(""layui-show"");
                                            var textContent = $(this).ht");
                WriteLiteral(@"ml();
                                            if (textContent.includes(value)) {

                                                textContent = textContent.replace(value, '<span class=""currentMouse"">' + value + '</span>');

                                            } else if (textContent.includes('：')) {
                                                value = value.replace('：', ':</strong>').replace(/""/g, '');
                                                value = '<strong class=""item_title"">' + value;
                                                if (textContent.includes(value)) {
                                                    textContent = textContent.replace(value, '<span class=""currentMouse"">' + value + '</span>');
                                                }
                                            }
                                            else if (textContent.includes(':')) {
                                                value = value.replace(':', ':</strong>').r");
                WriteLiteral(@"eplace(/""/g, '');
                                                value = '<strong class=""item_title"">' + value;
                                                if (textContent.includes(value)) {
                                                    textContent = textContent.replace(value, '<span class=""currentMouse"">' + value + '</span>');
                                                }
                                            }
                                        }
                                        $(this).html(textContent);
                                    });
                                    // addbyzolf 自动滚动到定位位置 2024-10-30
                                    var targetElement = document.querySelector('.currentMouse');
                                    // 滚动到该元素的位置
                                    if (targetElement) {
                                        targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                    }
 ");
                WriteLiteral(@"                               });
                            }
                        });
                    }
                } catch (error) {
                    console.error('JSON字符串格式错误:', error.message);
                    alert('JSON字符串格式错误:' + error.message);
                }
            }
            function scrollToBottom() {
                var div = $('.layui-table-view');
                div.scrollTop(div[0].scrollHeight - div.innerHeight());
            }
        });
    </script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"

<script id=""NoStructurModel"" type=""text/html"">
    {{#  for (var i=0;i<d.data.length;i++) {  }}
    <div class=""layui-colla-item"">
        <div class=""layui-colla-title"">
            <input type=""checkbox"" name=""pathologyTitle"" bldata=""{{= JSON.stringify(d.data[i])}}"" title=""{{ d.data[i][""病理申请ID""]}}"" lay-skin=""primary"" />

            <p><span class=""pathology_title"">{{d.xh+i+1}}.{{ d.data[i][""患者姓名""]}}</span> <span> {{ d.data[i][""项目名称""]}} </span>(<span class=""pathology_time"">{{ d.data[i][""报告时间""]}}</span>)</p>
        </div>
        <div class=""layui-colla-content"" title=""{{ d.data[i][""患者姓名""]}}"" typeName=""{{ d.data[i][""项目名称""]}}"">
            <div class=""pathology_inner_list"">
                <div class=""pathology_inner_item"">
                    {{# for (let key in d.data[i]) {   }}
                    <p class=""report_bg""><strong class=""item_title"">{{key}}:</strong>{{d.data[i][key]}}</p>
                    {{# } }}
                </div>
            </div>

        </div>
    </div>
  ");
            WriteLiteral("  {{# } }}\r\n</script>\r\n\r\n</html>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
