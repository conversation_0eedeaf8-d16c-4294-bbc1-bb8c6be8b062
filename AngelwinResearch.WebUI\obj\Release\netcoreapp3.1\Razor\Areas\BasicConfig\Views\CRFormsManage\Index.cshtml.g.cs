#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\CRFormsManage\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "b438ddf367a082a9b6a7cad85e78a1bc16b0102318b9743d70f18be9d5cd8771"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_BasicConfig_Views_CRFormsManage_Index), @"mvc.1.0.view", @"/Areas/BasicConfig/Views/CRFormsManage/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"b438ddf367a082a9b6a7cad85e78a1bc16b0102318b9743d70f18be9d5cd8771", @"/Areas/BasicConfig/Views/CRFormsManage/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_BasicConfig_Views_CRFormsManage_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/jquery/dist/jquery.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\CRFormsManage\Index.cshtml"
  
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "b438ddf367a082a9b6a7cad85e78a1bc16b0102318b9743d70f18be9d5cd87716359", async() => {
                WriteLiteral("\r\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n    <meta charset=\"utf-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>CRF表单管理</title>\r\n\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "b438ddf367a082a9b6a7cad85e78a1bc16b0102318b9743d70f18be9d5cd87716876", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "b438ddf367a082a9b6a7cad85e78a1bc16b0102318b9743d70f18be9d5cd87718078", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "b438ddf367a082a9b6a7cad85e78a1bc16b0102318b9743d70f18be9d5cd87719280", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n");
                WriteLiteral(@"    <style>
        /*弹窗*/
        .window_wrap {
            padding: 15px;
        }

        .search_wrap {
            background-color: #fff;
            display: flex;
            flex-direction: row;
            padding: 10px 4px;
        }

        #detail_window .layui-form-label {
            width: 100px;
        }

        #detail_window .layui-form-val {
            padding: 9px 15px;
        }

        #detail_window .mindow_icon .title_icon {
            display: inline-block;
        }

        #detail_window .mindow_icon {
            text-align: center;
            padding: 20px 0;
        }

        #detail_window .layui-form-item {
            margin-bottom: 0;
        }

        .layui-form-label {
            width: 100px;
        }

        .layui-input-block {
            margin-left: 140px;
            padding-right: 15px;
        }

        .line_wrap {
            display: flex;
            flex-direction: row;
            padding-right:");
                WriteLiteral(@" 15px;
        }


        .layui-form-item .layui-input-inline {
            width: 50%;
            margin-right: 0;
            margin-left: 10px;
        }
        /* 定义表头样式 */
        .layui-table th {
            font-weight: bold; /* 加粗 */
            font-size: 14px; /* 调整字体大小 */
            color: #333; /* 字体颜色，可根据需要调整 */
        }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "b438ddf367a082a9b6a7cad85e78a1bc16b0102318b9743d70f18be9d5cd877112579", async() => {
                WriteLiteral(@"
    <div class=""layui-col-md12"">
        <div class=""layui-card"">
            <div class=""layui-card-body layui-form search_wrap"">

                <div class=""layui-inline"" style=""margin-left: 10px; width: 300px;"">

                    <div id=""xmDeptsList""></div>

                </div>
                <div class=""layui-inline"" style=""margin-left: 10px; width: 300px;"">

                    <div id=""xmDeptsList5""></div>

                </div>
                <div class=""layui-inline"" style=""margin-left: 10px;"">
                    <label class=""layui-form-label"">启停状态</label>
                    <div class=""layui-inline"">
                        <select name=""modules"" id=""status""");
                BeginWriteAttribute("lay-search", " lay-search=\"", 2658, "\"", 2671, 0);
                EndWriteAttribute();
                WriteLiteral(">\r\n                            <option");
                BeginWriteAttribute("value", " value=\"", 2710, "\"", 2718, 0);
                EndWriteAttribute();
                WriteLiteral(@" selected=""selected"">选择启停状态</option>
                            <option value=""true"">启用</option>
                            <option value=""false"">停用</option>
                        </select>
                    </div>
                </div>
                <div class=""layui-inline""style=""margin-left: 10px;"">
                    <button class=""layui-btn layui-btn-normal fr"" id=""Search"">查询</button>
                    <button class=""layui-btn"" id=""addFolder"" data-type=""add"">新增文件夹</button>
");
                WriteLiteral(@"                </div>
            </div>
            <div class=""layui-card-body"">
                <table class=""layui-table layui-form"" id=""docmentTreeTb""></table>
                <script type=""text/html"" id=""tableBar1"">
                    <a class=""layui-btn layui-btn-normal layui-btn-xs"" lay-event=""edit""><i class=""layui-icon layui-icon-edit""></i>编辑</a>
                    {{# if(d.LevelType == ""1""){ }}
                    {{# if(d.ParentId == ""0""){ }}
                    <a class=""layui-btn  layui-btn-xs"" lay-event=""addFolder""><i class=""layui-icon layui-icon-add-circle""></i>新增文件夹</a>
                    {{# } else { }}
                    <a class=""layui-btn  layui-btn-xs"" lay-event=""addFolder""><i class=""layui-icon layui-icon-add-circle""></i>新增文件夹</a>
                    <a class=""layui-btn  layui-btn-xs"" lay-event=""addFile""><i class=""layui-icon layui-icon-add-circle""></i>新增文件</a>
                    {{# } }}
                    {{# } }}
                    {{# if(d.LevelType == ""2""){ }}
 ");
                WriteLiteral(@"                   <a class=""layui-btn  layui-btn-normal  layui-btn-xs"" lay-event=""move""><i class=""layui-icon layui-icon-transfer""></i>移动</a>
                    <a class=""layui-btn layui-btn-normal layui-btn-xs"" lay-event=""show""><i class=""layui-icon layui-icon-eye""></i>查看文件</a>
                    {{# } }}
                    <a class=""layui-btn layui-btn-danger layui-btn-xs"" lay-event=""del""><i class=""layui-icon layui-icon-delete""></i>删除</a>
                </script>
            </div>
        </div>

        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "b438ddf367a082a9b6a7cad85e78a1bc16b0102318b9743d70f18be9d5cd877116158", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "b438ddf367a082a9b6a7cad85e78a1bc16b0102318b9743d70f18be9d5cd877117286", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "b438ddf367a082a9b6a7cad85e78a1bc16b0102318b9743d70f18be9d5cd877118414", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"

        <script>
            layui.config({
                base: '/layuiadmin/layuiextend/'
            }).use(['element', 'layer', 'treeTable', 'upload', 'form'], function () {
                var element = layui.element
                    , layer = layui.layer
                    , treeTable = layui.treeTable //表格
                    , upload = layui.upload
                    , $ = layui.$
                    , form = layui.form;
                ;
                var windowsIndex;

                // 渲染表格
                var docmentTreeTb = treeTable.render({
                    elem: '#docmentTreeTb',
                    url: ""/BasicConfig/CRFormsManage/List"",
                    type: ""post"",
                    tree: {
                        idName: 'Id',
                        pidName: 'ParentId',
                        haveChildName: 'haveChildName',
                        iconIndex: 1,    // 折叠图标显示在第几列
                        isPidData: true,  // 是否是pid形式数据
         ");
                WriteLiteral(@"               arrowType: 'arrow2',
                        getIcon: 'ew-tree-icon-style2'
                    },
                    cols: [[
                        { field: 'No', title: '序号', type: 'numbers', fixed: 'left', width: 60 }
                        , { field: 'FormName', title: 'CRF表单名称', width: 270 }
                        , { field: 'FormId', title: 'CRF表单ID', width: 290 }
                        , { field: 'StopUsing', title: '启停开关', templet: '#switchTpl', unresize: true, align: 'center' }
                        , { field: 'OrderBy', title: '排序', width: 90 }
                        , { field: 'BusinessDomainName', title: '业务域', width: 100 }
                        , { field: 'DeptName', title: '所属组织/专病', width: 300 }
                        , { field: 'Remark', title: '备注' }
                        , { title: '操作', toolbar: '#tableBar1', minWidth: 220, fixed: 'right' }
                    ]],
                    done: function () {
                        docmentTreeTb.expand");
                WriteLiteral(@"(1);
                    }
                });

                //监听tablelist工具条
                treeTable.on('tool(docmentTreeTb)', function (obj) {
                    var data = obj.data;
                    if (data.haveChildName) {
                        $(""#parentId"").attr(""haveChildName"", ""1"");
                    }
                    else {
                        $(""#parentId"").attr(""haveChildName"", ""0"");
                    }
                    if (obj.event === 'addFile') {
                        $(""#ZBZ1"").css(""display"", ""block"");
                        $(""#YWY1"").css(""display"", ""block"");
                        $(""#fm"")[0].reset();
                        $(""#Id"").val(""0"");
                        $('#ParentId').val(data.Id);
                        $('#LevelType').val(2);
                        $('#PatientId').attr(""readonly"", ""readonly"");
                        xmDeptsList2.update({
                            disabled: true
                        });
           ");
                WriteLiteral(@"             if (data.HospitalDeptId != undefined) {
                            xmDeptsList2.setValue([data.HospitalDeptId]);
                            GetGroupsTree7(data.HospitalDeptId);
                        }
                        if (data.DiseaseSpecificGroupId != undefined) {
                            xmDeptsList7.setValue([data.DiseaseSpecificGroupId]);
                            xmDeptsList7.update({
                                disabled: true
                            });
                        }
                        if (data.BusinessDomainID != undefined) {
                            var valuesArray = data.BusinessDomainID.split(',');
                            xmDeptsList8.setValue(valuesArray);
                        } else {
                            xmDeptsList8.setValue([]);
                        }
                        windowsIndex = layer.open({
                            type: 1,
                            title: '新增表单',
                      ");
                WriteLiteral(@"      area: '600px',
                            resize: true,
                            content: $('#form_window')
                        });

                        url = '/BasicConfig/CRFormsManage/Create';
                    }
                    else if (obj.event === 'addFolder') {
                        $(""#ZBZ"").css(""display"", ""block"");
                        $(""#YWY"").css(""display"", ""none"");
                        $(""#fm1"")[0].reset();
                        $(""#Folder_Id"").val(""0"");
                        $('#Folder_ParentId').val(data.Id);
                        $('#Folder_LevelType').val(1);
                        $('#Folder_PatientId').attr(""readonly"", ""readonly"");
                        xmDeptsList3.update({
                            disabled: true
                        });
                        if (data.HospitalDeptId != undefined) {
                            xmDeptsList3.setValue([data.HospitalDeptId]);
                            GetGroupsTree6(data.Ho");
                WriteLiteral(@"spitalDeptId);
                        }
                        if (data.DiseaseSpecificGroupId != undefined) {
                            xmDeptsList6.setValue([data.DiseaseSpecificGroupId]);
                            xmDeptsList6.update({
                                disabled: true
                            });
                        }
                        windowsIndex = layer.open({
                            type: 1,
                            title: '新增文件夹',
                            area: '500px',
                            resize: true,
                            content: $('#Folder_window')
                        });
                        url = '/BasicConfig/CRFormsManage/Create';
                    }
                    else if (obj.event === 'edit') {
                        form.val('fm', data);
                        xmDeptsList2.update({
                            disabled: true
                        });
                        if (data.ParentId ==");
                WriteLiteral(@"0) {
                            $(""#ZBZ1"").css(""display"", ""none"");
                        }
                        else {
                            $(""#ZBZ1"").css(""display"", ""block"");
                        }
                        if (data.LevelType == 2) {
                            $(""#YWY1"").css(""display"", ""block"");
                        }
                        else {
                            $(""#YWY1"").css(""display"", ""none"");
                        }
                        if (data.HospitalDeptId != undefined) {
                            xmDeptsList2.setValue([data.HospitalDeptId]);
                            GetGroupsTree7([data.HospitalDeptId]);
                        }
                        if (data.DiseaseSpecificGroupId != undefined) {
                            xmDeptsList7.setValue([data.DiseaseSpecificGroupId]);
                            xmDeptsList7.update({
                                disabled: true
                            });
             ");
                WriteLiteral(@"           }
                        if (data.BusinessDomainID != undefined) {
                            var valuesArray = data.BusinessDomainID.split(',');
                            xmDeptsList8.setValue(valuesArray);
                        } else {
                            xmDeptsList8.setValue([]);
                        }
                        if (data.StopUsing == 'on') {
                            data.StopUsing = true;
                        }
                        else {
                            data.StopUsing = false;
                        }

                        windowsIndex = layer.open({
                            type: 1,
                            title: '修改【' + data.FormName + '】信息',
                            area: '600px',
                            resize: true,
                            content: $('#form_window')
                        });
                        url = '/BasicConfig/CRFormsManage/Edit';
                    }
             ");
                WriteLiteral(@"       else if (obj.event === 'show') {
                        layer.open({
                            type: 2,
                            title: 'CRF表单预览',
                            shadeClose: true, // 点击遮罩关闭层
                            area: ['80%', '80%'], // 设置弹窗大小
                            content: """);
                Write(
#nullable restore
#line 311 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\CRFormsManage\Index.cshtml"
                                        $"{ViewBag.formUrl}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\" + data.FormId + \"");
                Write(
#nullable restore
#line 311 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\CRFormsManage\Index.cshtml"
                                                                                  $".form?token={ViewBag.token}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"""
                        });
                    }
                    else if (obj.event === 'del') {
                        layer.confirm('确定要删除名为【' + data.FormName + '】的文件(夹)吗？将无法恢复。', {
                            title: '',
                            btn: ['确定', '取消'], //按钮
                            resize: false
                        }, function (index) {
                            $.post('/BasicConfig/CRFormsManage/Del', { id: data.Id }, function (result) {
                                if (result.okMsg) {
                                    debugger;
                                    layer.msg(result.okMsg);
                                    if (data.ParentId === '' || data.ParentId == undefined || data.ParentId === 0) {
                                        getData();
                                    }
                                    else {
                                        docmentTreeTb.refresh(parseInt(data.ParentId)); //重载表格
                          ");
                WriteLiteral(@"          }
                                } else {
                                    layer.msg(result.errorMsg);
                                }
                            }, 'json');
                            layer.close(index);
                        });
                    }
                    else if (obj.event === 'move') {
                        $(""#CopyFormName"").val(data.FormName);
                        $(""#Move"")[0].checked = false;
                        form.render('checkbox', 'fm_copy')
                        getMoveMenu();
                        windowsIndex = layer.open({
                            type: 1,
                            title: '移动【' + data.FormName + '】模块',
                            area: ['650px', '500px'],
                            content: $('#copy_window'),
                            btn: ['保存', '取消'],
                            yes: function (index, layero) {
                                var menuToId = xmTreeMenu.getValue('valueS");
                WriteLiteral(@"tr');
                                var oper = $(""#Move"")[0].checked;
                                if (!menuToId) {
                                    layer.msg(""请选择文件夹名称！"");
                                    return false;
                                }
                                $.ajax({
                                    url: '/BasicConfig/CRFormsManage/Move',
                                    type: ""post"",
                                    data: {
                                        ""FromId"": data.Id,
                                        ""FormToId"": menuToId,
                                        ""oper"": oper
                                    },
                                    datatype: 'json',
                                    success: function (re) {

                                        if (re.status) {

                                            layer.msg(re.msg);
                                            if (oper)
                       ");
                WriteLiteral(@"                         docmentTreeTb.refresh(parseInt(menuToId));//重载表格
                                            else {
                                                //复制只刷新目标节点 移动刷新两个节点
                                                docmentTreeTb.refresh(parseInt(menuToId))
                                                docmentTreeTb.refresh(parseInt(data.ParentId))

                                            }
                                            layer.close(windowsIndex);//关闭弹出层

                                        }
                                        else {
                                            layer.msg(re.msg);
                                        }
                                    }, error: function (res) {
                                        layer.msg(""加载统计信息错误："" + res.responseText);
                                        layer.close(windowsIndex);
                                    }
                                });
                    ");
                WriteLiteral(@"        },
                            cancel: function (index, layro) { },
                        });
                    }
                });

                //监听停用操作
                form.on('switch(stop)', function (data) {
                    var status = data.value == ""on"" ? false : true;
                    layer.confirm('确定要变更状态吗', {
                        title: '',
                        btn: ['确定', '取消'], //按钮
                        cancel: function (index, layero) {
                            layer.close(index); // 关闭弹窗
                            //getData();
                            docmentTreeTb.refresh(parseInt(data.ParentId)); //重载表格
                        },
                        resize: false
                    }, function (index) {
                        $.ajax(
                            {
                                url: '/BasicConfig/CRFormsManage/UpdateStatus',
                                type: ""post"",
                                data: ");
                WriteLiteral(@"{ 'id': data.elem.name, 'status': status },
                                datatype: 'json',
                                success: function (data) {
                                    debugger
                                    if (data.okMsg) {
                                        layer.msg(data.okMsg);
                                        //getData();
                                        docmentTreeTb.refresh(parseInt(data.ParentId)); //重载表格
                                    }
                                }
                            }
                        )
                    }
                        , function (index) {
                            layer.close(index); // 关闭弹窗
                            //getData();
                            docmentTreeTb.refresh(parseInt(data.ParentId)); //重载表格
                        }
                    );

                });

                //监听提交
                form.on('submit(submit)', function (data) {
        ");
                WriteLiteral(@"            var indexs = layer.load(1);
                    data.field.HospitalDeptId = xmDeptsList2.getValue('valueStr');
                    data.field.DiseaseSpecificGroupId = xmDeptsList7.getValue('valueStr');
                    data.field.BusinessDomain = xmDeptsList8.getValue('valueStr');
                    data.field.StopUsing = data.field.StopUsing == ""on"" ? true : false;
                    //提交 Ajax 成功后，关闭当前弹层并重载表格
                    $.ajax({
                        url: url,
                        type: ""post"",
                        data: { 'node': data.field },
                        datatype: 'json',
                        success: function (result) {
                            if (result.okMsg) {
                                debugger;
                                layer.msg(result.okMsg);
                                layer.close(windowsIndex);//关闭弹出层
                                if ($(""#ParentId"").attr(""reload"") == ""1"") {
                                    d");
                WriteLiteral(@"ocmentTreeTb.refresh(parseInt(data.field.ParentId)); //重载表格
                                }
                                else {
                                    if ($(""#ParentId"").attr(""haveChildName"") == ""1"") {
                                        docmentTreeTb.refresh(parseInt(data.field.ParentId)); //重载表格
                                    }
                                    else {
                                        //getData();
                                        docmentTreeTb.refresh(parseInt(data.field.ParentId)); //重载表格
                                    }
                                }
                            }
                            else {
                                layer.msg(result.errorMsg);
                            }
                            layer.close(indexs);
                        }, error: function (res) {
                            layer.msg(""加载统计信息错误："" + res.responseText);
                            layer.close(indexs);
  ");
                WriteLiteral(@"                      }
                    });
                    return false;
                });

                //监听提交
                form.on('submit(submitFolder)', function (data) {
                    var indexs = layer.load(1);
                    data.field.HospitalDeptId = xmDeptsList3.getValue('valueStr');
                    data.field.DiseaseSpecificGroupId = xmDeptsList6.getValue('valueStr');
                    data.field.StopUsing = true;
                    data.field.FormId = data.field.Folder_FormId;
                    data.field.FormName = data.field.Folder_FormId;
                    data.field.Id = data.field.Folder_Id;
                    data.field.LevelType = data.field.Folder_LevelType;
                    data.field.ParentId = data.field.Folder_ParentId;
                    data.field.OrderBy = data.field.Folder_OrderBy;
                    data.field.Remark = data.field.Folder_Remark;

                    //提交 Ajax 成功后，关闭当前弹层并重载表格
                    $.ajax");
                WriteLiteral(@"({
                        url: url,
                        type: ""post"",
                        data: { 'node': data.field },
                        datatype: 'json',
                        success: function (result) {
                            if (result.okMsg) {
                                debugger;
                                layer.msg(result.okMsg);
                                layer.close(windowsIndex);//关闭弹出层
                                if ($(""#ParentId"").attr(""reload"") == ""1"") {
                                    docmentTreeTb.refresh(parseInt(data.field.ParentId)); //重载表格
                                }
                                else {
                                    if ($(""#ParentId"").attr(""haveChildName"") == ""1"") {
                                        docmentTreeTb.refresh(parseInt(data.field.ParentId)); //重载表格
                                    }
                                    else {
                                        docmentTreeTb.");
                WriteLiteral(@"refresh(parseInt(data.field.ParentId)); //重载表格
                                    }
                                }
                            }
                            else {
                                layer.msg(result.errorMsg);
                            }
                            layer.close(indexs);
                        }, error: function (res) {
                            layer.msg(""加载统计信息错误："" + res.responseText);
                            layer.close(indexs);
                        }
                    });
                    return false;
                });

                $(document).ready(function () {
                    GetDeptsTree();
                    GetGroupsTree8();
                    $(document).on('click', '#Search', function () {
                        getData();
                    });


                    $(document).on('click', '#designForms', function () {
                        layer.open({
                            type: 2,");
                WriteLiteral("\r\n                            title: \'CRF表单设计\',\r\n                            shadeClose: true, // 点击遮罩关闭层\r\n                            area: [\'80%\', \'80%\'], // 设置弹窗大小\r\n                            content: \"");
                Write(
#nullable restore
#line 539 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\CRFormsManage\Index.cshtml"
                                        $"{ViewBag.formUrl}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@""" + '/formMgr/def/add_form.htm'
                        });
                    });

                    $(document).on('click', '#addFolder', function () {
                        $(""#ZBZ"").css(""display"",""none"");
                        $(""#YWY"").css(""display"",""none"");
                        $(""#fm1"")[0].reset();
                        $(""#Folder_Id"").val(""0"");
                        $('#Folder_ParentId').val(0);
                        $('#Folder_LevelType').val(1);
                        $('#Folder_PatientId').attr(""readonly"", ""readonly"");
                        xmDeptsList3.update({
                            disabled: false
                        });
                        windowsIndex = layer.open({
                            type: 1,
                            title: '新增文件夹',
                            area: '500px',
                            resize: true,
                            content: $('#Folder_window')
                        });
                        url ");
                WriteLiteral(@"= '/BasicConfig/CRFormsManage/Create';
                    });
                });

                var xmTreeMenu = xmSelect.render({
                    el: '#treeMenu',
                    model: { label: { type: 'text' } },
                    prop: {
                        name: 'name',
                        value: 'id',
                    },
                    minWidth: 200,
                    radio: true,
                    filterable: true,
                    clickClose: true,
                    //树
                    tree: {
                        show: true,
                        //非严格模式
                        strict: false,
                        //默认展开节点
                        expandedKeys: [-1],
                    }
                    ,
                    data: []
                });

                var xmDeptsList = xmSelect.render({
                    el: '#xmDeptsList',
                    model: { label: { type: 'text' } },
                  ");
                WriteLiteral(@"  prop: {
                        name: 'title',
                        value: 'id',
                    },
                    minWidth: 200,
                    radio: true,
                    filterable: true,
                    clickClose: true,
                    //树
                    tree: {
                        show: true,
                        //非严格模式
                        strict: false,
                        //默认展开节点
                        expandedKeys: [-1],
                    },
                    data: [],
                    // 添加 onChange 事件处理器
                    on: function (val) {
                        GetGroupsTree5(val.arr[0].id);
                    }
                });

                var xmDeptsList2 = xmSelect.render({
                    el: '#xmDeptsList2',
                    model: { label: { type: 'text' } },
                    prop: {
                        name: 'title',
                        value: 'id',
                   ");
                WriteLiteral(@" },
                    minWidth: 200,
                    radio: true,
                    filterable: true,
                    clickClose: true,
                    //树
                    tree: {
                        show: true,
                        //非严格模式
                        strict: false,
                        //默认展开节点
                        expandedKeys: [-1],
                    },
                    data: [],
                    // 添加 onChange 事件处理器
                    on: function (val) {
                        GetGroupsTree7(val.arr[0].id);
                    }
                });

                var xmDeptsList3 = xmSelect.render({
                    el: '#xmDeptsList3',
                    model: { label: { type: 'text' } },
                    prop: {
                        name: 'title',
                        value: 'id',
                    },
                    minWidth: 200,
                    radio: true,
                    filterable: t");
                WriteLiteral(@"rue,
                    clickClose: true,
                    //树
                    tree: {
                        show: true,
                        //非严格模式
                        strict: false,
                        //默认展开节点
                        expandedKeys: [-1],
                    },
                    data: [],
                    // 添加 onChange 事件处理器
                    on: function (val) {
                        GetGroupsTree6(val.arr[0].id);
                    }
                });

                var xmDeptsList5 = xmSelect.render({
                    el: '#xmDeptsList5',
                    model: { label: { type: 'text' } },
                    prop: {
                        name: 'title',
                        value: 'id',
                    },
                    minWidth: 200,
                    radio: false,
                    filterable: true,
                    clickClose: true,
                    //树
                    tree: {
        ");
                WriteLiteral(@"                show: true,
                        //非严格模式
                        strict: false,
                        //默认展开节点
                        expandedKeys: [-1],
                    },
                    data: []
                });
                var xmDeptsList6 = xmSelect.render({
                    el: '#xmDeptsList6',
                    model: { label: { type: 'text' } },
                    prop: {
                        name: 'title',
                        value: 'id',
                    },
                    minWidth: 200,
                    radio: true,
                    filterable: true,
                    clickClose: true,
                    //树
                    tree: {
                        show: true,
                        //非严格模式
                        strict: false,
                        //默认展开节点
                        expandedKeys: [-1],
                    },
                    data: []
                });
                va");
                WriteLiteral(@"r xmDeptsList7 = xmSelect.render({
                    el: '#xmDeptsList7',
                    model: { label: { type: 'text' } },
                    prop: {
                        name: 'title',
                        value: 'id',
                    },
                    minWidth: 200,
                    radio: true,
                    filterable: true,
                    clickClose: true,
                    //树
                    tree: {
                        show: true,
                        //非严格模式
                        strict: false,
                        //默认展开节点
                        expandedKeys: [-1],
                    },
                    data: []
                });

                var xmDeptsList8 = xmSelect.render({
                    el: '#xmDeptsList8',
                    model: { label: { type: 'text' } },
                    prop: {
                        name: 'Name',
                        value: 'Id',
                    },
       ");
                WriteLiteral(@"             minWidth: 200,
                    radio: false,
                    filterable: true,
                    //clickClose: true,
                    //树
                    tree: {
                        show: true,
                        //非严格模式
                        strict: false,
                        //默认展开节点
                        expandedKeys: [-1],
                    },
                    data: []
                });

                function GetDeptsTree() {
                    $.ajax({
                        url: '/CommAPI/GetOrgsTreeList',
                        type: ""post"",
                        datatype: 'json',
                        success: function (result) {
                            xmDeptsList.update({
                                data: result
                            });
                            if (result[0].id) {
                                var arr = new Array();
                                arr.push(result[0].id);
  ");
                WriteLiteral(@"                              GetGroupsTree5(result[0].id);
                                setTimeout(function () {
                                }, 1000);
                            }
                            xmDeptsList2.update({
                                data: result
                            });
                            xmDeptsList3.update({
                                data: result
                            });

                        }, error: function () {
                            layer.msg(""获取失败！"");
                        }
                    })
                };

                function GetGroupsTree5(val) {
                    $.ajax({
                        url: '/CommAPI/GetGroupTreeList?hospitalDeptId=' + val,
                        type: ""post"",
                        datatype: 'json',
                        async: false,
                        success: function (result) {
                            xmDeptsList5.update({
             ");
                WriteLiteral(@"                   data: result
                            });
                        }, error: function () {
                            layer.msg(""获取失败！"");
                        }
                    })
                };
                function GetGroupsTree6(val) {
                    $.ajax({
                        url: '/CommAPI/GetGroupTreeList?hospitalDeptId=' + val,
                        type: ""post"",
                        datatype: 'json',
                        async: false,
                        success: function (result) {
                            xmDeptsList6.update({
                                data: result
                            });
                        }, error: function () {
                            layer.msg(""获取失败！"");
                        }
                    })
                };
                function GetGroupsTree7(val) {
                    $.ajax({
                        url: '/CommAPI/GetGroupTreeList?hospitalDeptId=' + val");
                WriteLiteral(@",
                        type: ""post"",
                        datatype: 'json',
                        async: false,
                        success: function (result) {
                            xmDeptsList7.update({
                                data: result
                            });
                        }, error: function () {
                            layer.msg(""获取失败！"");
                        }
                    })
                };

                function GetGroupsTree8() {
                    $.ajax({
                        url:'/CommAPI/SelectDataDicts?DataDictName=业务域',
                        type: ""post"",
                        datatype: 'json',
                        async: false,
                        success: function (result) {
                            xmDeptsList8.update({
                                data: JSON.parse(result)
                            });
                        }, error: function () {
                            la");
                WriteLiteral(@"yer.msg(""获取失败！"");
                        }
                    })
                };

                function getData() {
                    treeTable.reload('docmentTreeTb', {
                        page: {
                            curr: 1
                        },
                        where: {
                            'pid':null,
                            'hospitalDeptId': xmDeptsList.getValue('valueStr'),
                            'groupid' : xmDeptsList5.getValue('valueStr'),
                            'stopUsing': $(""#status option:selected"").val(),
                        }
                    });
                };

                function getMoveMenu() {
                    $.get('/BasicConfig/CRFormsManage/GetMenuAll', function (result) {
                        xmTreeMenu.update({
                            data: result
                        });
                    })
                };

                $(document).on('click', '#Search', function () ");
                WriteLiteral(@"{
                    getData();
                });
            });
        </script>
        <script type=""text/html"" id=""switchTpl"">
            <!-- 这里的 checked 的状态只是演示 -->
            <input type=""checkbox"" id=""switch"" name=""{{d.Id}}"" value=""{{d.StopUsing}}"" lay-skin=""switch""");
                BeginWriteAttribute("lay-text", " lay-text=\"", 37706, "\"", 37717, 0);
                EndWriteAttribute();
                WriteLiteral(" lay-filter=\"stop\" {{");
                BeginWriteAttribute("d.StopUsing", " d.StopUsing =", 37739, "", 37753, 0);
                EndWriteAttribute();
                WriteLiteral("= \'on\' ? \'checked\' : \'\' }}>\r\n        </script>\r\n    </div>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n<!--新增/修改弹框-->\r\n<div class=\"window_wrap\" id=\"form_window\" style=\"display: none\">\r\n    <form class=\"layui-form\" lay-filter=\"fm\" id=\"fm\"");
            BeginWriteAttribute("action", " action=\"", 37958, "\"", 37967, 0);
            EndWriteAttribute();
            WriteLiteral(@">
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">组织</label>
            <div class=""layui-input-block"">
                <div id=""xmDeptsList2""></div>
            </div>
        </div>
        <div class=""layui-form-item"" id=""ZBZ1"">
            <label class=""layui-form-label"">专病组</label>
            <div class=""layui-input-block"">
                <div id=""xmDeptsList7""></div>
            </div>
        </div>
        <div class=""layui-form-item"" id=""YWY1"">
            <label class=""layui-form-label"">业务域</label>
            <div class=""layui-input-block"">
                <div id=""xmDeptsList8""></div>
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">表单ID</label>
            <div class=""layui-input-block "">
                <input type=""text"" name=""Id"" id=""Id"" style=""display:none;"" />
                <input type=""text"" name=""ParentId"" id=""ParentId"" haveChildName=""0"" reload=""0"" style=""display");
            WriteLiteral(@":none;"" />
                <input type=""text"" name=""LevelType"" id=""LevelType"" style=""display:none;"" />
                <input type=""text"" name=""FormId"" id=""FormId"" lay-verify=""required"" placeholder=""请输入表单ID"" autocomplete=""off"" class=""layui-input"">
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">表单名称</label>
            <div class=""layui-input-block "">
                <input type=""text"" name=""FormName"" id=""FormName"" placeholder=""请输入表单名称"" autocomplete=""off"" class=""layui-input"">
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">启停开关</label>
            <div class=""layui-input-block"">
                <input type=""checkbox"" name=""StopUsing"" id=""StopUsing"" lay-skin=""switch""");
            BeginWriteAttribute("lay-text", " lay-text=\"", 39816, "\"", 39827, 0);
            EndWriteAttribute();
            WriteLiteral(@">
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">排序</label>
            <div class=""layui-input-block"">
                <input type=""text"" name=""OrderBy"" id=""OrderBy"" placeholder=""请输入排序号"" autocomplete=""off"" class=""layui-input"">
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">备注</label>
            <div class=""layui-input-block"">
                <textarea name=""Remark"" id=""Remark"" class=""layui-textarea"" style=""resize: none""></textarea>
            </div>
        </div>
        <div class=""layui-form-item"">
            <div class=""layui-input-block"">
                <button type=""button"" class=""layui-btn"" lay-submit lay-filter=""submit"">提交</button>
                <button type=""reset"" class=""layui-btn layui-btn-primary"" id=""btn_reset"">重置</button>
            </div>
        </div>
    </form>
</div>


<!--新增文件夹-->
<div class=""window_wrap"" id=""Folder_win");
            WriteLiteral("dow\" style=\"display: none\">\r\n    <form class=\"layui-form\" lay-filter=\"fm1\" id=\"fm1\"");
            BeginWriteAttribute("action", " action=\"", 40935, "\"", 40944, 0);
            EndWriteAttribute();
            WriteLiteral(@">
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">组织</label>
            <div class=""layui-input-block"">
                <div id=""xmDeptsList3""></div>
            </div>
        </div>
        <div class=""layui-form-item"" id=""ZBZ"">
            <label class=""layui-form-label"">专病组</label>
            <div class=""layui-input-block"">
                <div id=""xmDeptsList6""></div>
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">文件夹名称</label>
            <div class=""layui-input-block "">
                <input type=""text"" name=""Folder_Id"" id=""Folder_Id"" style=""display:none;"" />
                <input type=""text"" name=""Folder_ParentId"" id=""Folder_ParentId"" haveChildName=""0"" reload=""0"" style=""display:none;"" />
                <input type=""text"" name=""Folder_LevelType"" id=""Folder_LevelType"" style=""display:none;"" />
                <input type=""text"" name=""Folder_FormName"" id=""Folder_FormName"" style=""d");
            WriteLiteral(@"isplay:none;"">
                <input type=""text"" name=""Folder_FormId"" id=""Folder_FormId"" lay-verify=""required"" placeholder=""请输入文件夹名称"" autocomplete=""off"" class=""layui-input"">
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">排序</label>
            <div class=""layui-input-block"">
                <input type=""text"" name=""Folder_OrderBy"" id=""Folder_OrderBy"" lay-verify=""required"" placeholder=""请输入排队序号"" autocomplete=""off"" class=""layui-input"">
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">备注</label>
            <div class=""layui-input-block"">
                <textarea name=""Folder_Remark"" id=""Folder_Remark"" class=""layui-textarea"" style=""resize: none""></textarea>
            </div>
        </div>
        <div class=""layui-form-item"">
            <div class=""layui-input-block"">
                <button type=""button"" class=""layui-btn"" lay-submit lay-filter=""submitFolde");
            WriteLiteral(@"r"">提交</button>
                <button type=""reset"" class=""layui-btn layui-btn-primary"" id=""btn_reset"">重置</button>
            </div>
        </div>
    </form>
</div>

<!--复制/移动弹框-->
<div class=""window_wrap"" id=""copy_window"" style=""display: none ;min-height:90%; position:relative;"">
    <form class=""layui-form"" lay-filter=""fm_copy"" id=""fm_copy""");
            BeginWriteAttribute("action", " action=\"", 43349, "\"", 43358, 0);
            EndWriteAttribute();
            WriteLiteral(@">
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">表单名称</label>
            <div class=""layui-input-block"">
                <input type=""text"" name=""CopyFormName"" id=""CopyFormName"" readonly=""readonly"" class=""layui-input"" />
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">操作方式</label>
            <div class=""layui-input-block"">
                <input type=""checkbox"" name=""Move"" id=""Move"" lay-skin=""switch"" lay-text=""复制|移动"">
                <input type=""hidden"" name=""MoveValue"" id=""MoveValue"">
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">目标菜单</label>
            <div class=""layui-input-block"">
                <div id=""treeMenu"">

                </div>
            </div>
        </div>

    </form>
</div>
</html>
");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
