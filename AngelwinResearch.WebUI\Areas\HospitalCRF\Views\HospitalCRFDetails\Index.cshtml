﻿@{
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全院级CRF详细数据</title>

    <link href="~/layuiadmin/layui/css/layui.css" rel="stylesheet" />

    <link href="~/querybuilder/css/bootstrap.min.css" rel="stylesheet" />
    <link href="~/querybuilder/css/chosen.min.css" rel="stylesheet" />
    <link href="~/querybuilder/css/awesome-bootstrap-checkbox.css" rel="stylesheet" />
    <link href="~/querybuilder/css/bootstrap-slider.min.css" rel="stylesheet" />
    <link href="~/querybuilder/css/selectize.bootstrap5.css" rel="stylesheet" />
    <link href="~/querybuilder/css/bootstrap-icons.min.css" rel="stylesheet" />
    <link href="~/querybuilder/css/query-builder.default.css" rel="stylesheet" />
    <script src="~/js/jquery-3.5.1.min.js"></script>
    <script src="~/js/common.js"></script>
    <script src="~/js/marked.min.js"></script>

    <style>
        .rectangle {
            width: 200px; /* 宽度 */
            float: left;
            margin-left: 20px;
            padding: 40px 15px;
        }

        .sex0 {
            color: #FF5722;
        }

        .sex1 {
            color: #1E9FFF;
        }
        /* 定义表头样式 */
        .table_header {
            font-weight: bold; /* 加粗 */
            font-size: 14px; /* 可选：调整字体大小以提升可读性 */
            color: #333; /* 可选：调整字体颜色 */
        }
    </style>
</head>

<body style="padding:5px;">
    <div class="layui-col-md12">
        <div class="layui-card">
            <div class="layui-card-body layui-form">
                <div class="layui-inline">
                    <div id="xmDeptsList" style="width:220px"></div>
                </div>
                <div class="layui-inline">
                    <div id="xmGroupList" style="width:250px"></div>
                </div>
                <div class="layui-inline">
                    <input type="text" class="layui-input" name="keyWord" placeholder="请输入患者ID/住院号/姓名" id="keyWord" value="" style="width:260px" />
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-normal fr" id="Search">查 &nbsp;&nbsp;询</button>
                    <button class="layui-btn fr" id="download">下 &nbsp;&nbsp;载</button>
                </div>
            </div>
            <div class="layui-card-body">
                <table id="tablelist" lay-filter="tablelist"></table>
                <script type="text/html" id="tableBar1">
                    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="show"><i class="layui-icon layui-icon-eye"></i>病历文书</a>
                </script>
                <div class="layui-inline">
                    <span style="color:red;">注：“——”表示无需填写内容。</span>
                </div>
            </div>
            <div id="txtMedical" style="display:none; padding : 15px "></div>
        </div>
    </div>
    <script src="~/layuiadmin/layui/layui.js"></script>
    <script src="~/layuiadmin/layuiextend/xm-select.js"></script>
    <script src="~/js/jquery-3.5.1.min.js"></script>

    <script>
        layui.use(['element', 'layer', 'table', 'laydate', 'form'], function () {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , $ = layui.$
                , form = layui.form;
            ;

            var xmDeptsList = xmSelect.render({
                el: '#xmDeptsList',
                model: { label: { type: 'text' } },
                prop: {
                    name: 'title',
                    value: 'id',
                },
                minWidth: 200,
                radio: true,
                filterable: true,
                clickClose: true,
                //树
                tree: {
                    show: true,
                    //非严格模式
                    strict: false,
                    //默认展开节点
                    expandedKeys: [-1],
                },
                data: [],
                // 添加 onChange 事件处理器
                on: function (val) {
                    GetGroupsTree(val.arr[0].id);
                }
            });

            var xmGroupList = xmSelect.render({
                el: '#xmGroupList',
                model: { label: { type: 'text' } },
                prop: {
                    name: 'title',
                    value: 'id',
                },
                minWidth: 200,
                radio: true,
                filterable: true,
                clickClose: true,
                //树
                tree: {
                    show: true,
                    //非严格模式
                    strict: false,
                    //默认展开节点
                    expandedKeys: [-1],
                },
                data: [],
                // 添加 onChange 事件处理器
                on: function (val) {
                    setTimeout(function () {
                        SearchData();
                    }, 500);
                }
            });

            function GetDeptsTree() {
                $.ajax({
                    url: '/HospitalCRF/HospitalCRFDetails/GetOrgsTreeList?Type=03',
                    type: "post",
                    datatype: 'json',
                    success: function (result) {
                        xmDeptsList.update({
                            data: result
                        });
                        if (result[0].id) {
                            var arr = new Array();
                            arr.push(result[0].id);
                            xmDeptsList.setValue(arr);
                            GetGroupsTree(result[0].id);
                        }

                    }, error: function () {
                        layer.msg("获取失败！");
                    }
                })
            };

            function GetGroupsTree(val) {
                $.ajax({
                    url: '/HospitalCRF/HospitalCRFDetails/GetGroupList?hospitalDeptId=' + val,
                    type: "post",
                    datatype: 'json',
                    success: function (result) {
                        xmGroupList.update({
                            data: result
                        });
                        if (result[0].id) {
                            var arr = new Array();
                            arr.push(result[0].id);
                            xmGroupList.setValue(arr);
                            setTimeout(function () {
                                SearchData();
                            }, 500);
                        }
                    }, error: function () {
                        layer.msg("获取失败！");
                    }
                })
            };


            $(document).ready(function () {
                GetDeptsTree();
                $(document).on('click', '#Search', function () {
                    var deptId = xmDeptsList.getValue('valueStr');
                    var groupName = xmGroupList.getValue('valueStr');
                    if ($.trim(groupName) != "") {
                        SearchData();
                    }
                    else {
                        layer.msg("请选择执行科室以及表单分组!");
                    }

                })

                $("#download").on("click", function () {
                    bdownload();
                });
            });



            function SearchData() {
                var indes = layer.load(1);
                var deptId = xmDeptsList.getValue('valueStr');
                var groupName = xmGroupList.getValue('valueStr');
                if ($.trim(deptId) != "" && $.trim(groupName) != "") {

                    $.ajax({
                        type: 'post',
                        url: "/HospitalCRF/HospitalCRFDetails/List?deptId=" + deptId + "&groupName=" + groupName + "&keyWord=" + $("#keyWord").val() ,
                        dataType: 'json',
                        contentType: "application/json; charset=utf-8",
                        success: function (result) {
                            if (result.okMsg) {
                                layer.close(indes);
                                if (result.columns[1] && Array.isArray(result.columns[1]) && result.columns[1].length > 0) {
                                    result.columns[1].push({
                                        title: '<span class=\"table_header\">操作</span>',
                                        field: 'operation',
                                        toolbar: '#tableBar1',
                                        align: 'center',
                                        width: 100,
                                        fixed: 'right'
                                    });
                                }
                                table.render({
                                    elem: '#tablelist'
                                    , id: 'tablelist'
                                    , limit: Number.MAX_VALUE // 数据表格默认全部显示
                                    , page: false
                                    , cols: result.columns
                                    , height: 'full-150' // 最大高度减去其他容器已占有的高度差
                                    , data: result.list
                                    , done: function (res, curr, count, origin) {
                                        // 找到class为NoNeedInputSpan的span元素
                                        $('.NoNeedInputSpan').closest('td').css('background-color', '#eee');
                                    }
                                });
                            }
                            else {
                                layer.close(indes);
                                layer.msg(result.errorMsg);
                            }
                        },
                        error: function (data) {
                            layer.close(indes);
                        }
                    });
                }
                else {
                    layer.alert('请选择执行科室以及表单分组!');
                    layer.close(indes);
                }
            }

            function bdownload() {
                var keyWord = $("#keyWord").val();
                var deptId = xmDeptsList.getValue('valueStr');
                var groupName = xmGroupList.getValue('valueStr');

                if ($.trim(deptId) == "" || $.trim(groupName) == "")
                    layer.alert('请选择执行科室以及表单分组!');
                else {
                    var url = "/HospitalCRF/HospitalCRFDetails/DownLoad?deptId=" + deptId + "&groupName=" + groupName + "&keyWord=" + keyWord ;
                    window.location.href = url;
                }
            }

            //触发单元格工具事件
            table.on('tool(tablelist)', function (obj) { // 双击 toolDouble
                var data = obj.data;
                if (obj.event === 'show') {
                    var htmlContent = marked.parse(data.AIExtractJsonValue);
                    $("#txtMedical").html(htmlContent);

                    var div = $('#txtMedical');
                    div.scrollTop(div[0].scrollHeight - div.innerHeight());
                    layer.open({
                        type: 1,
                        title: '病历文书',
                        shadeClose: true, // 点击遮罩关闭层
                        area: ['45%', '70%'], // 设置弹窗大小
                        content: $("#txtMedical")
                    });
                }
            });
        });
    </script>


</body>



</html>
