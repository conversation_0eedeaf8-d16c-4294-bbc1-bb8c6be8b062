#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\OrgManage\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "3cbb919ab28d97dff511f9c5764d8cfe9261eb323dd917d702ae94c65146c9f9"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_BasicConfig_Views_OrgManage_Index), @"mvc.1.0.view", @"/Areas/BasicConfig/Views/OrgManage/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"3cbb919ab28d97dff511f9c5764d8cfe9261eb323dd917d702ae94c65146c9f9", @"/Areas/BasicConfig/Views/OrgManage/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_BasicConfig_Views_OrgManage_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("type", new global::Microsoft.AspNetCore.Html.HtmlString("text/javascript"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\OrgManage\Index.cshtml"
  
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "3cbb919ab28d97dff511f9c5764d8cfe9261eb323dd917d702ae94c65146c9f95595", async() => {
                WriteLiteral("\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>组织管理</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "3cbb919ab28d97dff511f9c5764d8cfe9261eb323dd917d702ae94c65146c9f96022", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"

    <style>
        .layui-tab-brief {
            background-color: #fff;
        }

        .search_wrap {
            background-color: #f0f0f0;
        }

        .line_wrap {
            padding: 10px 15px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .layui-colla-title {
            padding: 0 15px 0 15px;
        }

        .layui-show {
            display: flex !important;
            align-items: flex-end;
        }

        .form_wrap {
            flex: 1;
        }

        .form_item {
            padding-top: 10px;
        }

        .btnwrap {
            padding-left: 10px;
        }

        .table_wrap {
            overflow: hidden;
        }

        .layui-form-label {
            width: 120px;
        }

        .layui-input-block {
            margin-left: 150px;
        }
        /* 定义表头样式 */
        .layui-table-header .layui-table-cell {
            fon");
                WriteLiteral("t-weight: bold; /* 加粗 */\r\n            font-size: 14px; /* 可选：调整字体大小以提升可读性 */\r\n            color: #333; /* 可选：调整字体颜色 */\r\n        }\r\n    </style>\r\n\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "3cbb919ab28d97dff511f9c5764d8cfe9261eb323dd917d702ae94c65146c9f99143", async() => {
                WriteLiteral(@"
    <div class=""container layui-form"">
        <div class=""layui-tab-brief"">
            <ul class=""layui-tab-title"">
                <li class=""layui-this"" data-value=""01"">医学中心</li>
                <li data-value=""02"">医疗组</li>
                <li data-value=""03"">科室</li>
            </ul>
        </div>
        <!--搜索区-->
        <div class=""line_wrap search_wrap"">
            <div>
                <div class=""layui-inline"">

                    <div class=""layui-input-inline"">
                        <input type=""text"" name=""KeyWords"" id=""KeyWords"" placeholder=""请输入关键字"" autocomplete=""off"" class=""layui-input"">
                    </div>
                    <button class=""layui-btn layui-btn-primary layui-border-green"" id=""Search""><i class=""layui-icon layui-icon-search""></i> </button>
                </div>
            </div>
            <div>
                <button type=""button"" class=""layui-btn layui-bg-blue"" id=""Add""><i class=""layui-icon layui-icon-add-1""></i></button>
            </d");
                WriteLiteral("iv>\r\n        </div>\r\n        <!--编辑区-->\r\n        <div class=\"edit_area\">\r\n");
                WriteLiteral(@"            <div class=""line_wrap layui-card layui-colla-content layui-show"">
                <div class=""layui-row form_wrap layui-form"">
                    <div class=""layui-col-xs6 layui-col-sm6 layui-col-md6"">
                        <div class=""form_item"">
                            <label class=""layui-form-label"" id=""lblCode"">医学中心代码：</label>
                            <div class=""layui-input-block"">
                                <input type=""text"" name=""Id"" id=""Id"" style=""display:none;"" />
                                <input type=""text"" name=""ParentId"" id=""ParentId"" style=""display:none;"" haveChildName=""0"" reload=""0"" />

                                <input type=""text"" name=""DeptCode"" id=""DeptCode"" required lay-verify=""required"" autocomplete=""off"" class=""layui-input"">
                            </div>
                        </div>
                    </div>
                    <div class=""layui-col-xs6 layui-col-sm6 layui-col-md6"">
                        <div class=""form_item"">");
                WriteLiteral(@"
                            <label class=""layui-form-label"" id=""lblName"">医学中心名称：</label>
                            <div class=""layui-input-block"">
                                <input type=""text"" name=""DeptName"" id=""DeptName"" required lay-verify=""required"" autocomplete=""off"" class=""layui-input"" />
                            </div>
                        </div>
                    </div>
                    <div class=""layui-col-xs12 layui-col-sm12 layui-col-md12"" id=""xmDeptsDiv"">
                        <div class=""form_item"">
                            <label class=""layui-form-label"" id=""labelContent"">组成科室：</label>
                            <div class=""layui-input-block"">
                                <div id=""xmDeptsList""></div>
                            </div>
                        </div>
                    </div>
                    <div class=""layui-col-xs12 layui-col-sm12 layui-col-md12"">
                        <div class=""form_item"">
                            <label");
                WriteLiteral(@" class=""layui-form-label"">简介：</label>
                            <div class=""layui-input-block"">
                                <textarea name=""DeptIntro"" id=""DeptIntro"" class=""layui-textarea"" style=""resize: none""></textarea>
                            </div>
                        </div>
                    </div>

                </div>
                <div class=""btnwrap"">
                    <button class=""layui-btn"" lay-submit lay-filter=""save"">保存</button>
                </div>
            </div>
        </div>

        <!--数据列表区-->
        <div");
                BeginWriteAttribute("class", " class=\"", 5250, "\"", 5258, 0);
                EndWriteAttribute();
                WriteLiteral(">\r\n            <div class=\"layui-card\">\r\n");
                WriteLiteral(@"                <div class=""layui-card-body table_wrap"">
                    <table class=""layui-hide"" id=""tablelist"" lay-filter=""tablelist""></table>
                    <script type=""text/html"" id=""tableBar1"">

                        <a class=""layui-btn layui-btn-danger layui-btn-xs"" lay-event=""del""><i class=""layui-icon layui-icon-delete""></i>删除</a>

                    </script>
                </div>
            </div>
        </div>
    </div>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "3cbb919ab28d97dff511f9c5764d8cfe9261eb323dd917d702ae94c65146c9f914961", async() => {
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "3cbb919ab28d97dff511f9c5764d8cfe9261eb323dd917d702ae94c65146c9f916108", async() => {
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"


<script type=""text/javascript"">
    layui.use(['jquery', 'layer', 'form', 'element', 'code', 'laytpl', 'table'], function () {
        var layer = layui.layer,
            $ = layui.$,
            form = layui.form,
            laytpl = layui.laytpl,
            element = layui.element,
            table = layui.table;

        var type = ""01"";
        $("".layui-tab-title"").on(""click"", ""li"", function (e) {
            var name = $(this).text();
            $(""#lblCode"").html(name + ""代码："");
            $(""#lblName"").html(name + ""名称："");
            type = $(this).attr(""data-value"");
            var keyWord = $.trim($(""#KeyWords"").val());

            var newCols = """";
            if (type == ""02"") {  //医疗组显示列：医疗组名称、医疗自代码、所属科室(只能选一个) 、简介；
                $(""#xmDeptsDiv"").show();
                $(""#labelContent"").html(""所属科室"");
                newCols = [
                    { type: 'numbers', fixed: 'left' },
                    { field: 'DeptName', title: '医疗组名称', width: 400 },
   ");
            WriteLiteral(@"                 { field: 'DeptCode', title: '医疗自代码', width: 250 },
                    { field: 'MakeUpName', title: '所属科室', width: 300 },
                    { field: 'DeptIntro', title: '简介', },
                    { fixed: 'right', align: 'center', title: '操作', width: 220, toolbar: '#tableBar1' }
                ];
            }
            else if (type == ""03"")  // 科室显示：科室名称、科室代码、简介；
            {
                $(""#xmDeptsDiv"").hide();
                newCols = [
                    { type: 'numbers', fixed: 'left' },
                    { field: 'DeptName', title: '科室名称', width: 400 },
                    { field: 'DeptCode', title: '科室代码', width: 250 },
                    { field: 'DeptIntro', title: '简介', },
                    { fixed: 'right', align: 'center', title: '操作', width: 220, toolbar: '#tableBar1' }
                ];
            }
            else {
                $(""#xmDeptsDiv"").show();
                $(""#labelContent"").html(""组成科室"");
                newCols = [
");
            WriteLiteral(@"
                    { type: 'numbers', fixed: 'left' },
                    { field: 'DeptName', title: '名称', width: 400 },
                    { field: 'DeptCode', title: '代码', width: 250 },
                    { field: 'MakeUpName', title: '组成科室', width: 300 },
                    { field: 'DeptIntro', title: '简介', },
                    { fixed: 'right', align: 'center', title: '操作', width: 220, toolbar: '#tableBar1' }
                ];
            }
            delete tableIns.config.cols; // 删除当前的
            tableIns = table.reload('tablelist', {
                where: { type: type, keyWord: keyWord }
                , cols: [newCols]
                , page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
            clearForm();
        });


        $(document).on('click', '#Search', function () {
            var keyWord = $.trim($(""#KeyWords"").val());
            tableIns = table.reload('tablelist', { where: { type: type, keyWord: keyWord } });
  ");
            WriteLiteral(@"      });

        $(document).on('click', '#Add', function () {
            clearForm();
        });

        function clearForm() {
            $('#Id').val("""");
            $('#ParentId').val("""");
            $('#DeptName').val("""");
            $('#DeptCode').val("""");
            $('#DeptIntro').val("""");
            xmDeptsList.setValue([]);
        }
        var tableIns = table.render({
            elem: '#tablelist',
            url: ""/BasicConfig/OrgManage/GetList"",
            where: { type: type },
            height: 'full-370',
            cols: [[
                { type: 'numbers', fixed: 'left' },
                { field: 'DeptName', title: '名称', width: 400 },
                { field: 'DeptCode', title: '代码', width: 250 },
                { field: 'MakeUpName', title: '组成科室', width: 300 },
                { field: 'DeptIntro', title: '简介', },
                { fixed: 'right', align: 'center', title: '操作', width: 220, toolbar: '#tableBar1' }
            ]],
            p");
            WriteLiteral(@"age: true
        });

        // 工具列点击事件
        table.on('row(tablelist)', function (obj) {
            var event = obj.event;
            var data = obj.data;
            if (data.MakeUp) {
                var arr = data.MakeUp.split(',');
                xmDeptsList.setValue(arr);
            }
            else {
                xmDeptsList.setValue([]);
            }
            $('#Id').val(data.Id);
            $('#ParentId').val(data.ParentId);
            $('#DeptName').val(data.DeptName);
            $('#DeptCode').val(data.DeptCode);
            $('#DeptIntro').val(data.DeptName);
        });

        var xmDeptsList = xmSelect.render({
            el: '#xmDeptsList',
            model: { label: { type: 'text' } },
            prop: {
                name: 'title',
                value: 'id',
            },
            minWidth: 200,
            radio: false,
            filterable: true,
            //树
            tree: {
                show: true,
            ");
            WriteLiteral(@"    //非严格模式
                strict: true,
                //默认展开节点
                expandedKeys: [-1]
            },
            data: []
        });

        function GetDeptsTree() {
            $.ajax({
                url: '/CommAPI/GetOrgCompositionTreeList',
                type: ""post"",
                datatype: 'json',
                success: function (result) {
                    xmDeptsList.update({
                        data: result
                    });
                }, error: function () {
                    layer.msg(""获取失败！"");
                }
            })
        };
        GetDeptsTree();
        table.on('tool(tablelist)', function (obj) {
            var event = obj.event;
            var data = obj.data;
            if (obj.event === 'del') {
                layer.confirm('确定要删除【' + data.DeptName + '】吗？将无法恢复。', {
                    title: '',
                    btn: ['确定', '取消'], //按钮
                    resize: false
                }, function ");
            WriteLiteral(@"(index) {
                    $.post('/BasicConfig/OrgManage/DelDept', { id: data.Id }, function (res) {
                        layer.msg(res.msg);
                        if (res.code == 0) {
                            var keyWord = $.trim($(""#KeyWords"").val());
                            tableIns =  table.reload('tablelist', { where: { type: type, keyWord: keyWord } });
                            clearForm();
                        }
                    })
                    layer.close(index);
                });
            }
        })

        // 提交事件
        form.on('submit(save)', function (data) {
            var field = data.field; // 获取表单字段值
            // 显示填写结果，仅作演示用
            field.DeptType = type;
            var makeUp = xmDeptsList.getValue('valueStr');
            field.MakeUp = makeUp;
            $.post('/BasicConfig/OrgManage/SaveForm', field, function (res) {
                layer.msg(res.msg);
                if (res.code == 0) {
                    var k");
            WriteLiteral(@"eyWord = $.trim($(""#KeyWords"").val());
                    tableIns =   table.reload('tablelist', { where: { type: type, keyWord: keyWord } });
                    if (!field.id) {
                        clearForm();
                    }
                }
            })
            return false; // 阻止默认 form 跳转
        });

        function setTableH() {
            var winH = $(window).height();
            var navH = $("".layui-tab-brief"").height();
            var searchH = $("".search_wrap"").height();
            var editAreaH = $("".edit_area"").height();
            var tableH = winH - (navH + searchH + editAreaH) - 55 + ""px"";
            console.log(tableH)
            $("".table_wrap"").css(""height"", tableH);

        };
        setTableH();
        $(window).resize(function () {
            setTableH()
        });

    });
</script>
</html>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
