#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Account\Login.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "1c2c5290dd2ac327f82e06e7e90092f3b6705e6d140fe17a9fd50d3b8b8bc09f"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_Account_Login), @"mvc.1.0.view", @"/Views/Account/Login.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI

#nullable disable
    ;
#nullable restore
#line 2 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"1c2c5290dd2ac327f82e06e7e90092f3b6705e6d140fe17a9fd50d3b8b8bc09f", @"/Views/Account/Login.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"a5bcd83cf27c8ffb8baf597d24314352bf7b52b5a9bb5ee83e83e9d0089a628e", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_Account_Login : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/login2_style.css?V=1.3"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/images/Hospitallogo.png"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("alt", new global::Microsoft.AspNetCore.Html.HtmlString(""), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("layui-form"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("min-width:320px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/images/color.png"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Account\Login.cshtml"
  
    ViewBag.Title = "Login";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"zh\">\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "1c2c5290dd2ac327f82e06e7e90092f3b6705e6d140fe17a9fd50d3b8b8bc09f7755", async() => {
                WriteLiteral(@"
    <meta charset=""UTF-8"">
    <meta http-equiv=""X-UA-Compatible"" content=""IE=edge"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <meta name=""full-screen"" content=""yes"">
    <meta name=""x5-fullscreen"" content=""true"">
    <title>登录</title>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "1c2c5290dd2ac327f82e06e7e90092f3b6705e6d140fe17a9fd50d3b8b8bc09f8337", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "1c2c5290dd2ac327f82e06e7e90092f3b6705e6d140fe17a9fd50d3b8b8bc09f9540", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "1c2c5290dd2ac327f82e06e7e90092f3b6705e6d140fe17a9fd50d3b8b8bc09f10743", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "1c2c5290dd2ac327f82e06e7e90092f3b6705e6d140fe17a9fd50d3b8b8bc09f11867", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n<script>\r\n    if (window != top) {\r\n        top.location.href = location.href;\r\n    };\r\n</script>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "1c2c5290dd2ac327f82e06e7e90092f3b6705e6d140fe17a9fd50d3b8b8bc09f13804", async() => {
                WriteLiteral(@"
    <div class=""wrap"">
        <div class=""content_wrap"">
            <div class=""content"">
                <div class=""content_L"">
                    <div class=""content_L_title"">
                        <div class=""logo2"">
                            <label hidden>$$$$登陆页面$$$$</label>
                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("img", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "1c2c5290dd2ac327f82e06e7e90092f3b6705e6d140fe17a9fd50d3b8b8bc09f14422", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n                        </div>\r\n                        <div class=\"title_wrap\">\r\n                            <p class=\"title_text\">");
                Write(
#nullable restore
#line 37 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Account\Login.cshtml"
                                                    ViewBag.SiteTitle

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</p>\r\n                        </div>\r\n");
                WriteLiteral("                    </div>\r\n                </div>\r\n                <div class=\"content_R\">\r\n                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "1c2c5290dd2ac327f82e06e7e90092f3b6705e6d140fe17a9fd50d3b8b8bc09f16275", async() => {
                    WriteLiteral(@"
                        <div class=""content_R_title"">用户登录</div>
                        <!-- 账号 -->
                        <div class=""layui-form-item"">
                            <label class=""layui-form-label"">账 号:</label>
                            <div class=""layui-input-inline"">
                                <input type=""text"" name=""username"" lay-verify=""username"" autocomplete=""off""
                                       placeholder=""请输入用户名"" class=""layui-input"" />
                            </div>
                        </div>
                        <!-- 密码 -->
                        <div class=""layui-form-item "">
                            <label class=""layui-form-label"">密 码:</label>
                            <div class=""layui-input-inline"">
                                <input type=""password"" name=""password"" lay-verify=""password"" autocomplete=""off""
                                       placeholder=""请输入密码"" class=""layui-input"" />
                            </div>
       ");
                    WriteLiteral(@"                 </div>
                        <!-- 验证码 -->
                        <div class=""layui-form-item "">

                            <label class=""layui-form-label"">验证码:</label>
                            <div class=""layui-input-inline"">
                                <div class=""layui-input-inline code_input"">
                                    <input type=""text"" name=""PassCode"" lay-verify=""vercode"" autocomplete=""off""
                                           placeholder=""请输入验证码"" class=""layui-input vercode_input"" />
                                    <div class=""vercode"">  <img");
                    BeginWriteAttribute("src", " src=\"", 3181, "\"", 3228, 1);
                    WriteAttributeValue("", 3187, 
#nullable restore
#line 69 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Account\Login.cshtml"
                                                                      Url.Content("~/Account/GetValidateCode")

#line default
#line hidden
#nullable disable
                    , 3187, 41, false);
                    EndWriteAttribute();
                    WriteLiteral(@" alt='点击刷新' style=""cursor: pointer;"" id=""valiCode"" /></div>
                                </div>
                            </div>
                            <!-- <div class=""error_text"">用户名或密码错误!</div> -->
                        </div>
                        <!-- 按钮 -->
                        <div class=""layui-form-item login_btn"">
                            <label class=""layui-form-label""></label>
                            <div class=""layui-input-inline"">
                                <button type=""button"" class=""layui-btn layui-btn-fluid layui-bg-black"" lay-submit lay-filter=""Submit"">登 录</button>
                            </div>
                        </div>
                    ");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n                </div>\r\n\r\n            </div>\r\n        </div>\r\n        <div class=\"foot_wrap\">\r\n            <div class=\"foot_inner\">\r\n                <div class=\"logo\">\r\n                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("img", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "1c2c5290dd2ac327f82e06e7e90092f3b6705e6d140fe17a9fd50d3b8b8bc09f20979", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
                </div>
                <div class=""provider"">
                    <p>北京天助盈通技术有限公司 V1.0</p>
                    <p>Beijing Angelwin Technology Co.Ltd.</p>
                </div>
            </div>
        </div>
    </div>

    <div id=""ChangePWD"" style=""display:none"">
        <div class=""layui-card"">
            <div class=""layui-card-header"" style=""color:red;"" id=""updatepwdtips"">修改密码</div>
            <div class=""layui-card-body""");
                BeginWriteAttribute("pad15", " pad15=\"", 4645, "\"", 4653, 0);
                EndWriteAttribute();
                WriteLiteral(">\r\n                <div class=\"layui-form\"");
                BeginWriteAttribute("lay-filter", " lay-filter=\"", 4696, "\"", 4709, 0);
                EndWriteAttribute();
                WriteLiteral(@">
                    <div class=""layui-form-item"">
                        <label class=""layui-form-label"">当前用户</label>
                        <div class=""layui-input-inline"">
                            <input type=""text"" name=""ModifyUserName"" id=""ModifyUserName"" lay-verify=""required"" lay-vertype=""tips"" class=""layui-input"">
                        </div>
                    </div>
                    <div class=""layui-form-item"">
                        <label class=""layui-form-label"">当前密码</label>
                        <div class=""layui-input-inline"">
                            <input type=""password"" name=""OldPassword"" id=""OldPassword"" lay-verify=""required"" lay-vertype=""tips"" class=""layui-input"">
                        </div>
                    </div>
                    <div class=""layui-form-item"">
                        <label class=""layui-form-label"">新密码</label>
                        <div class=""layui-input-inline"">
                            <input type=""password"" name=""NewPas");
                WriteLiteral(@"sword"" id=""NewPassword"" lay-verify=""pass1"" lay-vertype=""tips"" autocomplete=""off"" class=""layui-input"">
                        </div>
                        <div class=""layui-form-mid layui-word-aux"">密码最少6位，由数字、字母、符号组成</div>
                    </div>
                    <div class=""layui-form-item"">
                        <label class=""layui-form-label"">确认新密码</label>
                        <div class=""layui-input-inline"">
                            <input type=""password"" name=""ConfirmPassword"" id=""ConfirmPassword"" lay-verify=""repass"" lay-vertype=""tips"" autocomplete=""off"" class=""layui-input"">
                        </div>
                    </div>
                    <div class=""layui-form-item"">
                        <div class=""layui-input-block"">
                            <button class=""layui-btn"" lay-submit lay-filter=""btn-submit"">确认修改</button>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
<script>
    layui.use(['jquery', 'layer', 'form'], function () {
        var layer = layui.layer,
            $ = layui.$,
            form = layui.form;
        //监听提交
        form.on('submit(Submit)', function (data) {
            var indes = layer.load(1);
            //提交 Ajax 成功后，关闭当前弹层并重载表格
            $.ajax({
                url: '/Account/Login',
                type: ""post"",
                data: {
                    'model': data.field
                },
                datatype: 'json',
                success: function (data) {
                    if (data.okMsg) {

                        if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {

                            window.location.href = ""/Home/ipadIndex"";

                        }
                        else {
                            window.location.href = data.okMsg;
          ");
            WriteLiteral(@"              }
                    }
                    else {
                        if (data.code && data.code == -100) {
                            $(""#updatepwdtips"").html(data.errorMsg);
                            $(""#ModifyUserName"").val(data.userName);
                            $(""#OldPassword"").val('');
                            $(""#NewPassword"").val('');
                            $(""#ConfirmPassword"").val('');
                            layer.open({
                                type: 1,
                                area: ['620px','430px'],
                                resize: true,
                                shadeClose: true,
                                title: '修改密码',
                                content: $(""#ChangePWD"")
                            })
                        }
                        else {
                            layer.msg(data.errorMsg);
                        }
                    }
                    layer.close(indes);");
            WriteLiteral(@"
                }, error: function (res) {
                    layer.msg(""加载统计信息错误："" + res.responseText);
                    layer.close(indes);
                }
            });
            return false;
        });

        form.verify({
            usernameck: [
                /^[a-zA-Z][a-zA-Z0-9_]{3,14}$/
                , ""用户名不合法（字母开头，允许4-15字节，允许字母数字下划线）""
            ]
            , PassCodeVer: []
            , repass: function (value, item) { //value：表单的值、item：表单的DOM对象
                var password = $(""#NewPassword"").val();
                if (password != value) {
                    return '两次密码输入不一致';
                }
            },
            //我们既支持上述函数式的方式，也支持下述数组的形式
            //数组的两个值分别代表：[正则匹配、匹配不符时的提示文字]
            pass1: [
                /^[\S]{6,100}$/
                , '密码长度不得小于6位，且不能出现空格'
            ],
        });

        function resizeH() {
            var winH = $(window).height();
            var footH = $("".foot_wrap"").height();
            v");
            WriteLiteral(@"ar contentH = winH - footH;
            $("".content_wrap"").css(""height"", contentH + ""px"");
        };

        //修改密码  监听提交
        form.on('submit(btn-submit)', function (data) {
            var indes = layer.load(1);
            //提交 Ajax 成功后，关闭当前弹层并重载表格
            $.ajax({
                url: '/Account/UpdatePwd?UserName=' + $(""#ModifyUserName"").val(),
                type: ""post"",
                data: { 'model': data.field },
                datatype: 'json',
                success: function (data) {
                    if (data.okMsg) {
                        layer.msg(data.okMsg);
                    //    window.location.href = ""/Account/Login"";
                    }
                    else {
                        layer.msg(data.errorMsg);
                    }

                    layer.close(indes);
                }, error: function (res) {
                    layer.msg(""加载统计信息错误："" + res.responseText);
                    layer.close(indes);
                }
     ");
            WriteLiteral(@"       });
            return false;
        });

        resizeH();
        //屏幕改变时
        $(window).resize(function () {
            resizeH()
        });

    });
</script>
<script type=""text/javascript"">
	document.onkeydown = function (evt) {
		var evt = window.event ? window.event : evt;
		if (evt.keyCode == 13) {
			//回车登录，考虑到浏览器自动填写功能，需要模拟鼠标点击事件和焦点事件，否则自动填写的内容无效，提示密码为空。
			var e = document.createEvent(""MouseEvents"");
			e.initEvent(""click"", true, true);
			document.getElementById(""loginBtn"").dispatchEvent(e);
		}
	}

	$(document).ready(function () {
		$(""#valiCode"").click(function () {
			this.src = """);
            Write(
#nullable restore
#line 272 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Account\Login.cshtml"
                Url.Content("~/Account/GetValidateCode")

#line default
#line hidden
#nullable disable
            );
            WriteLiteral("?time=\" + (new Date()).getTime();\r\n\t\t});\r\n\r\n\t\t$(\"#readmebtn\").bind(\"click\", function () {\r\n\t\t\twindow.open(\"/Readme/MIMIC_Tbale.html\");\r\n\t\t});\r\n\t});\r\n</script>\r\n</html>\r\n\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
