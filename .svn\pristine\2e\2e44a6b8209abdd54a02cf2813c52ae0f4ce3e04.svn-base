﻿@model MenuPartialDTO


@foreach (var menuDto in Model.MenuTreeDTOList)
{
    if (menuDto.Children.Count > 0)
    {
        <dd data-name="@menuDto.MenuID">
            <a href="javascript:;">
                <i class="layui-icon @menuDto.MenuIcon"></i>
                <cite id="Name">@menuDto.MenuName</cite>
            </a>
            <dl class="layui-nav-child">
                @*<partial name="MenuHtmlPartialChild" model="@menuDto.Children" />*@
                <partial name="MenuHtmlPartialChild" model="@(new MenuPartialDTO
                                            {
                                                MenuTreeDTOList = menuDto.Children,
                                                Ticket = Model.Ticket,
                                                UserName = Model.UserName
                                            })" />
            </dl>

        </dd>
    }
    else
    {
        var url = "";

        if (!string.IsNullOrWhiteSpace(menuDto.MenuArea) && menuDto.MenuArea.ToUpper().StartsWith("HTTP"))
            url = menuDto.MenuArea + menuDto.MenuController + menuDto.MenuAction;
        else
            url = "/" + menuDto.MenuArea + "/" + menuDto.MenuController + "/" + menuDto.MenuAction;
        if (string.IsNullOrEmpty(menuDto.MenuArea))
        {
            url = "/" + menuDto.MenuController + "/" + menuDto.MenuAction;

        }
        if (url.ToUpper().StartsWith("HTTP"))
        {
            if (menuDto.MenuNote.Contains("AIFollowUp"))
            {
                url = url + "&UserName=" + Model.UserName;
                <dd data-name="@menuDto.MenuID">
                    <a href="@url" target="_blank">
                        <i class="layui-icon @menuDto.MenuIcon"></i>
                        <cite id="Name">@menuDto.MenuName</cite>
                    </a>
                </dd>
            }
            else
            {
                url = url + "?ticket=" + Model.Ticket;
                <dd data-name="@menuDto.MenuID">
                    <a href="@url" target="_blank">
                        <i class="layui-icon @menuDto.MenuIcon"></i>
                        <cite id="Name">@menuDto.MenuName</cite>
                    </a>
                </dd>
            }
        }
        else
        {
            <dd data-name="@menuDto.MenuID">
                <a lay-href="@url">
                    <i class="layui-icon @menuDto.MenuIcon"></i>
                    <cite id="Name">@menuDto.MenuName</cite>
                </a>
            </dd>
        }
    }


}