{"format": 1, "restore": {"D:\\work space\\project\\三部\\科研AI Demo\\AngelwinResearch.Library\\AngelwinResearch.Library.csproj": {}}, "projects": {"D:\\work space\\project\\三部\\科研AI Demo\\AngelwinResearch.Library\\AngelwinResearch.Library.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\work space\\project\\三部\\科研AI Demo\\AngelwinResearch.Library\\AngelwinResearch.Library.csproj", "projectName": "AngelwinResearch.Library", "projectPath": "D:\\work space\\project\\三部\\科研AI Demo\\AngelwinResearch.Library\\AngelwinResearch.Library.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\work space\\project\\三部\\科研AI Demo\\AngelwinResearch.Library\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["D:\\work space\\project\\三部\\科研AI Demo\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://8.131.88.44:5091/nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[3.1.10, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[3.1.10, )"}, "Microsoft.AspNetCore.Mvc": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[3.1.10, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[3.1.10, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200\\RuntimeIdentifierGraph.json"}}}}}