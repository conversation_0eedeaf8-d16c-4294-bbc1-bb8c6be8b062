﻿using AngelwinResearch.ModelExtends;
using AngelwinResearch.Models;
using AngelwinResearch.WebUI.Filters;
using AngelwinResearch.WebUI.Models;
using AngelwinResearch.WebUI.Unity;
using Common.Tools;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace AngelwinResearch.WebUI.Areas.ReportingManage.Controllers
{
    // [Authorizing]
    [Area("ReportingManage")]
    public class RehabilitationDeptController : Controller
    {
        private readonly AngelwinResearchDbContext db;
        public IConfiguration config { get; }
        public IWebHostEnvironment webhostEnv { get; set; }
        public RehabilitationDeptController(AngelwinResearchDbContext _db, IConfiguration _config, IWebHostEnvironment _webHostEnv)
        {
            db = _db;
            config = _config;
            webhostEnv = _webHostEnv;
        }
        public IActionResult Index(int ResearchID)
        {
            var token = TokenGenerator.getToken();
            ViewBag.token = WebUtility.UrlEncode(token);
            ViewBag.formUrl = config["AppSettings:AnyReportUrl"];
            ViewBag.userName = User.Identity?.Name;
            var CRFDelayMinTime = config["AppSettings:CRFDelayMinTime"];
            if (!string.IsNullOrEmpty(CRFDelayMinTime))
                CRFDelayMinTime = "2000";
            var CRFDelayMaxTime = config["AppSettings:CRFDelayMaxTime"];
            if (!string.IsNullOrEmpty(CRFDelayMaxTime))
                CRFDelayMaxTime = "3000";
            ViewBag.CRFDelayMinTime = CRFDelayMinTime;
            ViewBag.CRFDelayMaxTime = CRFDelayMaxTime;
            ViewBag.XuFeiAPPID = config["AppSettings:XuFeiAPPID"];
            ViewBag.XuFeiAPI_KEY = config["AppSettings:XuFeiAPI_KEY"];
            ViewBag.setIntervalTime = string.IsNullOrWhiteSpace(config["AppSettings:RoleSplitIntervalTime"].ToString()) ? "20" : config["AppSettings:RoleSplitIntervalTime"].ToString();
            ViewBag.autoMedicalIntervalTime = string.IsNullOrWhiteSpace(config["AppSettings:AutoMedicalIntervalTime"].ToString()) ? "30" : config["AppSettings:AutoMedicalIntervalTime"].ToString();
            var patient = db.ResearchPatients.Include(a => a.DiseaseSpecificGroup).Include(a => a.HospitalDept).FirstOrDefault(a => a.Id == ResearchID);


            return View(patient);
        }

        public IActionResult GetCRFList(int ResearchPatientId)
        {
            try
            {
                //  var dicList = db.DataDicts.Where(a => a.DataDictType == "HospitalBussinessDomain").ToList();
                var HideList = db.PatientFormHides.Where(a => a.ResearchPatientId == ResearchPatientId).Select(a => a.CRFormId).ToList();
                var CRFDataList = db.CRFDatas.Where(a => a.ResearchPatientId == ResearchPatientId).ToList();
                var ResearchPatient = db.ResearchPatients.Find(ResearchPatientId);
                var username = User.Identity.Name;
                var user = db.Users.FirstOrDefault(a => a.UserName == username);
                var arr = new[] { 1, 2 };
                var crfFormList = db.CRForms.Include(a => a.CRFormFieldSets).Where(a => a.HospitalDeptId == ResearchPatient.HospitalDeptId && a.StopUsing == true && !HideList.Contains(a.Id)).ToList();
                //先找出第一层
                var firstCRForm = crfFormList.FirstOrDefault(a => a.ParentId == 0);
                //找出第二层
                var secondCRForm = crfFormList.Where(a => a.ParentId == firstCRForm.Id && a.DiseaseSpecificGroupId == ResearchPatient.DiseaseSpecificGroupId).OrderBy(a => a.OrderBy).ToList();
                var parentArr = secondCRForm.Select(a => a.Id).ToList();

                //第三层 四级目录结构
                var crfFormList2 = crfFormList.Where(a => parentArr.Contains(a.ParentId) && a.LevelType == 1).OrderBy(a => a.OrderBy).ToList();

                var list = new List<dynamic>();
                //三级目录结构  第三级是表单
                foreach (var item in secondCRForm)
                {
                    dynamic data = new ExpandoObject();
                    data.id = item.Id;
                    data.formId = item.FormId;
                    data.title = item.FormId;
                    var crfChild = crfFormList.Where(a => a.ParentId == item.Id && a.LevelType == 2).OrderBy(a => a.OrderBy).Select(a => new { id = a.Id, formId = a.FormId, title = a.FormName, businessDomain = (string.IsNullOrEmpty(a.BusinessDomain) ? "" : a.BusinessDomain), per = getPercent(CRFDataList, a.Id, a.CRFormFieldSets) }).ToList();
                    data.children = crfChild;
                    if (crfChild.Count > 0)
                        list.Add(data);
                }
                //四级目录
                foreach (var item in crfFormList2)
                {


                    dynamic data = new ExpandoObject();
                    data.id = item.Id;
                    data.formId = item.FormId;
                    data.title = item.FormId;
                    var crfChild = crfFormList.Where(a => a.ParentId == item.Id).OrderBy(a => a.OrderBy).Select(a => new { id = a.Id, formId = a.FormId, title = a.FormName, businessDomain = (string.IsNullOrEmpty(a.BusinessDomain) ? "" : a.BusinessDomain), per = getPercent(CRFDataList, a.Id, a.CRFormFieldSets) }).ToList();
                    data.children = crfChild;
                    if (crfChild.Count > 0)
                        list.Add(data);
                }
                return Json(new { code = 0, msg = "操作成功！", data = list });
            }
            catch (Exception ex)
            {
                LoggerHelper.WriteErrorLog("获取表单列表数据报错！", ex);
                return Json(new { code = -100, msg = "获取数据失败！", data = ex });
            }
        }

        private string getPercent(List<CRFData> list, int CRFormId, ICollection<CRFormFieldSet> formFieldSets)
        {
            var allCount = formFieldSets == null ? 0 : formFieldSets.Count();
            if (list.Count == 0 || list == null)
            {
                var res = allCount == 0 ? "0" : $"0/{allCount}";
                return res;
            }


            var CRFData = list.FirstOrDefault(a => a.CRFormId == CRFormId);
            if (CRFData == null)
            {
                var res = allCount == 0 ? "0" : $"0/{allCount}";
                return res;
            }

            // 如果需要遍历所有属性，可以使用JObject
            JObject jObject = JObject.Parse(CRFData.CRFJsonValue);
            // var totalFields = jObject.Count;
            var totalFields = 0;
            int NotEmptyFields = 0;

            foreach (JProperty property in jObject.Properties())
            {
                JToken propertyValue = property.Value;
                totalFields++;
                switch (propertyValue.Type)
                {
                    case JTokenType.String:
                        if (!string.IsNullOrEmpty((string)propertyValue))
                        {
                            NotEmptyFields++;
                        }
                        break;
                    case JTokenType.Integer:
                    case JTokenType.Float:
                        if (((IConvertible)propertyValue).ToString() != "0")
                        {
                            NotEmptyFields++;
                        }
                        break;
                    case JTokenType.Array:
                        //if (((JArray)propertyValue).Count > 0)
                        //{
                        //    NotEmptyFields++; // 如果数组非空，则计数增加
                        //}
                        getDetailCount((JArray)propertyValue, ref totalFields, ref NotEmptyFields);
                        totalFields--;
                        break;
                    // 根据需要添加其他类型的处理
                    default:
                        // 处理其他类型的值或忽略
                        break;
                }
            }

            return NotEmptyFields + "/" + totalFields == null ? "" : NotEmptyFields + "/" + totalFields;
        }

        private void getDetailCount(JArray detailValue, ref int totalFields, ref int NotEmptyFields)
        {
            foreach (JObject detail in detailValue)
            {
                var detailPropertyList = detail.Properties();
                foreach (var property in detailPropertyList)
                {
                    totalFields++;
                    JToken propertyValue = property.Value;

                    switch (propertyValue.Type)
                    {
                        case JTokenType.String:
                            if (!string.IsNullOrEmpty((string)propertyValue))
                            {
                                NotEmptyFields++;
                            }
                            break;
                        case JTokenType.Integer:
                        case JTokenType.Float:
                            if (((IConvertible)propertyValue).ToString() != "0")
                            {
                                NotEmptyFields++;
                            }
                            break;
                        case JTokenType.Array:
                            if (((JArray)propertyValue).Count > 0)
                            {
                                NotEmptyFields++; // 如果数组非空，则计数增加
                            }

                            break;
                        // 根据需要添加其他类型的处理
                        default:
                            // 处理其他类型的值或忽略
                            break;
                    }
                }
            }
        }
        //private string getTips(string formId)
        //{
        //    var tips = "";
        //    var modelType = config["AppSettings:modelType"];
        //    var promptInfo = db.PromptInfos.Where(o => o.PropmtIdentifier == "CollectDataPage" && o.IsUsed
        //                && (o.ModelName == "通用" || o.ModelName == modelType)).OrderBy(o => o.ModelName).FirstOrDefault();
        //    if (promptInfo == null)
        //    {
        //        return $"不存在该页面[CollectDataPage]基于该模型[{modelType}]的通用或专用提示词；";
        //    }

        //    return promptInfo.Prompt;
        //}

        public string TransformData(string jsonInput)
        {

            var inputObject = JObject.Parse(jsonInput);
            Root root = new Root { variables = new List<Variable>() };

            foreach (var property in inputObject.Properties())
            {
                JToken propertyValue = property.Value;

                string value;
                if (propertyValue.Type == JTokenType.Array)
                {
                    // 如果值是数组，将其转换为用逗号分隔的字符串
                    //   value = string.Join(",", ((JArray)propertyValue).Select(token => (string)token));
                    value = JsonConvert.SerializeObject(((JArray)propertyValue));
                }
                else
                {
                    value = (string)propertyValue;
                }

                Variable variable = new Variable
                {
                    variable_name = property.Name,
                    value = value,
                    source = ""
                };
                root.variables.Add(variable);
            }

            return JsonConvert.SerializeObject(root);
        }
        public IActionResult LoadFormData(int ResearchPatientId, int CRFormId)
        {
            try
            {
                var CRForm = db.CRForms.Find(CRFormId);
                //var tips = getTips(CRForm.FormId);
                var commAPI = new CommAPIController(db, webhostEnv, config);
                string modelType = config["AppSettings:modelType"];//dto.modelType;
                var tips = "";
                if (string.IsNullOrWhiteSpace(tips))
                {
                    #region 提示词注释
                    //                    var zhushitips = @"
                    //<角色>
                    //角色：你是一个数据提取与分析专家。
                    //</角色>
                    //<任务>
                    //任务：根据用户的输入，准确提取指定特征变量，并按照规定的格式输出。
                    //</任务>
                    //<背景>
                    //背景：用户提供了包含特定数据的文本，并要求从中提取特定的特征变量。
                    //</背景>

                    //<执行要求>
                    //执行要求：
                    //1. 仔细分析用户提供的文本，确保理解其内容和要求。
                    //2. 如果文本中信息不明确或不足以完成任务，需向用户澄清。
                    //3. 严格按照用户指定的格式输出结果。
                    //4. 确保输出内容与用户提供的文本描述一致。
                    //5. 使用与用户问题相同的语言进行回答。
                    //6. 日期格式统一采用“yyyy-MM-dd”。
                    //</执行要求>

                    //<输出要求>
                    //输出要求：
                    //1. 按照用户指定的输出JSON格式串进行输出。
                    //2. 确保提取的特征变量准确无误。
                    //3. 保持回答的逻辑性和结构清晰。
                    //</输出要求>

                    //<输入>
                    //用户输入：使用 <data></data> 标记中的内容作为你的知识:   <data>{{报告内容}}</data> 回答要求：
                    //- 如果你不清楚答案，你需要澄清。
                    //- 避免提及你是从 <data></data> 获取的知识。
                    //- 保持答案 与 <data></data>中描述的一致。日期格式请采用“yyyy-MM-dd”
                    //- 使用与问题相同的语言回答。
                    //从报告中提取以下特征变量：{{提取变量}}
                    //输出格式串要求如下：{{提取格式}}
                    //</输入>
                    //                        ";
                    #endregion
                    var TipsResult = commAPI.GetPromptTips(CRForm.FormId, "CollectDataPage", modelType);
                    var jsonResult = TipsResult as JsonResult;
                    if (jsonResult != null)
                    {
                        // 将JsonResult的Value对象序列化为字符串
                        string jsonString = JsonConvert.SerializeObject(jsonResult.Value);
                        // 反序列化字符串为匿名对象
                        var dataObject = JsonConvert.DeserializeObject<dynamic>(jsonString);
                        // 获取data属性的值
                        tips = dataObject.data;
                        var code = dataObject.code;
                        if (code != 0)
                        {
                            return Json(new { code = -100, msg = "获取提示词出错！", tips = "" });
                        }
                    }
                }
                var formData = db.CRFDatas.FirstOrDefault(a => a.ResearchPatientId == ResearchPatientId && a.CRFormId == CRFormId);
                if (formData == null)
                    return Json(new { code = -100, msg = "暂未填报！", tips = tips });

                return Json(new { code = 0, msg = "操作成功！", data = TransformData(formData.CRFJsonValue), tips = tips });
            }
            catch (Exception ex)
            {
                LoggerHelper.WriteErrorLog("加载表单数据报错！", ex);
                return Json(new { code = -100, msg = "获取数据失败！" + ex.Message });
            }

        }

        [HttpPost]
        public IActionResult SaveForm(int ResearchPatientId, int CRFormId, string CRFJsonValue, string AIExtractJsonValue, string reportId)
        {
            try
            {
                LoggerHelper.WriteInfo("其他日志", $"患者ID：【{ResearchPatientId}】开始提交表单ID：【{CRFormId}】,表单数据：{CRFJsonValue}");
                if (CRFormId == 0)
                    return Json(new { code = -100, msg = "请选择要填写的表单！" });
                if (ResearchPatientId == 0)
                    return Json(new { code = -100, msg = "请选择患者！" });
                var crfForm = db.CRForms.Find(CRFormId);
                if (crfForm == null)
                    return Json(new { code = -100, msg = "CRF表单ID有误！" });


                // 如果需要遍历所有属性，可以使用JObject
                JObject jObject = JObject.Parse(CRFJsonValue);
                //  var totalFields = jObject.Count;
                var totalFields = 0;
                int NotEmptyFields = 0;
                var ResearchPatient = db.ResearchPatients.Find(ResearchPatientId);
                var fieldValueDict = new List<FillFieldDTO>();
                var listFieldSets = db.CRFormFieldSets.Where(a => a.CRFormId == CRFormId).ToList();
                foreach (JProperty property in jObject.Properties())
                {
                    JToken propertyValue = property.Value;
                    var propertyName = property.Name;
                    totalFields++;
                    if (!propertyName.ToLower().Contains("detailarray_"))
                    {
                        var FieldSets = listFieldSets.FirstOrDefault(a => a.FieldName == propertyName);
                        var comment = FieldSets == null ? "" : FieldSets.FieldComment;
                        fieldValueDict.Add(new FillFieldDTO { FieldName = propertyName, FieldValue = propertyValue.ToString(), FieldComment = comment });
                    }
                    else
                    {
                        var DetailList = (JArray)property.Value;
                        foreach (JObject detail in DetailList)
                        {
                            foreach (JProperty pro in detail.Properties())
                            {
                                var proValue = pro.Value;
                                var proName = pro.Name;
                                var proFieldSets = listFieldSets.FirstOrDefault(a => a.FieldName == proName);
                                var comment = proFieldSets == null ? "" : proFieldSets.FieldComment;
                                fieldValueDict.Add(new FillFieldDTO { FieldName = proName, FieldValue = proValue.ToString(), FieldComment = comment });
                            }
                        }
                    }
                    switch (propertyValue.Type)
                    {
                        case JTokenType.String:
                            if (!string.IsNullOrEmpty((string)propertyValue))
                            {
                                NotEmptyFields++;
                            }
                            break;
                        case JTokenType.Integer:
                        case JTokenType.Float:
                            if (((IConvertible)propertyValue).ToString() != "0")
                            {
                                NotEmptyFields++;
                            }
                            break;
                        case JTokenType.Array:
                            //if (((JArray)propertyValue).Count > 0)
                            //{
                            //    NotEmptyFields++; // 如果数组非空，则计数增加
                            //}
                            getDetailCount((JArray)propertyValue, ref totalFields, ref NotEmptyFields);
                            totalFields--;
                            break;
                        // 根据需要添加其他类型的处理
                        default:
                            // 处理其他类型的值或忽略
                            break;
                    }
                }

                var crfData = db.CRFDatas.FirstOrDefault(a => a.ResearchPatientId == ResearchPatientId && a.CRFormId == CRFormId);
                var commAPI = new CommAPIController(db, webhostEnv, config);


                //fieldValueDict.Add("download", "下载");


                if (crfData != null)
                {
                    crfData.TotalField = totalFields;
                    crfData.FillField = NotEmptyFields;
                    crfData.CRFJsonValue = CRFJsonValue;
                    crfData.CreatedTime = DateTime.Now;
                    crfData.AIExtractJsonValue = AIExtractJsonValue;
                    crfData.SoureDatas = reportId;
                    db.SaveChanges();

                    var addLogs = commAPI.AddOperationLogs(OperationTypeName.数据修改, User.Identity.Name, ResearchPatient.DiseaseSpecificGroupId
                               , CRFormId, ResearchPatientId, fieldValueDict, "CFR表单数据修改", HttpContext);
                }
                else
                {
                    crfData = new CRFData();
                    crfData.ResearchPatientId = ResearchPatientId;
                    crfData.CRFormId = crfForm.Id;
                    crfForm.FormId = crfForm.FormId;
                    crfData.CRFJsonValue = CRFJsonValue;
                    crfData.CreatedTime = DateTime.Now;
                    crfData.CreateUserName = User.Identity.Name;
                    crfData.TotalField = totalFields;
                    crfData.FillField = NotEmptyFields;
                    if (!string.IsNullOrEmpty(AIExtractJsonValue))
                        crfData.AIExtractJsonValue = AIExtractJsonValue;
                    if (!string.IsNullOrEmpty(reportId))
                        crfData.SoureDatas = reportId;
                    db.CRFDatas.Add(crfData);
                    db.SaveChanges();

                    var addLogs = commAPI.AddOperationLogs(OperationTypeName.数据填报, User.Identity.Name, ResearchPatient.DiseaseSpecificGroupId
                             , CRFormId, ResearchPatientId, fieldValueDict, "CFR表单数据填报", HttpContext);
                }

                return Json(new { code = 0, msg = "操作成功！" });
            }
            catch (Exception ex)
            {
                LoggerHelper.WriteErrorLog("提交表单报错！", ex);
                return Json(new { code = -100, msg = "操作失败！", data = ex });
            }

        }

        [HttpPost]
        public IActionResult HideForm(PatientFormHide entity)
        {
            try
            {
                var formHide = db.PatientFormHides.Include(a => a.CRForm).FirstOrDefault(a => a.ResearchPatientId == entity.ResearchPatientId && a.CRFormId == entity.CRFormId);
                if (formHide != null)
                {
                    return Json(new { code = -100, msg = $"当前患者的{formHide.CRForm.FormName}已被隐藏，请刷新页面！" });
                }
                entity.CreatedTime = DateTime.Now;
                entity.CreateUserName = User.Identity?.Name;
                db.PatientFormHides.Add(entity);
                db.SaveChanges();
                return Json(new { code = 0, msg = "操作成功！" });
            }
            catch (Exception ex)
            {

                LoggerHelper.WriteErrorLog("提交表单报错！", ex);
                return Json(new { code = -100, msg = "操作失败！", data = ex });
            }
        }

        public IActionResult getHideList(int ResearchPatientId)
        {
            try
            {
                var hideList = db.PatientFormHides.Where(a => a.ResearchPatientId == ResearchPatientId).
                    Include(a => a.CRForm).Select(a => new { a.Id, a.CRForm.FormName }).ToList();
                return Json(new { code = 0, msg = "操作成功！", data = hideList });
            }
            catch (Exception ex)
            {
                LoggerHelper.WriteErrorLog("获取隐藏列表数据报错！", ex);
                return Json(new { code = -100, msg = "获取数据失败！", data = ex });
            }
        }

        [HttpPost]
        public IActionResult RemoveHide(int id)
        {
            try
            {
                var hide = db.PatientFormHides.FirstOrDefault(a => a.Id == id);
                if (hide != null)
                {
                    db.Remove(hide);
                    db.SaveChanges();
                }
                return Json(new { code = 0, msg = "操作成功！" });
            }
            catch (Exception ex)
            {
                LoggerHelper.WriteErrorLog("删除失败！", ex);
                return Json(new { code = -100, msg = "删除失败！", data = ex });
            }
        }

        [SSE]
        public async Task<ActionResult> GetBLTextResult(int ResearchID, string crFormIds, string tips)
        {
            var response = Response;
            response.Headers.Add("Cache-Control", "no-cache");
            response.Headers.Add("Connection", "keep-alive");

            if (string.IsNullOrWhiteSpace(crFormIds))
            {
                var errorMsg = "你未选择CRF表单！";
                await response.WriteAsync($"event: end\ndata:{JsonConvert.SerializeObject(new { errorMsg = $"{errorMsg}" })}\n\n");
                await response.Body.FlushAsync();
                return new EmptyResult();
            }
            var crFormIdList = crFormIds.Split(",", StringSplitOptions.RemoveEmptyEntries).ToList();
            var crfDataList = db.CRFDatas.Include(o => o.CRForm).Where(o => o.ResearchPatientId == ResearchID && crFormIdList.Contains(o.CRFormId.ToString())).ToList();
            if (crfDataList.Count == 0)
            {
                var errorMsg = "请先提交表单数据！";
                await response.WriteAsync($"event: end\ndata:{JsonConvert.SerializeObject(new { errorMsg = $"{errorMsg}" })}\n\n");
                await response.Body.FlushAsync();
                return new EmptyResult();
            }


            var modelType = config["AppSettings:modelType"];
            var promptInfo = db.PromptInfos.Where(o => o.PropmtIdentifier == "RehabilitationDept" && o.IsUsed
                        && (o.ModelName == "通用" || o.ModelName == modelType)).OrderBy(o => o.ModelName).FirstOrDefault();
            if (promptInfo == null)
            {

                var errorMsg = $"不存在该页面[RehabilitationDept]基于该模型[{modelType}]的通用或专用提示词；！";
                await response.WriteAsync($"event: end\ndata:{JsonConvert.SerializeObject(new { errorMsg = $"{errorMsg}" })}\n\n");
                await response.Body.FlushAsync();
                return new EmptyResult();
            }

            var formContent = "";
            var formField = "";
            var fromList = db.CRFormFieldSets.Where(o => crFormIdList.Contains(o.CRFormId.ToString())).ToList();

            foreach (var item in crfDataList)
            {
                formContent += $"【{item.CRForm.FormName}】数据如下：{item.CRFJsonValue};";

                var resultResult = new List<dynamic>();
                foreach (var CRFormFieldSets in fromList)
                {
                    dynamic result = new ExpandoObject();
                    result.@变量名 = CRFormFieldSets.FieldName;
                    result.@变量描述 = CRFormFieldSets.FieldComment;
                    resultResult.Add(result);
                }
                formField += $"【{item.CRForm.FormName}】数据字段解释如下：{JsonConvert.SerializeObject(resultResult)};";
            }

            //prompt += $"。根据我提供的JSON数据，我需要您生成一个完全连贯的病历文书。" +
            //    $"请确保段落内的信息完整、无遗漏，并保持文本连贯、专业，类似于经验丰富的医疗专业人员所书写的临床病历。请避免使用任何分段或" +
            //    $"子标题，确保整个文本是一个统一的、连续的整体，并且不引入任何额外的假设或模拟数据,避免在输出中添加不相关的前导语。";
            var prompt = promptInfo.Prompt.Replace("{{报告内容}}", formContent).Replace("{{提取特征要求}}", formField);


            try
            {
                // var modelType = config["AppSettings:modelType"];
                string endpoint = "chat/completions";
                dynamic history = new List<dynamic>();
                var ResponseContent = "";
                var requestMsgTime = System.DateTime.Now;

                var contentStr = "";
                #region 读取配置文件
                string apiKey = config[$"GPTSetting:{modelType}:ApiKey"];
                string model = config[$"GPTSetting:{modelType}:Model"];
                string apiUrl = config[$"GPTSetting:{modelType}:apiUrl"];
                string maxTokenStr = config[$"GPTSetting:{modelType}:MaxTokens"];
                int maxTokens = 4000;
                if (!string.IsNullOrEmpty(maxTokenStr))
                {
                    int.TryParse(maxTokenStr, out maxTokens);
                }
                HttpClientHandler handler = null;
                string webProxy = config[$"GPTSetting:{modelType}:WebProxy"];
                if (!string.IsNullOrWhiteSpace(webProxy))
                {
                    handler = new HttpClientHandler()
                    {
                        Proxy = new WebProxy(webProxy)
                    };
                }
                else
                {
                    handler = new HttpClientHandler() { };
                }
                #endregion
                handler.ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator;
                var httpClient = new HttpClient(handler);
                httpClient.BaseAddress = new Uri(apiUrl);
                httpClient.Timeout = TimeSpan.FromMinutes(5);
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                history.Add(new { role = "user", content = $"{prompt}" });

                dynamic requstDTO = new ExpandoObject();
                requstDTO.model = model;
                requstDTO.max_tokens = maxTokens;
                requstDTO.messages = history;
                requstDTO.stream = true;
                var requestBody = JsonConvert.SerializeObject(requstDTO);
                var request = new HttpRequestMessage(HttpMethod.Post, apiUrl + endpoint)
                {
                    Content = new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
                };

                using var APIResponse = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);
                {
                    if (APIResponse.IsSuccessStatusCode)
                    {
                        var stream = await APIResponse.Content.ReadAsStreamAsync();
                        var streamReader = new StreamReader(stream);
                        var ddd = await streamReader.ReadLineAsync();
                        while (!streamReader.EndOfStream)
                        {
                            var line = await streamReader.ReadLineAsync();
                            if (!string.IsNullOrWhiteSpace(line))
                            {
                                if (line != "data: [DONE]")
                                {
                                    if (line.StartsWith("data:"))
                                        line = line.Substring(5, line.Length - 5);
                                    var delta = JObject.Parse(line).SelectToken("choices").First().SelectToken("delta");
                                    var finish_reason = JObject.Parse(line).SelectToken("choices").First().SelectToken("finish_reason");
                                    if (delta != null && ((JContainer)delta).Count > 0)
                                    {
                                        if (delta["content"] != null)
                                        {
                                            contentStr = delta["content"].ToString();
                                            ResponseContent += contentStr;
                                            await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { okMsg = "成功", data = contentStr })}\n\n");
                                            await response.Body.FlushAsync();
                                        }
                                    }
                                    if (finish_reason != null)
                                    {
                                        var resultfinish = finish_reason as JProperty;
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        var errorMsg = "Failed to connect to API.";
                        await response.WriteAsync($"event: end\ndata:{JsonConvert.SerializeObject(new { errorMsg = $"{errorMsg}" })}\n\n");
                        await response.Body.FlushAsync();
                        return new EmptyResult();
                    }
                }

                var resultAll = new
                {
                    okMsg = $"成功",
                    role = "assistant",
                    content = ResponseContent,
                };
                await Response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(resultAll)}\n\n");
                await Response.Body.FlushAsync();
                return new EmptyResult();
            }
            catch (Exception ex)
            {
                await Response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(new { errorMsg = $"报错:{ex.Message}-{ex.InnerException?.Message}" })}\n\n");
                await Response.Body.FlushAsync();
                return new EmptyResult();
            }
        }

        [SSE]
        public async Task<ActionResult> GetBLTextResultByReportId(int reportId, string words)
        {
            var response = Response;
            response.Headers.Add("Cache-Control", "no-cache");
            response.Headers.Add("Connection", "keep-alive");

            //if (reportId==0)
            //{
            //    var errorMsg = "请选择语音！";
            //    await response.WriteAsync($"event: end\ndata:{JsonConvert.SerializeObject(new { errorMsg = $"{errorMsg}" })}\n\n");
            //    await response.Body.FlushAsync();
            //    return new EmptyResult();
            //}

            if (string.IsNullOrEmpty(words))
            {
                var errorMsg = "请选择语音文件！";
                await response.WriteAsync($"event: end\ndata:{JsonConvert.SerializeObject(new { errorMsg = $"{errorMsg}" })}\n\n");
                await response.Body.FlushAsync();
                return new EmptyResult();
            }

            //var externalReports = db.ExternalReports.Find(reportId);
            //if (externalReports==null)
            //{
            //    var errorMsg = "语音文件不存在！";
            //    await response.WriteAsync($"event: end\ndata:{JsonConvert.SerializeObject(new { errorMsg = $"{errorMsg}" })}\n\n");
            //    await response.Body.FlushAsync();
            //    return new EmptyResult();
            //}

            //  var prompt =  $"数据如下：{externalReports.ReportContent};";

            // var prompt = $"数据如下：{words};";
            //prompt += $"。根据我提供的JSON数据，我需要您生成一个完全连贯的病历文书。" +
            //    $"请确保段落内的信息完整、无遗漏，并保持文本连贯、专业，类似于经验丰富的医疗专业人员所书写的临床病历。请避免使用任何分段或" +
            //    $"子标题，确保整个文本是一个统一的、连续的整体，并且不引入任何额外的假设或模拟数据,避免在输出中添加不相关的前导语。";
            //prompt += $"。请根据以下描述的病历文书格式，从以下长文本中提取相应的病历信息，并整理成清晰的文书格式。" +
            //$"\r\n\r\n病历文书格式包括以下字段：\r\n- 是否急诊\r\n- 主诉\r\n- 现病史\r\n- 既往史\r\n- 过敏史\r\n" +
            //$"- 体格检查\r\n- 辅助检查\r\n- 初步诊断\r\n- 处理意见\r\n" +
            //$"- 医师签名\r\n\r\n长文本内容如下：[长文本内容]\r\n\r\n请确保提取的信息完整地包含上述所有字段。" +
            //$"如果长文本中缺少某些信息，请标注为“不详”。提取的信息需要保持原意，不得添加个人主观判断或解释。";

            // var tips = "";
            var modelType = config["AppSettings:modelType"];
            var promptInfo = db.PromptInfos.Where(o => o.PropmtIdentifier == "RehabilitationDept-2" && o.IsUsed
                        && (o.ModelName == "通用" || o.ModelName == modelType)).OrderBy(o => o.ModelName).FirstOrDefault();
            if (promptInfo == null)
            {

                await Response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(new { errorMsg = $"不存在该页面[RehabilitationDept-2]基于该模型[{modelType}]的通用或专用提示词；" })}\n\n");
                await Response.Body.FlushAsync();
                return new EmptyResult();
            }
            var prompt = promptInfo.Prompt.Replace("{{报告内容}}", words);
            try
            {
                // var modelType = config["AppSettings:modelType"];
                string endpoint = "chat/completions";
                dynamic history = new List<dynamic>();
                var ResponseContent = "";
                var requestMsgTime = System.DateTime.Now;

                var contentStr = "";
                #region 读取配置文件
                string apiKey = config[$"GPTSetting:{modelType}:ApiKey"];
                string model = config[$"GPTSetting:{modelType}:Model"];
                string apiUrl = config[$"GPTSetting:{modelType}:apiUrl"];
                string maxTokenStr = config[$"GPTSetting:{modelType}:MaxTokens"];
                int maxTokens = 4000;
                if (!string.IsNullOrEmpty(maxTokenStr))
                {
                    int.TryParse(maxTokenStr, out maxTokens);
                }
                HttpClientHandler handler = null;
                string webProxy = config[$"GPTSetting:{modelType}:WebProxy"];
                if (!string.IsNullOrWhiteSpace(webProxy))
                {
                    handler = new HttpClientHandler()
                    {
                        Proxy = new WebProxy(webProxy)
                    };
                }
                else
                {
                    handler = new HttpClientHandler() { };
                }
                #endregion
                handler.ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator;
                var httpClient = new HttpClient(handler);
                httpClient.BaseAddress = new Uri(apiUrl);
                httpClient.Timeout = TimeSpan.FromMinutes(5);
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                history.Add(new { role = "user", content = $"{prompt}" });

                dynamic requstDTO = new ExpandoObject();
                requstDTO.model = model;
                requstDTO.max_tokens = maxTokens;
                requstDTO.messages = history;
                requstDTO.stream = true;
                var requestBody = JsonConvert.SerializeObject(requstDTO);
                var request = new HttpRequestMessage(HttpMethod.Post, apiUrl + endpoint)
                {
                    Content = new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
                };

                using var APIResponse = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);
                {
                    if (APIResponse.IsSuccessStatusCode)
                    {
                        var stream = await APIResponse.Content.ReadAsStreamAsync();
                        var streamReader = new StreamReader(stream);
                        var ddd = await streamReader.ReadLineAsync();
                        while (!streamReader.EndOfStream)
                        {
                            var line = await streamReader.ReadLineAsync();
                            if (!string.IsNullOrWhiteSpace(line))
                            {
                                if (line != "data: [DONE]")
                                {
                                    if (line.StartsWith("data:"))
                                        line = line.Substring(5, line.Length - 5);
                                    var delta = JObject.Parse(line).SelectToken("choices").First().SelectToken("delta");
                                    var finish_reason = JObject.Parse(line).SelectToken("choices").First().SelectToken("finish_reason");
                                    if (delta != null && ((JContainer)delta).Count > 0)
                                    {
                                        if (delta["content"] != null)
                                        {
                                            contentStr = delta["content"].ToString();
                                            ResponseContent += contentStr;
                                            await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { okMsg = "成功", data = contentStr })}\n\n");
                                            await response.Body.FlushAsync();
                                        }
                                    }
                                    if (finish_reason != null)
                                    {
                                        var resultfinish = finish_reason as JProperty;
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        var errorMsg = "Failed to connect to API.";
                        await response.WriteAsync($"event: end\ndata:{JsonConvert.SerializeObject(new { errorMsg = $"{errorMsg}" })}\n\n");
                        await response.Body.FlushAsync();
                        return new EmptyResult();
                    }
                }

                var resultAll = new
                {
                    okMsg = $"成功",
                    role = "assistant",
                    content = ResponseContent,
                };
                await Response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(resultAll)}\n\n");
                await Response.Body.FlushAsync();
                return new EmptyResult();
            }
            catch (Exception ex)
            {
                await Response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(new { errorMsg = $"报错:{ex.Message}-{ex.InnerException?.Message}" })}\n\n");
                await Response.Body.FlushAsync();
                return new EmptyResult();
            }
        }

        [HttpPost]
        public IActionResult UploadFile(IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                return Json(new { code = -100, msg = "选择文件有误！" });
            }

            try
            {
                // 获取文件夹路径
                string folderPath = System.IO.Path.Combine(webhostEnv.WebRootPath, "ExternalReports");

                // 如果文件夹不存在，则创建文件夹
                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }
                // 获取文件扩展名
                string fileExtension = System.IO.Path.GetExtension(file.FileName);
                var guid = Guid.NewGuid().ToString();
                var newFileName = guid + fileExtension;
                // 文件完整路径
                string filePath = System.IO.Path.Combine(folderPath, newFileName);

                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    file.CopyTo(stream);
                }
                return Json(new { code = 0, data = new { fileName = file.FileName, newFileName = newFileName, guid = guid } });
            }
            catch (Exception ex)
            {

                return Json(new { code = -100, msg = "选择文件报错！", data = ex });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SaveFile(int RId, int CRFormId, string fileName, string newFileName, string guid)
        {
            try
            {
                string folderPath = System.IO.Path.Combine(webhostEnv.WebRootPath, "ExternalReports");
                var pdfPath = System.IO.Path.Combine(folderPath, newFileName);
                // 获取文件扩展名
                string fileExtension = System.IO.Path.GetExtension(fileName);
                var ReportUrl = "\\ExternalReports\\" + newFileName;
                var aa = await AnalyseFile(ReportUrl);
                var ExReport = new ExternalReport();
                ExReport.ReportName = fileName;
                ExReport.ResearchPatientId = RId;
                ExReport.CRFormId = CRFormId;
                ExReport.ReportType = fileExtension;
                ExReport.ReportURL = ReportUrl;
                ExReport.IsDel = false;
                ExReport.ReportContent = aa;
                ExReport.CreatedTime = DateTime.Now;
                ExReport.CreateUserName = User.Identity?.Name;
                db.ExternalReports.Add(ExReport);
                db.SaveChanges();
                return Json(new { code = 0, msg = "操作成功！" });
            }
            catch (Exception ex)
            {

                return Json(new { code = -100, msg = "保存文件报错！", data = ex });
            }
        }

        //获取文件内容
        public async Task<string> AnalyseFile(string FileUrl)
        {
            var response = Response;
            response.Headers.Add("Cache-Control", "no-cache");
            response.Headers.Add("Connection", "keep-alive");
            try
            {
                var modelType = "glm-4";
                #region 读取配置文件
                string apiKey = config[$"GPTSetting:{modelType}:ApiKey"];
                string model = "glm-4";
                string apiUrl = config[$"GPTSetting:{modelType}:apiUrl"];

                string maxTokenStr = config[$"GPTSetting:{modelType}:MaxTokens"];
                int maxTokens = 4000;
                if (!string.IsNullOrEmpty(maxTokenStr))
                {
                    int.TryParse(maxTokenStr, out maxTokens);
                }

                HttpClientHandler handler = null;
                string webProxy = config[$"GPTSetting:{modelType}:WebProxy"];
                if (!string.IsNullOrWhiteSpace(webProxy))
                {
                    handler = new HttpClientHandler()
                    {
                        Proxy = new WebProxy(webProxy)
                    };
                }
                else
                {
                    handler = new HttpClientHandler() { };
                }
                #endregion
                var _httpClient = new HttpClient();
                // 设置请求头
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"{apiKey}");
                try
                {

                    var fileIndex = 1;

                    using (var form = new MultipartFormDataContent())
                    {
                        var filePath = webhostEnv.WebRootPath + FileUrl;
                        // 确保文件存在
                        if (!System.IO.File.Exists(filePath))
                        {
                            return "地址指向的文件不存在";
                        }

                        // 读取文件内容
                        var fileContent = new ByteArrayContent(await System.IO.File.ReadAllBytesAsync(filePath));
                        fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");

                        // 添加文件到表单数据
                        form.Add(fileContent, "file", System.IO.Path.GetFileName(filePath));

                        // 添加其他表单字段
                        form.Add(new StringContent("file-extract"), "purpose");

                        // 发送 POST 请求
                        var resp = await _httpClient.PostAsync($"{apiUrl}files", form);
                        if (resp.IsSuccessStatusCode)
                        {
                            var res = await resp.Content.ReadAsStringAsync();
                            var resObj = JsonConvert.DeserializeObject<JObject>(res);
                            if (resObj.ContainsKey("id"))
                            {
                                var fileId = (string)resObj["id"];

                                var fileContentUrl = $"{apiUrl}files/{fileId}/content";
                                var newResp = await _httpClient.GetAsync(fileContentUrl);
                                if (newResp.IsSuccessStatusCode)
                                {
                                    var newRes = await newResp.Content.ReadAsStringAsync();
                                    var newResObj = JsonConvert.DeserializeObject<JObject>(newRes);
                                    return newResObj.First.First.ToString();
                                }
                                else
                                {
                                    var errorMsg = "获取文件内容失败！";
                                    return errorMsg;
                                }
                            }
                            else
                            {
                                var errorMsg = (string)resObj["message"];
                                return errorMsg;
                            }
                        }
                        else
                        {
                            var errorMsg = "上传文件失败！";
                            return errorMsg;
                        }
                    }
                }
                catch (Exception ex)
                {
                    var errorMsg = ex.Message;
                    return errorMsg;

                }
            }
            catch (Exception ex)
            {
                return $"报错" + ex.Message;
            }

        }

        [HttpPost]
        public IActionResult GetImgList(int ResearchPatientId, int CRFormId)
        {
            try
            {
                var CRFForm = db.CRForms.Find(CRFormId);
                var imgArr = new[] { ".jpg", ".jpeg", ".png" };
                var list = db.ExternalReports.Where(a => a.ResearchPatientId == ResearchPatientId
                && a.CRFormId == CRFormId && a.IsDel == false && imgArr.Contains(a.ReportType.ToLower())).Select(a => new { a.Id, a.ReportURL }).ToList();
                if (list.Count == 0)
                    return Json(new { code = -100, msg = $"【{CRFForm.FormName}】暂未上传图片！" });
                return Json(new { code = 0, data = list });
            }
            catch (Exception ex)
            {

                return Json(new { code = -100, msg = $"获取图片报错！{ex.Message}" });
            }
        }

        [HttpPost]
        public IActionResult GetPdfList(int ResearchPatientId, int CRFormId)
        {
            try
            {
                var CRFForm = db.CRForms.Find(CRFormId);
                var imgArr = new[] { ".pdf" };
                var list = db.ExternalReports.Where(a => a.ResearchPatientId == ResearchPatientId
                && a.CRFormId == CRFormId && a.IsDel == false && imgArr.Contains(a.ReportType.ToLower())).Select(a => new { a.Id, a.ReportURL }).ToList();
                if (list.Count == 0)
                    return Json(new { code = -100, msg = $"【{CRFForm.FormName}】暂未上传PDF！" });
                return Json(new { code = 0, data = list });
            }
            catch (Exception ex)
            {

                return Json(new { code = -100, msg = $"获取PDF报错！{ex.Message}" });
            }
        }

        //AI报告提取
        [HttpPost]
        public async Task<IActionResult> NewAIExtract(int ResearchPatientId, int CRFormId)
        {
            try
            {
                var CRFForm = db.CRForms.Find(CRFormId);
                var commAPI = new CommAPIController(db, webhostEnv, config);
                string modelType = config["AppSettings:modelType"];//dto.modelType;
                var tips = "";
                if (string.IsNullOrWhiteSpace(tips))
                {
                    #region 提示词注释
                    //                    var zhushitips = @"
                    //<角色>
                    //角色：你是一个数据提取与分析专家。
                    //</角色>
                    //<任务>
                    //任务：根据用户的输入，准确提取指定特征变量，并按照规定的格式输出。
                    //</任务>
                    //<背景>
                    //背景：用户提供了包含特定数据的文本，并要求从中提取特定的特征变量。
                    //</背景>

                    //<执行要求>
                    //执行要求：
                    //1. 仔细分析用户提供的文本，确保理解其内容和要求。
                    //2. 如果文本中信息不明确或不足以完成任务，需向用户澄清。
                    //3. 严格按照用户指定的格式输出结果。
                    //4. 确保输出内容与用户提供的文本描述一致。
                    //5. 使用与用户问题相同的语言进行回答。
                    //6. 日期格式统一采用“yyyy-MM-dd”。
                    //</执行要求>

                    //<输出要求>
                    //输出要求：
                    //1. 按照用户指定的输出JSON格式串进行输出。
                    //2. 确保提取的特征变量准确无误。
                    //3. 保持回答的逻辑性和结构清晰。
                    //</输出要求>

                    //<输入>
                    //用户输入：使用 <data></data> 标记中的内容作为你的知识:   <data>{{报告内容}}</data> 回答要求：
                    //- 如果你不清楚答案，你需要澄清。
                    //- 避免提及你是从 <data></data> 获取的知识。
                    //- 保持答案 与 <data></data>中描述的一致。日期格式请采用“yyyy-MM-dd”
                    //- 使用与问题相同的语言回答。
                    //从报告中提取以下特征变量：{{提取变量}}
                    //输出格式串要求如下：{{提取格式}}
                    //</输入>
                    //                        ";
                    #endregion
                    var TipsResult = commAPI.GetPromptTips(CRFForm.FormId, "CollectDataPage", modelType);
                    var jsonResult = TipsResult as JsonResult;
                    if (jsonResult != null)
                    {
                        // 将JsonResult的Value对象序列化为字符串
                        string jsonString = JsonConvert.SerializeObject(jsonResult.Value);
                        // 反序列化字符串为匿名对象
                        var dataObject = JsonConvert.DeserializeObject<dynamic>(jsonString);
                        // 获取data属性的值
                        tips = dataObject.data;
                        var code = dataObject.code;
                        if (code != 0)
                        {
                            return Json(new { code = -100, msg = "获取提示词出错！", tips = "" });
                        }
                    }
                }
                var FileList = db.ExternalReports.Where(a => a.ResearchPatientId == ResearchPatientId && a.CRFormId == CRFormId && a.IsDel == false).ToList(); ;

                if (FileList.Count == 0)
                    return Json(new { code = -100, msg = $"【{CRFForm.FormName}】未上传报告文件！" });

                LoggerHelper.WriteInfo("其他日志", $"AIExtract:开始");
                var reportId = string.Join(',', FileList.Select(a => a.Id).ToArray());
                //  var modelType = config["AppSettings:modelType"];

                #region 读取配置文件
                string apiKey = config[$"GPTSetting:{modelType}:ApiKey"];
                string model = config[$"GPTSetting:{modelType}:Model"];
                string apiUrl = config[$"GPTSetting:{modelType}:apiUrl"];
                string maxTokenStr = config[$"GPTSetting:{modelType}:MaxTokens"];
                int maxTokens = 4000;
                var modelName = modelType;

                var JArrayData = new JArray();
                var DataJson = "";
                foreach (var item in FileList)
                {

                    DataJson += item.ReportContent + ",";

                }

                DataJson = $"[{DataJson.TrimEnd(',')}]";
                var Newprompt = tips.Replace("{{报告内容}}", DataJson);
                HttpClientHandler handler = null;
                string webProxy = config[$"GPTSetting:{modelType}:WebProxy"];
                if (!string.IsNullOrWhiteSpace(webProxy))
                {
                    handler = new HttpClientHandler()
                    {
                        Proxy = new WebProxy(webProxy)
                    };
                }
                else
                {
                    handler = new HttpClientHandler() { };
                }

                #endregion
                string endpoint = "chat/completions";

                dynamic history = new List<dynamic>();

                var contentStr = "";
                var httpClient = new HttpClient(handler);
                httpClient.Timeout = TimeSpan.FromMinutes(5);
                httpClient.BaseAddress = new Uri(apiUrl);
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                history.Add(new { role = "user", content = $"{Newprompt}" });

                var requestMsgTime = System.DateTime.Now;

                dynamic requstDTO = new ExpandoObject();
                requstDTO.model = model;
                requstDTO.messages = history;
                requstDTO.stream = false;
                requstDTO.max_tokens = maxTokens;
                var requestBody = JsonConvert.SerializeObject(requstDTO);
                var request = new HttpRequestMessage(HttpMethod.Post, endpoint)
                {
                    Content = new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
                };
                var IsAdd = false;
                var ResponseContent = "";
                using var APIResponse = await httpClient.SendAsync(request);
                {
                    if (APIResponse.IsSuccessStatusCode)
                    {
                        var stream = await APIResponse.Content.ReadAsStreamAsync();
                        using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
                        {
                            // ResponseContent = reader.ReadToEnd();
                            var resultAI = reader.ReadToEnd();
                            var jobj = JsonConvert.DeserializeObject<JObject>(resultAI);
                            if (jobj.ContainsKey("choices"))
                            {
                                var choicesList = jobj.Value<JArray>("choices");
                                var choicesItem = choicesList.FirstOrDefault();
                                if (choicesItem != null)
                                {
                                    var content = choicesItem.Value<JObject>("message").Value<string>("content");
                                    Console.WriteLine($"大模型结果:{content}");

                                    LoggerHelper.WriteInfo("其他日志", $"大模型结果:{content}");
                                    var first = content.IndexOf("{");
                                    var last = content.LastIndexOf("}");
                                    ResponseContent = content.Substring(first, last - first + 1);

                                    // ResponseContent = choicesItem.Value<JObject>("message").Value<string>("content");
                                }
                            }
                        }
                    }
                    else
                    {
                        return Json(new { code = -100, msg = "Failed to connect to API！" });

                    }
                }

                return Json(new { code = 0, data = ResponseContent, reportId = reportId });
            }
            catch (Exception ex)
            {
                LoggerHelper.WriteInfo("其他日志", $"调用AI提取报错:{ex.Message + ex.InnerException?.Message}");
                return Json(new { code = -100, msg = ex.Message });
            }
        }

        /// <summary>
        /// 根据语音对话填写表单
        /// </summary>
        /// <param name="ResearchPatientId"></param>
        /// <param name="CRFormId"></param>
        /// <param name="tips"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> AIExtractByAudio(int ResearchPatientId, int CRFormId, int reportId, string words)
        {
            try
            {
                var CRFForm = db.CRForms.Find(CRFormId);

                var commAPI = new CommAPIController(db, webhostEnv, config);
                string modelType = config["AppSettings:modelType"];//dto.modelType;
                                                                   // var tips = "";
                var tips = "";
                if (string.IsNullOrWhiteSpace(tips))
                {
                    var TipsResult = commAPI.GetPromptTips(CRFForm.FormId, "CollectDataPage", modelType);
                    var jsonResult = TipsResult as JsonResult;
                    if (jsonResult != null)
                    {
                        // 将JsonResult的Value对象序列化为字符串
                        string jsonString = JsonConvert.SerializeObject(jsonResult.Value);
                        // 反序列化字符串为匿名对象
                        var dataObject = JsonConvert.DeserializeObject<dynamic>(jsonString);
                        // 获取data属性的值
                        tips = dataObject.data;
                        var code = dataObject.code;
                        if (code != 0)
                        {
                            return Json(new { code = -100, msg = "获取提示词出错！", tips = "" });
                        }
                    }
                }
                // var FileList = db.ExternalReports.Find(reportId);

                //if (FileList == null)
                //    return Json(new { code = -100, msg = $"【{CRFForm.FormName}】未选择语音文件！" });
                if (string.IsNullOrEmpty(words))
                    return Json(new { code = -100, msg = $"【{CRFForm.FormName}】未选择语音文件！" });
                LoggerHelper.WriteInfo("其他日志", $"AIExtract:开始");

                // var modelType = config["AppSettings:modelType"];

                #region 读取配置文件
                string apiKey = config[$"GPTSetting:{modelType}:ApiKey"];
                string model = config[$"GPTSetting:{modelType}:Model"];
                string apiUrl = config[$"GPTSetting:{modelType}:apiUrl"];
                string maxTokenStr = config[$"GPTSetting:{modelType}:MaxTokens"];
                int maxTokens = 4000;
                var modelName = modelType;

                var JArrayData = new JArray();
                var DataJson = "";

                //  DataJson += FileList.ReportContent + ",";
                DataJson += words + ",";



                DataJson = $"[{DataJson.TrimEnd(',')}]";

                //20250221 获取语音文件和获取表单数据是两个异步ajax，自动填充表单/转写病历文书时
                //tip可能还未赋值

                var Newprompt = tips.Replace("{{报告内容}}", DataJson);
                HttpClientHandler handler = null;
                string webProxy = config[$"GPTSetting:{modelType}:WebProxy"];
                if (!string.IsNullOrWhiteSpace(webProxy))
                {
                    handler = new HttpClientHandler()
                    {
                        Proxy = new WebProxy(webProxy)
                    };
                }
                else
                {
                    handler = new HttpClientHandler() { };
                }

                #endregion
                string endpoint = "chat/completions";

                dynamic history = new List<dynamic>();

                var contentStr = "";
                var httpClient = new HttpClient(handler);
                httpClient.Timeout = TimeSpan.FromMinutes(5);
                httpClient.BaseAddress = new Uri(apiUrl);
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                history.Add(new { role = "user", content = $"{Newprompt}" });

                var requestMsgTime = System.DateTime.Now;

                dynamic requstDTO = new ExpandoObject();
                requstDTO.model = model;
                requstDTO.messages = history;
                requstDTO.stream = false;
                requstDTO.max_tokens = maxTokens;
                var requestBody = JsonConvert.SerializeObject(requstDTO);
                var request = new HttpRequestMessage(HttpMethod.Post, endpoint)
                {
                    Content = new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
                };
                var IsAdd = false;
                var ResponseContent = "";
                using var APIResponse = await httpClient.SendAsync(request);
                {
                    if (APIResponse.IsSuccessStatusCode)
                    {
                        var stream = await APIResponse.Content.ReadAsStreamAsync();
                        using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
                        {
                            // ResponseContent = reader.ReadToEnd();
                            var resultAI = reader.ReadToEnd();
                            var jobj = JsonConvert.DeserializeObject<JObject>(resultAI);
                            if (jobj.ContainsKey("choices"))
                            {
                                var choicesList = jobj.Value<JArray>("choices");
                                var choicesItem = choicesList.FirstOrDefault();
                                if (choicesItem != null)
                                {
                                    var content = choicesItem.Value<JObject>("message").Value<string>("content");
                                    Console.WriteLine($"大模型结果:{content}");

                                    LoggerHelper.WriteInfo("其他日志", $"大模型结果:{content}");
                                    var first = content.IndexOf("{");
                                    var last = content.LastIndexOf("}");
                                    ResponseContent = content.Substring(first, last - first + 1);

                                    // ResponseContent = choicesItem.Value<JObject>("message").Value<string>("content");
                                }
                            }
                        }
                    }
                    else
                    {
                        return Json(new { code = -100, msg = "Failed to connect to API！" });

                    }
                }

                return Json(new { code = 0, data = ResponseContent, reportId = reportId });
            }
            catch (Exception ex)
            {
                LoggerHelper.WriteInfo("其他日志", $"调用AI提取报错:{ex.Message + ex.InnerException?.Message}");
                return Json(new { code = -100, msg = ex.Message });
            }
        }

        //上传文件
        [HttpPost]
        public IActionResult UploadAudio(string txt, int RId, int CRFormId, int pageSize, string AIResult)
        {
            try
            {
                txt = txt.TrimStart('\r', '\n');
                if (Request.Form.Files.Count > 0)
                {
                    var file = Request.Form.Files[0];
                    // 获取文件夹路径
                    string folderPath = System.IO.Path.Combine(webhostEnv.WebRootPath, "ExternalReports");

                    // 如果文件夹不存在，则创建文件夹
                    if (!Directory.Exists(folderPath))
                    {
                        Directory.CreateDirectory(folderPath);
                    }
                    // 获取文件扩展名
                    string fileExtension = ".wav";
                    var guid = Guid.NewGuid().ToString();
                    var newFileName = guid + fileExtension;
                    // 文件完整路径
                    string filePath = System.IO.Path.Combine(folderPath, newFileName);

                    using (var stream = new FileStream(filePath, FileMode.Create))
                    {
                        file.CopyTo(stream);
                    }
                    var ExReport = new ExternalReport();
                    ExReport.ReportName = guid;
                    ExReport.ResearchPatientId = RId;
                    ExReport.CRFormId = CRFormId;
                    ExReport.ReportType = fileExtension;
                    ExReport.ReportURL = "\\ExternalReports\\" + newFileName;
                    ExReport.IsDel = false;
                    ExReport.ReportContent = txt;
                    ExReport.AIContent = AIResult;
                    ExReport.CreatedTime = DateTime.Now;
                    ExReport.CreateUserName = User.Identity?.Name;
                    db.ExternalReports.Add(ExReport);
                    db.SaveChanges();

                    var listReport = db.ExternalReports.Where(a => a.IsDel == false && a.ResearchPatientId ==
                    RId && a.CRFormId == CRFormId && (a.ReportType == ".wav" || a.ReportType == ".mp3")).OrderByDescending(a => a.CreatedTime).Take(pageSize)
                    .Select(a => new { a.Id, ReportContent = a.AIContent, a.ReportURL, CreatedTime = a.CreatedTime.ToString("yyyy-MM-dd HH:mm:ss") }).ToList();
                    return Json(new { code = 0, data = txt, reportId = ExReport.Id, reportList = listReport });
                }
                else
                {
                    LoggerHelper.WriteInfo("其他日志", $"语音文件不能为空！");
                    return Json(new { code = -100, msg = "语音文件不能为空" });
                }
            }
            catch (Exception ex)
            {

                LoggerHelper.WriteInfo("其他日志", $"上传语音文件时报错:{ex.Message + ex.InnerException?.Message}");
                return Json(new { code = -100, msg = ex.Message });
            }

        }

        [HttpPost]
        public IActionResult getMsgAndVoice(int RId, int CRFormId, int pageIndex, int pageSize)
        {
            try
            {
                var listReport = db.ExternalReports.Where(a => a.IsDel == false && a.ResearchPatientId ==
                              RId && a.CRFormId == CRFormId && (a.ReportType == ".wav" || a.ReportType == ".mp3")).OrderByDescending(a => a.CreatedTime).Skip((pageIndex - 1) * pageSize).Take(pageSize)
                              .Select(a => new { a.Id, ReportContent = a.AIContent, a.ReportURL, CreatedTime = a.CreatedTime.ToString("yyyy-MM-dd HH:mm:ss") }).ToList();
                return Json(new { code = 0, data = listReport });
            }
            catch (Exception ex)
            {

                return Json(new { code = -100, msg = $"获取语音文件报错【{ex.Message}】" });
            }
        }

        [HttpPost]
        public IActionResult DelVoice(int Id)
        {
            try
            {
                var externalReport = db.ExternalReports.Find(Id);
                if (externalReport == null)
                    return Json(new { code = -100, msg = "语音文件不存在，请刷新页面重试！" });
                db.ExternalReports.Remove(externalReport);
                db.SaveChanges();
                return Json(new { code = 0, msg = "删除成功！" });
            }
            catch (Exception ex)
            {

                return Json(new { code = -100, msg = $"删除语音文件失败！", data = ex });
            }
        }

        public ActionResult GetKey(bool isCheck)
        {
            try
            {
                string appkey = "", appsecret = "";
                if (isCheck)
                {
                    appkey = config["AppSettings:YZSAPI_KEY"];
                    appsecret = config["AppSettings:YZS_Appseret"];
                }
                else
                {
                    appkey = config["AppSettings:XuFeiAPI_KEY"];
                    appsecret = config["AppSettings:XuFeiAPPID"];
                }
                return Json(new { code = 0, appkey = appkey, appsecret = appsecret });
            }
            catch (Exception ex)
            {

                return Json(new { code = -100, data = ex, msg = "获取语音密钥报错！" });
            }
        }

        public IActionResult GetSHA256Hash(String appkey, String timestamp, String secret)
        {
            String originalStr = appkey + timestamp + secret;
            using (SHA256 sha256Hash = SHA256.Create())
            {
                byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(originalStr));

                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2").ToUpper());
                }
                return Json(builder.ToString());
            }
        }

        [AllowAnonymous]
        /// <summary>
        /// 使用大模型拆分语音
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> resultSplitByAI(string words)
        {
            try
            {
                if (string.IsNullOrEmpty(words))
                {
                    return Json(new { code = 0, data = "" });
                }
                var modelType = config["AppSettings:modelType"];

                #region 读取配置文件
                string apiKey = config[$"GPTSetting:{modelType}:ApiKey"];
                string model = config[$"GPTSetting:{modelType}:Model"];
                string apiUrl = config[$"GPTSetting:{modelType}:apiUrl"];
                string maxTokenStr = config[$"GPTSetting:{modelType}:MaxTokens"];
                int maxTokens = 4000;
                var modelName = modelType;

                var tips = "";
                // var modelType = config["AppSettings:modelType"];
                var promptInfo = db.PromptInfos.Where(o => o.PropmtIdentifier == "RehabilitationDept-1" && o.IsUsed
                            && (o.ModelName == "通用" || o.ModelName == modelType)).OrderBy(o => o.ModelName).FirstOrDefault();
                if (promptInfo == null)
                {
                    return Json(new { code = -100, msg = $"不存在该页面[RehabilitationDept-1]基于该模型[{modelType}]的通用或专用提示词；" });

                }
                var Newprompt = promptInfo.Prompt.Replace("{{用户输入提示词}}", words);
                HttpClientHandler handler = null;
                string webProxy = config[$"GPTSetting:{modelType}:WebProxy"];
                if (!string.IsNullOrWhiteSpace(webProxy))
                {
                    handler = new HttpClientHandler()
                    {
                        Proxy = new WebProxy(webProxy)
                    };
                }
                else
                {
                    handler = new HttpClientHandler() { };
                }

                #endregion
                string endpoint = "chat/completions";

                dynamic history = new List<dynamic>();

                var contentStr = "";
                var httpClient = new HttpClient(handler);
                httpClient.Timeout = TimeSpan.FromMinutes(5);
                httpClient.BaseAddress = new Uri(apiUrl);
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                history.Add(new { role = "user", content = $"{Newprompt}" });

                var requestMsgTime = System.DateTime.Now;

                dynamic requstDTO = new ExpandoObject();
                requstDTO.model = model;
                requstDTO.messages = history;
                requstDTO.stream = false;
                requstDTO.max_tokens = maxTokens;
                var requestBody = JsonConvert.SerializeObject(requstDTO);
                var request = new HttpRequestMessage(HttpMethod.Post, endpoint)
                {
                    Content = new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
                };
                var IsAdd = false;
                var ResponseContent = "";
                using var APIResponse = await httpClient.SendAsync(request);
                {
                    if (APIResponse.IsSuccessStatusCode)
                    {
                        var stream = await APIResponse.Content.ReadAsStreamAsync();
                        using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
                        {
                            // ResponseContent = reader.ReadToEnd();
                            var resultAI = reader.ReadToEnd();
                            var jobj = JsonConvert.DeserializeObject<JObject>(resultAI);
                            if (jobj.ContainsKey("choices"))
                            {
                                var choicesList = jobj.Value<JArray>("choices");
                                var choicesItem = choicesList.FirstOrDefault();
                                if (choicesItem != null)
                                {
                                    ResponseContent = choicesItem.Value<JObject>("message").Value<string>("content");
                                    Console.WriteLine($"大模型结果:{ResponseContent}");
                                    ResponseContent = ResponseContent.Replace("\n\n", "\n");
                                    LoggerHelper.WriteInfo("其他日志", $"大模型结果:{ResponseContent}");

                                }
                            }
                        }
                    }
                    else
                    {
                        return Json(new { code = -100, msg = "Failed to connect to API！" });

                    }
                }

                return Json(new { code = 0, data = ResponseContent });
            }
            catch (Exception ex)
            {
                LoggerHelper.WriteInfo("其他日志", $"调用AI提取报错:{ex.Message + ex.InnerException?.Message}");
                return Json(new { code = -100, msg = ex.Message });
            }
        }
    }
}
