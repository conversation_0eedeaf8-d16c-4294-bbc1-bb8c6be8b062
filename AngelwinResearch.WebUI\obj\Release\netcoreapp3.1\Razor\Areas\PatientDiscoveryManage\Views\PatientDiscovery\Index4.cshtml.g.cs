#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\PatientDiscoveryManage\Views\PatientDiscovery\Index4.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "849caf29c7a9b9e00cef9c8e31cdd43fb63aa71033d9972684b6e83d7084d98e"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_PatientDiscoveryManage_Views_PatientDiscovery_Index4), @"mvc.1.0.view", @"/Areas/PatientDiscoveryManage/Views/PatientDiscovery/Index4.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"849caf29c7a9b9e00cef9c8e31cdd43fb63aa71033d9972684b6e83d7084d98e", @"/Areas/PatientDiscoveryManage/Views/PatientDiscovery/Index4.cshtml")]
    #nullable restore
    internal sealed class Areas_PatientDiscoveryManage_Views_PatientDiscovery_Index4 : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/css/bootstrap.min.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/css/chosen.min.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/css/awesome-bootstrap-checkbox.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/css/bootstrap-slider.min.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/css/selectize.bootstrap5.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/css/bootstrap-icons.min.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/css/query-builder.default.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\PatientDiscoveryManage\Views\PatientDiscovery\Index4.cshtml"
  
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "849caf29c7a9b9e00cef9c8e31cdd43fb63aa71033d9972684b6e83d7084d98e8383", async() => {
                WriteLiteral("\r\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n    <meta charset=\"utf-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>患者发现</title>\r\n\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "849caf29c7a9b9e00cef9c8e31cdd43fb63aa71033d9972684b6e83d7084d98e8897", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n");
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "849caf29c7a9b9e00cef9c8e31cdd43fb63aa71033d9972684b6e83d7084d98e10138", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "849caf29c7a9b9e00cef9c8e31cdd43fb63aa71033d9972684b6e83d7084d98e11341", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "849caf29c7a9b9e00cef9c8e31cdd43fb63aa71033d9972684b6e83d7084d98e12544", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "849caf29c7a9b9e00cef9c8e31cdd43fb63aa71033d9972684b6e83d7084d98e13747", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "849caf29c7a9b9e00cef9c8e31cdd43fb63aa71033d9972684b6e83d7084d98e14950", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "849caf29c7a9b9e00cef9c8e31cdd43fb63aa71033d9972684b6e83d7084d98e16153", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "849caf29c7a9b9e00cef9c8e31cdd43fb63aa71033d9972684b6e83d7084d98e17356", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "849caf29c7a9b9e00cef9c8e31cdd43fb63aa71033d9972684b6e83d7084d98e18559", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    <style>\r\n        .rectangle {\r\n            width: 200px; /* 宽度 */\r\n\r\n            float: left;\r\n            margin-left: 20px;\r\n            padding: 40px 15px;\r\n           \r\n        }\r\n    </style>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "849caf29c7a9b9e00cef9c8e31cdd43fb63aa71033d9972684b6e83d7084d98e20613", async() => {
                WriteLiteral(@"
    <div class=""layui-col-md12"">
        <div class=""layui-card"">
            <div class=""layui-card-body layui-form"">
                <div class=""layui-inline"" style=""width:380px"">


                    <label class=""layui-form-label"" style=""width:120px"">科研组：</label>
                    <div class=""layui-input-inline"">
                        <select id=""status"">
                            <option");
                BeginWriteAttribute("value", " value=\"", 1686, "\"", 1694, 0);
                EndWriteAttribute();
                WriteLiteral(@">全部</option>
                            <option value=""甲状腺"">甲状腺</option>
                            <option value=""肾移植"">肾移植</option>
                            <option value=""脓毒症"">脓毒症</option>
                            <option value=""泌尿系结石"">泌尿系结石</option>
                        </select>
                    </div>

                </div>
                <div class=""layui-inline"">
                    <div class=""layui-input-inline"" style=""width:300px;"">
                        <input type=""text"" class=""layui-input"" name=""keyWord"" placeholder=""请输入病历号/患者姓名"" id=""keyWord"" />
                    </div>

                </div>

                <div class=""layui-inline"">
                    <button class=""layui-btn"" id=""Search2"">查询</button>

                </div>
            </div>
            <div class=""layui-card-body"">
                <table id=""tablelist"" lay-filter=""tablelist""></table>
                <script type=""text/html"" id=""tableBar1"">
                        <a class=""layui");
                WriteLiteral(@"-btn layui-btn-normal layui-btn-xs"" lay-event=""rz"" style=""text-decoration:none""><i class=""layui-icon""></i>科研设置</a>

                       <a class=""layui-btn layui-btn-warm layui-btn-xs"" lay-event=""jd"" style=""text-decoration:none""><i class=""layui-icon""></i>查看进度</a>

                </script>
                <script type=""text/html"" id=""tableBar2"">
                       <a class=""layui-btn layui-btn-normal layui-btn-xs"" lay-event=""sj"" style=""text-decoration:none""><i class=""layui-icon""></i>设计表单</a>
                        <a class=""layui-btn layui-btn-warm layui-btn-xs"" lay-event=""look"" style=""text-decoration:none""><i class=""layui-icon""></i>查看表单</a>

");
                WriteLiteral("\r\n                </script>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "849caf29c7a9b9e00cef9c8e31cdd43fb63aa71033d9972684b6e83d7084d98e23409", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "849caf29c7a9b9e00cef9c8e31cdd43fb63aa71033d9972684b6e83d7084d98e24534", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "849caf29c7a9b9e00cef9c8e31cdd43fb63aa71033d9972684b6e83d7084d98e25659", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"

    <script>
        layui.use(['element', 'layer', 'table', 'laydate', 'form'], function () {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , laydate = layui.laydate
                , $ = layui.$
                , form = layui.form;
            ;


            table.render({
                elem: '#tablelist'
                , id: 'tablelist'
                , url: '/PatientDiscoveryManage/PatientDiscovery/GetData2'
                , page: true
                , limit: 20
                , height: 'full-110'
                , cols: [[
                    { field: 'No', title: '序号', width: 60, type: 'numbers' }
                    , { field: 'KYRZ', title: '科研入组', width: 200, templet: function (d){
                            return ""<span  style=' font-weight:bold'>"" + d.KYRZ + ""</span>"";
                    } }
                    , {
                        title: '科研项目', width: 200, temple");
                WriteLiteral(@"t: function (d) {
                            var htmlstr = '<div style=""margin-top: 5px; ""><div class=""layui-progress layui-progress-big"" lay-showPercent=""true"">';
                            var num = d.count + ""/"" + d.all;
                            htmlstr += '<div class=""layui-progress-bar"" lay-percent=""' + num + '""></div></div></div>';
                            return htmlstr;
                        }
                    }
                    , { field: 'BLH', title: '病历号', width: 100 }
                    , { field: 'HZXM', title: '患者姓名', width: 120 }
                    , { field: 'NL', title: '年龄', width: 80 }
                    , { field: 'RYZDMC', title: '入院诊断', width: 200 }
                    , { field: 'CYZDMC', title: '出院诊断', width: 200 }
                    , { field: 'SSMC', title: '手术', width: 200 }
                    , { field: 'CSZZ', title: '初始症状', width: 110 }
                    , { field: 'CBZD', title: '初步诊断', width: 110 }
                    , { field: 'CBZL', ti");
                WriteLiteral(@"tle: '初步治疗', width: 110 }
                    , { field: 'TW', title: '体温', width: 110 }
                    , { field: 'MB', title: '脉搏', width: 110 }
                    , { field: 'HXL', title: '呼吸率', width: 110 }
                    , { field: 'TGJC', title: '体格检查', width: 200 }
                    , { field: 'XBS', title: '现病史', width: 200 }
                    //, { field: 'CWDM', title: '床位代码', width: 100 }
                    //, { field: 'KSDM', title: '科室代码', width: 100 }
                    //, { field: 'KSMC', title: '科室名称', width: 100 }
                    //, { field: 'BQDM', title: '病区代码', width: 100 }
                    //, { field: 'BQMC', title: '病区名称', width: 100 }
                    //, { field: 'ZXKSDM', title: '执行科室', width: 100 }
                    //, { field: 'YLZDM', title: '医疗组代码', width: 100 }
                    //, { field: 'YLZMC', title: '医疗组名称', width: 100 }

                    //, {
                    //    field: 'Status', title: '状态', width: 100, fixed:");
                WriteLiteral(@" 'right', templet: function (d) {
                    //        if (d.Status == 1)
                    //            return ""已入组"";
                    //        else if (d.Status == -1)
                    //            return ""不符合"";
                    //        else
                    //            return '待定';
                    //    }
                    //}
                   
                    , {
                        title: '操作',
                        minWidth: 240,
                        fixed: 'right',
                        toolbar: '#tableBar1'
                    }
                ]]
                , done: function (res, curr, count) {
                    element.render('progress');

                }
            });

            var PATIENTID="""";
            //监听tablelist工具条
            table.on('tool(tablelist)', function (obj) {
                var data = obj.data;
                if (obj.event === 'rz') {
                   PATIENTID=data.PATIENTID;
  ");
                WriteLiteral(@"                  windowsIndex = layer.open({
                        type: 1,
                        //title: '患者【' + data.HZXM + '】入组',
                        title: '科研设置',
                        area: ['450px', '600px'],
                        resize: true,
                        content: $('#form_window_rz'),
                        btn: ['确认', '取消'],
                        yes: function (index, layero) {
                            var checkStatus = table.checkStatus(""tablelist2""); //获取选中行状态
                            var tabledata = checkStatus.data;
                            if(tabledata.length==0)
                            {
                                layer.msg(""请勾选数据！"");
                                return;
                            }
                          var  examClasses = tabledata.map(item => item.ExamClass);
                           var result = examClasses.join("","");
                            $.post('/PatientDiscoveryManage/PatientDiscovery/SaveEx");
                WriteLiteral(@"am', { patid: data.PATIENTID, str: result, RZBZ: data.KYRZ }, function (res) {
                                if (res.code == 0) {
                                    layer.msg(res.msg);
                                    layer.close(index);
                                    table.reload('tablelist');
                                }
                                else
                                    layer.msg(res.msg);
                               
                            })
                        }
                        , btn2: function (index, layero) {
                          
                        },
                        success: function () {
                            setTable(data.PATIENTID);
                        }

                    });
                }
              
                else if(obj.event==='jd')
                {
                    //PatientDiscoveryManage/ResearchProgress/Index(string patid)
                    parent.layui.inde");
                WriteLiteral(@"x.openTabsPage('PatientDiscoveryManage/ResearchProgress/Index?patid=' + data.PATIENTID, ""科研进度采集"");
                }
            });

            table.on('tool(tablelist2)', function (obj) {
                var data = obj.data;
                if (obj.event === 'look') {
                    var Url = """";
                    if (data.ExamClass == ""体格检查"")
                        Url = ""/vform/Index2"";
                    else if (data.ExamClass == ""现病史"")
                        Url = ""/vform/Index"";
                    else if (data.ExamClass == ""肿瘤评估"")
                        Url = ""/vform/zlpg"";
                    else if (data.ExamClass == ""放射"")
                        Url = ""/vform/zjp"";
                    if (Url)
                        parent.layui.index.openTabsPage(Url + ""?patid="" + PATIENTID, data.ExamClass);
                }
                else if (obj.event === 'sj'){
                     var iframeWin;
                    if (data.ExamClass != ""体格检查"" && data.ExamClass != """);
                WriteLiteral(@"现病史"" && data.ExamClass != ""肿瘤评估"" && data.ExamClass != ""放射"")
                        return;
                    layer.open({
                        type: 2,
                        area: ['100%', '100%'],
                        //  content: $('#form_window_template'),
                        content: '/vform/template',
                        fixed: false, // 不固定
                        maxmin: true,
                        title: '设计模板表单',
                        shadeClose: true,
                       // btn: ['保存', '关闭'],
                        btnAlign: 'c',
                        btn1: function (index1, layero) {
                            var iframeWin = window[layero.find('iframe')[0]['name']];
                            // iframeWin.app.$refs.vFormRef.disableForm();
                            var formsetting = iframeWin.app.$refs.VFDesigner.getFormJson();

                            //alert(json);
                            $.post('/vform/SaveTemplate', { ""str"": JSON.strin");
                WriteLiteral(@"gify(formsetting), ""type"": ""xbs"" }, function (res) {
                                if (res.code == 0) {
                                    app.$refs.vFormRef.setFormJson(JSON.stringify(formsetting));
                                    layer.close(index1);
                                }
                                else
                                    layer.msg(res.msg);
                            })

                        },
                        btn2: function (index, layero) {

                        },
                        success: function (layero, index) {
                            var Urls = """";
                            if (data.ExamClass == ""体格检查"")
                                Urls = ""/vform/CheckTemplate?type=tgjc"";
                            else if (data.ExamClass == ""现病史"")
                                Urls = ""/vform/CheckTemplate?type=xbs"";
                            else if (data.ExamClass == ""肿瘤评估"")
                                Urls = ""/v");
                WriteLiteral(@"form/CheckTemplate?type=zlpg"";
                            else if (data.ExamClass == ""放射"")
                                Urls = ""/vform/CheckTemplate?type=zjp"";
                            $.get(Urls, function (res) {

                                if (res.code == 0) {
                                    //// console.log(res.data.obj);
                                    //app.$refs.vFormRef.setFormJson(res.data.obj);
                                    //if (res.data.obj1)
                                    //    app.$refs.vFormRef.setFormData(JSON.parse(res.data.obj1));
                                    ////  console.log(this);
                                     iframeWin = window[layero.find('iframe')[0]['name']];
                                    iframeWin.app.$refs.VFDesigner.clearDesigner();
                                   
                                        iframeWin.app.$refs.VFDesigner.setFormJson(res.data.obj)
                                    
                 ");
                WriteLiteral(@"               }
                            })
                           
                           
                        },
                        end :function()
                        {
                            iframeWin.app.$refs.VFDesigner.clearDesigner();
                        }

                    })
                }

            });
            $(document).on('click', '#Search2', function () {
                table.reload('tablelist', {
                    page: {
                        curr: 1
                    },
                    where: {
                        'keyWord': $.trim($(""#keyWord"").val()),
                        'keySql': '',
                        'status': $(""#status"").val()
                    }
                });
            })

            function setTable(patid)
            {
                table.render({
                    elem: '#tablelist2'
                    , id: 'tablelist2'
                    , url: '/PatientDiscov");
                WriteLiteral(@"eryManage/PatientDiscovery/GetExam?patid='+patid
                    , page: false
                 
                   
                    , cols: [[
                        { type: 'checkbox', fixed: 'left' }
                        , { field: 'ExamClass', title: '项目名称', width: 150 }
                      
                   
                        , {
                            title: '操作',
                            minWidth: 210,
                            fixed: 'right',
                            toolbar: '#tableBar2'
                        }
                    ]]
                });
            }

        });

      
    </script>


    <div class=""window_wrap"" id=""form_window_rz"" style=""display: none;"">

        <form class=""layui-form"" id=""fm_rz""");
                BeginWriteAttribute("action", " action=\"", 15860, "\"", 15869, 0);
                EndWriteAttribute();
                WriteLiteral(">\r\n\r\n            <div class=\"layui-form-item\" style=\"padding-top:15px\">\r\n                <table id=\"tablelist2\" lay-filter=\"tablelist2\"></table>\r\n            </div>\r\n\r\n        </form>\r\n\r\n    </div>\r\n\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n\r\n\r\n</html>\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
