#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\OperationHistory\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "7bc4b3f7227b085ffd67b7578795ffa6061abbf200e9b2dd716b1a4f1685ae43"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_OperationHistory_Index), @"mvc.1.0.view", @"/Views/OperationHistory/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI

#nullable disable
    ;
#nullable restore
#line 2 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"7bc4b3f7227b085ffd67b7578795ffa6061abbf200e9b2dd716b1a4f1685ae43", @"/Views/OperationHistory/Index.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"a5bcd83cf27c8ffb8baf597d24314352bf7b52b5a9bb5ee83e83e9d0089a628e", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_OperationHistory_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/jquery/dist/jquery.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\OperationHistory\Index.cshtml"
  
    ViewBag.Title = "操作留痕日志";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("<!DOCTYPE html>\r\n<html lang=\"en\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7bc4b3f7227b085ffd67b7578795ffa6061abbf200e9b2dd716b1a4f1685ae436839", async() => {
                WriteLiteral(@"
    <meta charset=""UTF-8"">
    <meta name=""viewport""
          content=""width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"">
    <meta http-equiv=""X-UA-Compatible"" content=""ie=edge"">
    <title>操作留痕日志</title>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "7bc4b3f7227b085ffd67b7578795ffa6061abbf200e9b2dd716b1a4f1685ae437391", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "7bc4b3f7227b085ffd67b7578795ffa6061abbf200e9b2dd716b1a4f1685ae438593", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7bc4b3f7227b085ffd67b7578795ffa6061abbf200e9b2dd716b1a4f1685ae439795", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7bc4b3f7227b085ffd67b7578795ffa6061abbf200e9b2dd716b1a4f1685ae4310918", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7bc4b3f7227b085ffd67b7578795ffa6061abbf200e9b2dd716b1a4f1685ae4312042", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7bc4b3f7227b085ffd67b7578795ffa6061abbf200e9b2dd716b1a4f1685ae4313166", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    <style>\r\n        /* 定义表头样式 */\r\n        .layui-table-header .layui-table-cell {\r\n            font-weight: bold; /* 加粗 */\r\n            font-size: 14px; /* 可选：调整字体大小以提升可读性 */\r\n            color: #333; /* 可选：调整字体颜色 */\r\n        }\r\n    </style>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "7bc4b3f7227b085ffd67b7578795ffa6061abbf200e9b2dd716b1a4f1685ae4315258", async() => {
                WriteLiteral(@"
    <div class=""layui-col-md12"">
        <div class=""layui-card"">
            <div class=""layui-card-body layui-form"">
                <div class=""layui-inline"">
                    <div class=""layui-inline"">
                        <label>操作人:</label>
                    </div>
                    <div class=""layui-input-inline"" style=""width:200px;"">
                        <input type=""text"" class=""layui-input"" name=""KeyWords"" id=""KeyWords"" />
                    </div>
                    <div class=""layui-inline"">
                        <label>科研编号:</label>
                    </div>
                    <div class=""layui-input-inline"" style=""width:200px;"">
                        <input type=""text"" class=""layui-input"" name=""RID"" id=""RID"" />
                    </div>
                    <div class=""layui-inline"">
                        <div id=""xmDeptsList"" class=""xm-select-demo"" style=""width:250px""></div>
                    </div>
                    <div class=""layui-inline"">
  ");
                WriteLiteral(@"                      <div id=""xmGroupsList"" class=""xm-select-demo"" style=""width:250px""></div>
                    </div>
                    <div class=""layui-inline"">
                        <div id=""xmSelectFormList"" class=""xm-select-demo"" style=""width:250px""></div>
                    </div>
                </div>
                <div class=""layui-inline"" style=""margin-top:20px;"">
                    <div class=""layui-inline"">
                        <label>操作类型:</label>
                    </div>
                    <div class=""layui-input-inline"">
                        <input type=""checkbox"" name=""operationType"" value=""0"" lay-skin=""primary"" title=""CRF表单新增""");
                BeginWriteAttribute("checked", " checked=\"", 2710, "\"", 2720, 0);
                EndWriteAttribute();
                WriteLiteral(">\r\n                        <input type=\"checkbox\" name=\"operationType\" value=\"1\" lay-skin=\"primary\" title=\"数据采集\"");
                BeginWriteAttribute("checked", " checked=\"", 2833, "\"", 2843, 0);
                EndWriteAttribute();
                WriteLiteral(">\r\n                        <input type=\"checkbox\" name=\"operationType\" value=\"2\" lay-skin=\"primary\" title=\"数据修改\"");
                BeginWriteAttribute("checked", " checked=\"", 2956, "\"", 2966, 0);
                EndWriteAttribute();
                WriteLiteral(">\r\n                        <input type=\"checkbox\" name=\"operationType\" value=\"3\" lay-skin=\"primary\" title=\"数据下载\"");
                BeginWriteAttribute("checked", " checked=\"", 3079, "\"", 3089, 0);
                EndWriteAttribute();
                WriteLiteral(@">
                    </div>
                    <div class=""layui-inline"">
                        <label>日期范围:</label>
                    </div>
                    <div class=""layui-input-inline"" style=""width:200px;"">
                        <input type=""text"" name=""searchDate"" id=""searchDate"" placeholder=""yyyy-MM-dd~yyyy-MM-dd"" autocomplete=""off"" class=""layui-input"">
                    </div>
                    <div class=""layui-inline"">
                        <button class=""layui-btn layui-btn-normal fr"" id=""Search"">查 &nbsp;&nbsp;询</button>
                    </div>
                </div>

            </div>
            <div class=""layui-card-body"">
                <table id=""tablelist"" lay-filter=""tablelist""></table>
            </div>
        </div>
        <script>
            layui.use(['laydate', 'table'], function () {
                var table = layui.table//表格
                    , laydate = layui.laydate
                    , $ = layui.$
                    ;
       ");
                WriteLiteral("        var beginDateStr = \'");
                Write(
#nullable restore
#line 88 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\OperationHistory\Index.cshtml"
                                    ViewBag.beginDate

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\';\r\n                var endDateStr = \'");
                Write(
#nullable restore
#line 89 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\OperationHistory\Index.cshtml"
                                   ViewBag.endDate

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\';\r\n                var researchId = \'");
                Write(
#nullable restore
#line 90 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\OperationHistory\Index.cshtml"
                                   ViewBag.researchId

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\'\r\n                var deptId = \'");
                Write(
#nullable restore
#line 91 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\OperationHistory\Index.cshtml"
                               ViewBag.deptId

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\';\r\n                var groupId = \'");
                Write(
#nullable restore
#line 92 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\OperationHistory\Index.cshtml"
                                ViewBag.groupId

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\';\r\n                var formIds = \'");
                Write(
#nullable restore
#line 93 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\OperationHistory\Index.cshtml"
                                ViewBag.formIds

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"';

                $('#RID').val(researchId);


                var start = laydate.render({
                    elem: '#searchDate' //指定元素
                    , range: '~'
                    , value: beginDateStr + ' ~ ' + endDateStr
                    , format: 'yyyy-MM-dd'
                    , btns: ['confirm']
                    , done: function (value, date, endDate) {
                        beginDateStr = value.split('~')[0];
                        endDateStr = value.split('~')[1];
                    }
                });

                $(""#searchDate"").blur(function () {

                    start.config.min = {
                        year: '1900',
                        month: '0',//关键
                        date: '1',
                    };
                    start.config.max = {
                        year: '2100',
                        month: '0',//关键
                        date: '1',
                    };

                });
                //tabl");
                WriteLiteral(@"elist
                table.render({
                    elem: '#tablelist'
                    , id: 'tablelist'
                    , url: '/OperationHistory/LogsList'
                    , limit: 20
                    , height: 'full-160'
                    , page: true
                    , cols: [[
                        { field: 'zizeng', title: '', type: 'numbers', fixed: 'left' }
                        , { field: 'OperationType', title: '操作类型' }
                        /* , { field: 'DeptName', title: '科研机构' }*/
                        , { field: 'GroupName', title: '专病名称' }
                        , { field: 'CRFName', title: 'CRF表单名' }
                        , { field: 'PatientName', title: '患者' }
                        , { field: 'OperationField', title: '变量' }
                        , { field: 'OperationValue', title: '变量值' }
                        , { field: 'OperaUserIp', title: '操作人IP' }
                        , { field: 'CreateUserName', title: '操作人' }
             ");
                WriteLiteral(@"           , { field: 'CreatedTime', title: '操作时间' }
                        , { field: 'Remark', title: '备注' }
                    ]]
                });

                var xmDeptsList = xmSelect.render({
                    el: '#xmDeptsList',
                    model: { label: { type: 'text' } },
                    prop: {
                        name: 'title',
                        value: 'id',
                    },
                    minWidth: 200,
                    radio: true,
                    filterable: true,
                    clickClose: true,
                    //树
                    tree: {
                        show: true,
                        //非严格模式
                        strict: false,
                        //默认展开节点
                        expandedKeys: [-1],
                    },
                    data: []
                    , on: function (val) {
                        getGroupsList(1, val.arr[0].id, """");
                        getFor");
                WriteLiteral(@"mList(1, val.arr[0].id, """", """");
                    }
                });

                var xmGroupsList = xmSelect.render({
                    el: '#xmGroupsList',
                    model: { label: { type: 'text' } },
                    prop: {
                        name: 'title',
                        value: 'id',
                    },
                    minWidth: 200,
                    radio: true,
                    filterable: true,
                    clickClose: true,
                    //树
                    tree: {
                        show: true,
                        //非严格模式
                        strict: false,
                        //默认展开节点
                        expandedKeys: [-1],
                    },
                    data: []
                    , on: function (val) {
                        getFormList(1, """", val.arr[0].id, """");
                    }
                });

                var xmSelectFormList = xmSelect.render({
   ");
                WriteLiteral(@"                 el: '#xmSelectFormList',
                    autoRow: true,
                    prop: {
                        name: 'Name',
                        value: 'Id',
                    },
                    filterable: true,
                    tips: '请选择CRF表单',
                    on: function (data) {
                    },
                    done: function (res) {
                    }, model: {
                        label: {
                            type: 'total', //自定义与下面的对应
                            total: {
                                template(data, sels) {
                                    return ""已选中 "" + sels.length + "" 项, 共 "" + data.length + "" 项""
                                }
                            },
                        }
                    }
                })

                function GetDeptsTree() {
                    $.ajax({
                        url: '/CommAPI/GetOrgsTreeList',
                        type: ""post"",
   ");
                WriteLiteral(@"                     datatype: 'json',
                        async: false,
                        success: function (result) {
                            xmDeptsList.update({
                                data: result
                            });
                            if (result[0].id) {
                                var arr = new Array();
                                arr.push(result[0].id);
                                xmDeptsList.setValue(arr);
                                getGroupsList(1, result[0].id, """");
                                getFormList(1, result[0].id, """", """");
                                setTimeout(function () {
                                    //getData();
                                }, 1000);
                            }
                        }, error: function () {
                            layer.msg(""获取失败！"");
                        }
                    })
                };

                function getGroupsList(type, va");
                WriteLiteral(@"l, defaultValue) {
                    $.ajax({
                        url: '/CommAPI/GetGroupTreeList?hospitalDeptId=' + val,
                        // contentType: ""application/json"",
                        type: ""get"",
                        datatype: 'json',
                        async: false,
                        success: function (result) {
                            if (type == 1) {
                                xmGroupsList.update({
                                    data: result
                                });
                                if ($.trim(defaultValue) != '' && result[0].id) {
                                    var arr = new Array();
                                    arr.push(defaultValue);
                                    xmGroupsList.setValue(arr);
                                }
                            }
                        }, error: function () {
                            layer.msg(""获取失败！"");
                        }
          ");
                WriteLiteral(@"          });
                }


                function getFormList(type, deptId, groupId, defaultValue) {
                    if (deptId == """") {
                        if (type == 1)
                            deptId = xmDeptsList.getValue('valueStr');
                    }
                    if (groupId == """") {
                        if (type == 1)
                            groupId = xmGroupsList.getValue('valueStr');
                        else
                            groupId = xmGroupsList.getValue('valueStr');
                    }
                    $.ajax({
                        url: '/OperationHistory/GetFormList?deptId=' + deptId + ""&groupId="" + groupId,
                        // contentType: ""application/json"",
                        type: ""get"",
                        datatype: 'json',
                        async: false,
                        success: function (result) {
                            if (type == 1) {
                                xmS");
                WriteLiteral(@"electFormList.update({
                                    data: result.data
                                });
                                if ($.trim(defaultValue) != '') {
                                    var arr = new Array();
                                    arr.push(defaultValue);
                                    xmSelectFormList.setValue(arr);
                                }
                            }

                        }, error: function () {
                            layer.msg(""获取失败！"");
                        }
                    });
                }


                $(document).ready(function () {
                    GetDeptsTree();
                    if (deptId != """") {
                        xmDeptsList.setValue([deptId]);
                        if (groupId != """") {
                            getGroupsList(1, [deptId],"""");
                            xmGroupsList.setValue([groupId])
                        }
                        if (for");
                WriteLiteral(@"mIds != """") {
                            getFormList(1, [deptId], """", """");
                            xmSelectFormList.setValue([formIds]);
                        }
                        var checkboxValue = [];
                        $(""input[name='operationType']:checked"").each(function () {
                            checkboxValue.push($(this).attr('title'));
                        });
                        var operationType = checkboxValue.join(',');
                        table.reload('tablelist', {
                            page: {
                                curr: 1
                            },
                            where: {
                                'username': $.trim($(""#KeyWords"").val())
                                , 'researchId': $.trim($(""#RID"").val())
                                , 'beginDate': beginDateStr
                                , 'endDate': endDateStr
                                , 'operationType': operationType
               ");
                WriteLiteral(@"                 , 'deptId': deptId
                                , 'groupId': groupId
                                , 'formIds': formIds
                            }
                        });
                    }
                    $(document).on('click', '#Search', function () {
                        /*  var xcn = xmCategory.getValue('value').join();*/
                        var checkboxValue = [];
                        $(""input[name='operationType']:checked"").each(function () {
                            checkboxValue.push($(this).attr('title'));
                        });
                        var operationType = checkboxValue.join(',');
                        var detID = xmDeptsList.getValue('valueStr');
                        var groupID = xmGroupsList.getValue('valueStr');
                        var formID = xmSelectFormList.getValue('valueStr');
                        table.reload('tablelist', {
                            page: {
                               ");
                WriteLiteral(@" curr: 1
                            },
                            where: {
                                'username': $.trim($(""#KeyWords"").val())
                                ,'researchId': $.trim($(""#RID"").val())
                                , 'beginDate': beginDateStr
                                , 'endDate': endDateStr
                                , 'operationType': operationType
                                , 'deptId': detID
                                , 'groupId': groupID
                                , 'formIds': formID
                            }
                        });
                    });

                });
            });
        </script>
    </div>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
