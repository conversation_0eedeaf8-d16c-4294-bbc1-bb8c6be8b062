#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c1557"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_HospitalCRF_Views_VoiceMedical_Index), @"mvc.1.0.view", @"/Areas/HospitalCRF/Views/VoiceMedical/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c1557", @"/Areas/HospitalCRF/Views/VoiceMedical/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_HospitalCRF_Views_VoiceMedical_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<
#nullable restore
#line 4 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
       AngelwinResearch.Models.ResearchPatient

#line default
#line hidden
#nullable disable
    >
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/muihk/css/mui.min.css?v=1.0"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/font/eyes_icon/iconfont.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/columnDrag/column_drag.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/lib/codemirror.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/theme/pastel-on-dark.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/dialog/style.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/KeDaXunFei/hmac-sha256.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/KeDaXunFei/HmacSHA1.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/KeDaXunFei/md5.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/KeDaXunFei/enc-base64-min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_13 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/KeDaXunFei/dist/index.umd.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_14 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("type", new global::Microsoft.AspNetCore.Html.HtmlString("text/javascript"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_15 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/KeDaXunFei/HZRecorder.js?v=6"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_16 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/marked.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_17 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/dialog/handlebars.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.SingleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_18 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_19 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/muihk/js/mui.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
  
	Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("<!DOCTYPE html>\r\n<html lang=\"zh\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155711016", async() => {
                WriteLiteral(@"
	<meta charset=""UTF-8"">
	<meta http-equiv=""X-UA-Compatible"" content=""IE=edge"">
	<meta name=""viewport"" content=""width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"">
	<meta name=""screen-orientation"" content=""landscape"">

	<title>语音病理文书转写</title>
	");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155711587", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155712788", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155713989", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155715190", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n\t<!-- 代码高亮 -->\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155716414", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155717616", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155718817", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155720019", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155721141", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155722263", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155723386", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155724509", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155725632", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155726755", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_14);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_15);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n");
                WriteLiteral("\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155728001", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_16);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
	<style>
		body {
			background-color: #fafafa;
			overflow: hidden;
		}

		.layui-card-header {
			line-height: 20px;
			font-weight: bold;
		}


		/* 移动端头部 */

		.headrow {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			background-color: #f6f6f6;
			height: 38px;
		}

			.headrow .layui-btn-primary {
				background-color: transparent;
				border: none;
				color: #333;
			}

		.patient_information {
			align-items: center;
		}

		.middle {
			text-align: center;
		}

		.left, .right {
			min-width: 58px;
			height: 38px;
		}
		/* 移动端头部 */
		.space-between {
			justify-content: space-between;
		}

		.user_info {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			background-color: #fff;
			padding: 10px 15px;
		}

		.info_middle, .info_right {
			display: flex;
			flex-direction: row;
		}


		.user_info_item {
			margin-right: 20px;
		}

		.content_wrap {
			");
                WriteLiteral(@"display: flex;
			flex-direction: row;
			padding: 10px 5px;
		}

		.flex_one {
			flex: 1;
		}

		.side_nav {
			width: 40px;
		}

		.nav_btn {
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			width: 100%;
			padding: 20px 0;
			margin-bottom: 2px;
			writing-mode: vertical-rl;
			text-orientation: mixed;
			background: #e6e6e6;
			border-radius: 20px;
		}

		.nav_btn_active {
			color: #fff;
			background: rgb(122, 77, 123);
		}

		.content_inner {
			position: relative;
			padding: 10px;
			margin: 5px;
			margin-top: 0;
			background-color: #fff;
		}

		.btn_bottom {
			width: 96%;
			padding: 4px;
			padding-top: 5px;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			position: absolute;
			left: 0;
			bottom: 15px;
			/* background-color:#f6f6f6; */
		}

		.nav_btn_group {
			display: flex;
			flex-direction: row;
		}

			.nav_btn_group .active {
				border: 1px solid #");
                WriteLiteral(@"1e9fff;
				color: #1e9fff;
			}

		.nav_btn_item {
			text-align: center;
			padding: 0 10px;
			line-height: 30px;
			background-color: #fff;
			color: #666;
			border-radius: 2px;
			border: 1px solid #e6e6e6;
			cursor: pointer;
		}

		.left_content {
			height: 90%;
		}

		.left_inner {
			width: 100%;
			height: 100%;
		}


		.inner_user_info {
			justify-content: flex-start;
			background-color: transparent;
			padding: 0;
		}

		.writ {
			position: relative;
			margin: 10px 0;
			border: 1px solid #e6e6e6;
			border-radius: 2px;
		}

		.writ_bottom {
			width: 96%;
			position: absolute;
			bottom: 20px;
			text-align: right;
		}

		.empty {
			position: absolute;
			top: 50%;
			left: 50%;
			width: 200px;
			height: 100px;
			margin-left: -100px;
			margin-top: -50px;
			text-align: center;
		}

			.empty i {
				font-size: 80px;
				color: #20222A;
			}

		.more_btn {
			display: flex;
			flex-direction: row;
			justify-content: flex-");
                WriteLiteral(@"end;
			background-color: #fff;
		}

		.audio_item {
			padding-bottom: 10px;
			flex: 1;
			padding-right: 20px;
		}

			.audio_item audio {
				width: 100%;
			}

		.audio_list button {
			margin-top: 20px;
		}

		.audio_title {
			padding-left: 5px;
			line-height: 34px;
		}

		.history_wrap {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			border: 1px solid #f6f6f6;
			border-radius: 2px;
		}

			.history_wrap .layui-input {
				border-width: 0;
			}

		.transfer_btn {
			width: 38px;
			height: 38px;
			margin-right: 15px;
			text-align: center;
			cursor: pointer;
		}

			.transfer_btn i {
				font-size: 20px;
				line-height: 38px;
			}

		.alert {
			display: none;
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background-color: rgba(0, 0, 0, 0.8);
			color: white;
			text-align: center;
			padding-top: 20%;
			z-index: 1000;
		}

			.alert i {
				font-size: 40px;
			}

		");
                WriteLiteral(@"	.alert p {
				margin-top: 20px;
			}

		.row_wrap {
			display: flex;
			flex-direction: row;
			align-content: center;
		}

		.layui-border-blue {
			border-color: #1E9FFF;
			color: #1E9FFF;
		}

		.translation_btn {
			background: rgb(122, 77, 123);
		}

		.btn_bg {
			width: 38px;
			height: 38px;
			margin-left: 10px;
			background-color: #f6f6f6;
			border-radius: 2px;
			cursor: pointer;
		}

		.right_btn_group {
			align-items: center;
		}

		.ws_btn {
			padding: 4px;
			background-color: #f6f6f6;
			border-radius: 2px;
			color: #1E9FFF;
			cursor: pointer;
		}

		.Menu_side {
			width: 0px;
			overflow-y: auto;
			overflow-x: hidden;
			transition: width 0.5s ease;
			flex: none;
		}

			.Menu_side.folded {
				width: 220px;
			}

		.nav_item {
			position: relative;
			background-color: #fff;
			border: 1px solid #eee;
			padding: 10px 15px;
			border-radius: 4px;
			margin: 0 5px 10px;
		}

		.active {
			background: linear-gradient(");
                WriteLiteral(@"70deg, #dcffff, #62ffff);
			border-color: transparent;
			color: #1E9FFF;
		}

			.active .layui-progress-text {
				color: #1E9FFF;
			}

		.layui-colla-content {
			padding: 0;
		}
		/* 折叠状态下左侧菜单的宽度 */
		.sider_btn {
			position: absolute;
			display: block;
			right: 0;
			bottom: 10%;
			z-index: 99;
			line-height: 0;
			padding: 18px 0px 18px 0px;
			border: none;
			color: #fff;
			border-radius: 50% 0 0 50%;
			background: linear-gradient(to right, #56d1f9, #1eaddc);
		}

			.sider_btn i {
				font-size: 22px !important;
				padding-left: 8px;
			}

		.side_menu_title {
			font-size: 18px;
			color: #000;
			text-align: center;
			line-height: 30px;
			padding: 10px 0;
		}

		.form_name {
			margin-bottom: 15px;
		}

		.layui-colla-content .layui-card-body {
			padding: 10px 0;
		}

		.flex_row {
			display: flex;
			flex-direction: row;
		}

		.align_center {
			align-items: center;
		}

		.handle_R {
			padding-left: 10px;
			flex: 1;
		}
");
                WriteLiteral(@"

		.title_wrap {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
		}

		.tools_wrap {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
		}

		.tools_btn {
			height: 30px;
			padding: 2px 5px;
			border: 1px solid #1E9FFF;
			border-radius: 2px;
			margin: 0 1px;
			cursor: pointer;
		}

		.tools_wrap .layui-icon {
			font-size: 18px;
		}

		.form_name {
			margin-bottom: 15px;
			flex: 1;
		}


		.hide_report_list {
			padding: 15px;
		}

		.null_wrap {
			text-align: center;
			margin-top: 10%;
		}

			.null_wrap img {
				width: 200px;
			}

		.audio_list {
			max-height: 210px;
			overflow-y: auto;
		}

			.audio_list li {
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
			}

		.layui-form-switch {
			margin: 0;
		}
	</style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155737103", async() => {
                WriteLiteral(@"

	<div class=""wrap"" id=""content"">
		<div class=""headrow patient_information"" style=""display:none;"">
			<div class=""left"">
				<button id=""previous"" class=""layui-btn layui-btn-primary layui-border-green"" style=""margin-right:15px;"">
					<i class=""layui-icon layui-icon-close""></i>
				</button>
			</div>
			<div class=""middle"">
				<h5>数据采集</h5>
			</div>
			<div class=""right""></div>
		</div>

		<div class=""user_info"">
			<div class=""info_left"" style=""display:none"">
			</div>

			<div class=""info_middle"">
				<div class=""user_info_item"">患者:  <strong>");
                Write(
#nullable restore
#line 491 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                                              Model?.PatientName

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</strong></div>\r\n\t\t\t\t<div class=\"user_info_item\">患者卡号:  <strong>");
                Write(
#nullable restore
#line 492 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                                                Model?.BRKH

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</strong></div>\r\n\t\t\t</div>\r\n\r\n\t\t\t<div class=\"info_right\">\r\n\t\t\t\t<div class=\"user_info_item\">科室:  <strong>");
                Write(
#nullable restore
#line 496 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                                              Model?.HospitalDept?.DeptName

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</strong></div>\r\n\t\t\t\t<div class=\"user_info_item\">当前用户:  <strong>");
                Write(
#nullable restore
#line 497 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                                                ViewBag.userName

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</strong></div>
				<textarea id=""model_wrapR"" class=""layui-textarea"" style="" height:100%;width:100%;line-height:40px;
font-size:20px;resize:none;display:none""></textarea>
				<input type=""hidden"" id=""AIExtractJsonValue"" />
				<input type=""hidden"" id=""reportId"" />
				<input type=""hidden"" id=""GroupName""");
                BeginWriteAttribute("value", " value=\"", 9688, "\"", 9714, 1);
                WriteAttributeValue("", 9696, 
#nullable restore
#line 502 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                                                ViewBag.GroupName

#line default
#line hidden
#nullable disable
                , 9696, 18, false);
                EndWriteAttribute();
                WriteLiteral(" />\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<div class=\"content_wrap\">\r\n");
                WriteLiteral(@"			<div class=""layui-row flex_one row_wrap"">

				<!--医疗对话区-->
				<div class=""layui-col-xs3 layui-col-md3 "">
					<div class=""content_inner "">
						<div class=""layui-card-header row_wrap space-between layui-form"">
							<div>医疗对话框</div>





							<div>
								<div class=""layui-btn layui-btn-primary layui-btn-xs layui-border-blue "" id=""btnFill"" style=""margin-right:10px;display:none""
                                    <i class=""layui-icon layui-icon-time""></i>  实时填写
                                </div>

								<input type=""checkbox"" name=""close"" lay-skin=""switch"" lay-text=""云知声|科大讯飞"" lay-filter=""switchTest"">
							</div>


						</div>
						<div class=""chat"">
							<div class=""chat-history"">
								<ul id=""ulList"" style=""height:100%;overflow-y:auto;"">
								</ul>
							</div> <!-- end chat-history -->
							<div class=""more_btn "">
								<div class=""nav_btn_item""> 更多>>></div>
							</div>
						</div>
						<div class=""right_bottom"">
							<ul class=""audio_lis");
                WriteLiteral(@"t"" id=""audioList"">
							</ul>
							<div class=""layui-row"" style=""display: flex; align-items: center;"">
								<div class=""layui-col-xs10 layui-col-sm10 layui-col-md10"">
									<div class=""history_wrap"" style=""align-items: center;"">
										<div class=""layui-input-inline"" style=""width:100%"">
											<textarea id=""result""");
                BeginWriteAttribute("message", " message=\"", 11316, "\"", 11326, 0);
                EndWriteAttribute();
                WriteLiteral(" class=\"layui-textarea\" placeholder=\"语音转文字区域\" autocomplete=\"off\" style=\"width:95%\"></textarea>\r\n\t\t\t\t\t\t\t\t\t\t\t<textarea id=\"result2\"");
                BeginWriteAttribute("message", " message=\"", 11456, "\"", 11466, 0);
                EndWriteAttribute();
                WriteLiteral(@" class=""layui-textarea"" placeholder=""语音转文字区域"" autocomplete=""off"" style=""width:95%;display:none;""></textarea>

										</div>
										<div class=""transfer_btn"" id=""btnSend"" style='display:none'>
											<i class=""layui-icon layui-icon-release""></i>
										</div>
									</div>
								</div>

								<div class=""layui-col-xs2 layui-col-sm2 layui-col-md2"" style=""text-align:right;margin-top:5px;"">
									<button type=""button"" id=""btnRecord"" class=""layui-btn layui-btn-sm translation_btn"">
										<i class=""layui-icon layui-icon-mike""></i>开始录音
									</button>
								</div>
							</div>

						</div>

					</div>
				</div>

				<!--提取报告区-->
				<div class=""layui-col-xs4 layui-col-md4"">

					<div class=""content_inner"">

						<div class=""layui-card-header row_wrap space-between"">
							<div");
                BeginWriteAttribute("class", " class=\"", 12311, "\"", 12319, 0);
                EndWriteAttribute();
                WriteLiteral(@">
								电子病历
							</div>
							<div class=""right_btn_group row_wrap"">
								<div style=""position:absolute;right:10px;"">
									<button type=""button"" class=""layui-btn layui-btn-primary layui-btn-radius  layui-btn-xs"" id=""btnUse""><i class=""layui-icon layui-icon-template-1 ""></i>一键复制</button>
								</div>
							</div>
						</div>
						<div class=""writ"" style=""height:90%"">

							<div");
                BeginWriteAttribute("class", " class=\"", 12729, "\"", 12737, 0);
                EndWriteAttribute();
                WriteLiteral(@" style=""height:100%;width:100%;"" id=""divMedical"">
								<div id=""txtMedical"" style=""height:100%;width:100%;resize:none;line-height:40px;font-size:20px; overflow:auto;""></div>
							</div>

						</div>

					</div>
				</div>

				<!--CRF表单区-->
				<div class=""layui-col-xs5 layui-col-md5  flex_one"">
					<div class=""content_inner "">
						<!--菜单折叠-->
						<button type=""button"" id=""toggleSidebar"" class=""layui-btn layui-btn-sm layui-btn-primary sider_btn"">
							<i class=""layui-icon layui-icon-left"" style=""font-size:22px;""></i>
						</button>

						<div class=""layui-card"" style=""height:90%"">
							<div class=""layui-card-header row_wrap space-between"">
								<div");
                BeginWriteAttribute("class", " class=\"", 13435, "\"", 13443, 0);
                EndWriteAttribute();
                WriteLiteral(@">
									CRF表单
								</div>
							</div>
							<div class=""writ"" style=""height:100%"">
								<div id=""crForm"" style=""height:100%"">
									<iframe id=""reportingFrom"" frameborder=""0"" width=""100%"" height=""100%""></iframe>
								</div>
							</div>


						</div>


					</div>
				</div>



				<!-- 左侧折叠菜单 -->
				<div id=""sideMenu"" class=""Menu_side content_L layui-card mui-table-view baseline"">
					<div class=""content_inner"" style=""padding:0;margin:0;"">
						<div class=""layui-collapse"" id=""OA_task_1"" style=""height:100%;"">
							<div class=""collapse_list""></div>
						</div>
					</div>
				</div>

			</div>

		</div>

	</div>


	

	<!--对话框start-->
	");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155745711", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155746833", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_17);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n");
                WriteLiteral("\t<!--对话框end-->\r\n\r\n\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155748018", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_18);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t<!--滑动删除插件-->\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f42e71b636a16ad091126df050bd9238a23448e45b2aa07bab115f37340c155749160", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_19);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n");
                WriteLiteral("<script>\r\n        var APPID = \"");
                Write(
#nullable restore
#line 657 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                      ViewBag.XuFeiAPPID

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\";\r\n        var API_KEY = \"");
                Write(
#nullable restore
#line 658 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                        ViewBag.XuFeiAPI_KEY

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\";\r\n\tvar setIntervalTime = \"");
                Write(
#nullable restore
#line 659 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                         ViewBag.setIntervalTime

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\";\r\n\t\tvar autoMedicalIntervalTime = \"");
                Write(
#nullable restore
#line 660 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                                  ViewBag.autoMedicalIntervalTime

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@""";
        const btnControl = document.getElementById(""btnRecord"");
        var audio = document.querySelector('audio');
        layui.use(['jquery', 'layer', 'form', 'element', 'code', 'laytpl', 'table', 'upload', 'carousel'], function() {
            var layer = layui.layer,
                $ = layui.$,
                form = layui.form,
                laytpl = layui.laytpl,
                element = layui.element,
                table = layui.table,
                upload = layui.upload,
                carousel = layui.carousel;
            var firstId = 0;
            var pageIndex = 1;
            var pageSize = 2;
            var loadIndex;
			var isSwiping = false;




            // 点击折叠按钮时触发的函数
            document.getElementById('toggleSidebar').addEventListener('click', function() {

                var sideMenu = document.getElementById('sideMenu');

                var spreadIcon = document.getElementById('toggleSidebar');

                // 切换folded类来实现折叠效果
     ");
                WriteLiteral(@"           sideMenu.classList.toggle('folded');

                // 根据菜单的状态切换图标显示
                if (sideMenu.classList.contains('folded')) {
                    spreadIcon.children[0].classList.remove('layui-icon-left'); // 显示展开图标
                    spreadIcon.children[0].classList.add('layui-icon-right'); // 隐藏展开图标
                } else {
                    spreadIcon.children[0].classList.remove('layui-icon-right'); // 显示展开图标
                    spreadIcon.children[0].classList.add('layui-icon-left'); // 隐藏展开图标
                }

                // 如果需要，可以通过Layui重新初始化相关元素，以保证样式和事件正常工作
                element.init();



            });
            var IsCheck = false;
            $(document).ready(function() {

                //查看更多 语音
                $("".nav_btn_item"").click(function () {

                    pageIndex++;
                    $.post(""/HospitalCRF/VoiceMedical/getMsgAndVoice"", {
                        RId: '");
                Write(
#nullable restore
#line 714 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                               Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"', CRFormId: id, pageIndex: pageIndex, pageSize: pageSize
                    },
                        function (res) {
                            if (res.code == 0) {
                                fillVoice(res.data);
                                setChatH();
                            }
                        });
                });

                //一键复制
                $(""#btnUse"").click(function () {
                    //var textBox = document.getElementById('txtMedical');
                    //textBox.select(); // 选择文本
                    //document.execCommand('copy'); // 执行复制命令
                    //textBox.blur();
                    //layer.msg('病历文书已复制');

                    // 创建一个临时的textarea来复制内容
                    var $temp = $(""<textarea>"");
                    $(""body"").append($temp);
                    $temp.val($(""#txtMedical"").text()).select();
                    try {
                        var successful = document.execCommand('copy');
             ");
                WriteLiteral(@"           var msg = successful ? '成功复制到剪贴板' : '复制失败';
                        layer.msg(msg);
                    } catch (err) {
                        layer.msg('无法复制，浏览器不支持');
                    }
                    // 移除临时textarea
                    $temp.remove();
                });

                // 使用原生JavaScript添加事件监听器
                var audios = document.querySelectorAll('#audioList audio');
                // 遍历所有音频元素并添加事件监听器
                audios.forEach(function(audio) {
                    audio.addEventListener('play', function(e) {

                        // 遍历所有音频元素
                        Array.from(audios).forEach(function(otherAudio) {
                            if (otherAudio !== e.target) {
                                console.log('暂停其他音频 (原生JS):', otherAudio.id);
                                otherAudio.pause();
                                otherAudio.currentTime = 0; // 可选：将时间重置为0
                            }
                        });
      ");
                WriteLiteral(@"              });

                });

                // 页面加载时检查一次
                setContentH();
                setWrit();
                setChatH();

                //切换语音 动态加载语音js
                loadJs(IsCheck);

            });

            function setContentH() {
                var winH = $(window).height(),
                    userInfoH = $("".user_info"").height();
                var conentH = winH - userInfoH - 40;
                $("".content_inner"").css(""height"", conentH + ""px"");
                return conentH;

            };

            function setWrit() {
                var conentH = setContentH();
                var thead = $("".layui-card-header"").height();

                var isshow = $("".writ_bottom"").css(""display"");
                var writBottom;
                var xx = 73;
                if (isshow == ""none"") {
                    writBottom = 0;
                    xx = 50;
                } else {
                    writBottom = $("".writ_bot");
                WriteLiteral(@"tom"").height();
                }



                console.log(""writBottom"", writBottom);
                var writH = conentH - thead - writBottom - xx;

                $("".writ"").css(""height"", writH + ""px"");
            };

            function setChatH() {
                var conentH = setContentH();
                var theadH = $("".layui-card-header"").height();
                var bottomH = $("".right_bottom"").height();
                var chatH = conentH - theadH - bottomH - 98;
                $("".chat-history"").css(""height"", chatH + ""px"");
            };



             function getFormLit() {
       var HospitalDeptId = '");
                Write(
#nullable restore
#line 817 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                              Model.HospitalDeptId

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"';
	var GroupName = $(""#GroupName"").val();

     $.get({
		 url: '/HospitalCRF/VoiceMedical/GetCRFList?HospitalDeptId=' + HospitalDeptId + '&GroupName=' + GroupName, // 你的请求URL
         async: false, // 设置为false以使请求同步执行
         success: function(res) {
             if (res.code == 0) {
                 var htm = """";
                 $("".layui-collapse"").html("""");
                 $.each(res.data, function(index, item) {

                     htm += `<div class=""layui-colla-item"">
                              <h2 class=""layui-colla-title"">`+ item.title + `</h2>
                                <div class=""layui-colla-content form_name layui-show"">
                                <ul class=""navMenu layui-card-body"" lay-filter=""navMenu"">`;
                     var childHtm = """";

                     $.each(item.children, function(index1, child) {
                         // console.log(index1);
                         if (index1 == 0 && index == 0) {
                             firstId =");
                WriteLiteral(@" child.id;
                         }
                         childHtm += `<li class=""nav_item mui-table-view-cell"" data-businessDomain=""` + child.businessDomain + `"" data-id=""` + child.id + `"" data-formId=""` + child.formId + `"" data-formName=""` + child.title + `"">
                                                                        <p class=""form_name"">`+ child.title + `</p>

                                                                     </li>`;
                     });

                     htm += childHtm + `</ul>
                                                             </div>
                                                         </div>`;

                 })

                 $("".layui-collapse"").append(htm);
                 element.render('collapse');
                 // 渲染进度条组件
                 element.render('progress');

                 //报表切换事件
                 $("".navMenu"").on(""click"", "".nav_item"", function() {
                     $("".nav_item"").removeClass(""");
                WriteLiteral(@"active"");
                     $("".tools_wrap"").css(""display"", ""none"");
                     $(this).addClass(""active"");
                     $(this).find("".tools_wrap"").css(""display"", ""flex"");
                     var randVersion = Math.random();

                     formId = $(this).attr(""data-formId"");
                     id = $(this).attr(""data-id"");
                     $(""#AIExtractJsonValue"").val("""");
                     var url = """);
                Write(
#nullable restore
#line 868 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                                  $"{ViewBag.formUrl}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\" + formId + \"");
                Write(
#nullable restore
#line 868 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                                                                       $".form?token={ViewBag.token}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"&version="" + randVersion;

                     $(""#reportingFrom"").attr(""src"", url);

                     if (isSwiping) {
                         document.getElementById('toggleSidebar').click();
                     }
                     var patientId = '");
                Write(
#nullable restore
#line 875 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                                       Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\';\r\n\t\t\t\t\t getMsgAndVoice();\r\n\t\t\t\t\t pageIndex = 1;\r\n\t\t\t\t\t pageSize = 2;\r\n                     var DelayTime = parseInt(\'");
                Write(
#nullable restore
#line 879 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                                                ViewBag.CRFDelayMinTime

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"', 10);
                     if (formId == ""13e1fa87369b49dfa61528cd24be0c54""
                         || formId == ""13e1fa87369b49dfa61528cd24be0c54""
                         || formId == ""9180f2c1a6af406091fa7623583a2805""
                         || formId == ""baba446e1bf2453c98d89c4c41202581"") {
                         DelayTime = parseInt('");
                Write(
#nullable restore
#line 884 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                                                ViewBag.CRFDelayMaxTime

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"', 10);
                     }
                     setTimeout(function() {
                         loadData(patientId, id);
                     }, DelayTime);
                 })

             }
         }
     });
 }

            getFormLit();

			//第一次默认选中第一个;
			// 选择data-id为的li元素
			var targetLi = $('li[data-id=""' + firstId + '""]');
			// 模拟点击事件
			targetLi.trigger('click');

            //加载表单数据
            function loadData(ResearchPatientId, formId) {
				var url = ""/HospitalCRF/VoiceMedical/LoadFormData?ResearchID="" + ResearchPatientId + ""&CRFormId="" + formId;
                $.get(url, function(res) {
                    var iframe = document.getElementById('reportingFrom');

                    if (res.code == 0) {
                        //  var iframe = document.getElementById('reportingFrom');
                        // 发送消息到子页面

                        iframe.contentWindow.postMessage({ action: ""show"", data: res.data }, ""*"");


                    }
         ");
                WriteLiteral(@"           else {
                        // 发送消息到子页面
                        iframe.contentWindow.postMessage({ action: ""show"" }, ""*"");
                    }
                    $(""#model_wrapR"").val(res.tips);

                })

            };

            // 父窗口
            window.addEventListener('message', function(event) {
                if (event.data.action === 'save') {
                    var ResearchPatientId = '");
                Write(
#nullable restore
#line 931 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                                              Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"'
                    var formData = event.data.data;
                    var AIExtractJsonValue = $(""#AIExtractJsonValue"").val();

                    var obj = {
						ResearchPatientId: ResearchPatientId, CRFormId: id, CRFJsonValue: formData,
						AIExtractJsonValue: AIExtractJsonValue, reportId: audioId
                    };
                    $.post('/HospitalCRF/VoiceMedical/SaveForm', obj, function(res) {
                        if (res.code == 0) {
                            getFormLit();
                            // 选择data-id为的li元素
                            var targetLi = $('div[data-id=""' + id + '""]');

                            // 模拟点击事件
                            targetLi.trigger('click');

                            layer.msg(""操作成功"");
                        }
                        else
                            layer.msg(""操作失败"");

                    })

                }

            }, false);



            //第一次默认选中第一个;
            // 选择data-id为");
                WriteLiteral(@"的li元素
            var targetLi = $('div[data-id=""' + firstId + '""]');
            // 模拟点击事件
            targetLi.trigger('click');
            var crformIds = """";

            var source;
            //病历文书转写
            function MedicalWrite(type) {
                var url = '/HospitalCRF/VoiceMedical/GetBLTextResult?ResearchID=");
                Write(
#nullable restore
#line 971 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                                                                                  Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"&crFormIds=' + crformIds;
                if (type == ""audio"")
                {
                    //之前是文件语音上传后再转写，现在不上传语音也转写，改成通过聊天文字生成
                    var json = $(""#result2"").val();

                   // url = '/HospitalCRF/VoiceMedical/GetBLTextResultByReportId?reportId=' + audioId ;
                    url = '/HospitalCRF/VoiceMedical/GetBLTextResultByReportId?reportId=0&words=' + encodeURIComponent(json);


                }
                if (source)
                {
                    source.close();
                    $(""#txtMedical"").html("""");
                }
                var i = 0;
                var allData = """";
                source = new EventSource(url);
                source.onmessage = function(event) {
                    var result = JSON.parse(event.data);
                    if (result.okMsg) {
                        allData += result.data;
                        var htmlContent = marked.parse(allData);
                        $(""#txtMedical"")");
                WriteLiteral(@".html(htmlContent);

                        var div = $('#txtMedical');
                        div.scrollTop(div[0].scrollHeight - div.innerHeight());

                    }
                    else {
                        layer.msg(result.errorMsg);
                        source.close();
                    }
                };

                source.addEventListener('end', function(event) {
                    var result = JSON.parse(event.data);

                    $(""#btnFill"").removeClass('disabled');
                    if (result.okMsg) {

						$(""#AIExtractJsonValue"").val(result.content);
                    }
                    else {
                        layer.msg(result.errorMsg);
                    }
                    source.close();
                }, false);

                source.onerror = function(event) {
                    $(""#btnFill"").removeClass('disabled');
                    source.close();
                };
            };

            ");
                WriteLiteral(@"//
            function AIExtractByAudio() {
                //之前是文件语音上传后再转写，现在不上传语音也转写，改成通过聊天文字生成
                var json = $(""#result2"").val();
                // url = '/HospitalCRF/VoiceMedical/GetBLTextResultByReportId?reportId=' + audioId ;
                loadIndex = layer.open({
                    type: 1,
                    area: ['600px', '49px'],
                    shade: 0.1,
                    closeBtn: 0,
                    resize: false,
                    title: false,
                    content: $(""#form_window_load""),
                    success: function () {
                        // $(""#model_wrapL"").val(modelText);
                        element.progress('demo-filter-progress', '0%');
                        load();
                    }
                });
                var patientId = '");
                Write(
#nullable restore
#line 1046 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                                  Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\';\r\n                $.post(\'/HospitalCRF/VoiceMedical/AIExtractByAudio\', { \"ResearchPatientId\": \'");
                Write(
#nullable restore
#line 1047 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                                                                                              Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"', ""CRFormId"": id, ""words"": json, ""tips"": $(""#model_wrapR"").val() }, function (res) {

                    element.progress('demo-filter-progress', '100%');
                    clearInterval(timer);
                    if (res.code == 0) {

                       // $(""#AIExtractJsonValue"").val(res.data);
                        $(""#reportId"").val(res.reportId);

                        var iframe = document.getElementById('reportingFrom');
                        // 发送消息到子页面
                        iframe.contentWindow.postMessage({ data: res.data }, ""*"");
                    }
                    else
                        layer.msg(res.msg);
                    layer.close(loadIndex);
                    element.progress('demo-filter-progress', '0%');
                })
            };

            function load() {
                var n = 0;
                timer = setInterval(function() {
                    n = n + Math.random() * 10 | 0;
                    if (n > 99) {
     ");
                WriteLiteral(@"                   n = 99;
                        clearInterval(timer);

                    }
                    element.progress('demo-filter-progress', n + '%');
                }, 300 + Math.random() * 4000);
            };

           // let timerId;
           // let wenshutimerId;
			let intervalIds = [];
			function Record()
			{
				btnControl.onclick = function ()
				{
					if (btnStatus === ""UNDEFINED"" || btnStatus === ""CLOSED"")
					{
						$(""#ulList"").html("""");
						if (IsCheck)
						{
							var sign = """";
							var timestamp = Math.floor(new Date().getTime() / 1000);
							$.ajax({
								url: ""/HospitalCRF/VoiceMedical/GetSHA256Hash?appkey="" + API_KEY + ""&timestamp="" + timestamp + ""&secret="" + APPID,
								type: ""get"",
								async: false,
								success: function (res)
								{
									console.log(res);
									sign = res;
								}
							});
							connectWebSocket(""result"", sign, timestamp);
						}
						else
							connectWebSocket(""resu");
                WriteLiteral(@"lt"");
						startSoundRecording();
						//定时把语音交给AI区分语音
						// timerId = setInterval(resultSplitByAI, parseInt(setIntervalTime) * 1000);
						intervalIds.push(setInterval(() =>
						{
							resultSplitByAI();
						}, parseInt(setIntervalTime) * 1000));

						//定时转写病历文书
						// wenshutimerId = setInterval(AutoMedicalWrite,30 * 1000);
						//  intervalIds.push(AutoMedicalWrite,30 * 1000);
						intervalIds.push(setInterval(() =>
						{
							AutoMedicalWrite();
						}, parseInt(autoMedicalIntervalTime) * 1000));

					}
					else if (btnStatus === ""CONNECTING"" || btnStatus === ""OPEN"")
					{
						// 结束录音
						//  clearInterval(timerId);
						//  clearInterval(wenshutimerId);
						intervalIds.forEach(id => clearInterval(id)); // 清除所有定时器
						if (source != undefined)
						{
							source.close();
						}
						RecordStop();
						SoundRecorder.stop();
						AutoMedicalWrite();
						// SendLastMsg();
						//大模型调用成功再上传
						var words = $(""#result"").val();
				");
                WriteLiteral(@"		if (words)
						{

							$.ajax({
								url: '/HospitalCRF/VoiceMedical/resultSplitByAI',
								type: 'POST',
								async: false, // 设置为同步请求
								data: { ""words"": $(""#result2"").val() },
								success: function (res)
								{
									// 请求成功后的回调函数
									if (res.code == 0)
									{
										$(""#ulList"").html("""");
										if (res.data)
										{
											$(""#result2"").attr(""data-AIResult"", res.data);
											var arr = res.data.split(""\n"");
											$.each(arr, function (index, item)
											{
												if (item.indexOf(""医生："") > -1)
													$(""#ulList"").append('<li class=""clearfix""> <div class=""message other-message float-right"">' + item + '</div></li>');
												else
													$(""#ulList"").append('<div class=""message my-message"">' + item + '</div>');
											});
										};
										uploadVoice();
										scrollToBottom();
									}
									else
										layer.msg(res.msg);

								},
								error: function (xhr, stat");
                WriteLiteral(@"us, error)
								{
									// 请求失败后的回调函数
								}
							});
							$(""#result"").val("""");
						}
						else
							uploadVoice();


						// SoundstopRecord(""Home/Upload"", ""Record.wav"")
					}
				};
			};

            Record();


            function uploadVoice() {
                var result = $(""#result2"").val();
                var AIResult = $(""#result2"").attr(""data-AIResult"");
                if (result) {


                    var formData = new FormData();

                    formData.append('file', SoundRecorder.getBlob());
                    formData.append('txt', $(""#result2"").val());
                    formData.append('AIResult', AIResult);
                    formData.append('RId', '");
                Write(
#nullable restore
#line 1204 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                                             Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"');
                    formData.append('CRFormId', id);
                    formData.append('pageSize', pageSize * pageIndex);
                    fetch(""/HospitalCRF/VoiceMedical/UploadAudio"", {
                        method: 'POST',
                        body: formData
                    }).then(response => response.json())
                        .then(data => {
                            //$(""#ulList"").html("""");
                            $(""#audioList"").html("""");
                           // fillMsg(data.data);
                            fillVoice(data.reportList);
                            audioId = data.reportId;
                            setChatH();
                            $(""#result"").val("""");
                            $(""#result2"").attr(""data-AIResult"", """");
                        }
                            )
                        .catch(error => console.error(error));


                }
                else {
                    layer.msg(""对话文字不能为空！""");
                WriteLiteral(");\r\n                    $(\"#result\").focus();\r\n                }\r\n\r\n\r\n            };\r\n\r\n\r\n            function getMsgAndVoice() {\r\n\r\n                $.post(\"/HospitalCRF/VoiceMedical/getMsgAndVoice\", {\r\n                    RId: \'");
                Write(
#nullable restore
#line 1238 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\VoiceMedical\Index.cshtml"
                           Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"', CRFormId: id, pageIndex:pageIndex,pageSize: pageSize
                },
                    function(res) {
                        if (res.code == 0) {
                            if (res.data.length > 0) {
                                fillMsg(res.data[0].reportContent);
                                fillVoice(res.data);

                                //切换表单时 默认选中第一个
                               $('#audioList li:first-child button[class*=""look""]').click();
                            }
                            setChatH();
                        }
                    });
            };

            function fillMsg(data)
            {
                // $(""#ulList"").html("""");
                // $(""#audioList"").html("""");
               // var arr = data.split(""\n"").filter(Boolean);
                if (data&&data!=""null"") {
                    var arr = data.split(""\n"");
                    $.each(arr, function (index, item) {
                        if (item.indexOf(""医生：");
                WriteLiteral(@""") > -1)
                            $(""#ulList"").append('<li class=""clearfix""> <div class=""message other-message float-right"">' + item + '</div></li>');
                        else
                            $(""#ulList"").append('<div class=""message my-message"">' + item + '</div>');
                    });
                    setChatH();
                    scrollToBottom();
                }



            };

            function fillVoice(list)
            {
                var audioHtm = """";
                $.each(list, function (index, item) {

                    audioHtm += ` <li >
                                    <div class=""audio_item"">
                                        <div class=""audio_title"">`+ item.createdTime + `</div>
                                          <audio id=""audio2"" controls>
                                              <source src=""`+ item.reportURL + `"" type=""audio/mpeg"" />设置不支持音频文件
                                           </audio>
           ");
                WriteLiteral(@"                        </div>
                                    <button type=""button"" class=""layui-btn layui-btn-sm layui-btn-primary look"" title=""查看"" data-id='` + item.id + `' data-msg='` + item.reportContent + `'>
                                          <i class=""layui-icon icon-shuyi_yanjing-xianshi""></i>
                                   </button>
                                  <button type=""button"" class=""layui-btn layui-btn-sm layui-btn-primary del"" title=""删除语音"" data-id='` + item.id + `'>
                                                  <i class=""layui-icon layui-icon-delete""></i>
                                  </button>
                                </li>`;
                })
                $(""#audioList"").append(audioHtm);


            };

            var audioId = 0;
            $(""#audioList"").on(""click"", ""button"", function (event) {
                event.stopPropagation();
                var aid = parseInt($(this).attr(""data-id""));
				if ($(this).hasClass('look'");
                WriteLiteral(@"))
				{
					audioId = aid;
                    $(""#audioList"").find(""li"").css(""background-color"", ""#fff"");
                    $(this).parent().css(""background-color"", ""#ddd"");
                    var msg = $(this).attr(""data-msg"");
                    $(""#ulList"").html("""");
                    fillMsg(msg);
                    $(""#result2"").val(msg);
                    //20250221 .当切换语音列表时，切换完毕后自动执行语音转写病历或crf表单填充功能；

                       if(source!=undefined){
                           source.close();
                       }

                        MedicalWrite(""audio"")

                        //根据语音填写表单
                        AIExtractByAudio();

                }
                else if ($(this).hasClass('del'))
                {
                    // 处理删除逻辑


                    layer.confirm('确定要删除此语音文件吗？', {
                        title: '',
                        btn: ['确定', '取消'], //按钮
                        resize: false
                    }, function(inde");
                WriteLiteral(@"x) {
                        $.post('/HospitalCRF/VoiceMedical/DelVoice', { ""Id"": aid }, function (res) {
                            if (res.code == 0) {
                                if (aid == audioId) {
                                    $(""#result2"").val("""");
                                }
                                $(""#ulList"").html("""");
                                $(""#audioList"").html("""");
                                getMsgAndVoice();
                                layer.msg(""删除成功！"");

                            }
                            else
                                layer.msg(res.msg);
                        })
                        layer.close(index);
                    });
                }
            });


            function getCurrentFormattedTime() {
                var now = new Date(); // 创建一个Date对象，表示当前时间
                var year = now.getFullYear(); // 获取当前年份
                var month = addZero(now.getMonth() + 1); // 获取当前月份，注意月份从");
                WriteLiteral(@"0开始计数
                var day = addZero(now.getDate()); // 获取当前日
                var hour = addZero(now.getHours()); // 获取当前小时
                var minute = addZero(now.getMinutes()); // 获取当前分钟
                var second = addZero(now.getSeconds()); // 获取当前秒

                // 返回格式化的字符串
                return year + '/' + month + '/' + day + ' ' + hour + ':' + minute + ':' + second;
            };

            // 辅助函数，确保时间部分是两位数字
            function addZero(i) {
                if (i < 10) {
                    return '0' + i;
                }
                return i;
            };

            //把最后的语音填充到对话框
            function SendLastMsg() {
                var res = $(""#result"").val();
                if (res) {
                    resultSplitByAI();
                    $(""#result"").val("""");
                }

            };


            //切换语音
            form.on('switch(switchTest)', function(data) {
                IsCheck = this.checked;
                if (btnSta");
                WriteLiteral(@"tus === ""CONNECTING"" || btnStatus === ""OPEN"")
                {
                  //  clearInterval(timerId);
                  //  clearInterval(wenshutimerId);
                   intervalIds.forEach(id => clearInterval(id)); // 清除所有定时器
                    RecordStop();
                    SoundRecorder.stop();
                }


                var existingScript = document.getElementById('yuyin');
                if (existingScript) {
                    document.head.removeChild(existingScript);
                };
                $.post('/HospitalCRF/VoiceMedical/GetKey', { 'isCheck': IsCheck }, function(res) {
                    API_KEY = res.appkey;
                    APPID = res.appsecret;
                });
                $(""#result"").val("""");
                $(""#result2"").val("""");
                $(""#ulList"").html("""");
                loadJs(this.checked);
              //  layer.close(loadindex);
            });


            function loadJs(IsCheck)
            {

");
                WriteLiteral(@"                var kdxf = ""/KeDaXunFei/rtasr/XunFeiRecord.js?v="" + Math.random();
                var yzs = ""/YunZhiSheng/rtasr/yunzhisheng.js?v="" + Math.random();
                var script = document.createElement('script');
                if (IsCheck)
                    script.src = yzs;
                else
                    script.src = kdxf;
                script.type = 'text/javascript';
                script.id = ""yuyin"";

                document.head.appendChild(script);
            };

            function splitStringBySpeakers(inputString) {
                // 修改后的正则表达式，匹配发言人1到100及其后的内容，并捕获发言人标记
                // 使用非贪婪匹配 .*? 确保每个发言人后面的内容只匹配到下一个发言人标签之前
                const regex = /(?:发言人\d{1,3}：)(.*?)(?=发言人\d{1,3}：|$)/g;
                let match;
                const parts = [];
                // 循环匹配正则表达式，直到没有更多匹配项
                while ((match = regex.exec(inputString)) !== null) {
                    // 将匹配到的发言人标记和内容作为一个数组元素
                    // 注意这里使用 match[");
                WriteLiteral(@"0] 而不是 match[1]，因为 match[0] 包含整个匹配的结果
                    parts.push(match[0]);
                }
                return parts;
            };

            function scrollToBottom() {
                var div = $('#ulList');
                div.scrollTop(div[0].scrollHeight - div.innerHeight());
            }

            //定时把文本框里面的值，丢给大模型处理再丢到对话框
            function resultSplitByAI()
            {
                var result2 = $(""#result2"").val();
                $(""#result"").val("""");
                $.post('/HospitalCRF/VoiceMedical/resultSplitByAI', { ""words"": result2 }, function(res) {
                   if(res.code==0)
                   {
                       $(""#ulList"").html("""");
                       if (res.data)
                       {
                           $(""#result2"").attr(""data-AIResult"", res.data);
                           var arr = res.data.split(""\n"");
                           $.each(arr, function (index, item) {
                                if (item");
                WriteLiteral(@".indexOf(""医生："") > -1)
                                    $(""#ulList"").append('<li class=""clearfix""> <div class=""message other-message float-right"">' + item + '</div></li>');
                                else
                                    $(""#ulList"").append('<div class=""message my-message"">' + item + '</div>');
                           });
                       }
                       scrollToBottom();
                   }
                })
            }

            // 20250217 增加语音定时自动转写病历文书  表单语音结束才自动填写
            function AutoMedicalWrite()
            {
                if (btnStatus === ""UNDEFINED"" || btnStatus === ""CLOSED"")
                {
                    //填充表单
                    AIExtractByAudio();
                }
                else
                {
                    //转写病历文书
					MedicalWrite(""audio"");
                }
               //  var btnId = $("".nav_btn_active"").attr(""id"");
                // if (btnId == ""recordsBtn"")
               //");
                WriteLiteral(@"  {
                        //MedicalWrite(""audio"");
                       // $('#baselineBtn').css('pointer-events', 'auto');
                  //  }
                   // else if (btnId == ""baselineBtn""&&(btnStatus === ""UNDEFINED"" || btnStatus === ""CLOSED"")) {
                      // AIExtractByAudio();
                   // }
            }

        });
</script>

");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n<div class=\"window_wrap\" id=\"form_window\" style=\"display: none;\">\r\n\t<form class=\"layui-form\" lay-filter=\"fm\" id=\"fm\"");
            BeginWriteAttribute("action", " action=\"", 46666, "\"", 46675, 0);
            EndWriteAttribute();
            WriteLiteral(@">

		<div class=""layui-form-item"" id=""div10"" style=""margin-top:15px;"">
			<label class=""layui-form-label"" style=""width:124px;"">上传附件：</label>
			<div class=""layui-input-block line_wrap"" style=""margin-left:130px;"">
				<div class=""layui-upload-drag"" id=""UploadFile"">
					<i class=""layui-icon""></i>
					<p>点击上传，或将文件拖拽到此处</p>
					<div class=""layui-hide"" id=""uploadDemoView"">
						<hr>
						<input type=""hidden"" id=""newFileName"" />
						<input type=""hidden"" id=""guid"" />
					</div>
				</div>
			</div>
		</div>
		<div class=""layui-form-item"">
			<label class=""layui-form-label"" style=""width:124px;"">文件名称：</label>
			<div class=""layui-input-block"" style=""width:300px;margin-left:130px;"">
				<input type=""text"" id=""fileName"" class=""layui-input"" name=""fileName"" />

			</div>
		</div>
		<div class=""layui-form-item"">
			<div class=""layui-input-block"" style=""margin-left:130px;"">
				<button type=""submit"" class=""layui-btn"" lay-submit lay-filter=""submit"">提交</button>
				<button type=""reset"" class");
            WriteLiteral(@"=""layui-btn layui-btn-primary"" id=""btn_reset"">重置</button>
			</div>
		</div>
	</form>
</div>

<div class=""window_wrap"" id=""form_window_Img"" style=""display: none;"">
	<form class=""layui-form"">

		<div class=""layui-form-item"">
			<div id=""fileList"">
			</div>
		</div>

	</form>
</div>

<div class=""window_wrap"" id=""form_window_load"" style=""display: none;background-color:transparent"">
	<div class=""layui-progress layui-progress-big"" lay-showPercent=""true"" lay-filter=""demo-filter-progress"">
		<div class=""layui-progress-bar"" lay-percent=""0%"">
		</div>

	</div>
	<p style=""text-align:center""> AI数据提取中...</p>
</div>
</html>
");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<AngelwinResearch.Models.ResearchPatient> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
