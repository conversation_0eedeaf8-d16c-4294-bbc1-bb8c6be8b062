#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\vform\soundAnalysis.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "f96838524d21445bb9b219c00cc4fdbf6cd1f47bb5e7bc26a406d9edc95783e8"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_vform_soundAnalysis), @"mvc.1.0.view", @"/Views/vform/soundAnalysis.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI

#nullable disable
    ;
#nullable restore
#line 2 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"f96838524d21445bb9b219c00cc4fdbf6cd1f47bb5e7bc26a406d9edc95783e8", @"/Views/vform/soundAnalysis.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"a5bcd83cf27c8ffb8baf597d24314352bf7b52b5a9bb5ee83e83e9d0089a628e", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_vform_soundAnalysis : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css?v1.0"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/vform/index1.min.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/vform/VFormDesigner.css?t=20210730"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("audio"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/mp3/Consultation.m4a"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/vform/vue.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/vform/element-ui_2.15.7.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/vform/VFormDesigner.umd.min.js?t=20210730"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\vform\soundAnalysis.cshtml"
  
    ViewBag.Title = "科研AIDemo";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"en\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f96838524d21445bb9b219c00cc4fdbf6cd1f47bb5e7bc26a406d9edc95783e88287", async() => {
                WriteLiteral(@"
    <meta charset=""UTF-8"">
    <meta name=""viewport""
          content=""width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"">
    <meta http-equiv=""X-UA-Compatible"" content=""ie=edge"">
    <title>科研AIDemo</title>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "f96838524d21445bb9b219c00cc4fdbf6cd1f47bb5e7bc26a406d9edc95783e88841", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "f96838524d21445bb9b219c00cc4fdbf6cd1f47bb5e7bc26a406d9edc95783e810043", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "f96838524d21445bb9b219c00cc4fdbf6cd1f47bb5e7bc26a406d9edc95783e811246", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "f96838524d21445bb9b219c00cc4fdbf6cd1f47bb5e7bc26a406d9edc95783e812450", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@" <!-- 根据Web服务器或CDN路径修改 -->
    <style>
        .audio-player {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .audio-player .audio-box {
            background-color: #d0f3c7;
            padding: 5px;
            border-radius: 4px;
            display: inline-flex;
            align-items: center;
            width: 500px;
        }

        .audio-player .audio-btn {
            width: 20px;
            height: 20px;
            background-color: #fff;
            border: 1px solid #ccc;
            border-radius: 50%;
            margin-left: 10px;
            position: relative;
            cursor: pointer;
        }

        .audio-player .audio-btn::after {
            content: """";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 6px;
            height: 10px;
            background-color: #000;
            bor");
                WriteLiteral(@"der-radius: 2px;
        }

        .audio-player .audio-btn.playing::after {
            width: 10px;
            height: 10px;
            background-color: #fff;
            border-radius: 50%;
        }

        .audio-player .duration {
            font-size: 14px;
            color: #999;
            margin-left: 10px;
        }

        .line_wrap{
            display:flex;
            flex-direction:row;
            align-items:center;
        }
        .line_between {
            justify-content: space-between
        }
    </style>

");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f96838524d21445bb9b219c00cc4fdbf6cd1f47bb5e7bc26a406d9edc95783e815996", async() => {
                WriteLiteral(@"
    <div class=""layui-col-md12"">
        <div class=""layui-card"">
            <div class=""audio-player"">
                <div class=""line_wrap line_between"" style=""width:98%;"">
                    <div class=""line_wrap"">
                        <label class=""el-form-item__label"" style=""width: 150px; line-height: 60px; font-size: 20px;"">随访录音：</label>
                        <div class=""audio-box"" style=""justify-content: space-between;"">
                            <div class=""duration"">00:00</div>
                            <div class=""audio-btn""></div>
                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("audio", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f96838524d21445bb9b219c00cc4fdbf6cd1f47bb5e7bc26a406d9edc95783e816899", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
                            <div class=""total-duration"">00:00</div>
                        </div>
                        <div class=""layui-input-block"">
                            <button class=""layui-btn"" id=""submitBtn"" style=""display:none;"">信息提取</button>
                            <button class=""layui-btn"" id=""reset"">重写</button>
                        </div>
                    </div>

                    <button id=""popbtn"" type=""button"" class=""layui-btn layui-bg-blue"">
                        <i class=""layui-icon layui-icon-survey""></i>
                    </button>
                </div>
            </div>


            <div class=""layui-card-body"">
                <div class=""layui-row"" style=""height: 85vh;"">
                    <div style=""font-size: 25px;"">
                        <pre style=""white-space: pre; text-align: left;"">随访回填表单：</pre>
                    </div>
                    <div style=""background-color: #fff;height: 91%; padding:15px 15px;"">
                 ");
                WriteLiteral(@"       <textarea id=""btnloading"" class=""layui-textarea"" style="" height:100%;width:100%;line-height:40px;font-size:20px;resize:none""></textarea>
                    </div>
                </div>

            </div>
        </div>


        <div id=""popwrap"" style=""display:none;height:100%;overflow:hidden;"">
            <div class=""layui-row layui-col-space30"" style=""height:100%"">
                <div class=""layui-col-md6"" style=""height:100%"">
                    <div style=""background-color: #fff;height: 100%; padding:15px 15px;"">
                        <textarea id=""model_wrapR"" class=""layui-textarea"" style="" height:100%;width:100%;line-height:40px;font-size:20px;resize:none"">
录音转文本信息：
请问你的姓名？
我叫马海霞。
您好，请问您最近的身体状况怎么样？有没有出现新的症状或不适？
我感觉整体还不错，但最近有时会感到轻微的头痛。
我明白了，您是否有按时服用我们上次开的药物？有没有出现任何不良反应或副作用？
是的，我都按时服药。目前没有发现什么副作用。
很好。请问您的睡眠和饮食习惯有没有什么改变？睡眠充足吗？
睡眠还可以，但由于工作压力，有时会感到焦虑，影响睡眠。
</textarea>
                    </div>
                </div>
                <div class=""layui-col-md6"" style=""he");
                WriteLiteral(@"ight:100%"">
                    <div style=""background-color: #fff;height: 100%; padding:15px 15px;"">
                        <textarea id=""model_wrapL"" class=""layui-textarea"" style="" height:100%;width:100%;line-height:40px;font-size:20px;resize:none""></textarea>
                    </div>

                </div>
            </div>


        </div>
        <script type=""text/javascript"">
            if (!!window.ActiveXObject || ""ActiveXObject"" in window) { //IE load polyfill.js for Promise
                var scriptEle = document.createElement(""script"");
                scriptEle.type = ""text/javascript"";
                scriptEle.src = ""/vform/6.23.0_polyfill.min.js""
                document.body.appendChild(scriptEle)
            }
        </script>
        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f96838524d21445bb9b219c00cc4fdbf6cd1f47bb5e7bc26a406d9edc95783e821093", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f96838524d21445bb9b219c00cc4fdbf6cd1f47bb5e7bc26a406d9edc95783e822221", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f96838524d21445bb9b219c00cc4fdbf6cd1f47bb5e7bc26a406d9edc95783e823349", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(" <!-- 根据Web服务器或CDN路径修改 -->\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "f96838524d21445bb9b219c00cc4fdbf6cd1f47bb5e7bc26a406d9edc95783e824503", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"


        <script>
            layui.config({
                base: '/layuiadmin/layuiextend/'
            }).use(['layer', 'laydate', 'table', 'form'], function () {
                var layer = layui.layer
                    , table = layui.table//表格
                    , laydate = layui.laydate
                    , $ = layui.$
                    , form = layui.form;



                var source;
                var modelText = """";
                getModelText();
                $(""#popbtn"").on(""click"", function () {
                    layer.open({
                        type: 1,
                        area: ['60%', '80%'],
                        resize: false,
                        shadeClose: true,
                        title: '帮助',
                        content: $(""#popwrap""),
                        success: function () {
                            $(""#model_wrapL"").val(modelText);
                        }
                    })
                })



   ");
                WriteLiteral(@"             // 播放按钮点击事件处理函数
                function toggleAudio() {
                    if (audio.paused) {
                        audio.play();
                        audioBtn.classList.add('playing');
                    } else {
                        audio.pause();
                        audioBtn.classList.remove('playing');
                    }
                }

                // 获取音频元素
                var audio = document.getElementById('audio');
                var durationDisplay = document.querySelector('.duration');
                var totalDurationDisplay = document.querySelector('.total-duration');

                // 播放按钮点击事件处理函数
                var k = 0;
                function toggleAudio() {
                    if (audio.paused) {
                        audio.play();
                        audioBtn.classList.add('playing');

                        if (k == 0) {
                            if (source) {
                                source.close();
    ");
                WriteLiteral(@"                        }
                            submitBtn(modelText);
                            var _isDis = $(this).hasClass(""layui-btn-disabled"")
                            if (_isDis) {
                                return false;
                            } else {
                                $(this).addClass(""layui-btn-disabled"")
                            }
                        }
                        k = k + 1;
                        
                    } else {
                        audio.pause();
                        audioBtn.classList.remove('playing');
                    }
                }

                // 格式化时间显示
                function formatTime(time) {
                    var minutes = Math.floor(time / 60);
                    var seconds = Math.floor(time % 60);
                    return ('0' + minutes).slice(-2) + ':' + ('0' + seconds).slice(-2);
                }

                // 音频元素加载完成事件处理函数
                function handleLoa");
                WriteLiteral(@"dedMetadata() {
                    var duration = audio.duration;
                    //durationDisplay.textContent = formatTime(duration);
                    totalDurationDisplay.textContent = formatTime(duration);
                }

                // 音频元素时间更新事件处理函数
                function handleTimeUpdate() {
                    var currentTime = audio.currentTime;
                    durationDisplay.textContent = formatTime(currentTime);
                }

                // 获取播放按钮元素
                var audioBtn = document.querySelector('.audio-btn');

                // 绑定播放按钮点击事件
                audioBtn.addEventListener('click', toggleAudio);

                // 监听音频元素的加载完成事件
                audio.addEventListener('loadedmetadata', handleLoadedMetadata);

                // 监听音频元素的时间更新事件
                audio.addEventListener('timeupdate', handleTimeUpdate);

                // 监听音频元素的加载完成事件
                audio.addEventListener('loadedmetadata', handleLoadedMetadata);

 ");
                WriteLiteral(@"               // 页面加载时获取音频时长并更新显示
                handleLoadedMetadata();

                //获取文本信息
                function getModelText() {
                    var fileURL = '/model/FollowUpBackfillInfo.txt';
                    // 创建一个XMLHttpRequest对象
                    var xhr = new XMLHttpRequest();
                    // 发送GET请求以获取文件内容
                    xhr.open('GET', fileURL, true);
                    // 设置响应类型为文本
                    xhr.responseType = 'text';
                    // 当请求完成时执行的回调函数
                    xhr.onload = function () {
                        if (xhr.status === 200) {
                            modelText = xhr.responseText;
                            console.log('文件内容已存储到modelText变量中：', modelText);
                        } else {
                            console.error('无法获取文件内容：', xhr.statusText);
                        }
                    };
                    // 发送请求
                    xhr.send();
                }

                $(d");
                WriteLiteral(@"ocument).ready(function () {
                    $(document).on('click', '#submitBtn', function () {
                        if (source) {
                            source.close();
                        }
                        submitBtn(modelText);

                        var _isDis = $(this).hasClass(""layui-btn-disabled"")
                        if (_isDis) {
                            return false;
                        } else {
                            $(this).addClass(""layui-btn-disabled"")
                        }
                    });

                    $(document).on('click', '#reset', function () {
                        if (source) {
                            source.close();
                        }
                        submitBtn(modelText);

                    });
                });

                function submitBtn(modelText) {
                    var currentDate = new Date();
                    var year = currentDate.getFullYear();
          ");
                WriteLiteral(@"          var month = currentDate.getMonth() + 1;
                    var day = currentDate.getDate();

                    var variable = $(""#model_wrapR"").val();
                    var prompt = "" 录音文本：“"" + variable + ""”，表单模板：“"" + modelText + ""”。你好，根据录音文本,回答表单模板中的问题，将问题答案填写到问题下面的横线上。"";
                    if (prompt == """") {
                        layer.msg(""请输入报告描述"");
                        $(""#message-to-send"").focus();
                        return;
                    }
                    var i = 0;
                    source = new EventSource('/vform/GetChatStreamAnswer?&prompt=' + encodeURIComponent(prompt));

                    source.onmessage = function (event) {
                        var result = JSON.parse(event.data);
                        if (result.okMsg) {
                            var btnloading = document.getElementById(""btnloading"");
                            if (i == 0) {
                                var Info = result.data;
                              ");
                WriteLiteral(@"  btnloading.value = Info;
                            }
                            else {
                                var Info = result.data;
                                btnloading.value += Info;
                            }
                            i = i + 1;
                        }
                        else {
                            layer.msg(result.errorMsg);
                            source.close();
                        }
                    };

                    source.addEventListener('end', function (event) {
                        $(""#submitBtn"").removeClass(""layui-btn-disabled"");
                        var result = JSON.parse(event.data);
                        if (result.okMsg) {
                            layui.each(result.newContent, function (idx, item) {

                            });
                        }
                        else {
                            layer.msg(result.errorMsg);
                        }

            ");
                WriteLiteral(@"            // 结束事件源连接
                        source.close();
                    }, false);

                    source.onerror = function (event) {
                        source.close();
                    };
                }
                //固定列表高度
                function tableheight() {
                    var winH = $(window).height();
                    var serH = $("".ser_div"").outerHeight(true);
                    $("".layui-table-main"").css(""height"", (winH - serH - 180) + ""px"");
                }


            });
        </script>
    </div>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
