﻿++解决方案 'AngelwinResearch' ‎ (4 个项目，共 4 个)
i:{00000000-0000-0000-0000-000000000000}:AngelwinResearch.sln
++AngelwinResearch.Library
i:{00000000-0000-0000-0000-000000000000}:AngelwinResearch.Library
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:>10632
i:{************************************}:>10584
++依赖项
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:>5466
i:{d141c258-72b3-42a1-82f2-92e4f060ab69}:>5467
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:>5465
i:{************************************}:>10582
++AngelwinResearch.Filters
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.filters\
++AngelwinResearch.ModelExtends
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.modelextends\
++AngelwinResearch.Models
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\
++AngelwinResearch.Services
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.services\
++数据库调整
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\
++Class1.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\class1.cs
i:{d141c258-72b3-42a1-82f2-92e4f060ab69}:d:\work space\project\三部\科研ai demo\common.tools\class1.cs
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:d:\work space\project\三部\科研ai demo\common.quartznet\class1.cs
++包
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:>10603
i:{d141c258-72b3-42a1-82f2-92e4f060ab69}:>10651
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:>10643
i:{************************************}:>10610
++分析器
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:>10589
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:>10634
i:{************************************}:>10587
++框架
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:>10600
i:{d141c258-72b3-42a1-82f2-92e4f060ab69}:>10649
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:>10641
i:{************************************}:>10604
++LoggingAttribute.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.filters\loggingattribute.cs
++AppSettings.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.modelextends\appsettings.cs
++DataDictMenus.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.modelextends\datadictmenus.cs
++UserMenuList.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.modelextends\usermenulist.cs
++AgentInfo.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\agentinfo.cs
++AngelwinResearchDbContext.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\angelwinresearchdbcontext.cs
++ChatMessage.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\chatmessage.cs
++ChatTheme.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\chattheme.cs
++Classification.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\classification.cs
++CMISCRFData.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\cmiscrfdata.cs
++CMISCRFSetting.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\cmiscrfsetting.cs
++CRFData.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\crfdata.cs
++CRForm.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\crform.cs
++CRFormFieldSet.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\crformfieldset.cs
++DataDict.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\datadict.cs
++DiseaseSpecificGroup.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\diseasespecificgroup.cs
++ExternalReport.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\externalreport.cs
++FollowupPlan.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\followupplan.cs
++FollowupRecord.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\followuprecord.cs
++FollowupRecordDetail.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\followuprecorddetail.cs
++FollowupTemplate.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\followuptemplate.cs
++FollowupTemplateDetail.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\followuptemplatedetail.cs
++HospitalCRFData.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\hospitalcrfdata.cs
++HospitalCRForm.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\hospitalcrform.cs
++HospitalDept.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\hospitaldept.cs
++InspectionItemDetails.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\inspectionitemdetails.cs
++JianChaDTO.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\jianchadto.cs
++Log.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\log.cs
++LoginLog.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\loginlog.cs
++MedicalDocument.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\medicaldocument.cs
++Menu.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\menu.cs
++MultiCenter.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\multicenter.cs
++OperationHistory.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\operationhistory.cs
++PatientFormHide.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\patientformhide.cs
++PdfInfo.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\pdfinfo.cs
++PromptInfo.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\promptinfo.cs
++ResearchPatient.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\researchpatient.cs
++RoleInfo.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\roleinfo.cs
++RoleMenu.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\rolemenu.cs
++TaskAutoCollect.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\taskautocollect.cs
++TaskAutoCollectCRF.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\taskautocollectcrf.cs
++TaskCMISCRFData.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\taskcmiscrfdata.cs
++TaskInfo.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\taskinfo.cs
++UserInfo.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\userinfo.cs
++UserZBZSetting.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\userzbzsetting.cs
++WebConfig.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\webconfig.cs
++Wiki.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\wiki.cs
++WikiDetail.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\wikidetail.cs
++WikiDetailSplit.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\wikidetailsplit.cs
++YY_KSBMK.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.models\yy_ksbmk.cs
++CommonFunction.cs
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\angelwinresearch.services\commonfunction.cs
++2024070301.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2024070301.txt
++2024071501.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2024071501.txt
++2024073101.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2024073101.txt
++2024081901.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2024081901.txt
++2024090301.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2024090301.txt
++2024101501.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2024101501.txt
++2024102901.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2024102901.txt
++2024103001.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2024103001.txt
++2024110101.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2024110101.txt
++2024121801.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2024121801.txt
++2025031001.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2025031001.txt
++2025031301.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2025031301.txt
++2025031401.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2025031401.txt
++2025040201.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2025040201.txt
++2025041601.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2025041601.txt
++2025042701.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2025042701.txt
++2025050801.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2025050801.txt
++2025051401.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2025051401.txt
++2025052001.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2025052001.txt
++2025052201.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2025052201.txt
++2025060301.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2025060301.txt
++2025062001.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\2025062001.txt
++20250702001.txt
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:d:\work space\project\三部\科研ai demo\angelwinresearch.library\数据库调整\20250702001.txt
++Microsoft.AspNetCore.Http.Abstractions (2.2.0)
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:>10616
++Microsoft.AspNetCore.Http.Features (3.1.10)
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:>10606
++Microsoft.AspNetCore.Identity.EntityFrameworkCore (3.1.10)
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:>10612
++Microsoft.AspNetCore.Mvc (2.2.0)
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:>10614
++Microsoft.EntityFrameworkCore (3.1.10)
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:>10608
i:{************************************}:>10613
++Microsoft.EntityFrameworkCore.SqlServer (3.1.10)
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:>10609
i:{************************************}:>10617
++Microsoft.AspNetCore.Mvc.Analyzers
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:c:\users\<USER>\.nuget\packages\microsoft.aspnetcore.mvc.analyzers\2.2.0\analyzers\dotnet\cs\microsoft.aspnetcore.mvc.analyzers.dll
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:c:\users\<USER>\.nuget\packages\microsoft.aspnetcore.mvc.analyzers\2.2.0\analyzers\dotnet\cs\microsoft.aspnetcore.mvc.analyzers.dll
i:{************************************}:c:\program files\dotnet\sdk\9.0.200\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.mvc.analyzers.dll
++Microsoft.CodeAnalysis.Analyzers
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:c:\program files\dotnet\sdk\nugetfallbackfolder\microsoft.codeanalysis.analyzers\1.1.0\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:c:\program files\dotnet\sdk\nugetfallbackfolder\microsoft.codeanalysis.analyzers\1.1.0\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
i:{************************************}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\2.9.4\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
++Microsoft.CodeAnalysis.CSharp.Analyzers
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:c:\program files\dotnet\sdk\nugetfallbackfolder\microsoft.codeanalysis.analyzers\1.1.0\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:c:\program files\dotnet\sdk\nugetfallbackfolder\microsoft.codeanalysis.analyzers\1.1.0\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
i:{************************************}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\2.9.4\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
++Microsoft.EntityFrameworkCore.Analyzers
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\3.1.10\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\3.1.10\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{************************************}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\3.1.10\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
++Microsoft.NETCore.App
i:{6a9cd88b-a95a-455d-84e2-49559f1ca78d}:>10602
i:{d141c258-72b3-42a1-82f2-92e4f060ab69}:>10650
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:>10642
i:{************************************}:>10607
++Common.Tools
i:{00000000-0000-0000-0000-000000000000}:Common.Tools
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:>10633
i:{************************************}:>10586
++Logs
i:{d141c258-72b3-42a1-82f2-92e4f060ab69}:d:\work space\project\三部\科研ai demo\common.tools\logs\
++Utility
i:{d141c258-72b3-42a1-82f2-92e4f060ab69}:d:\work space\project\三部\科研ai demo\common.tools\utility\
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:d:\work space\project\三部\科研ai demo\common.quartznet\utility\
++LoggerHelper.cs
i:{d141c258-72b3-42a1-82f2-92e4f060ab69}:d:\work space\project\三部\科研ai demo\common.tools\logs\loggerhelper.cs
++ApiHelper.cs
i:{d141c258-72b3-42a1-82f2-92e4f060ab69}:d:\work space\project\三部\科研ai demo\common.tools\utility\apihelper.cs
++AuthenticatorService.cs
i:{d141c258-72b3-42a1-82f2-92e4f060ab69}:d:\work space\project\三部\科研ai demo\common.tools\utility\authenticatorservice.cs
++Common.cs
i:{d141c258-72b3-42a1-82f2-92e4f060ab69}:d:\work space\project\三部\科研ai demo\common.tools\utility\common.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\unity\common.cs
++DataFormat.cs
i:{d141c258-72b3-42a1-82f2-92e4f060ab69}:d:\work space\project\三部\科研ai demo\common.tools\utility\dataformat.cs
++JsonConverter.Newtonsoft.Json (0.7.0)
i:{d141c258-72b3-42a1-82f2-92e4f060ab69}:>10656
++Microsoft.Extensions.Configuration.Abstractions (3.1.10)
i:{d141c258-72b3-42a1-82f2-92e4f060ab69}:>10653
++Microsoft.Extensions.Configuration.Json (3.1.10)
i:{d141c258-72b3-42a1-82f2-92e4f060ab69}:>10655
++Microsoft.Extensions.Configuration.Xml (3.1.10)
i:{d141c258-72b3-42a1-82f2-92e4f060ab69}:>10654
++Microsoft.Extensions.Logging.Log4Net.AspNetCore (3.1.5)
i:{d141c258-72b3-42a1-82f2-92e4f060ab69}:>10657
i:{************************************}:>10623
++Otp.NET (1.3.0)
i:{d141c258-72b3-42a1-82f2-92e4f060ab69}:>10658
++QRCoder (1.4.1)
i:{d141c258-72b3-42a1-82f2-92e4f060ab69}:>10652
++Common.QuartzNet
i:{00000000-0000-0000-0000-000000000000}:Common.QuartzNet
i:{************************************}:>10585
++Enum
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:d:\work space\project\三部\科研ai demo\common.quartznet\enum\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\enum\
++Extensions
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:d:\work space\project\三部\科研ai demo\common.quartznet\extensions\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\extensions\
++Models
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:d:\work space\project\三部\科研ai demo\common.quartznet\models\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\models\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\models\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\reportingmanage\models\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\models\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\cmis\models\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\taskmanage\models\
++项目
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:>10631
i:{************************************}:>10583
++JobAction.cs
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:d:\work space\project\三部\科研ai demo\common.quartznet\enum\jobaction.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\enum\jobaction.cs
++QuartzNETExtension.cs
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:d:\work space\project\三部\科研ai demo\common.quartznet\extensions\quartznetextension.cs
++StaticHttpContextExtensions.cs
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:d:\work space\project\三部\科研ai demo\common.quartznet\extensions\statichttpcontextextensions.cs
++BusinessDTO.cs
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:d:\work space\project\三部\科研ai demo\common.quartznet\models\businessdto.cs
++JobDTO.cs
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:d:\work space\project\三部\科研ai demo\common.quartznet\models\jobdto.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\models\jobdto.cs
++AutoFillInJob.cs
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:d:\work space\project\三部\科研ai demo\common.quartznet\utility\autofillinjob.cs
++CMISStructuredextractJob.cs
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:d:\work space\project\三部\科研ai demo\common.quartznet\utility\cmisstructuredextractjob.cs
++HttpContext.cs
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:d:\work space\project\三部\科研ai demo\common.quartznet\utility\httpcontext.cs
++IOCJobFactory.cs
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:d:\work space\project\三部\科研ai demo\common.quartznet\utility\iocjobfactory.cs
++LimitedConcurrencyLevelTaskScheduler.cs
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:d:\work space\project\三部\科研ai demo\common.quartznet\utility\limitedconcurrencyleveltaskscheduler.cs
++TestJob.cs
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:d:\work space\project\三部\科研ai demo\common.quartznet\utility\testjob.cs
++WikiJob.cs
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:d:\work space\project\三部\科研ai demo\common.quartznet\utility\wikijob.cs
++Common.DataSourceSupport (1.1.35)
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:>10646
++Microsoft.Extensions.Hosting.Abstractions (3.1.10)
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:>10648
++Microsoft.Extensions.Http (3.1.10)
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:>10645
++Microsoft.SemanticKernel.Core (1.6.2)
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:>10644
i:{************************************}:>10620
++Quartz (3.3.0)
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:>10647
++Microsoft.Extensions.Logging.Generators
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\8.0.0\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{************************************}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\8.0.0\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
++System.Text.Json.SourceGeneration
i:{f86cc962-e701-43d5-8f8b-38138fbabd1a}:c:\users\<USER>\.nuget\packages\system.text.json\8.0.2\analyzers\dotnet\roslyn4.4\cs\system.text.json.sourcegeneration.dll
i:{************************************}:c:\users\<USER>\.nuget\packages\system.text.json\8.0.2\analyzers\dotnet\roslyn4.4\cs\system.text.json.sourcegeneration.dll
++AngelwinResearch.WebUI
i:{00000000-0000-0000-0000-000000000000}:AngelwinResearch.WebUI
++Connected Services 
i:{************************************}:>10580
++Properties
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\properties\
++wwwroot
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\
++css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\css\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\css\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\audio\css\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\audioypbf\css\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-steps\css\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\limarquee\css\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\muihk\css\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\mui-player\css\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\css\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\css\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\swiper\dist\css\
++DIPdfinfo
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\dipdfinfo\
++echarts
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\echarts\
++ExternalReports
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\
++files
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\files\
++images
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-steps\css\images\
++js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\js\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\audioypbf\js\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-steps\js\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\limarquee\js\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\muihk\js\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\mui-player\js\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\js\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\swiper\dist\js\
++json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\json\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\
++KeDaXunFei
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\
++dist
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\dist\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts\dist\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts-demo\dist\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\yunzhisheng\dist\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery\dist\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-validation\dist\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\swiper\dist\
++rtasr
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\rtasr\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\yunzhisheng\rtasr\
++tts
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts-demo\example\tts\
++tts.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts\tts.js
++ttsModule.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts\ttsmodule.js
++xunfei_tts.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts\xunfei_tts.js
++tts-demo
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts-demo\
++base64.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\base64.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts-demo\example\base64.js
++crypto-js.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\crypto-js.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\yunzhisheng\crypto-js.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts-demo\example\crypto-js.js
++enc-base64-min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\enc-base64-min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\yunzhisheng\enc-base64-min.js
++fast-xml-parser.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\fast-xml-parser.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\yunzhisheng\fast-xml-parser.min.js
++HmacSHA1.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\hmacsha1.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\yunzhisheng\hmacsha1.js
++hmac-sha256.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\hmac-sha256.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\yunzhisheng\hmac-sha256.js
++HZRecorder.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\hzrecorder.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\yunzhisheng\hzrecorder.js
++md5.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\md5.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\yunzhisheng\md5.js
++layuiadmin
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\
++lib
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\lib\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\lib\
++MedicalDoc
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\
++model
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\model\
++mp3
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\mp3\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\audioypbf\mp3\
++pdfinfo
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\
++querybuilder
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\
++test
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\test\
++vform
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\vform\
++YunZhiSheng
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\yunzhisheng\
++favicon.ico
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\favicon.ico
++Areas
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\
++BasicConfig
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\
++Controllers
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\controllers\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\controllers\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\reportingmanage\controllers\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\controllers\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\chat\controllers\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\cmis\controllers\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\controllers\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\followup\controllers\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\report\controllers\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\taskmanage\controllers\
++AgentInfoController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\controllers\agentinfocontroller.cs
++ClinicDeptManageController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\controllers\clinicdeptmanagecontroller.cs
++CRFormFieldSettingController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\controllers\crformfieldsettingcontroller.cs
++CRFormFieldsImportController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\controllers\crformfieldsimportcontroller.cs
++CRFormsManageController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\controllers\crformsmanagecontroller.cs
++CRFormsPadController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\controllers\crformspadcontroller.cs
++CRFormXmlImportController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\controllers\crformxmlimportcontroller.cs
++DSpecificGroupManageController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\controllers\dspecificgroupmanagecontroller.cs
++HospitalDeptsController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\controllers\hospitaldeptscontroller.cs
++MedicalRecordUploadController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\controllers\medicalrecorduploadcontroller.cs
++MultiCentersManageController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\controllers\multicentersmanagecontroller.cs
++OrgManageController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\controllers\orgmanagecontroller.cs
++PromptInfoController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\controllers\promptinfocontroller.cs
++Data
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\data\
++DTO.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\models\dto.cs
++Views
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\views\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\reportingmanage\views\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\chat\views\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\cmis\views\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\followup\views\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\report\views\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\taskmanage\views\
++AgentInfo
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\agentinfo\
++ClinicDeptManage
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\clinicdeptmanage\
++CRFormFieldSetting
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\crformfieldsetting\
++CRFormFieldsImport
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\crformfieldsimport\
++CRFormsManage
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\crformsmanage\
++CRFormsPad
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\crformspad\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\
++CRFormXmlImport
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\crformxmlimport\
++Index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\crformxmlimport\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\views\classification\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\views\crfdatadetails\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\reportingmanage\views\patientmanage\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\reportingmanage\views\rehabilitationdept\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\agentinfo\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\clinicdeptmanage\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\crformfieldsetting\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\crformfieldsimport\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\crformsmanage\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\crformspad\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\dspecificgroupmanage\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\hospitaldepts\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\medicalrecordupload\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\multicentersmanage\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\orgmanage\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\promptinfo\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\views\datacollectionmanagement\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\views\fillinprogresssum\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\views\patientdiscovery\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\views\researchdatadetails\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\views\researchprogress\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\reportingmanage\views\medicalrecordai\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\chat\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\crfmanager\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\home\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\menu\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\operationhistory\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\role\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\sample\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\user\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\vform\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\chat\views\chat\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\cmis\views\afterstructured\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\cmis\views\beforestructured\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\cmis\views\cmisconfig\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\cmis\views\cmisdatadetails\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\batchcaseextraction\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\batchinspectextraction\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\batchinspectextractiontask\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\crformtraceability\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\crformtraceabilitynew\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\followupai\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\followuptemplates\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\jifangsuggest\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\jsongrid\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\newpdfai\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\pdfai\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\pilot\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\pilotds\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\structureddata\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\textaianalysis\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\followup\views\followupvisit\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\followup\views\soundanalysis\index.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\taskmanage\views\taskinfomanage\index.cshtml
++DSpecificGroupManage
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\dspecificgroupmanage\
++HospitalDepts
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\hospitaldepts\
++MedicalRecordUpLoad
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\medicalrecordupload\
++MultiCentersManage
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\multicentersmanage\
++OrgManage
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\orgmanage\
++PromptInfo
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\promptinfo\
++Chat
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\chat\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\chat\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\chat\views\chat\
++CMIS
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\cmis\
++Demo
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\
++FollowUp
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\followup\
++PatientDiscoveryManage
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\
++Classification
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\views\classification\
++CRFDataDetails
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\views\crfdatadetails\
++DataCollectionManagement
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\views\datacollectionmanagement\
++FillInProgressSum
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\views\fillinprogresssum\
++PatientDiscovery
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\views\patientdiscovery\
++ResearchDataDetails
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\views\researchdatadetails\
++ResearchProgress
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\views\researchprogress\
++Report
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\report\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\report\views\report\
++ReportingManage
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\reportingmanage\
++MedicalRecordAIController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\reportingmanage\controllers\medicalrecordaicontroller.cs
++PatientManageController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\reportingmanage\controllers\patientmanagecontroller.cs
++RehabilitationDeptController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\reportingmanage\controllers\rehabilitationdeptcontroller.cs
++MedicalRecordAI
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\reportingmanage\views\medicalrecordai\
++PatientManage
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\reportingmanage\views\patientmanage\
++DataCollecPC.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\reportingmanage\views\patientmanage\datacollecpc.cshtml
++DataCollect.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\reportingmanage\views\patientmanage\datacollect.cshtml
++DepartmentCollection.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\reportingmanage\views\patientmanage\departmentcollection.cshtml
++detail.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\reportingmanage\views\patientmanage\detail.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\crformspad\detail.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\chat\detail.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\cmis\views\cmisdatadetails\detail.cshtml
++RehabilitationDept
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\reportingmanage\views\rehabilitationdept\
++TaskManage
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\taskmanage\
++Filters
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\filters\
++SQLMaps
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\sqlmaps\
++Unity
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\unity\
++appsettings.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\appsettings.json
++log4net.config
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\log4net.config
++Program.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\program.cs
++ScaffoldingReadMe.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\scaffoldingreadme.txt
++Startup.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\startup.cs
++未发现任何服务依赖项
i:{************************************}:>10581
++PublishProfiles
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\properties\publishprofiles\
++launchSettings.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\properties\launchsettings.json
++chatGTPIndex.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\css\chatgtpindex.css
++index_style2.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\css\index_style2.css
++jquery.magnify.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\css\jquery.magnify.css
++knowledgeBase_style.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\css\knowledgebase_style.css
++loading.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\css\loading.css
++login2_style.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\css\login2_style.css
++paddemo_style.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\css\paddemo_style.css
++site.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\css\site.css
++welcome_style.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\css\welcome_style.css
++pathology_202323031.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\dipdfinfo\pathology_202323031.jpg
++pathology_202325308.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\dipdfinfo\pathology_202325308.jpg
++pathology_202325361.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\dipdfinfo\pathology_202325361.jpg
++pathology_MM20231036.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\dipdfinfo\pathology_mm20231036.jpg
++pathology_MM20231137.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\dipdfinfo\pathology_mm20231137.jpg
++theme
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\echarts\theme\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\
++echarts.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\echarts\echarts.min.js
++124c82eb-125f-44c3-9746-3bfb32b30fa2.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\124c82eb-125f-44c3-9746-3bfb32b30fa2.pdf
++1885c14e-e82e-441a-8dcf-c050eceb4eb6.PDF
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\1885c14e-e82e-441a-8dcf-c050eceb4eb6.pdf
++1b200d73-259e-4b5d-9221-51c3e03b28c4.PDF
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\1b200d73-259e-4b5d-9221-51c3e03b28c4.pdf
++1fce9a06-dcf0-4b20-95e9-29d56aee3f1d.PDF
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\1fce9a06-dcf0-4b20-95e9-29d56aee3f1d.pdf
++25722a6f-a4d2-4512-b14c-0129ca869bb7.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\25722a6f-a4d2-4512-b14c-0129ca869bb7.png
++2cc6c818-0278-4bb0-8397-c69fb742eb24.PDF
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\2cc6c818-0278-4bb0-8397-c69fb742eb24.pdf
++319d7626-8946-48cf-945c-00625b9f11db.wav
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\319d7626-8946-48cf-945c-00625b9f11db.wav
++3c38bcc9-3398-4d74-bfac-ced1204f9a4b.PDF
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\3c38bcc9-3398-4d74-bfac-ced1204f9a4b.pdf
++42f92c1d-4286-4d97-ba18-5d8c221f41ce.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\42f92c1d-4286-4d97-ba18-5d8c221f41ce.png
++46d17b15-43e6-46b4-acf3-939a6fec6b7d.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\46d17b15-43e6-46b4-acf3-939a6fec6b7d.png
++63238a46-31ac-40ec-813b-3c079bc45371.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\63238a46-31ac-40ec-813b-3c079bc45371.pdf
++65b6c79f-fa0d-4b21-9976-3e8dd4b129a8.PDF
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\65b6c79f-fa0d-4b21-9976-3e8dd4b129a8.pdf
++6e0af6b1-3226-41cf-b57a-b1f130996bdf.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\6e0af6b1-3226-41cf-b57a-b1f130996bdf.png
++762c52ed-f724-4ad8-9a7c-ec34714a98dd.wav
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\762c52ed-f724-4ad8-9a7c-ec34714a98dd.wav
++7ce56493-ed75-46b5-9ee8-54eff1ab88e7.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\7ce56493-ed75-46b5-9ee8-54eff1ab88e7.png
++7f375cf2-e245-4234-b54d-29ce30349791.PDF
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\7f375cf2-e245-4234-b54d-29ce30349791.pdf
++924f7e09-202d-4ad2-bbd6-227486b7db8c.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\924f7e09-202d-4ad2-bbd6-227486b7db8c.png
++9ce165ba-cedf-4c8a-bd6a-022db342ce65.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\9ce165ba-cedf-4c8a-bd6a-022db342ce65.png
++a1af3a97-650d-489f-b225-1b10a04ce827.wav
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\a1af3a97-650d-489f-b225-1b10a04ce827.wav
++a78db2a4-d42d-4419-bfc2-0691c24f257c.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\a78db2a4-d42d-4419-bfc2-0691c24f257c.png
++b637229c-40ab-4c22-b517-7b24dc0361ae.wav
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\b637229c-40ab-4c22-b517-7b24dc0361ae.wav
++b72480d1-1892-449d-b924-fa7faf13f42a.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\b72480d1-1892-449d-b924-fa7faf13f42a.png
++b9da748c-be02-4bf5-a872-9943164c73db.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\b9da748c-be02-4bf5-a872-9943164c73db.png
++be28bec6-ac3b-4771-8326-45ada107989e.PDF
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\be28bec6-ac3b-4771-8326-45ada107989e.pdf
++e9e7f40b-41c2-4419-98e1-4bc033281b3b.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\e9e7f40b-41c2-4419-98e1-4bc033281b3b.png
++fa340032-9678-448c-be9e-35093822fddd.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\fa340032-9678-448c-be9e-35093822fddd.png
++fcde32e4-0f43-4bc8-b60c-973d29b758ee.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\fcde32e4-0f43-4bc8-b60c-973d29b758ee.png
++zhongyikewenzhen.mp3
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\zhongyikewenzhen.mp3
++zhongyikewenzhen2.mp3
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\externalreports\zhongyikewenzhen2.mp3
++1b1ab398-3cf3-4597-8e84-3ed7b1bfe166.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\files\1b1ab398-3cf3-4597-8e84-3ed7b1bfe166.txt
++1ff45e7b-e6f1-469b-b74d-463508de00e5.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\files\1ff45e7b-e6f1-469b-b74d-463508de00e5.txt
++273cf19d-999b-4251-b429-d509c9f9789f.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\files\273cf19d-999b-4251-b429-d509c9f9789f.txt
++5407bd75-20d8-4379-aacb-c0ca4864078a.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\files\5407bd75-20d8-4379-aacb-c0ca4864078a.txt
++5cab8ab6-19e6-4777-94ab-b1d76b848248.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\files\5cab8ab6-19e6-4777-94ab-b1d76b848248.txt
++667a9509-8b73-431b-97ca-8efdb3c75169.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\files\667a9509-8b73-431b-97ca-8efdb3c75169.txt
++6fa8d3a6-c8d3-4e50-ba48-fa0046b5e302.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\files\6fa8d3a6-c8d3-4e50-ba48-fa0046b5e302.txt
++8e95c80b-cf50-4d90-a0fc-13da34c4c64f.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\files\8e95c80b-cf50-4d90-a0fc-13da34c4c64f.txt
++9110c0a9-1871-4153-9a51-054bafdcb350.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\files\9110c0a9-1871-4153-9a51-054bafdcb350.txt
++96e1474c-936e-4fc5-a3f9-2d67f6aaa546.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\files\96e1474c-936e-4fc5-a3f9-2d67f6aaa546.txt
++9dd60abb-9e29-4008-b7d3-76e27f192427.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\files\9dd60abb-9e29-4008-b7d3-76e27f192427.txt
++a4bf6c28-a150-4afd-9aec-d770ce394204.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\files\a4bf6c28-a150-4afd-9aec-d770ce394204.txt
++a7c2a016-f68a-4800-91d6-f084189bf4d5.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\files\a7c2a016-f68a-4800-91d6-f084189bf4d5.txt
++b3cde410-d671-4861-9b2d-1715f617213b.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\files\b3cde410-d671-4861-9b2d-1715f617213b.txt
++be3c6f19-1a96-4b54-b508-087af03e7fcf.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\files\be3c6f19-1a96-4b54-b508-087af03e7fcf.txt
++ce5b02b0-d3c9-44a6-832b-1e0ab523c390.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\files\ce5b02b0-d3c9-44a6-832b-1e0ab523c390.txt
++ipadindex
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\
++login2
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\login2\
++mp4
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\mp4\
++welcome
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\welcome\
++AI_icon.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ai_icon.png
++AI_icon2.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ai_icon2.png
++base_icon.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\base_icon.png
++caduceus.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\caduceus.png
++chat_icon_A.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\chat_icon_a.png
++chat_icon_A1.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\chat_icon_a1.png
++chat_icon_A2.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\chat_icon_a2.png
++chat_icon_AGPT.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\chat_icon_agpt.jpg
++chat_icon_Q.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\chat_icon_q.png
++color.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\color.png
++crfdemo1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crfdemo1.jpg
++crfdemo2.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crfdemo2.jpg
++CT2.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ct2.pdf
++dataNull.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\datanull.png
++Hospitallogo.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\hospitallogo.png
++img_icon.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\img_icon.png
++img_icon1.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\img_icon1.png
++JY1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\jy1.jpg
++JY2.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\jy2.jpg
++logo.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\logo.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\style\res\logo.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\style\res\logo.png
++not_open.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\not_open.png
++pdf_icon.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\pdf_icon.png
++pdf_icon1.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\pdf_icon1.png
++user_pic.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\user_pic.png
++zjpBG.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\zjpbg.png
++zjpcrf.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\zjpcrf.jpg
++AngelwinForm.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\js\angelwinform.js
++common.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\js\common.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\modules\common.js
++jquery.easing.1.3.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\js\jquery.easing.1.3.js
++jquery.magnify.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\js\jquery.magnify.js
++jquery-1.10.2.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\js\jquery-1.10.2.min.js
++jquery-3.5.1.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\js\jquery-3.5.1.min.js
++marked.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\js\marked.min.js
++site.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\js\site.js
++report
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\json\report\
++CRFManagerData.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\json\crfmanagerdata.json
++EyeJson.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\json\eyejson.json
++StructedData.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\json\structeddata.json
++tgjc.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\json\tgjc.json
++tgjc_data.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\json\tgjc_data.json
++xbs.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\json\xbs.json
++xbs_data.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\json\xbs_data.json
++zjp.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\json\zjp.json
++zlpg.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\json\zlpg.json
++zlpg_data.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\json\zlpg_data.json
++病理.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\json\病理.json
++康复科.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\json\康复科.json
++眼科.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\json\眼科.json
++院外数据.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\json\院外数据.json
++index.cjs.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\dist\index.cjs.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts\dist\index.cjs.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts-demo\dist\index.cjs.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\yunzhisheng\dist\index.cjs.js
++index.d.ts
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\dist\index.d.ts
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts\dist\index.d.ts
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts-demo\dist\index.d.ts
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\yunzhisheng\dist\index.d.ts
++index.esm.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\dist\index.esm.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts\dist\index.esm.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts-demo\dist\index.esm.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\yunzhisheng\dist\index.esm.js
++index.umd.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\dist\index.umd.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts\dist\index.umd.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts-demo\dist\index.umd.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\yunzhisheng\dist\index.umd.js
++processor.worker.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\dist\processor.worker.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts\dist\processor.worker.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts-demo\dist\processor.worker.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\yunzhisheng\dist\processor.worker.js
++processor.worklet.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\dist\processor.worklet.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\yunzhisheng\dist\processor.worklet.js
++index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\rtasr\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\yunzhisheng\rtasr\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts-demo\example\tts\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\apl\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\asciiarmor\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\asn.1\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\asterisk\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\brainfuck\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\clike\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\clojure\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\cmake\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\cobol\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\coffeescript\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\commonlisp\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\crystal\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\css\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\cypher\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\d\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\dart\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\diff\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\django\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\dockerfile\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\dtd\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\dylan\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\ebnf\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\ecl\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\eiffel\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\elm\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\erlang\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\factor\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\fcl\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\forth\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\fortran\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\gas\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\gfm\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\gherkin\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\go\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\groovy\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\haml\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\handlebars\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\haskell\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\haskell-literate\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\haxe\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\htmlembedded\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\htmlmixed\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\http\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\idl\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\javascript\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\jinja2\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\jsx\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\julia\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\livescript\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\lua\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\markdown\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mathematica\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mbox\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mirc\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mllike\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\modelica\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mscgen\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mumps\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\nginx\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\nsis\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\ntriples\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\octave\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\oz\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\pascal\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\pegjs\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\perl\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\php\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\pig\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\powershell\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\properties\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\protobuf\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\pug\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\puppet\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\python\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\q\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\r\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\rpm\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\rst\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\ruby\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\rust\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\sas\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\sass\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\scheme\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\shell\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\sieve\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\slim\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\smalltalk\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\smarty\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\solr\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\soy\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\sparql\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\spreadsheet\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\sql\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\stex\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\stylus\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\swift\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\tcl\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\textile\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\tiddlywiki\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\tiki\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\toml\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\tornado\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\troff\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\ttcn\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\ttcn-cfg\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\turtle\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\twig\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\vb\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\vbscript\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\velocity\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\verilog\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\vhdl\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\vue\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\webidl\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\xml\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\xquery\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\yacas\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\yaml\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\yaml-frontmatter\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\z80\index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\rpm\changes\index.html
++xunfei_rtasr.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\rtasr\xunfei_rtasr.js
++XunFeiRecord.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\rtasr\xunfeirecord.js
++example
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts-demo\example\
++README.md
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts-demo\readme.md
++layui
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\
++layuiextend
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layuiextend\
++layui-soulTable
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui-soultable\
++layui-v2.5.6
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui-v2.5.6\
++modules
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\modules\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\
++style
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\style\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\style\
++tpl
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\tpl\
++config.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\config.js
++audio
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\audio\
++audioypbf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\audioypbf\
++bootstrap
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\
++CodeMirror
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\
++columnDrag
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\columndrag\
++dialog
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\dialog\
++jquery
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery\
++jquery-Steps
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-steps\
++jquery-validation
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-validation\
++jquery-validation-unobtrusive
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-validation-unobtrusive\
++Kindeditor
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\kindeditor\
++liMarquee
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\limarquee\
++MuiHk
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\muihk\
++mui-player
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\mui-player\
++perfect-scrollbar
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\perfect-scrollbar\
++swiper
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\swiper\
++touchslider
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\touchslider\
++**********的病历资料
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\
++FollowUpBackfillInfo - 副本.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\model\followupbackfillinfo - 副本.txt
++FollowUpBackfillInfo.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\model\followupbackfillinfo.txt
++ReportInfo.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\model\reportinfo.png
++ReportInfo1.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\model\reportinfo1.png
++Consultation.m4a
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\mp3\consultation.m4a
++fjsl.mp3
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\mp3\fjsl.mp3
++沁园春雪.mp3
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\mp3\沁园春雪.mp3
++02db29bb-0844-4a92-990c-354033edea14.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\02db29bb-0844-4a92-990c-354033edea14.jpg
++04f30878-be30-426f-a345-88c6a84395d7.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\04f30878-be30-426f-a345-88c6a84395d7.pdf
++04f30878-be30-426f-a345-88c6a84395d7_1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\04f30878-be30-426f-a345-88c6a84395d7_1.jpg
++04f30878-be30-426f-a345-88c6a84395d7_10.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\04f30878-be30-426f-a345-88c6a84395d7_10.jpg
++04f30878-be30-426f-a345-88c6a84395d7_11.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\04f30878-be30-426f-a345-88c6a84395d7_11.jpg
++04f30878-be30-426f-a345-88c6a84395d7_12.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\04f30878-be30-426f-a345-88c6a84395d7_12.jpg
++04f30878-be30-426f-a345-88c6a84395d7_13.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\04f30878-be30-426f-a345-88c6a84395d7_13.jpg
++04f30878-be30-426f-a345-88c6a84395d7_14.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\04f30878-be30-426f-a345-88c6a84395d7_14.jpg
++04f30878-be30-426f-a345-88c6a84395d7_15.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\04f30878-be30-426f-a345-88c6a84395d7_15.jpg
++04f30878-be30-426f-a345-88c6a84395d7_16.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\04f30878-be30-426f-a345-88c6a84395d7_16.jpg
++04f30878-be30-426f-a345-88c6a84395d7_17.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\04f30878-be30-426f-a345-88c6a84395d7_17.jpg
++04f30878-be30-426f-a345-88c6a84395d7_18.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\04f30878-be30-426f-a345-88c6a84395d7_18.jpg
++04f30878-be30-426f-a345-88c6a84395d7_2.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\04f30878-be30-426f-a345-88c6a84395d7_2.jpg
++04f30878-be30-426f-a345-88c6a84395d7_3.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\04f30878-be30-426f-a345-88c6a84395d7_3.jpg
++04f30878-be30-426f-a345-88c6a84395d7_4.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\04f30878-be30-426f-a345-88c6a84395d7_4.jpg
++04f30878-be30-426f-a345-88c6a84395d7_5.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\04f30878-be30-426f-a345-88c6a84395d7_5.jpg
++04f30878-be30-426f-a345-88c6a84395d7_6.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\04f30878-be30-426f-a345-88c6a84395d7_6.jpg
++04f30878-be30-426f-a345-88c6a84395d7_7.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\04f30878-be30-426f-a345-88c6a84395d7_7.jpg
++04f30878-be30-426f-a345-88c6a84395d7_8.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\04f30878-be30-426f-a345-88c6a84395d7_8.jpg
++04f30878-be30-426f-a345-88c6a84395d7_9.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\04f30878-be30-426f-a345-88c6a84395d7_9.jpg
++06086fbe-4e9d-440f-87d5-c045c0c8d6c8.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\06086fbe-4e9d-440f-87d5-c045c0c8d6c8.jpg
++0f81b231-b946-4593-a29e-74e4acd73653.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\0f81b231-b946-4593-a29e-74e4acd73653.pdf
++0f81b231-b946-4593-a29e-74e4acd73653_1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\0f81b231-b946-4593-a29e-74e4acd73653_1.jpg
++187dac77636a6d94f932378e99943dd.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\187dac77636a6d94f932378e99943dd.jpg
++23746bf2-0692-4db8-a437-cc3a28d2c451.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\23746bf2-0692-4db8-a437-cc3a28d2c451.jpg
++260ef138-0d82-4f3f-8460-b935b4a368a6.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\260ef138-0d82-4f3f-8460-b935b4a368a6.jpg
++297202af-a63e-441f-9206-db41ebe8349a.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\297202af-a63e-441f-9206-db41ebe8349a.jpg
++30d90a14-de6a-44d4-bae2-45a1cf18b529.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\30d90a14-de6a-44d4-bae2-45a1cf18b529.jpg
++3566b8cb-e9b7-45b7-854c-0c6df0c35fee.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\3566b8cb-e9b7-45b7-854c-0c6df0c35fee.pdf
++3566b8cb-e9b7-45b7-854c-0c6df0c35fee_1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\3566b8cb-e9b7-45b7-854c-0c6df0c35fee_1.jpg
++3a7a3f0f-0059-42f7-ad1e-01ac19d73467.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\3a7a3f0f-0059-42f7-ad1e-01ac19d73467.pdf
++3a7a3f0f-0059-42f7-ad1e-01ac19d73467_1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\3a7a3f0f-0059-42f7-ad1e-01ac19d73467_1.jpg
++3dd868ac-870a-4124-a733-b10c2d220689.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\3dd868ac-870a-4124-a733-b10c2d220689.pdf
++489e1a4c-25eb-489e-afbe-77a76d64fea2.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\489e1a4c-25eb-489e-afbe-77a76d64fea2.jpg
++4af37dbc-a41f-44c0-a05e-549be5138c0f.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\4af37dbc-a41f-44c0-a05e-549be5138c0f.jpg
++4b357681-30d9-4ce0-acde-dd0c48b3b4d9.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\4b357681-30d9-4ce0-acde-dd0c48b3b4d9.png
++52f91576-6dee-4770-84d0-bbdb05e940e2.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\52f91576-6dee-4770-84d0-bbdb05e940e2.pdf
++52f91576-6dee-4770-84d0-bbdb05e940e2_1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\52f91576-6dee-4770-84d0-bbdb05e940e2_1.jpg
++6481cc43-c34e-40b4-9f7e-5af5aac891ec.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\6481cc43-c34e-40b4-9f7e-5af5aac891ec.pdf
++6481cc43-c34e-40b4-9f7e-5af5aac891ec_1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\6481cc43-c34e-40b4-9f7e-5af5aac891ec_1.jpg
++692dacef-c424-436e-a3db-4258e6641f68..pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\692dacef-c424-436e-a3db-4258e6641f68..pdf
++692dacef-c424-436e-a3db-4258e6641f68_1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\692dacef-c424-436e-a3db-4258e6641f68_1.jpg
++6ba51abe-4275-4edf-8d02-be5c1ffb0a0f.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\6ba51abe-4275-4edf-8d02-be5c1ffb0a0f.pdf
++6ba51abe-4275-4edf-8d02-be5c1ffb0a0f_1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\6ba51abe-4275-4edf-8d02-be5c1ffb0a0f_1.jpg
++7094bcff-a9aa-420b-b2d3-79f280bde7d9.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\7094bcff-a9aa-420b-b2d3-79f280bde7d9.jpg
++71835884-17d3-4b19-8a49-aba8669d3ff7..pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\71835884-17d3-4b19-8a49-aba8669d3ff7..pdf
++71835884-17d3-4b19-8a49-aba8669d3ff7_1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\71835884-17d3-4b19-8a49-aba8669d3ff7_1.jpg
++72b947c9-e0b4-4160-8509-b5e72930c844.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\72b947c9-e0b4-4160-8509-b5e72930c844.pdf
++72b947c9-e0b4-4160-8509-b5e72930c844_1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\72b947c9-e0b4-4160-8509-b5e72930c844_1.jpg
++821c1a97-b118-49a5-871c-ce706e661f71.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\821c1a97-b118-49a5-871c-ce706e661f71.pdf
++821c1a97-b118-49a5-871c-ce706e661f71_1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\821c1a97-b118-49a5-871c-ce706e661f71_1.jpg
++82b778ea-7899-4216-b4d6-0c2604edff4f..pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\82b778ea-7899-4216-b4d6-0c2604edff4f..pdf
++a8fac0b0-e708-4555-adb7-93176f659194..pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\a8fac0b0-e708-4555-adb7-93176f659194..pdf
++b3e83c6e-5b1f-4ddf-b7b0-d20652c98d5e.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\b3e83c6e-5b1f-4ddf-b7b0-d20652c98d5e.png
++b6ab909a-89b8-4aea-87ac-21813bb4f2eb.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\b6ab909a-89b8-4aea-87ac-21813bb4f2eb.jpg
++b6ab909a-89b8-4aea-87ac-21813bb4f2eb.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\b6ab909a-89b8-4aea-87ac-21813bb4f2eb.pdf
++b8ca7790-eb41-4f2d-9bbe-274e47e829f5.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\b8ca7790-eb41-4f2d-9bbe-274e47e829f5.jpg
++be498362-8412-489e-a1fa-423216d708fa.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\be498362-8412-489e-a1fa-423216d708fa.pdf
++d2e47f50-d74c-4392-b277-0516ea0f0e87..pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\d2e47f50-d74c-4392-b277-0516ea0f0e87..pdf
++d2e47f50-d74c-4392-b277-0516ea0f0e87_1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\d2e47f50-d74c-4392-b277-0516ea0f0e87_1.jpg
++d36d769d-1eb6-4609-b159-05c4e8d86b52.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\d36d769d-1eb6-4609-b159-05c4e8d86b52.pdf
++d36d769d-1eb6-4609-b159-05c4e8d86b52_1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\d36d769d-1eb6-4609-b159-05c4e8d86b52_1.jpg
++d39d5f6f-0318-4f03-a2bf-b061be706d67..pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\d39d5f6f-0318-4f03-a2bf-b061be706d67..pdf
++d9e33e05-fbbb-49a5-9c8a-9a8ca705bc3f.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\d9e33e05-fbbb-49a5-9c8a-9a8ca705bc3f.pdf
++d9e33e05-fbbb-49a5-9c8a-9a8ca705bc3f_1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\d9e33e05-fbbb-49a5-9c8a-9a8ca705bc3f_1.jpg
++dedd7710-6671-404a-bd78-1d012e3ba530.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\dedd7710-6671-404a-bd78-1d012e3ba530.jpg
++e1edc0a3-2dd3-4019-b54d-8ae350128f4d..pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\e1edc0a3-2dd3-4019-b54d-8ae350128f4d..pdf
++e5cb4728-f170-4307-afe2-05531529f20e.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\e5cb4728-f170-4307-afe2-05531529f20e.jpg
++ebbc5ebd-98c7-4d75-bbb1-3ac4274b82b5..pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\ebbc5ebd-98c7-4d75-bbb1-3ac4274b82b5..pdf
++ee20c728-da18-4816-a502-2c630be838d8.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\ee20c728-da18-4816-a502-2c630be838d8.jpg
++fa3d3b93-9589-4c90-b666-eb2eaf48bcaa..pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\fa3d3b93-9589-4c90-b666-eb2eaf48bcaa..pdf
++fcffbc5c-9910-4a58-ad1a-97454a26b042.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\fcffbc5c-9910-4a58-ad1a-97454a26b042.pdf
++微信截图_20240806150051.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\pdfinfo\微信截图_20240806150051.png
++bootbox.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\bootbox.min.js
++bootstrap.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\bootstrap.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\js\bootstrap.min.js
++bootstrap-slider.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\bootstrap-slider.min.js
++chosen.jquery.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\chosen.jquery.min.js
++interact.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\interact.min.js
++jquery.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\jquery.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-steps\js\jquery.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery\dist\jquery.min.js
++jquery-extendext.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\jquery-extendext.js
++popper.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\popper.min.js
++query-builder.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\query-builder.js
++selectize.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\selectize.min.js
++sql-parser.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\sql-parser.min.js
++彩色多普勒_OCR.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\test\彩色多普勒_ocr.pdf
++fonts
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\fonts\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\css\fonts\
++6.23.0_polyfill.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\6.23.0_polyfill.min.js
++demo.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\demo.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\tpl\layim\demo.html
++element-icons.ttf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\element-icons.ttf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\fonts\element-icons.ttf
++element-icons.woff
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\element-icons.woff
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\fonts\element-icons.woff
++element-ui_2.15.7.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\element-ui_2.15.7.min.js
++index1.min.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\index1.min.css
++t1.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\t1.png
++t1.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\t1.txt
++VFormDesigner.common.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\vformdesigner.common.js
++VFormDesigner.common-report.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\vformdesigner.common-report.html
++VFormDesigner.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\vformdesigner.css
++VFormDesigner.umd.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\vformdesigner.umd.js
++VFormDesigner.umd.min-report.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\vformdesigner.umd.min-report.html
++VFormDesigner.umd-report.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\vformdesigner.umd-report.html
++vue.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\vue.min.js
++程序集
i:{************************************}:>10629
++DataProfiling.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\crformspad\dataprofiling.cshtml
++MedicalCenterManage.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\basicconfig\views\crformspad\medicalcentermanage.cshtml
++ClassificationController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\controllers\classificationcontroller.cs
++CRFDataDetailsController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\controllers\crfdatadetailscontroller.cs
++DataCollectionManagementController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\controllers\datacollectionmanagementcontroller.cs
++FillInProgressSumController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\controllers\fillinprogresssumcontroller.cs
++PatientDiscoveryController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\controllers\patientdiscoverycontroller.cs
++ResearchDataDetailsController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\controllers\researchdatadetailscontroller.cs
++ResearchProgressController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\controllers\researchprogresscontroller.cs
++DataTableToJson.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\models\datatabletojson.cs
++Patient.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\models\patient.cs
++ReseachDatas.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\models\reseachdatas.cs
++ResearchPaths.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\models\researchpaths.cs
++Index2.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\views\patientdiscovery\index2.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\vform\index2.cshtml
++Index3.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\views\patientdiscovery\index3.cshtml
++Index4.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\patientdiscoverymanage\views\patientdiscovery\index4.cshtml
++AccountController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\controllers\accountcontroller.cs
++ChatController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\controllers\chatcontroller.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\chat\controllers\chatcontroller.cs
++CRFManagerController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\controllers\crfmanagercontroller.cs
++followUpVisit.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\controllers\followupvisit.cs
++HomeController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\controllers\homecontroller.cs
++knowledgeBaseController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\controllers\knowledgebasecontroller.cs
++LogController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\controllers\logcontroller.cs
++MenuController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\controllers\menucontroller.cs
++OperationHistoryController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\controllers\operationhistorycontroller.cs
++RoleController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\controllers\rolecontroller.cs
++sample.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\controllers\sample.cs
++UserController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\controllers\usercontroller.cs
++vformController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\controllers\vformcontroller.cs
++ValidateCode.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\extensions\validatecode.cs
++AuthorizingAttribute.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\filters\authorizingattribute.cs
++SSEActionFilter.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\filters\sseactionfilter.cs
++AccountModels.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\models\accountmodels.cs
++ErrorViewModel.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\models\errorviewmodel.cs
++JianYanDTO.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\models\jianyandto.cs
++GetPDFUrl.xml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\sqlmaps\getpdfurl.xml
++CommAPIController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\unity\commapicontroller.cs
++ExternalCommAPIController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\unity\externalcommapicontroller.cs
++SessionExtensions.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\unity\sessionextensions.cs
++TokenGenerator.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\unity\tokengenerator.cs
++Account
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\account\
++CRFManager
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\crfmanager\
++Finding
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\finding\
++followUpVisit
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\followupvisit\
++Home
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\home\
++knowledgeBase
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\knowledgebase\
++Log
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\log\
++Menu
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\menu\
++OperationHistory
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\operationhistory\
++Role
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\role\
++sample
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\sample\
++Shared
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\shared\
++User
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\user\
++_ViewImports.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\_viewimports.cshtml
++_ViewStart.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\_viewstart.cshtml
++appsettings.Development.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\appsettings.development.json
++FolderProfile.pubxml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\properties\publishprofiles\folderprofile.pubxml
++dark.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\echarts\theme\dark.js
++macarons.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\echarts\theme\macarons.js
++shine.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\echarts\theme\shine.js
++walden.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\echarts\theme\walden.js
++eCRF_img
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\
++13e1fa87369b49dfa61528cd24be0c54.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\13e1fa87369b49dfa61528cd24be0c54.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\13e1fa87369b49dfa61528cd24be0c54.jpg
++13e1fa87369b49dfa61528cd24be0c54_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\13e1fa87369b49dfa61528cd24be0c54_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\13e1fa87369b49dfa61528cd24be0c54_t.jpg
++2.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\2.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layim\skin\2.jpg
++3.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\3.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layim\skin\3.jpg
++4.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\4.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layim\skin\4.jpg
++7.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\7.jpg
++banner.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\banner.jpg
++banner1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\banner1.jpg
++banner2.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\banner2.jpg
++banner2_pic.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\banner2_pic.png
++banner3.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\banner3.jpg
++banner5.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\banner5.jpg
++ipad_icon1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\ipad_icon1.jpg
++ipad_icon10.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\ipad_icon10.png
++ipad_icon11.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\ipad_icon11.png
++ipad_icon12.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\ipad_icon12.png
++ipad_icon2.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\ipad_icon2.jpg
++ipad_icon3.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\ipad_icon3.jpg
++ipad_icon4.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\ipad_icon4.jpg
++ipad_icon5.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\ipad_icon5.jpg
++ipad_icon6.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\ipad_icon6.jpg
++ipad_icon7.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\ipad_icon7.jpg
++ipad_icon8.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\ipad_icon8.jpg
++ipad_icon9.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\ipad_icon9.jpg
++next_icon.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\next_icon.png
++prev_icon.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\ipadindex\prev_icon.png
++bg2.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\login2\bg2.jpg
++video1.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\mp4\video1.png
++video2.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\mp4\video2.png
++video3.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\mp4\video3.png
++妇产新生儿科-病历转写2.mp4
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\mp4\妇产新生儿科-病历转写2.mp4
++随访转写.mp4
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\mp4\随访转写.mp4
++特征变量提取.mp4
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\mp4\特征变量提取.mp4
++bg_camera.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\welcome\bg_camera.png
++bg_consultation.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\welcome\bg_consultation.png
++bg_fav.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\welcome\bg_fav.png
++bg_home.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\welcome\bg_home.png
++bg_shop.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\welcome\bg_shop.png
++bg_user.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\welcome\bg_user.png
++bg1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\welcome\bg1.jpg
++camera.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\welcome\camera.png
++consultation.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\welcome\consultation.png
++fav.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\welcome\fav.png
++home.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\welcome\home.png
++line.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\welcome\line.png
++shop.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\welcome\shop.png
++title.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\welcome\title.png
++user.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\welcome\user.png
++ComplexStatisticsData.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\json\report\complexstatisticsdata.json
++DrugStatisticsData.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\json\report\drugstatisticsdata.json
++console
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\console\
++content
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\content\
++forum
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\forum\
++layer
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\layer\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layer\
++layim
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\layim\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\tpl\layim\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layim\
++mall
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\mall\
++message
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\message\
++table
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\table\
++upload
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\upload\
++user
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\user\
++useradmin
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\useradmin\
++workorder
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\workorder\
++menu.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\menu.js
++Package.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\package.json
++schedule.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\schedule.json
++User.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\user.json
++说明.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\说明.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\tpl\说明.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\tpl\system\说明.txt
++font
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\font\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\laydate\default\font\
++formSelects
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\formselects\
++icon_font
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\icon_font\
++lay
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\
++layui.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\layui.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui-v2.5.6\layui.js
++treeTable.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layuiextend\treetable.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\modules\treetable.js
++xm-select.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layuiextend\xm-select.js
++excel.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui-soultable\excel.js
++soulTable.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui-soultable\soultable.css
++soulTable.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui-soultable\soultable.js
++tableChild.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui-soultable\tablechild.js
++tableFilter.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui-soultable\tablefilter.js
++tableMerge.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui-soultable\tablemerge.js
++layui.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui-v2.5.6\layui.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\layui.css
++extend
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\lib\extend\
++admin.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\lib\admin.js
++index.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\lib\index.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\kedaxunfei\tts-demo\example\tts\index.js
++view.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\lib\view.js
++console.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\modules\console.js
++contlist.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\modules\contlist.js
++forum.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\modules\forum.js
++iconPicker.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\modules\iconpicker.js
++im.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\modules\im.js
++message.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\modules\message.js
++sample.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\modules\sample.js
++senior.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\modules\senior.js
++set.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\modules\set.js
++user.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\modules\user.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\table\user.js
++useradmin.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\modules\useradmin.js
++workorder.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\modules\workorder.js
++res
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\style\res\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\style\res\
++admin.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\style\admin.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\style\admin.css
++login.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\style\login.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\style\login.css
++template.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\style\template.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\style\template.css
++system
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\tpl\system\
++img
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\audio\img\
++LICENSE
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\license
++mode
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\
++column_drag.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\columndrag\column_drag.css
++column_drag.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\columndrag\column_drag.js
++handlebars.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\dialog\handlebars.min.js
++list.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\dialog\list.min.js
++script.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\dialog\script.js
++style.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\dialog\style.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\audioypbf\css\style.css
++LICENSE.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery\license.txt
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-validation-unobtrusive\license.txt
++LICENSE.md
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-validation\license.md
++jquery.validate.unobtrusive.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.js
++themes
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\kindeditor\themes\
++kindeditor-all.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\kindeditor\kindeditor-all.js
++perfect-scrollbar.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\perfect-scrollbar\perfect-scrollbar.css
++perfect-scrollbar.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\perfect-scrollbar\perfect-scrollbar.min.js
++touchslider.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\touchslider\touchslider.css
++touchslider.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\touchslider\touchslider.js
++病历文书
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\病历文书\
++检查
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检查\
++检验
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\
++awesome-bootstrap-checkbox.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\css\awesome-bootstrap-checkbox.css
++bootstrap.min.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\css\bootstrap.min.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\css\bootstrap.min.css
++bootstrap-icons.min.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\css\bootstrap-icons.min.css
++bootstrap-slider.min.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\css\bootstrap-slider.min.css
++chosen.min.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\css\chosen.min.css
++query-builder.default.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\css\query-builder.default.css
++selectize.bootstrap5.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\css\selectize.bootstrap5.css
++query-builder.zh-CN.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\query-builder.zh-cn.js
++VFormDesigner.common.js.map
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\vformdesigner.common.js.map
++VFormDesigner.umd.js.map
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\vformdesigner.umd.js.map
++VFormDesigner.umd.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\vform\vformdesigner.umd.min.js
++yunzhisheng.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\yunzhisheng\rtasr\yunzhisheng.js
++EPPlus (4.5.3.3)
i:{************************************}:>10621
++FluentFTP (52.1.0)
i:{************************************}:>10624
++Html2Markdown (3.2.3.392)
i:{************************************}:>10615
++Microsoft.AspNetCore.Mvc.NewtonsoftJson (3.1.32)
i:{************************************}:>10622
++Microsoft.SemanticKernel (1.6.2)
i:{************************************}:>10627
++Microsoft.VisualStudio.Web.CodeGeneration.Design (3.1.5)
i:{************************************}:>10625
++Newtonsoft.Json (13.0.3)
i:{************************************}:>10619
++PDFtoImage (4.1.0)
i:{************************************}:>10618
++Quartz (3.8.1)
i:{************************************}:>10628
++SharpToken (2.0.3)
i:{************************************}:>10626
++Spire.PDF (10.5.5)
i:{************************************}:>10611
++Sap.Data.Hana.Core.v2.1
i:{************************************}:>10630
++Microsoft.AspNetCore.Analyzers
i:{************************************}:c:\program files\dotnet\sdk\9.0.200\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.analyzers.dll
++Microsoft.AspNetCore.Components.SdkAnalyzers
i:{************************************}:c:\program files\dotnet\sdk\9.0.200\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.components.sdkanalyzers.dll
++Microsoft.AspNetCore.App
i:{************************************}:>10605
++AfterStructuredController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\cmis\controllers\afterstructuredcontroller.cs
++BeforeStructuredController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\cmis\controllers\beforestructuredcontroller.cs
++CMISConfigController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\cmis\controllers\cmisconfigcontroller.cs
++CMISDataDetailsController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\cmis\controllers\cmisdatadetailscontroller.cs
++TempCMIS.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\cmis\models\tempcmis.cs
++AfterStructured
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\cmis\views\afterstructured\
++BeforeStructured
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\cmis\views\beforestructured\
++CMISConfig
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\cmis\views\cmisconfig\
++CMISDataDetails
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\cmis\views\cmisdatadetails\
++BatchCaseExtractionController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\controllers\batchcaseextractioncontroller.cs
++BatchInspectExtractionController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\controllers\batchinspectextractioncontroller.cs
++BatchInspectExtractionTaskController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\controllers\batchinspectextractiontaskcontroller.cs
++CRFormTraceabilityController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\controllers\crformtraceabilitycontroller.cs
++CRFormTraceabilityNewController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\controllers\crformtraceabilitynewcontroller.cs
++FollowUpAIController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\controllers\followupaicontroller.cs
++JiFangSuggestController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\controllers\jifangsuggestcontroller.cs
++JsonGridController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\controllers\jsongridcontroller.cs
++NewPdfAIController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\controllers\newpdfaicontroller.cs
++PdfAIController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\controllers\pdfaicontroller.cs
++PilotController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\controllers\pilotcontroller.cs
++PilotDSController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\controllers\pilotdscontroller.cs
++StructuredDataController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\controllers\structureddatacontroller.cs
++TextAIAnalysisController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\controllers\textaianalysiscontroller.cs
++BatchCaseExtraction
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\batchcaseextraction\
++BatchInspectExtraction
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\batchinspectextraction\
++BatchInspectExtractionTask
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\batchinspectextractiontask\
++CRFormTraceability
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\crformtraceability\
++CRFormTraceabilityNew
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\crformtraceabilitynew\
++FollowUpAI
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\followupai\
++FollowUpTemplates
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\followuptemplates\
++JiFangSuggest
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\jifangsuggest\
++JsonGrid
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\jsongrid\
++NewPdfAI
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\newpdfai\
++PdfAI
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\pdfai\
++Pilot
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\pilot\
++PilotDS
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\pilotds\
++StructuredData
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\structureddata\
++TextAIAnalysis
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\demo\views\textaianalysis\
++FollowUpVisitController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\followup\controllers\followupvisitcontroller.cs
++SoundAnalysisController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\followup\controllers\soundanalysiscontroller.cs
++FollowUpVisit
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\followup\views\followupvisit\
++SoundAnalysis
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\followup\views\soundanalysis\
++ReportController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\report\controllers\reportcontroller.cs
++TaskInfoManageController.cs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\taskmanage\controllers\taskinfomanagecontroller.cs
++TaskInfoManage
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\taskmanage\views\taskinfomanage\
++Login.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\account\login.cshtml
++ResetPwd.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\account\resetpwd.cshtml
++AnyReportDB.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\chat\anyreportdb.cshtml
++ChatDoc.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\chat\chatdoc.cshtml
++IndexAnyReport.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\chat\indexanyreport.cshtml
++IndexPdf.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\chat\indexpdf.cshtml
++PdfInfo.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\chat\pdfinfo.cshtml
++AiFinding.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\finding\aifinding.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\knowledgebase\aifinding.cshtml
++followUpVisitList.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\followupvisit\followupvisitlist.cshtml
++AanyReportGMS.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\home\aanyreportgms.cshtml
++AanyReportHYS.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\home\aanyreporthys.cshtml
++AanyReportTGJC.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\home\aanyreporttgjc.cshtml
++changelog.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\home\changelog.cshtml
++CRF.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\home\crf.cshtml
++IndexDemo.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\home\indexdemo.cshtml
++ipadIndex.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\home\ipadindex.cshtml
++MenuHtmlPartialChild.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\home\menuhtmlpartialchild.cshtml
++NewTGJC.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\home\newtgjc.cshtml
++NewXBS.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\home\newxbs.cshtml
++NewZLPG.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\home\newzlpg.cshtml
++NullPage.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\home\nullpage.cshtml
++Privacy.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\home\privacy.cshtml
++welcome.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\home\welcome.cshtml
++DS.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\knowledgebase\ds.cshtml
++knowledgeBase.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\knowledgebase\knowledgebase.cshtml
++searchTest.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\knowledgebase\searchtest.cshtml
++LoginLog.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\log\loginlog.cshtml
++Logs.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\log\logs.cshtml
++sampleIndex.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\sample\sampleindex.cshtml
++_Layout.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\shared\_layout.cshtml
++_ValidationScriptsPartial.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\shared\_validationscriptspartial.cshtml
++Error.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\shared\error.cshtml
++conclusionExtract.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\vform\conclusionextract.cshtml
++conclusionExtract2.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\vform\conclusionextract2.cshtml
++FillInProgressSum.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\vform\fillinprogresssum.cshtml
++InspectionReport.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\vform\inspectionreport.cshtml
++Package.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\vform\package.cshtml
++ReportExtract.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\vform\reportextract.cshtml
++ResearchProgress.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\vform\researchprogress.cshtml
++soundAnalysis.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\vform\soundanalysis.cshtml
++template.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\vform\template.cshtml
++traceability.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\vform\traceability.cshtml
++zjp.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\vform\zjp.cshtml
++zjp2.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\vform\zjp2.cshtml
++zlpg.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\views\vform\zlpg.cshtml
++098e046860924f7391cc417cb727851d.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\098e046860924f7391cc417cb727851d.jpg
++098e046860924f7391cc417cb727851d_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\098e046860924f7391cc417cb727851d_t.jpg
++1f39d81a132f41daab51aa8a250cd367.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\1f39d81a132f41daab51aa8a250cd367.jpg
++1f39d81a132f41daab51aa8a250cd367_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\1f39d81a132f41daab51aa8a250cd367_t.jpg
++2e154767c5a141db9246a011f514c63a.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\2e154767c5a141db9246a011f514c63a.jpg
++2e154767c5a141db9246a011f514c63a_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\2e154767c5a141db9246a011f514c63a_t.jpg
++371cb67bb9ca44779ad179574a44ee20.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\371cb67bb9ca44779ad179574a44ee20.jpg
++371cb67bb9ca44779ad179574a44ee20_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\371cb67bb9ca44779ad179574a44ee20_t.jpg
++4097cd8758b94b37b31090c750d4abd3.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\4097cd8758b94b37b31090c750d4abd3.jpg
++4097cd8758b94b37b31090c750d4abd3_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\4097cd8758b94b37b31090c750d4abd3_t.jpg
++4389f677387c428d85ca5d584f84bee3.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\4389f677387c428d85ca5d584f84bee3.jpg
++4389f677387c428d85ca5d584f84bee3_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\4389f677387c428d85ca5d584f84bee3_t.jpg
++54c315e2908b4cbe9dd4cff341f0b65f.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\54c315e2908b4cbe9dd4cff341f0b65f.jpg
++54c315e2908b4cbe9dd4cff341f0b65f_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\54c315e2908b4cbe9dd4cff341f0b65f_t.jpg
++60d31ee2739c4e7a941412b3dfb8226d.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\60d31ee2739c4e7a941412b3dfb8226d.jpg
++60d31ee2739c4e7a941412b3dfb8226d_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\60d31ee2739c4e7a941412b3dfb8226d_t.jpg
++65fa56c05b9e4ac1a99cf1efc8d9f840.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\65fa56c05b9e4ac1a99cf1efc8d9f840.jpg
++65fa56c05b9e4ac1a99cf1efc8d9f840_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\65fa56c05b9e4ac1a99cf1efc8d9f840_t.jpg
++6b715389681749b79466a254d0fa45ca.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\6b715389681749b79466a254d0fa45ca.jpg
++6b715389681749b79466a254d0fa45ca_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\6b715389681749b79466a254d0fa45ca_t.jpg
++6d0d362236064bf29898d156fbecbf21.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\6d0d362236064bf29898d156fbecbf21.jpg
++6d0d362236064bf29898d156fbecbf21_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\6d0d362236064bf29898d156fbecbf21_t.jpg
++76506f9dee5e40e69bba449519dd1c53.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\76506f9dee5e40e69bba449519dd1c53.jpg
++76506f9dee5e40e69bba449519dd1c53_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\76506f9dee5e40e69bba449519dd1c53_t.jpg
++7704d11da1c141c0aa9742e1b17e5704.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\7704d11da1c141c0aa9742e1b17e5704.jpg
++7704d11da1c141c0aa9742e1b17e5704_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\7704d11da1c141c0aa9742e1b17e5704_t.jpg
++7e1411c454954655b523a3ab79757e31.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\7e1411c454954655b523a3ab79757e31.jpg
++7e1411c454954655b523a3ab79757e31_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\7e1411c454954655b523a3ab79757e31_t.jpg
++871fe5bee2ef4b37994eb4b64fde75e4.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\871fe5bee2ef4b37994eb4b64fde75e4.jpg
++871fe5bee2ef4b37994eb4b64fde75e4_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\871fe5bee2ef4b37994eb4b64fde75e4_t.jpg
++8a3ce8051fd441c1a5344ae025037859.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\8a3ce8051fd441c1a5344ae025037859.jpg
++8a3ce8051fd441c1a5344ae025037859_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\8a3ce8051fd441c1a5344ae025037859_t.jpg
++9180f2c1a6af406091fa7623583a2805.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\9180f2c1a6af406091fa7623583a2805.jpg
++9180f2c1a6af406091fa7623583a2805_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\9180f2c1a6af406091fa7623583a2805_t.jpg
++922a8859322e4a1f98e396e00be6b4f8.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\922a8859322e4a1f98e396e00be6b4f8.jpg
++922a8859322e4a1f98e396e00be6b4f8_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\922a8859322e4a1f98e396e00be6b4f8_t.jpg
++a79c1970fb4a4d29a1a865db532296c9.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\a79c1970fb4a4d29a1a865db532296c9.jpg
++a79c1970fb4a4d29a1a865db532296c9_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\a79c1970fb4a4d29a1a865db532296c9_t.jpg
++b08facb58c6b4a448313ef76e422defc.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\b08facb58c6b4a448313ef76e422defc.jpg
++b08facb58c6b4a448313ef76e422defc_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\b08facb58c6b4a448313ef76e422defc_t.jpg
++baba446e1bf2453c98d89c4c41202581.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\baba446e1bf2453c98d89c4c41202581.jpg
++baba446e1bf2453c98d89c4c41202581_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\baba446e1bf2453c98d89c4c41202581_t.jpg
++d6c9fe0a17f84d2f8393727fb78e4448.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\d6c9fe0a17f84d2f8393727fb78e4448.jpg
++d6c9fe0a17f84d2f8393727fb78e4448_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\d6c9fe0a17f84d2f8393727fb78e4448_t.jpg
++df276538f18e4d0e90295c2e1b513b36.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\df276538f18e4d0e90295c2e1b513b36.jpg
++df276538f18e4d0e90295c2e1b513b36_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\df276538f18e4d0e90295c2e1b513b36_t.jpg
++ebc75c9b87c643bfa4ce0f2ca0051608.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\ebc75c9b87c643bfa4ce0f2ca0051608.jpg
++ebc75c9b87c643bfa4ce0f2ca0051608_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\ebc75c9b87c643bfa4ce0f2ca0051608_t.jpg
++fefc866ddb2741e982b8ca21028aeafb.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\fefc866ddb2741e982b8ca21028aeafb.jpg
++fefc866ddb2741e982b8ca21028aeafb_t.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\images\crformspad\ecrf_img\fefc866ddb2741e982b8ca21028aeafb_t.jpg
++prograss.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\console\prograss.js
++top-card.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\console\top-card.js
++top-search.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\console\top-search.js
++comment.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\content\comment.js
++list.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\content\list.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\forum\list.js
++tags.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\content\tags.js
++replys.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\forum\replys.js
++photos.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\layer\photos.js
++getList.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\layim\getlist.js
++getMembers.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\layim\getmembers.js
++order.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\mall\order.js
++all.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\message\all.js
++detail.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\message\detail.js
++direct.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\message\direct.js
++new.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\message\new.js
++notice.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\message\notice.js
++demo.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\table\demo.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\upload\demo.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\workorder\demo.js
++demo2.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\table\demo2.js
++demo3.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\table\demo3.js
++prolis.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\table\prolis.js
++Reportfile.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\table\reportfile.json
++user30.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\table\user30.js
++userHC.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\table\userhc.js
++forget.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\user\forget.js
++login.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\user\login.js
++logout.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\user\logout.js
++reg.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\user\reg.js
++resetpass.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\user\resetpass.js
++session.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\user\session.js
++sms.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\user\sms.js
++mangadmin.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\useradmin\mangadmin.js
++role.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\useradmin\role.js
++webuser.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\json\useradmin\webuser.js
++appointment.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\appointment.css
++base.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\base.css
++eyes_icon
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\font\eyes_icon\
++web_font
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\font\web_font\
++iconfont.eot
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\font\iconfont.eot
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\icon_font\iconfont.eot
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\laydate\default\font\iconfont.eot
++iconfont.svg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\font\iconfont.svg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\icon_font\iconfont.svg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\laydate\default\font\iconfont.svg
++iconfont.ttf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\font\iconfont.ttf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\icon_font\iconfont.ttf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\font\eyes_icon\iconfont.ttf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\font\web_font\iconfont.ttf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\laydate\default\font\iconfont.ttf
++iconfont.woff
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\font\iconfont.woff
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\icon_font\iconfont.woff
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\laydate\default\font\iconfont.woff
++iconfont.woff2
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\font\iconfont.woff2
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\icon_font\iconfont.woff2
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\font\web_font\iconfont.woff2
++formSelects-v3.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\formselects\formselects-v3.js
++formSelects-v4.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\formselects\formselects-v4.css
++formSelects-v4.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\formselects\formselects-v4.js
++demo.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\icon_font\demo.css
++demo_index.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\icon_font\demo_index.html
++iconfont.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\icon_font\iconfont.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\font\eyes_icon\iconfont.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\font\web_font\iconfont.css
++iconfont.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\icon_font\iconfont.js
++iconfont.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\icon_font\iconfont.json
++face
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\
++layui.all.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\layui.all.js
++soulTable.slim.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui-soultable\soultable.slim.js
++echarts.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\lib\extend\echarts.js
++echartsTheme.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\lib\extend\echartstheme.js
++template
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\style\res\template\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\style\res\template\
++bg-none.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\style\res\bg-none.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\style\res\bg-none.jpg
++layui-logo.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\style\res\layui-logo.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\style\res\layui-logo.jpg
++logo-black.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\style\res\logo-black.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\style\res\logo-black.png
++about.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\tpl\system\about.html
++get.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\tpl\system\get.html
++more.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\tpl\system\more.html
++theme.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\tpl\system\theme.html
++audio_style.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\audio\css\audio_style.css
++audio_icon.psd
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\audio\img\audio_icon.psd
++iconloop.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\audio\img\iconloop.png
++jquery-1.8.3.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\audioypbf\js\jquery-1.8.3.min.js
++jweixin-1.0.0.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\audioypbf\js\jweixin-1.0.0.js
++guanju.mp3
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\audioypbf\mp3\guanju.mp3
++mov_bbb.mp4
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\audioypbf\mp3\mov_bbb.mp4
++你有新短消息.mp3
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\audioypbf\mp3\你有新短消息.mp3
++新的询价请回复.mp3
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\audioypbf\mp3\新的询价请回复.mp3
++新的询价委托.mp3
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\audioypbf\mp3\新的询价委托.mp3
++新的在线消息.mp3
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\audioypbf\mp3\新的在线消息.mp3
++codemirror.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\lib\codemirror.css
++codemirror.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\lib\codemirror.js
++apl
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\apl\
++asciiarmor
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\asciiarmor\
++asn.1
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\asn.1\
++asterisk
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\asterisk\
++brainfuck
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\brainfuck\
++clike
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\clike\
++clojure
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\clojure\
++cmake
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\cmake\
++cobol
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\cobol\
++coffeescript
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\coffeescript\
++commonlisp
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\commonlisp\
++crystal
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\crystal\
++cypher
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\cypher\
++d
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\d\
++dart
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\dart\
++diff
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\diff\
++django
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\django\
++dockerfile
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\dockerfile\
++dtd
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\dtd\
++dylan
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\dylan\
++ebnf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\ebnf\
++ecl
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\ecl\
++eiffel
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\eiffel\
++elm
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\elm\
++erlang
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\erlang\
++factor
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\factor\
++fcl
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\fcl\
++forth
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\forth\
++fortran
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\fortran\
++gas
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\gas\
++gfm
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\gfm\
++gherkin
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\gherkin\
++go
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\go\
++groovy
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\groovy\
++haml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\haml\
++handlebars
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\handlebars\
++haskell
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\haskell\
++haskell-literate
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\haskell-literate\
++haxe
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\haxe\
++htmlembedded
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\htmlembedded\
++htmlmixed
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\htmlmixed\
++http
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\http\
++idl
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\idl\
++javascript
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\javascript\
++jinja2
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\jinja2\
++jsx
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\jsx\
++julia
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\julia\
++livescript
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\livescript\
++lua
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\lua\
++markdown
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\markdown\
++mathematica
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mathematica\
++mbox
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mbox\
++mirc
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mirc\
++mllike
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mllike\
++modelica
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\modelica\
++mscgen
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mscgen\
++mumps
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mumps\
++nginx
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\nginx\
++nsis
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\nsis\
++ntriples
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\ntriples\
++octave
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\octave\
++oz
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\oz\
++pascal
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\pascal\
++pegjs
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\pegjs\
++perl
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\perl\
++php
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\php\
++pig
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\pig\
++powershell
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\powershell\
++properties
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\properties\
++protobuf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\protobuf\
++pug
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\pug\
++puppet
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\puppet\
++python
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\python\
++q
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\q\
++r
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\r\
++rpm
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\rpm\
++rst
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\rst\
++ruby
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\ruby\
++rust
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\rust\
++sas
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\sas\
++sass
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\sass\
++scheme
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\scheme\
++shell
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\shell\
++sieve
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\sieve\
++slim
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\slim\
++smalltalk
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\smalltalk\
++smarty
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\smarty\
++solr
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\solr\
++soy
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\soy\
++sparql
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\sparql\
++spreadsheet
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\spreadsheet\
++sql
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\sql\
++stex
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\stex\
++stylus
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\stylus\
++swift
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\swift\
++tcl
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\tcl\
++textile
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\textile\
++tiddlywiki
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\tiddlywiki\
++tiki
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\tiki\
++toml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\toml\
++tornado
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\tornado\
++troff
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\troff\
++ttcn
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\ttcn\
++ttcn-cfg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\ttcn-cfg\
++turtle
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\turtle\
++twig
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\twig\
++vb
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\vb\
++vbscript
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\vbscript\
++velocity
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\velocity\
++verilog
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\verilog\
++vhdl
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\vhdl\
++vue
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\vue\
++webidl
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\webidl\
++xml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\xml\
++xquery
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\xquery\
++yacas
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\yacas\
++yaml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\yaml\
++yaml-frontmatter
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\yaml-frontmatter\
++z80
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\z80\
++meta.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\meta.js
++3024-day.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\3024-day.css
++3024-night.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\3024-night.css
++abcdef.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\abcdef.css
++ambiance.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\ambiance.css
++ambiance-mobile.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\ambiance-mobile.css
++base16-dark.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\base16-dark.css
++base16-light.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\base16-light.css
++bespin.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\bespin.css
++blackboard.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\blackboard.css
++cobalt.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\cobalt.css
++colorforth.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\colorforth.css
++dracula.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\dracula.css
++eclipse.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\eclipse.css
++elegant.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\elegant.css
++erlang-dark.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\erlang-dark.css
++hopscotch.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\hopscotch.css
++icecoder.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\icecoder.css
++isotope.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\isotope.css
++lesser-dark.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\lesser-dark.css
++liquibyte.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\liquibyte.css
++material.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\material.css
++mbo.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\mbo.css
++mdn-like.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\mdn-like.css
++midnight.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\midnight.css
++monokai.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\monokai.css
++neat.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\neat.css
++neo.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\neo.css
++night.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\night.css
++panda-syntax.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\panda-syntax.css
++paraiso-dark.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\paraiso-dark.css
++paraiso-light.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\paraiso-light.css
++pastel-on-dark.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\pastel-on-dark.css
++railscasts.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\railscasts.css
++rubyblue.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\rubyblue.css
++seti.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\seti.css
++solarized.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\solarized.css
++the-matrix.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\the-matrix.css
++tomorrow-night-bright.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\tomorrow-night-bright.css
++tomorrow-night-eighties.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\tomorrow-night-eighties.css
++ttcn.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\ttcn.css
++twilight.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\twilight.css
++vibrant-ink.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\vibrant-ink.css
++xq-dark.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\xq-dark.css
++xq-light.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\xq-light.css
++yeti.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\yeti.css
++zenburn.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\theme\zenburn.css
++jquery.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery\dist\jquery.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\jquery.js
++ystep.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-steps\css\ystep.css
++setStep.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-steps\js\setstep.js
++additional-methods.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-validation\dist\additional-methods.js
++jquery.validate.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-validation\dist\jquery.validate.js
++jquery.validate.unobtrusive.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.min.js
++common
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\kindeditor\themes\common\
++default
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\kindeditor\themes\default\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\laydate\default\
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layer\default\
++qq
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\kindeditor\themes\qq\
++simple
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\kindeditor\themes\simple\
++liMarquee.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\limarquee\css\limarquee.css
++jquery.liMarquee.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\limarquee\js\jquery.limarquee.js
++mui.min.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\muihk\css\mui.min.css
++mui.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\muihk\js\mui.min.js
++mui-player.min.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\mui-player\css\mui-player.min.css
++mui-player.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\mui-player\js\mui-player.min.js
++病程记录.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\病历文书\病程记录.pdf
++出院小结.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\病历文书\出院小结.pdf
++入院记录.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\病历文书\入院记录.pdf
++1364772962MR.PDF
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检查\1364772962mr.pdf
++2024.04.17妇科彩超.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检查\2024.04.17妇科彩超.pdf
++2024.05.06妇科彩超.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检查\2024.05.06妇科彩超.pdf
++彩色多普勒(PDF).pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检查\彩色多普勒(pdf).pdf
++早孕彩超(PDF).pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检查\早孕彩超(pdf).pdf
++2024.04.11 TM.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\2024.04.11 tm.pdf
++2024.04.11 血小板聚集.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\2024.04.11 血小板聚集.pdf
++2024.04.11生化.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\2024.04.11生化.pdf
++2024.04.11血常规.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\2024.04.11血常规.pdf
++2024.05.06甲状腺相关.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\2024.05.06甲状腺相关.pdf
++2024.05.10补体相关.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\2024.05.10补体相关.pdf
++2024.05.10抗心等相关.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\2024.05.10抗心等相关.pdf
++2024.05.20尿常规.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\2024.05.20尿常规.pdf
++2024.06.14 凝血功能.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\2024.06.14 凝血功能.pdf
++2024.06.14 凝血功能_1.pdf
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\2024.06.14 凝血功能_1.pdf
++TM.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\tm.png
++补体.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\补体.png
++甲状腺.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\甲状腺.png
++抗心.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\抗心.png
++尿常规.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\尿常规.png
++凝血功能.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\凝血功能.png
++凝血功能_1.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\凝血功能_1.png
++生化.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\生化.png
++血常规.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\血常规.png
++血小板聚集.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\medicaldoc\**********的病历资料\检验\血小板聚集.png
++bootstrap-icons.woff
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\css\fonts\bootstrap-icons.woff
++bootstrap-icons.woff2
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\querybuilder\css\fonts\bootstrap-icons.woff2
++BookStatistics.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\report\views\report\bookstatistics.cshtml
++CrossReport.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\report\views\report\crossreport.cshtml
++DeptBillAnalysisReport.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\report\views\report\deptbillanalysisreport.cshtml
++DrugStatistics.cshtml
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\areas\report\views\report\drugstatistics.cshtml
++laydate
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\laydate\
++code.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\code.css
++formSelects-v4.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\formselects\formselects-v4.min.js
++0.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\0.gif
++1.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\1.gif
++10.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\10.gif
++11.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\11.gif
++12.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\12.gif
++13.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\13.gif
++14.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\14.gif
++15.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\15.gif
++16.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\16.gif
++17.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\17.gif
++18.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\18.gif
++19.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\19.gif
++2.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\2.gif
++20.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\20.gif
++21.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\21.gif
++22.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\22.gif
++23.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\23.gif
++24.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\24.gif
++25.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\25.gif
++26.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\26.gif
++27.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\27.gif
++28.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\28.gif
++29.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\29.gif
++3.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\3.gif
++30.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\30.gif
++31.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\31.gif
++32.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\32.gif
++33.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\33.gif
++34.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\34.gif
++35.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\35.gif
++36.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\36.gif
++37.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\37.gif
++38.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\38.gif
++39.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\39.gif
++4.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\4.gif
++40.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\40.gif
++41.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\41.gif
++42.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\42.gif
++43.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\43.gif
++44.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\44.gif
++45.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\45.gif
++46.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\46.gif
++47.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\47.gif
++48.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\48.gif
++49.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\49.gif
++5.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\5.gif
++50.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\50.gif
++51.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\51.gif
++52.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\52.gif
++53.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\53.gif
++54.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\54.gif
++55.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\55.gif
++56.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\56.gif
++57.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\57.gif
++58.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\58.gif
++59.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\59.gif
++6.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\6.gif
++60.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\60.gif
++61.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\61.gif
++62.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\62.gif
++63.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\63.gif
++64.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\64.gif
++65.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\65.gif
++66.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\66.gif
++67.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\67.gif
++68.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\68.gif
++69.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\69.gif
++7.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\7.gif
++70.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\70.gif
++71.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\71.gif
++8.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\8.gif
++9.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\images\face\9.gif
++carousel.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\carousel.js
++code.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\code.js
++colorpicker.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\colorpicker.js
++element.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\element.js
++flow.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\flow.js
++form.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\form.js
++laydate.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\laydate.js
++layedit.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\layedit.js
++layer.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\layer.js
++layim.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\layim.js
++laypage.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\laypage.js
++laytpl.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\laytpl.js
++mobile.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\mobile.js
++rate.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\rate.js
++slider.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\slider.js
++table.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\table.js
++transfer.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\transfer.js
++tree.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\tree.js
++treeNew.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\treenew.js
++upload.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\upload.js
++util.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\lay\modules\util.js
++character.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\style\res\template\character.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\style\res\template\character.jpg
++huge.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\style\res\template\huge.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\style\res\template\huge.jpg
++portrait.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\style\res\template\portrait.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\style\res\template\portrait.png
++bootstrap.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\css\bootstrap.css
++bootstrap-grid.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.css
++bootstrap-reboot.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.css
++bootstrap.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\js\bootstrap.js
++apl.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\apl\apl.js
++asciiarmor.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\asciiarmor\asciiarmor.js
++asn.1.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\asn.1\asn.1.js
++asterisk.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\asterisk\asterisk.js
++brainfuck.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\brainfuck\brainfuck.js
++clike.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\clike\clike.js
++scala.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\clike\scala.html
++test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\clike\test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\css\test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\dylan\test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\gfm\test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\haml\test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\javascript\test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\jsx\test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\markdown\test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\php\test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\powershell\test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\python\test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\ruby\test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\rust\test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\shell\test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\slim\test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\stex\test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\textile\test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\verilog\test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\xml\test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\xquery\test.js
++clojure.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\clojure\clojure.js
++cmake.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\cmake\cmake.js
++cobol.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\cobol\cobol.js
++coffeescript.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\coffeescript\coffeescript.js
++commonlisp.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\commonlisp\commonlisp.js
++crystal.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\crystal\crystal.js
++css.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\css\css.js
++gss.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\css\gss.html
++gss_test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\css\gss_test.js
++less.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\css\less.html
++less_test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\css\less_test.js
++scss.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\css\scss.html
++scss_test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\css\scss_test.js
++cypher.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\cypher\cypher.js
++d.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\d\d.js
++dart.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\dart\dart.js
++diff.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\diff\diff.js
++django.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\django\django.js
++dockerfile.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\dockerfile\dockerfile.js
++dtd.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\dtd\dtd.js
++dylan.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\dylan\dylan.js
++ebnf.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\ebnf\ebnf.js
++ecl.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\ecl\ecl.js
++eiffel.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\eiffel\eiffel.js
++elm.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\elm\elm.js
++erlang.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\erlang\erlang.js
++factor.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\factor\factor.js
++fcl.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\fcl\fcl.js
++forth.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\forth\forth.js
++fortran.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\fortran\fortran.js
++gas.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\gas\gas.js
++gfm.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\gfm\gfm.js
++gherkin.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\gherkin\gherkin.js
++go.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\go\go.js
++groovy.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\groovy\groovy.js
++haml.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\haml\haml.js
++handlebars.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\handlebars\handlebars.js
++haskell.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\haskell\haskell.js
++haskell-literate.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\haskell-literate\haskell-literate.js
++haxe.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\haxe\haxe.js
++htmlembedded.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\htmlembedded\htmlembedded.js
++htmlmixed.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\htmlmixed\htmlmixed.js
++http.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\http\http.js
++idl.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\idl\idl.js
++javascript.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\javascript\javascript.js
++json-ld.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\javascript\json-ld.html
++typescript.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\javascript\typescript.html
++jinja2.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\jinja2\jinja2.js
++jsx.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\jsx\jsx.js
++julia.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\julia\julia.js
++livescript.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\livescript\livescript.js
++lua.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\lua\lua.js
++markdown.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\markdown\markdown.js
++mathematica.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mathematica\mathematica.js
++mbox.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mbox\mbox.js
++mirc.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mirc\mirc.js
++mllike.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mllike\mllike.js
++modelica.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\modelica\modelica.js
++mscgen.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mscgen\mscgen.js
++mscgen_test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mscgen\mscgen_test.js
++msgenny_test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mscgen\msgenny_test.js
++xu_test.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mscgen\xu_test.js
++mumps.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\mumps\mumps.js
++nginx.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\nginx\nginx.js
++nsis.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\nsis\nsis.js
++ntriples.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\ntriples\ntriples.js
++octave.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\octave\octave.js
++oz.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\oz\oz.js
++pascal.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\pascal\pascal.js
++pegjs.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\pegjs\pegjs.js
++perl.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\perl\perl.js
++php.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\php\php.js
++pig.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\pig\pig.js
++powershell.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\powershell\powershell.js
++properties.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\properties\properties.js
++protobuf.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\protobuf\protobuf.js
++pug.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\pug\pug.js
++puppet.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\puppet\puppet.js
++python.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\python\python.js
++q.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\q\q.js
++r.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\r\r.js
++changes
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\rpm\changes\
++rpm.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\rpm\rpm.js
++rst.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\rst\rst.js
++ruby.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\ruby\ruby.js
++rust.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\rust\rust.js
++sas.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\sas\sas.js
++sass.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\sass\sass.js
++scheme.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\scheme\scheme.js
++shell.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\shell\shell.js
++sieve.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\sieve\sieve.js
++slim.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\slim\slim.js
++smalltalk.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\smalltalk\smalltalk.js
++smarty.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\smarty\smarty.js
++solr.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\solr\solr.js
++soy.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\soy\soy.js
++sparql.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\sparql\sparql.js
++spreadsheet.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\spreadsheet\spreadsheet.js
++sql.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\sql\sql.js
++stex.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\stex\stex.js
++stylus.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\stylus\stylus.js
++swift.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\swift\swift.js
++tcl.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\tcl\tcl.js
++textile.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\textile\textile.js
++tiddlywiki.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\tiddlywiki\tiddlywiki.css
++tiddlywiki.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\tiddlywiki\tiddlywiki.js
++tiki.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\tiki\tiki.css
++tiki.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\tiki\tiki.js
++toml.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\toml\toml.js
++tornado.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\tornado\tornado.js
++troff.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\troff\troff.js
++ttcn.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\ttcn\ttcn.js
++ttcn-cfg.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\ttcn-cfg\ttcn-cfg.js
++turtle.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\turtle\turtle.js
++twig.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\twig\twig.js
++vb.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\vb\vb.js
++vbscript.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\vbscript\vbscript.js
++velocity.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\velocity\velocity.js
++verilog.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\verilog\verilog.js
++vhdl.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\vhdl\vhdl.js
++vue.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\vue\vue.js
++webidl.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\webidl\webidl.js
++xml.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\xml\xml.js
++xquery.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\xquery\xquery.js
++yacas.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\yacas\yacas.js
++yaml.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\yaml\yaml.js
++yaml-frontmatter.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\yaml-frontmatter\yaml-frontmatter.js
++z80.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\codemirror\mode\z80\z80.js
++pointes_blue.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-steps\css\images\pointes_blue.png
++pointes_green.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-steps\css\images\pointes_green.png
++additional-methods.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-validation\dist\additional-methods.min.js
++jquery.validate.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery-validation\dist\jquery.validate.min.js
++anchor.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\kindeditor\themes\common\anchor.gif
++blank.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\kindeditor\themes\common\blank.gif
++flash.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\kindeditor\themes\common\flash.gif
++loading.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\kindeditor\themes\common\loading.gif
++media.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\kindeditor\themes\common\media.gif
++rm.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\kindeditor\themes\common\rm.gif
++background.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\kindeditor\themes\default\background.png
++default.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\kindeditor\themes\default\default.css
++default.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\kindeditor\themes\default\default.png
++editor.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\kindeditor\themes\qq\editor.gif
++qq.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\kindeditor\themes\qq\qq.css
++simple.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\kindeditor\themes\simple\simple.css
++swiper.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\swiper\dist\css\swiper.css
++maps
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\swiper\dist\js\maps\
++swiper.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\swiper\dist\js\swiper.js
++html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layim\html\
++skin
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layim\skin\
++voice
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layim\voice\
++layim.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layim\layim.css
++bootstrap.css.map
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\css\bootstrap.css.map
++bootstrap-grid.css.map
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.css.map
++bootstrap-grid.min.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.min.css
++bootstrap-reboot.css.map
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.css.map
++bootstrap-reboot.min.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.min.css
++bootstrap.bundle.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.js
++bootstrap.js.map
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\js\bootstrap.js.map
++jquery.min.map
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\jquery\dist\jquery.min.map
++swiper.min.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\swiper\dist\css\swiper.min.css
++swiper.jquery.min.js.map
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\swiper\dist\js\maps\swiper.jquery.min.js.map
++swiper.jquery.umd.min.js.map
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\swiper\dist\js\maps\swiper.jquery.umd.min.js.map
++swiper.min.js.map
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\swiper\dist\js\maps\swiper.min.js.map
++swiper.jquery.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\swiper\dist\js\swiper.jquery.js
++swiper.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\swiper\dist\js\swiper.min.js
++laydate.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\laydate\default\laydate.css
++icon.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layer\default\icon.png
++icon-ext.png
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layer\default\icon-ext.png
++layer.css
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layer\default\layer.css
++loading-0.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layer\default\loading-0.gif
++loading-1.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layer\default\loading-1.gif
++loading-2.gif
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layer\default\loading-2.gif
++chatlog.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layim\html\chatlog.html
++find.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layim\html\find.html
++getmsg.json
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layim\html\getmsg.json
++msgbox.html
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layim\html\msgbox.html
++1.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layim\skin\1.jpg
++5.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layim\skin\5.jpg
++logo.jpg
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layim\skin\logo.jpg
++default.mp3
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\layuiadmin\layui\css\modules\layim\voice\default.mp3
++bootstrap.min.css.map
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\css\bootstrap.min.css.map
++bootstrap-grid.min.css.map
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.min.css.map
++bootstrap-reboot.min.css.map
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.min.css.map
++bootstrap.bundle.js.map
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.js.map
++bootstrap.bundle.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js
++bootstrap.min.js.map
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\js\bootstrap.min.js.map
++swiper.jquery.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\swiper\dist\js\swiper.jquery.min.js
++swiper.jquery.umd.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\swiper\dist\js\swiper.jquery.umd.js
++bootstrap.bundle.min.js.map
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js.map
++swiper.jquery.umd.min.js
i:{************************************}:d:\work space\project\三部\科研ai demo\angelwinresearch.webui\wwwroot\lib\swiper\dist\js\swiper.jquery.umd.min.js
