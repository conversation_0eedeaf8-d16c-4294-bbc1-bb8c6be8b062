#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "19dd668d5cb7afd0430457646b3d9e7b7b882ed459b9b1ef65c3dde55c6fe7a2"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_ReportingManage_Views_PatientManage_DataCollecPC), @"mvc.1.0.view", @"/Areas/ReportingManage/Views/PatientManage/DataCollecPC.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"19dd668d5cb7afd0430457646b3d9e7b7b882ed459b9b1ef65c3dde55c6fe7a2", @"/Areas/ReportingManage/Views/PatientManage/DataCollecPC.cshtml")]
    #nullable restore
    internal sealed class Areas_ReportingManage_Views_PatientManage_DataCollecPC : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<
#nullable restore
#line 5 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
       AngelwinResearch.Models.ResearchPatient

#line default
#line hidden
#nullable disable
    >
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/muihk/css/mui.min.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/font/eyes_icon/iconfont.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/columnDrag/column_drag.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/jquery/dist/jquery.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/marked.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/muihk/js/mui.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_13 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("type", new global::Microsoft.AspNetCore.Html.HtmlString("text/javascript"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_14 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/columnDrag/column_drag.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_15 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
  
    ViewBag.Title = "数据采集";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("<!DOCTYPE html>\r\n<html lang=\"en\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "19dd668d5cb7afd0430457646b3d9e7b7b882ed459b9b1ef65c3dde55c6fe7a29631", async() => {
                WriteLiteral("\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\">\r\n\r\n    <meta http-equiv=\"X-UA-Compatible\" content=\"ie=edge\">\r\n    <title>数据采集</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "19dd668d5cb7afd0430457646b3d9e7b7b882ed459b9b1ef65c3dde55c6fe7a210164", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "19dd668d5cb7afd0430457646b3d9e7b7b882ed459b9b1ef65c3dde55c6fe7a211367", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "19dd668d5cb7afd0430457646b3d9e7b7b882ed459b9b1ef65c3dde55c6fe7a212570", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "19dd668d5cb7afd0430457646b3d9e7b7b882ed459b9b1ef65c3dde55c6fe7a213773", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "19dd668d5cb7afd0430457646b3d9e7b7b882ed459b9b1ef65c3dde55c6fe7a214976", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "19dd668d5cb7afd0430457646b3d9e7b7b882ed459b9b1ef65c3dde55c6fe7a216179", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "19dd668d5cb7afd0430457646b3d9e7b7b882ed459b9b1ef65c3dde55c6fe7a217303", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "19dd668d5cb7afd0430457646b3d9e7b7b882ed459b9b1ef65c3dde55c6fe7a218427", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"

    <style>

        /* 移动端头部 */
        .headrow {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            background-color: #f6f6f6;
            height: 38px;
            line-height: 38px;
        }

            .headrow .layui-btn-primary {
                background-color: transparent;
                border: none;
                color: #333;
            }
        /* 移动端头部 */



        .flex_row {
            display: flex;
            flex-direction: row;
        }

        .space_between {
            justify-content: space-between;
            align-items: center;
        }

        .layui-card {
            margin-bottom: 5px;
        }

        .content-inline {
            display: flex;
            flex-direction: row;
            line-height: 34px;
            padding-left: 20px;
        }

        .patient_information {
            align-items: center;
        }

        .user_informat");
                WriteLiteral(@"ion {
            flex: 1;
            flex-wrap: wrap;
            background-color: #fff;
            padding: 10px 0;
        }

        .user_name {
            padding-right: 20px;
            line-height: 38px;
            font-size: 18px;
        }

        .user_value {
            color: #000;
            padding-left: 10px;
        }

        .reporting_content {
            position: relative;
        }

        .content_R {
            position: relative;
            flex: 1;
        }

        .sex {
            font-size: 14px;
            font-weight: normal;
        }

        .sex0 {
            color: #FF5722;
        }

        .sex1 {
            color: #1E9FFF;
        }

        .reporting {
            overflow: hidden;
        }


        .nav_item {
            background-color: #fff;
            border: 1px solid #eee;
            padding: 10px 15px;
            border-radius: 4px;
            margin: 0 5px 10px;
        }

       ");
                WriteLiteral(@" .active {
            background: linear-gradient(70deg, #dcffff, #62ffff);
            border-color: transparent;
            color: #1E9FFF;
        }

            .active .layui-progress-text {
                color: #1E9FFF;
            }

        .layui-colla-content {
            padding: 0;
        }

            .layui-colla-content .layui-card-body {
                padding: 10px 0;
            }

        .search_hidden {
            text-align: center;
        }


            .search_hidden i {
                font-size: 22px;
                margin-right: 5px;
            }
        /* 折叠状态下左侧菜单的宽度 */
        .sider_btn {
            position: absolute;
            display: block;
            left: -5px;
            bottom: 10%;
            z-index: 99;
            line-height: 0;
            padding: 18px 0px 18px 0px;
            border: none;
            color: #fff;
            border-radius: 0 50% 50% 0;
            background: linear-gradient(to right, #");
                WriteLiteral(@"56d1f9, #1eaddc);
        }

        .sider_btn_R {
            position: absolute;
            display: block;
            right: -5px;
            bottom: 10%;
            z-index: 99;
            line-height: 0;
            padding: 18px 0px 18px 4px;
            border: none;
            color: #fff;
            border-radius: 50% 0 0 50%;
            background: linear-gradient(to right, #56d1f9, #1eaddc);
        }

        .sider_btn i {
            font-size: 22px !important;
            padding-right: 10px;
        }

        .sider_btn_R i {
            font-size: 22px !important;
            padding-left: 5px;
            padding-right: 5px;
        }

        .Menu_side {
            width: 220px;
            overflow-y: auto;
            overflow-x: hidden;
            transition: width 0.5s ease;
            flex: none;
        }

            .Menu_side.folded {
                width: 0;
            }


        .Menu_sideR {
            width: 0px;
    ");
                WriteLiteral(@"        overflow-y: auto;
            overflow-x: hidden;
            transition: width 0.5s ease;
        }

            .Menu_sideR.folded {
                width: 500px;
            }

            .Menu_sideR .layui-textarea {
                min-height: 100% !important;
                resize: none;
                font-size: 14px;
            }

        .side_menu_title {
            font-size: 18px;
            color: #000;
            text-align: center;
            line-height: 30px;
            padding: 10px 0;
        }

        .content_L {
            overflow-y: auto;
        }

        .mui-btn .layui-icon {
            font-size: 24px;
        }

        .hide_report_list {
            padding: 15px;
        }

        .stretch {
            padding-right: 20px;
            flex-grow: 1;
        }

        .collapse_list {
            overflow-y: auto;
        }

        .null_wrap {
            text-align: center;
            margin-top: 10%;
    ");
                WriteLiteral(@"    }

            .null_wrap img {
                width: 200px;
            }

        .left-panel {
            width: 20%;
        }

        .right-panel {
            width: 80%;
        }

        .line_center {
            margin-top: -8px;
            align-items: center;
        }

        .ai_btn {
            width: 38px;
            height: 38px;
            background-color: #f6f6f6;
            border-radius: 2px;
            cursor: pointer;
        }

            .ai_btn img {
                width: 100%;
                height: 100%;
            }

        .bottom_line {
            border-bottom: 1px solid #000;
            padding-bottom: 5px;
            margin-right: 10px;
        }

        .title_wrap {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .tools_wrap {
            display: flex;
            flex-direction: row;
            justify-content: space-between;");
                WriteLiteral(@"
        }

        .tools_btn {
            height: 30px;
            padding: 2px 5px;
            border: 1px solid #1E9FFF;
            border-radius: 2px;
            margin: 0 1px;
            cursor: pointer;
        }

        .tools_wrap .layui-icon {
            font-size: 18px;
        }

        .form_name {
            margin-bottom: 15px;
            flex: 1;
        }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "19dd668d5cb7afd0430457646b3d9e7b7b882ed459b9b1ef65c3dde55c6fe7a227032", async() => {
                WriteLiteral(@"
    <div class=""wrap"">
        <div class=""head_wrap patient_information"" id=""IpadHead"">
            <div class=""headrow"">
                <div class=""left"">
                    <button id=""previous"" class=""layui-btn layui-btn-primary layui-border-green "" style=""margin-right:15px;display:none""><i class=""layui-icon layui-icon-return""></i></button>
                </div>
                <div class=""middle"">
                    <h2 class=""user_name"">");
                Write(
#nullable restore
#line 328 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                           Model?.PatientName

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(" <span");
                BeginWriteAttribute("class", " class=\"", 7966, "\"", 8018, 1);
                WriteAttributeValue("", 7974, 
#nullable restore
#line 328 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                                                             Model?.Sex=="男" ? "sex sex1" : "sex sex0"

#line default
#line hidden
#nullable disable
                , 7974, 44, false);
                EndWriteAttribute();
                WriteLiteral(">");
                Write(
#nullable restore
#line 328 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                                                                                                          Model.Sex

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</span></h2>
                </div>
                <div class=""right"">
                    <div id=""popbtn"" style=""padding-right:10px;"">
                        <i class=""layui-icon layui-icon-survey"" style=""font-size:24px;""></i>
                    </div>

                </div>
            </div>
            <div class=""user_information flex_row space_between"">
                <div class=""flex_row user_information_left"">
                    <div class=""content-inline"">编号：<p class=""user_value"">");
                Write(
#nullable restore
#line 339 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                                                          Model?.ResearchID

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</p></div>\r\n                    <div class=\"content-inline\">专病组：<p class=\"user_value\">");
                Write(
#nullable restore
#line 340 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                                                           Model?.DiseaseSpecificGroup.GroupName

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</p></div>
                    <div class=""content-inline""> <button class=""layui-btn layui-btn-primary"" id=""btnAI"" title=""AI提取数据""><i class=""layui-icon icon-zhinengwendangchouqu""></i></button></div>
                    <div class=""content-inline""><div class=""ai_btn AutoFillIn"" id=""aiButton"" title=""AI批量提取"" isclick=""true""><img src=""/images/AI_icon.png"" /></div></div>
                    <div class=""content-inline"" style=""margin-top:10px;"">
                        <div id=""BatchExtractionProgress"" class=""layui-progress layui-progress-big herfform"" lay-showPercent=""true"" title=""批量采集进度""");
                BeginWriteAttribute("view", " view=\"", 9275, "\"", 9295, 1);
                WriteAttributeValue("", 9282, 
#nullable restore
#line 344 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                                                                                                                                          ViewBag.View

#line default
#line hidden
#nullable disable
                , 9282, 13, false);
                EndWriteAttribute();
                WriteLiteral(" style=\" cursor: pointer; display: block; width: 400px\">\r\n                            <div class=\"layui-progress-bar\"");
                BeginWriteAttribute("lay-percent", " lay-percent=\"", 9413, "\"", 9462, 3);
                WriteAttributeValue("", 9427, 
#nullable restore
#line 345 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                                                          ViewBag.dataCount

#line default
#line hidden
#nullable disable
                , 9427, 18, false);
                WriteAttributeValue("", 9445, "/", 9445, 1, true);
                WriteAttributeValue("", 9446, 
#nullable restore
#line 345 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                                                                             ViewBag.dataSum

#line default
#line hidden
#nullable disable
                , 9446, 16, false);
                EndWriteAttribute();
                WriteLiteral(@"></div>
                        </div>
                    </div>
                </div>
                <div class=""crf_btn_group"">
                    <div class=""status-bar"">
                        <div class=""bottom_line status-bar"">
                            <div class=""status-item active"">存在质疑(X)</div>
                            <div class=""status-item"">待审核(X)</div>
                        </div>
                        <button class=""layui-btn"">全部审核</button>
                        <button class=""layui-btn"">已审核</button>
                        <button class=""layui-btn layui-btn-danger"">质疑</button>
");
                WriteLiteral("                    </div>\r\n                </div>\r\n                <input type=\"hidden\" id=\"AIExtractJsonValue\" />\r\n            </div>\r\n        </div>\r\n\r\n");
                WriteLiteral(@"
        <div class=""reporting_content flex_row"">
            <!-- 左侧折叠菜单 -->
            <div id=""sideMenu"" class=""Menu_side content_L layui-card mui-table-view"">
                <div class=""layui-collapse"" id=""OA_task_1"" style=""height:100%;"">
                    <div class=""collapse_list""></div>
                </div>
            </div>


            <div class=""left-panel"">
                <div class=""layui-card mui-table-view"" style=""height:100%;position:relative;"">

                    <div id=""trees"" style=""width:100%; height:100%;overflow-y:auto;display:none""></div>
                    <div style=""width:100%; height:100%;overflow-y:auto"" id=""divLoading"">
                        <i class=""layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"" style=""font-size:50px;margin-left:50%;margin-top:50%;""></i>
                    </div>
                    <!--菜单折叠-->
                    <button type=""button"" id=""toggleSidebar"" class=""layui-btn layui-btn-sm layui-btn-primar");
                WriteLiteral(@"y sider_btn"">
                        <i class=""layui-icon layui-icon-left"" style=""font-size:22px;""></i>
                    </button>

                </div>
            </div>
            <div class=""resizer"">
                <div class=""resize_btn"">⋮</div>
            </div>
            <div class=""right-panel"" style=""height:100%;"">
                <!-- 填报内容区域 -->
                <div id=""mainContent"" class=""reporting content_R"" style=""width:100%;height:100%;"">




                    <!-- 这里是主体内容 -->
                    <iframe id=""reportingFrom"" frameborder=""0"" width=""100%"" height=""100%""></iframe>


                    <!--折叠消息-->
                    <button type=""button"" id=""toggleSidebarR"" class=""layui-btn layui-btn-sm layui-btn-primary sider_btn_R"">
                        <i class=""layui-icon layui-icon-left"" style=""font-size:22px;""></i>
                    </button>


                </div>
            </div>


            <!-- 右侧折叠菜 -->
            <div id=""sideMenuR""");
                WriteLiteral(@" class=""Menu_sideR content_L layui-card mui-table-view"">
                <div class=""layui-form layui-form-pane"" style=""height:100%"">
                    <div class=""layui-form-item layui-form-text"" style=""height:100%"" id=""divSelected"">
                    </div>
                </div>
            </div>

        </div>


    </div>

    <!--隐藏报表列表-->
    <div id=""hideReport"" style=""display:none"">
        <div class=""hide_report_list"">
            <ul id=""ulHideList"">
                <li class=""nav_item flex_row"" data-id="""" data-formId="""">
                    <div class=""stretch"">
                        <p class=""form_name"">随访信息-影像学检查-IM</p>
                        <div class=""layui-progress"" lay-showPercent=""true"">
                            <div class=""layui-progress-bar layui-bg-blue"" lay-percent=""5/10""></div>
                        </div>
                    </div>
                    <a class=""mui-btn mui-btn-red show_report"">
                        <i class=""layui-icon layui-");
                WriteLiteral(@"icon-add-circle""></i>
                    </a>
                </li>
            </ul>
        </div>
    </div>
    <div style=""display: none;"" id=""isShow"">
        <div class=""mui-popup mui-popup-in"">
            <div class=""mui-popup-inner"">
                <div class=""mui-popup-title"">提示</div>
                <div class=""mui-popup-text"">是否展示当前报表？</div>
            </div>
            <div class=""mui-popup-buttons"">
                <span class=""mui-popup-button Confirmation_btn"">确认</span>
                <span class=""mui-popup-button mui-popup-button-bold"">取消</span>
            </div>
        </div>
        <div class=""mui-popup-backdrop mui-active""></div>
    </div>

    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "19dd668d5cb7afd0430457646b3d9e7b7b882ed459b9b1ef65c3dde55c6fe7a237788", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "19dd668d5cb7afd0430457646b3d9e7b7b882ed459b9b1ef65c3dde55c6fe7a238912", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "19dd668d5cb7afd0430457646b3d9e7b7b882ed459b9b1ef65c3dde55c6fe7a240037", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    <!--滑动删除插件-->\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "19dd668d5cb7afd0430457646b3d9e7b7b882ed459b9b1ef65c3dde55c6fe7a241183", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "19dd668d5cb7afd0430457646b3d9e7b7b882ed459b9b1ef65c3dde55c6fe7a242312", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_14);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"

    <script>
        layui.use(['element', 'layer', 'table', 'laydate', 'upload', 'form', 'tree', 'laytpl'], function() {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , laydate = layui.laydate
                , $ = layui.$
                , form = layui.form
                , laytpl = layui.laytpl
                , upload = layui.upload
                , tree = layui.tree
                ;
            var id = 0;
            var firstId = 0;
            var formId = """";
            var businessDomain = """";

            $(document).ready(function () {
                if ($(""#BatchExtractionProgress"").attr(""view"") == ""true"")
                {
                    $(""#aiButton"").find(""img"").attr(""src"", ""/images/AI_icon2.png"");
                    $(""#aiButton"").attr(""isclick"", ""false"");
                    $(""#BatchExtractionProgress"").show();
                    element.render($(""#BatchExtraction");
                WriteLiteral(@"Progress""));
                }
                else {
                    $(""#aiButton"").find(""img"").attr(""src"", ""/images/AI_icon.png"");
                    $(""#aiButton"").attr(""isclick"", ""true"");
                    $(""#BatchExtractionProgress"").hide();
                }
                $(document).on('click', '#btnUpload', function () {
                    $(""#fileName"").val("""");
                    $(""#newFileName"").val("""");
                    $(""#guid"").val("""");
                    windowsIndex = layer.open({
                        type: 1,
                        title: '上传PDF文件',
                        area: '600px',
                        resize: true,
                        content: $('#form_window')
                    });

                });
                $(document).on('click', '#btnOperationLog', function () {
                    var RId = '");
                Write(
#nullable restore
#line 523 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                Model.ResearchID

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\';\r\n                    var deptId = \'");
                Write(
#nullable restore
#line 524 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                   Model.HospitalDeptId

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\';\r\n                    var groupId = \'");
                Write(
#nullable restore
#line 525 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                    Model.DiseaseSpecificGroupId

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"';
                    var url = ""/OperationHistory/Index?researchId="" + RId + ""&deptId="" + deptId + ""&groupId="" + groupId + ""&formIds="" +id;
                    parent.layui.index.openTabsPage(url, ""查看操作日志"");
                });
            });
            if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
                $(""#previous"").show();
            }
            else {
                $(""#previous"").hide();
            }
            $(""#previous"").click(function () {
                window.location.href = ""/ReportingManage/PatientManage/Index"";
            })
            $(""#BatchExtractionProgress"").click(function () {

                var url = ""/TaskManage/TaskInfoManage/Index?ResearchPatientId=");
                Write(
#nullable restore
#line 541 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                                                                Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@""";
                parent.layui.index.openTabsPage(url, ""采集任务"");
            })

            $(""#btnAI"").click(function () {
                AIExtract();
            });

            $("".AutoFillIn"").on(""click"", function (e) {
                e.preventDefault(); // 阻止默认行为
                var pId = '");
                Write(
#nullable restore
#line 551 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                            Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"';
                var isclick = $(""#aiButton"").attr(""isclick"");
                if (isclick==""true"") {
                    layer.confirm('您确定要进行自动填报操作吗？', {
                        title: '',
                        btn: ['确定', '取消'], //按钮
                        resize: false
                    }, function (index) {
                        $.post('/ReportingManage/PatientManage/AutoFillIn', { id: pId }, function (result) {
                            if (result.okMsg) {
                                layer.msg(result.okMsg);
                                $(""#BatchExtractionProgress"").show();
                            } else {
                                layer.msg(result.errorMsg);
                                $(""#BatchExtractionProgress"").hide();
                            }
                        }, 'json');
                        layer.close(index);
                    });
                }



            });

            $(""#popbtn"").on(""click"", function() {
    ");
                WriteLiteral(@"            layer.open({
                    type: 1,
                    area: ['60%', '80%'],
                    resize: false,
                    shadeClose: true,
                    title: '帮助',
                    content: $(""#popwrap""),
                    success: function() {
                        // $(""#model_wrapL"").val(modelText);
                    }
                })
            })
            $(document).on('click', '.btnDelete', function () {
                var Id = $(this).attr(""dataid"");
                var underscoreIndex = Id.lastIndexOf('_'); // 查找最后一个下划线的位置
                var Pid = Id.substring(underscoreIndex + 1); // 获取下划线后面的子字符串
                layer.confirm('您确定要将该文档删除吗？', {
                    title: '',
                    btn: ['确定', '取消'], //按钮
                    resize: false
                }, function (index) {
                        $.post('/ReportingManage/PatientManage/DeleteExternalReportsByID', { id: Pid }, function (result) {
             ");
                WriteLiteral(@"           if (result.okMsg) {
                            layer.msg(result.okMsg);
                            layer.closeAll();
                            getTree(businessDomain, id);
                        } else {
                            layer.msg(result.errorMsg);
                        }
                        }, 'json');
                        layer.close(index);
                });


            })

            // 点击折叠按钮时触发的函数
            document.getElementById('toggleSidebar').addEventListener('click', function() {

                var sideMenu = document.getElementById('sideMenu');

                var spreadIcon = document.getElementById('toggleSidebar');

                // 切换folded类来实现折叠效果
                sideMenu.classList.toggle('folded');

                // 根据菜单的状态切换图标显示
                if (sideMenu.classList.contains('folded')) {
                    spreadIcon.children[0].classList.remove('layui-icon-left'); // 显示展开图标
                    spreadIcon.childr");
                WriteLiteral(@"en[0].classList.add('layui-icon-right'); // 隐藏展开图标
                } else {
                    spreadIcon.children[0].classList.remove('layui-icon-right'); // 显示展开图标
                    spreadIcon.children[0].classList.add('layui-icon-left'); // 隐藏展开图标
                }

                // 如果需要，可以通过Layui重新初始化相关元素，以保证样式和事件正常工作
                element.init();



            });

            document.getElementById('toggleSidebarR').addEventListener('click', function() {


                var sideMenuR = document.getElementById('sideMenuR');

                var spreadIconR = document.getElementById('toggleSidebarR');



                // 切换folded类来实现折叠效果
                sideMenuR.classList.toggle('folded');

                console.log(""sideMenuR"", sideMenuR);

                // 根据菜单的状态切换图标显示
                if (sideMenuR.classList.contains('folded')) {
                    spreadIconR.children[0].classList.remove('layui-icon-left');
                    spreadIconR.children[0].cl");
                WriteLiteral(@"assList.add('layui-icon-right');
                } else {
                    spreadIconR.children[0].classList.remove('layui-icon-right');
                    spreadIconR.children[0].classList.add('layui-icon-left');
                };

                // 使用Layui的element模块重新初始化相关元素
                element.init();
            });


            // 动态添加菜单项示例（如果需要）
            // $('#navMenu').append('');



            //患者列表的高度
            function setReportingContenttH() {
                var winH = $(window).height();
                var informationH = $("".patient_information"").height();
                var menuH = $("".Menu_side "").height();
                // var menuTitleH = $("".side_menu_title"").height();
                // if (menuTitleH == 0) {
                //     menuTitleH = 106;
                // }

                var contentH = winH - informationH - 15 + ""px"";
                $("".reporting_content"").css(""height"", contentH);

                // var collapseListH = wi");
                WriteLiteral(@"nH - informationH - menuTitleH - 45 + ""px"";
                // console.log(""collapse_list:"", collapseListH);
                // $("".collapse_list"").css(""height"", collapseListH);
            }
            setReportingContenttH();

            $(window).resize(function() {
                setReportingContenttH();
            });

            var isSwiping = false;

            //模拟器
            function detectScreenOrientation() {
                var userAgent = navigator.userAgent.toLowerCase();
                if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
                    isSwiping = true;

                    //ipad端                    var orientation = window.orientation;
                    if (orientation === 0 || orientation === 180) {
                        // 竖屏状态
                        console.log(""竖屏"");
                    } else if (orient");
                WriteLiteral(@"ation === 90 || orientation === -90) {
                        $(document).ready(function() {
                            $('#LAY_app').removeClass('layadmin-side-shrink');
                        });
                        // 横屏状态
                        console.log(""横屏"");
                    }
                } else {
                    //pc端
                    isSwiping = false;
                    // $("".patient_btn"").removeClass(""patient_btn_1"").addClass(""patient_btn_2"");
                    // $("".patient_info"").removeClass(""flex-row"").addClass(""flex-column"");
                }
            }

            // 初始化时检查一次屏幕方向

            detectScreenOrientation();


            function getFormLit() {
                var patientId = '");
                Write(
#nullable restore
#line 728 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                  Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"';

                $.get({
                    url: '/ReportingManage/PatientManage/GetCRFList?ResearchPatientId=' + patientId, // 你的请求URL
                    async: false, // 设置为false以使请求同步执行
                    success: function(res) {
                        if (res.code == 0) {
                            var htm = """";
                            $("".layui-collapse"").html("""");
                            $.each(res.data, function(index, item) {

                                htm += `<div class=""layui-colla-item"">
                                                                        <h2 class=""layui-colla-title"">`+ item.title + `</h2>

                                                                        <div class=""layui-colla-content form_name layui-show"">

                                                                            <ul class=""navMenu layui-card-body"" lay-filter=""navMenu"">`;
                                var childHtm = """";

                                $.eac");
                WriteLiteral(@"h(item.children, function(index1, child) {
                                    // console.log(index1);
                                    if (index1 == 0 && index == 0) {
                                        firstId = child.id;
                                    }
                                    childHtm += `<li class=""nav_item mui-table-view-cell"" data-businessDomain=""` + child.businessDomain + `"" data-id=""` + child.id + `"" data-formId=""` + child.formId + `"" data-formName=""` + child.title + `"">
                                                                                    <div class=""mui-slider-handle"">
                                                                                        <div class=""title_wrap"">
                                                                                            <p class=""form_name"">`+ child.title + `</p>
                                                                                            <div class=""tools_wrap"" style=""display:none;"">
");
                WriteLiteral(@"                                                                                                <div class=""tools_btn"" id=""btnUpload"" title=""上传文件""><i class=""layui-icon layui-icon-upload-drag""></i></div>
                                                                                                <div class=""tools_btn"" id=""btnOperationLog"" title=""查看操作记录""><i class=""layui-icon layui-icon-log""></i></div>
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class=""layui-progress"" lay-showPercent=""true"">
                                                                                            <div class=""layui-progress-bar layui-bg-blue"" lay-percent=""`+ child.per + `""></div>
                                                                                        </div>
      ");
                WriteLiteral(@"                                                                              </div>
                                                                                    <div class=""mui-slider-right mui-disabled"">
                                                                                                                <a class=""mui-btn mui-btn-red""><i class=""layui-icon icon-yingcangxinxi""></i></a>
                                                                                    </div>
                                                                                </li>`;
                                });

                                htm += childHtm + `</ul>
                                                                        </div>
                                                                    </div>`;

                            })

                            htm = ` <div class=""side_menu_title"">
                                                                <div>基线</div");
                WriteLiteral(@">
                                                                    <div class=""search_hidden"" id=""ShowHidden"">
                                                                    <button type=""button"" class=""layui-btn layui-btn-xs layui-btn-primary"">
                                                                    <i class=""layui-icon icon-shuyi_yanjing-xianshi""></i>查看隐藏表单
                                                                    </button>
                                                                    </div></div>` + htm;

                            $("".layui-collapse"").append(htm);
                            element.render('collapse');
                            // 渲染进度条组件
                            element.render('progress');

                            //报表切换事件
                            $("".navMenu"").on(""click"", "".nav_item"", function() {
                                $("".nav_item"").removeClass(""active"");
                                $("".tools_wrap"").css(""disp");
                WriteLiteral(@"lay"", ""none"");
                                $(this).addClass(""active"");
                                $(this).find("".tools_wrap"").css(""display"", ""flex"");
                                var randVersion = Math.random();

                                formId = $(this).attr(""data-formId"");
                                id = $(this).attr(""data-id"");
                                //和之前的表单的业务域不一样才重新调接口
                                // if (businessDomain != $(this).attr(""data-businessDomain"")) {
                                businessDomain = $(this).attr(""data-businessDomain"");
                                getTree(businessDomain, id);
                                // }
                                // else {
                                //     tree.reload('demo-id-1');

                                // }
                                $(""#AIExtractJsonValue"").val("""");
                                //清空原始数据表格
                                clearTable();
              ");
                WriteLiteral("                  $(\"#divSelected\").html(\"\");\r\n                                var url = \"");
                Write(
#nullable restore
#line 813 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                             $"{ViewBag.formUrl}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\" + formId + \"");
                Write(
#nullable restore
#line 813 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                                                                  $".form?token={ViewBag.token}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"&version="" + randVersion;

                                $(""#reportingFrom"").attr(""src"", url);

                                if (isSwiping) {
                                    document.getElementById('toggleSidebar').click();
                                }
                                var patientId = '");
                Write(
#nullable restore
#line 820 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                                  Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\';\r\n\r\n                                var DelayTime = parseInt(\'");
                Write(
#nullable restore
#line 822 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                                           ViewBag.CRFDelayMinTime

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"', 10);
                                if (formId == ""13e1fa87369b49dfa61528cd24be0c54""
                                    || formId == ""13e1fa87369b49dfa61528cd24be0c54""
                                    || formId == ""9180f2c1a6af406091fa7623583a2805""
                                    || formId == ""baba446e1bf2453c98d89c4c41202581"") {
                                    DelayTime = parseInt('");
                Write(
#nullable restore
#line 827 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                                           ViewBag.CRFDelayMaxTime

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"', 10);
                                }
                                setTimeout(function() {
                                    loadData(patientId, id);
                                }, DelayTime);

                                //如果是人口学资料 显示AI提取按钮
                                var formName = $(this).attr(""data-formName"");
                                // if (formName.indexOf(""人口学"") != -1 || formName.indexOf(""基本信息"") != -1) {
                                //     $(""#btnAI"").show();
                                //     $(""#toggleSidebarR"").show();
                                // }
                                // else {
                                //     $(""#btnAI"").hide();
                                //      $(""#toggleSidebarR"").hide();
                                // }
                            })

                            $(""#ShowHidden"").on(""click"", function() {
                                layer.open({
                                    type: 1");
                WriteLiteral(@"
                                    , title: '已隐藏的表单'
                                    , area: ['70%', '80%']
                                    , shade: 0.3
                                    , content: $('#hideReport')
                                    , success: function(layero, index) {
                                        var id = 0;
                                        $(""#ulHideList"").on(""click"", "".show_report"", function() {
                                            $('#isShow').show();
                                            id = $(this).attr(""data-id"");
                                            var elem = this;
                                            $(""#layui-layer-shade1"").css(""z-index"", ""9999998"");
                                        })
                                        $("".Confirmation_btn"").on(""click"", function(event) {
                                            event.stopImmediatePropagation();
                                            var o");
                WriteLiteral(@"bj = { id: id };
                                            $.post('/ReportingManage/PatientManage/RemoveHide', obj, function(res) {
                                                if (res.code == 0) {
                                                    getHideList();
                                                    getFormLit();
                                                }
                                                else {
                                                    layer.msg(""操作失败"");
                                                }
                                            })
                                            $('#isShow').hide();
                                        })
                                        $("".mui-popup-button-bold"").on(""click"", function() {
                                            $('#isShow').hide();
                                        })
                                    }
                                });

             ");
                WriteLiteral(@"               })

                        }
                    }
                });
            }

            getFormLit();


            //右滑影藏start
            mui.init();
            $('#OA_task_1').on('tap', '.mui-btn', function(event) {
                var elem = this;
                var li = elem.parentNode.parentNode;
                // 在这个例子中，我们只关心确认按钮的回调
                mui.confirm('是否隐藏当前填报表单？', '提示', btnArray, function(e) {
                    // 这里是点击确认按钮后的回调函数
                    console.log('用户点击了确认按钮');
                    // 在这里执行你想要在点击确认后进行的操作
                    if (e.index == 0) {
                        var crformId = li.getAttribute(""data-id"");
                        var patid = '");
                Write(
#nullable restore
#line 902 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                      Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"'
                        var obj = { CRFormId: crformId, ResearchPatientId: patid };
                        $.post('/ReportingManage/PatientManage/HideForm', obj, function(res) {
                            if (res.code == 0) {
                                getHideList();
                                li.parentNode.removeChild(li);
                            }
                            else {
                                layer.msg(res.msg);
                            }
                        })
                    }
                }, function() {
                    // 这里是点击取消按钮后的回调函数
                    console.log('用户点击了取消按钮');
                });
            });
            var btnArray = ['确认', '取消'];
            //右滑影藏end




            //第一次默认选中第一个;
            // 选择data-id为的li元素
            var targetLi = $('li[data-id=""' + firstId + '""]');
            // 模拟点击事件
            targetLi.trigger('click');
            getTree(businessDomain,id);

            // 父");
                WriteLiteral("窗口\r\n            window.addEventListener(\'message\', function(event) {\r\n                if (event.data.action === \'save\') {\r\n                    var ResearchPatientId = \'");
                Write(
#nullable restore
#line 935 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                              Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"'
                    var formData = event.data.data;
                    var selectTrees = tree.getChecked('demo-id-1');
                    var AIExtractJsonValue = $(""#AIExtractJsonValue"").val();
                    var obj = {
                        ResearchPatientId: ResearchPatientId, CRFormId: id, CRFJsonValue: formData,
                        AIExtractJsonValue: AIExtractJsonValue, listTreeData: selectTrees
                    };
                    $.post('/ReportingManage/PatientManage/SaveForm', obj, function(res) {
                        if (res.code == 0) {
                            getFormLit();
                            // 选择data-id为的li元素
                            var targetLi = $('li[data-id=""' + id + '""]');

                            // 模拟点击事件
                            targetLi.trigger('click');
                            getTree(businessDomain, id);
                            layer.msg(""操作成功"");
                        }
                        else
        ");
                WriteLiteral(@"                    layer.msg(""操作失败"");

                    })

                }
                else if (event.data.action === 'show') {
                    var fieldname = event.data.fieldname;
                    var searchStringAll = getSourceByVariableName(fieldname);
                    var searchStringArray = $(searchStringAll.split('$$$'));

                    var sourceList = $("".record-section"");
                    sourceList.each(function(index, element) {
                        var textContent = $(this).html();
                        if (textContent.includes('<span class=""currentMouse"" style=""color:red; font-weight:bold;"">')) {
                            textContent = textContent.replace('<span class=""currentMouse"" style=""color:red; font-weight:bold;"">', """")
                                .replace(""</span>"", """");
                        }
                        // 检查textContent是否包含特定字符串
                        if (searchStringAll != ""NotFound"") {
                        ");
                WriteLiteral(@"    if (textContent.includes(searchStringArray[0])) {
                                textContent = textContent.replace(searchStringArray[0], '<span class=""currentMouse"" style=""color:red; font-weight:bold;"">' + searchStringArray[0] + '</span>');
                            }
                            else if (textContent.includes(searchStringArray[1])) {
                                textContent = textContent.replace(searchStringArray[1], '<span class=""currentMouse"" style=""color:red; font-weight:bold;"">' + searchStringArray[1] + '</span>');
                            }

                            //// 假设目标元素的class是'my-class'
                            //var $element = $('.currentMouse');

                            //// 滚动到该元素的位置，如果它在屏幕外，它会滚动到视口内
                            //$('html, body').animate({
                            //    scrollTop: $element.offset().top
                            //}, 'slow');
                        }
                        else {

                   ");
                WriteLiteral(@"     }
                        $(this).html(textContent);


                    });

                    // addbyzolf 自动滚动到定位位置 2024-10-30
                    var targetElement = document.querySelector('.currentMouse');
                    // 滚动到该元素的位置
                    if (targetElement) {
                        targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }

            }, false);

            var JsonObj = {};
            // 定义一个函数，接受variable_name作为参数
            function getSourceByVariableName(variableName) {
                var node = JsonObj.variables.find(function(item) {
                    return item.variable_name === variableName;
                });
                return node ? node.source + ""$$$"" + node.value : ""NotFound"";
            }

            function loadData(ResearchPatientId, formId) {
                var url = ""/ReportingManage/PatientManage/LoadFormData?ResearchPatientId="" + ResearchPatientI");
                WriteLiteral(@"d + ""&CRFormId="" + formId;
                $.get(url, function(res) {
                    var iframe = document.getElementById('reportingFrom');

                    if (res.code == 0) {
                        //  var iframe = document.getElementById('reportingFrom');
                        // 发送消息到子页面
                        if (res.aIExtractJson)
                            JsonObj = JSON.parse(res.aIExtractJson);
                        iframe.contentWindow.postMessage({ action: ""show"", data: res.data }, ""*"");

                        //20241016
                        if (res.soureDatas) {
                            var treeArrayData = JSON.parse(res.soureDatas);
                            $.each(treeArrayData, function(index, item) {
                                $.each(item.children, function(index1, child) {

                                    loadRightData(child);

                                })

                            })
                        }
               ");
                WriteLiteral(@"     }
                    else {
                        // 发送消息到子页面
                        iframe.contentWindow.postMessage({ action: ""show"" }, ""*"");
                    }
                      var htmlContent = marked.parse(res.tips);
                    $(""#model_wrapR"").html(htmlContent);

                })

            }

            function getHideList() {
                var patientId = '");
                Write(
#nullable restore
#line 1053 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                  Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"';
                $.get('/ReportingManage/PatientManage/getHideList?ResearchPatientId=' + patientId, function(res) {

                    if (res.code == 0) {
                        $(""#ulHideList"").html("""");

                        if (res.data.length > 0) {
                            $.each(res.data, function(index, item) {
                                var htm = `<li class=""nav_item flex_row"" data-id=""` + item.id + `""><div class=""stretch"" >
                                                        <p class=""form_name"">`+ item.formName + `</p>
                                                        <div class=""layui-progress"" lay-showPercent=""true"" style='display:none'>
                                                            <div class=""layui-progress-bar layui-bg-blue"" lay-percent=""5/10""></div>
                                                        </div>
                                                    </div >
                                                            <a class=""");
                WriteLiteral(@"mui-btn mui-btn-red show_report"" data-id=""` + item.id + `"">
                                                                        <i class=""layui-icon icon-shuyi_yanjing-xianshi"" data-id=""` + item.id + `""></i>
                                                            </a>
                                                </li >`;
                                $(""#ulHideList"").append(htm);

                            })

                        } else {
                            var nullDataHtml = `<div class=""null_wrap"">
                                                    <img src=""/images/datanull.png"" />
                                                    <p>暂无隐藏表单</p>
                                                </div>`;
                            $(""#ulHideList"").append(nullDataHtml);
                        }
                    }
                })
            }
            var timer;
            function AIExtract() {
                var loadIndex = layer.open({
        ");
                WriteLiteral(@"            type: 1,
                    area: ['600px', '49px'],
                    shade: 0.1,
                    closeBtn: 0,
                    resize: false,
                    title: false,
                    content: $(""#form_window_load""),
                    success: function() {
                        // $(""#model_wrapL"").val(modelText);
                        element.progress('demo-filter-progress', '0%');
                        load();
                    }
                });
                var patientId = '");
                Write(
#nullable restore
#line 1101 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                  Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"';
                var selectTrees = tree.getChecked('demo-id-1');
                $.post('/ReportingManage/PatientManage/NewAIExtract', { ""ResearchPatientId"": patientId, ""formId"": formId, ""tips"": $(""#model_wrapR"").text(), ""listTreeData"": selectTrees }, function(res) {

                    element.progress('demo-filter-progress', '100%');
                    clearInterval(timer);
                    if (res.code == 0) {
                        JsonObj = JSON.parse(res.data);
                        $(""#AIExtractJsonValue"").val(res.data);
                        var iframe = document.getElementById('reportingFrom');
                        // 发送消息到子页面
                        iframe.contentWindow.postMessage({ data: res.data }, ""*"");
                    }
                    else
                        layer.msg(res.msg);
                    layer.close(loadIndex);
                    element.progress('demo-filter-progress', '0%');
                })
            }

            getHideList()");
                WriteLiteral(@";

            function load() {
                var n = 0;
                timer = setInterval(function() {
                    n = n + Math.random() * 10 | 0;
                    if (n > 99) {
                        n = 99;
                        clearInterval(timer);

                    }
                    element.progress('demo-filter-progress', n + '%');
                }, 300 + Math.random() * 4000);
            }

            var heights = $("".reporting_content"").height();
            heights -= 38;



            var treeNodeId = 0;

            function getTree(BusinessDomain,id) {
                $(""#divLoading"").show();
                $(""#trees"").hide();
                var patientId = '");
                Write(
#nullable restore
#line 1146 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                  Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"';
                $.post(""/ReportingManage/PatientManage/getTreeList"", { ""ResearchPatientId"": patientId, ""BusinessDomain"": BusinessDomain,""formId"":id }, function(res) {
                    $(""#divLoading"").hide();
                    $(""#trees"").show();
                    $(""#trees"").html("""");
                    if (res.code == 0) {
                        // 渲染
                        tree.render({
                            elem: '#trees',
                            data: res.data,
                            edit: [""preview""],
                            showCheckbox: true,
                            onlyIconControl: true,  // 是否仅允许节点左侧图标控制展开收缩
                            id: 'demo-id-1',
                            isJump: true, // 是否允许点击节点时弹出新窗口跳转
                            click: function(obj) {
                                var data = obj.data;  //获取当前点击的节点数据





                            },
                            oncheck: function(obj) {

                     ");
                WriteLiteral(@"           console.log(obj);
                                var checked = obj.checked;
                                var data = obj.data;
                                if (checked) {
                                    if (data.level == 1) {
                                        $.each(data.children, function(index, item) {
                                            loadRightData(item);
                                        })
                                    }
                                    else
                                        loadRightData(data);
                                    var sideMenuR = document.getElementById('sideMenuR');
                                    // 根据菜单的状态切换图标显示
                                    if (!sideMenuR.classList.contains('folded')) {
                                        document.getElementById('toggleSidebarR').click();
                                    }

                                    var sideMenuL = document.getElemen");
                WriteLiteral(@"tById('sideMenu');
                                    // 根据菜单的状态切换图标显示
                                    if (!sideMenuL.classList.contains('folded')) {
                                        document.getElementById('toggleSidebar').click();
                                    }
                                }
                                else {
                                    var divId = ""#div_"" + data.id;

                                    $(divId).remove();
                                }

                            },
                            operate: function(obj) {
                                var type = obj.type; //得到操作类型：add、edit、del
                                var data = obj.data; //得到当前节点的数据
                                var elem = obj.elem; //得到当前节点元素
                                if (type == ""preview"") {
                                    if (data.level == ""1"") {
                                        layer.msg(""当前节点不支持数据预览！"");
                 ");
                WriteLiteral(@"                       return;
                                    }
                                    console.log(data);
                                    var id = data.id; //202323031,202325308,202325361,MM20231137,MM20231036
                                    if (data.id == ""pathology_202323031"" || data.id == ""pathology_202325308""
                                        || data.id == ""pathology_202325361"" || data.id == ""pathology_MM20231137""
                                        || data.id == ""pathology_MM20231036"")
                                    {
                                            layer.open({
                                                type: 2,
                                                title: 'PDF预览',
                                                resize: true,
                                                maxmin: true,
                                                shadeClose: true, // 点击遮罩关闭层
                                                area: ['90%',");
                WriteLiteral(@" '90%'], // 设置弹窗大小
                                                content: '/DIPdfinfo/' + data.id +'.jpg'
                                            });

                                    }
                                    else {
                                        $.post('/ReportingManage/PatientManage/PreviewData', { ""json"": data.json, ""type"": data.type }, function (res) {
                                            if (res.code != 0) {
                                                layer.msg(res.msg);
                                                return;
                                            };
                                            windowsIndex = layer.open({
                                                    type: 1,
                                                    title: '原始数据预览',
                                                    area: ['800px', '600px'],
                                                    resize: true,
                                    ");
                WriteLiteral(@"                yes: function (index, layero) {
                                                    },
                                                    cancel: function (index, layro) {
                                                    },
                                                    content: $('#previewDiv'),
                                                    success: function () {
                                                        if (data.type.indexOf(""检验"") > -1) {
                                                            table.render({
                                                                elem: '#tablelistJianYan'
                                                                , id: 'tablelistJianYan',
                                                                data: res.data
                                                                , limit: Number.MAX_VALUE
                                                                , cols: [res.columns]
           ");
                WriteLiteral(@"                                                     //, height: heights
                                                                , page: false // 关闭分页
                                                            });
                                                            $(""#divOther"").hide();
                                                            $(""#divMedical"").hide();
                                                            $(""#divJianYan"").show();
                                                        }
                                                        else if (data.type == ""患者基本信息"" || data.type.indexOf(""检查"") > -1 || data.type.indexOf(""病理"") >= 0) {
                                                            $(""#divOther"").show();
                                                            $(""#divMedical"").hide();
                                                            $(""#divJianYan"").hide();
                                                            table.render");
                WriteLiteral(@"({
                                                                elem: '#tablelistPre'
                                                                , id: 'tablelistPre'
                                                                , data: res.data
                                                                , limit: Number.MAX_VALUE
                                                                , cols: [[
                                                                    { field: 'zizeng', title: '序号', type: 'numbers' }
                                                                    , { field: 'fields', title: '字段名', width: 160 }
                                                                    , { field: 'fieldsValue', title: '字段值' }

                                                                ]]
                                                                // , height: heights
                                                                , page: false // 关闭分页
         ");
                WriteLiteral(@"                                                   });

                                                        }
                                                        else if (data.type == ""外院报告"") {
                                                            $(""#divOther"").hide();
                                                            $(""#divMedical"").show();
                                                            $(""#divJianYan"").hide();
                                                            $(""#pMedical"").html('<iframe id=""qwe"" src=""' + data.url + '"" style=""width:100%;""></iframe><button class=""layui-btn layui-btn-fluid layui-btn-primary layui-border-red btnDelete"" dataid=""' + data.id + '"" style=""margin:10px 0;""><i class=""layui-icon layui-icon-delete "" style=""font-size:30px;color:#FF5722;""></i></button>');
                                                            var pMedicalH = $(""#pMedical"").parent().parent().parent().parent().parent().parent().parent().height();

             ");
                WriteLiteral(@"                                               var qweH = pMedicalH - 116;
                                                            $(""#qwe"").css(""height"", qweH + ""px"");
                                                            $("".window_wrap"").parent().css(""overflow"", ""hidden"");
                                                        }
                                                        else {
                                                            $(""#divOther"").hide();
                                                            $(""#divMedical"").show();
                                                            $(""#divJianYan"").hide();
                                                            $(""#pMedical"").html('<pre style=""color: #666; font-size: 14px; line-height: 25px; font-family: \'Microsoft YaHei\', sans-serif; "">' + res.data[0] + '</pre>');
                                                        }
                                                    }
                     ");
                WriteLiteral(@"                           });
                                        // var sideMenuR = document.getElementById('sideMenuR');
                                        // // 根据菜单的状态切换图标显示
                                        // if (!sideMenuR.classList.contains('folded')) {
                                        //     document.getElementById('toggleSidebarR').click();
                                        // }
                                        })
                                    }
                                }

                            }
                        });
                    }
                    else {
                        layer.msg(res.msg);
                    }
                })
            }

            function loadRightData(data) {
                var divId = ""div_"" + data.id;
                var tableId = ""table_"" + data.id;
                if ($(""#"" + divId).length > 0)
                    return;
                if (data.type.indexOf(""检验""");
                WriteLiteral(@") > -1) {
                    var jsonData = JSON.parse(data.json);
                    // var divId = ""div_"" + data.id;
                    // var tableId = ""table_"" + data.id;

                    var htm = `<div class=""record-section"" id='` + divId + `'><p><strong>` + data.type + `-` + data.title + `</strong>
                                                                                                      <table id=""`+ tableId + `"" lay-filter=""` + tableId + `""></table></div>`;
                    $(""#divSelected"").append(htm);

                    var ArrayData = [];
                    for (var item of jsonData) {

                        ArrayData.push(item);
                    }

                    table.render({
                        elem: '#' + tableId
                        , id: tableId,

                        data: ArrayData
                        , limit: Number.MAX_VALUE
                        , cols: [[
                            { ""field"": ""zizeng"", ""type"": """);
                WriteLiteral(@"numbers"", ""title"": ""序号"" }
                            , { ""field"": ""项目名称"", ""title"": ""项目名称"", ""width"": 100 }
                            , { ""field"": ""检查结果"", ""title"": ""检查结果"", ""width"": 100 }
                            , { ""field"": ""异常标志"", ""title"": ""异常标志"", ""width"": 90 }
                            // , { ""field"": ""定量结果"", ""title"": ""定量结果"", ""width"": 90 }
                            // , { ""field"": ""定性结果"", ""title"": ""定性结果"", ""width"": 90 }
                            // , { ""field"": ""参考值"", ""title"": ""参考值"", ""width"": 90 }
                            , { ""field"": ""单位"", ""title"": ""单位"", ""width"": 60 }
                            // , { ""field"": ""报告时间"", ""title"": ""报告时间"", ""width"": 120 }
                        ]]
                        // , height: heights
                        , page: false // 关闭分页
                    });

                }
                else if (data.type == ""患者基本信息"") {
                    var jsonData = JSON.parse(data.json);
                    // var divId = ""div_"" + data.id;
         ");
                WriteLiteral(@"           // var tableId = ""table_"" + data.id;
                    var htm = `<div class=""record-section"" id='` + divId + `'><p><strong>` + data.type + `-` + data.title + `</strong>
                                                                                              <table id=""`+ tableId + `"" lay-filter=""` + tableId + `""></table></div>`;
                    $(""#divSelected"").append(htm);

                    var ArrayData = [];
                    for (let key in jsonData) {
                        var obj = {};
                        obj.fields = key;
                        obj.fieldsValue = jsonData[key];
                        ArrayData.push(obj);
                    }

                    table.render({
                        elem: ""#"" + tableId
                        , id: tableId,

                        data: ArrayData
                        , limit: Number.MAX_VALUE
                        , cols: [[
                            { field: 'zizeng', title: '序号', type:");
                WriteLiteral(@" 'numbers' }
                            , { field: 'fields', title: '字段名', width: 130 }
                            , { field: 'fieldsValue', title: '字段值', width: 300 }

                        ]]
                        // , height: heights
                        , page: false // 关闭分页
                    });

                }
                else if (data.type.indexOf(""检查"") > -1 || data.type.indexOf(""病理"") > -1) {
                    var jsonData = JSON.parse(data.json);
                    var ArrayData = [];
                    for (let key in jsonData) {
                        var obj = {};
                        obj.fields = key;
                        obj.fieldsValue = jsonData[key];
                        ArrayData.push(obj);
                    }
                    var myObject = {
                        divId: divId,
                        title: data.title, 
                        json: ArrayData 
                    };
                    var getTpl2 = NoStructurMo");
                WriteLiteral(@"del.innerHTML;
                    laytpl(getTpl2).render(myObject, function (html) {
                        console.log(html);
                        $('#divSelected').append(html);
                    });

                }
                else if (data.type == ""外院报告"") {
                    var text = data.json.replace(/\\n/g, '<br>');
                    var jsonData = JSON.parse(text);
                    var htm = `<div class=""record-section"" id='` + divId + `'><p><strong>` + data.title + `</strong></p>
                                                                                <div class=""subsection-header"">`+ data.type + `</div><pre style=""color: #666; font-size: 14px; line-height: 25px; font-family: 'Microsoft YaHei', sans-serif; "">` + jsonData + `</pre></div>
                                                       `;
                    $(""#divSelected"").append(htm);

                }

                else {
                    var jsonData = JSON.parse(data.json);
          ");
                WriteLiteral(@"          // var divId = ""div_"" + data.id;
                    var htm = `<div class=""record-section"" id='` + divId + `'><p><strong>` + data.title + `</strong></p>
                                                                                <div class=""subsection-header"">`+ data.type + `</div><pre style=""color: #666; font-size: 14px; line-height: 25px; font-family: 'Microsoft YaHei', sans-serif; "">` + jsonData.文书内容 + `</pre></div>
                                                       `;
                    $(""#divSelected"").append(htm);
                }
            }
            function clearTable() {
                table.reload('tablelistJianYan', {
                    data: []
                });
                table.reload('tablelistPre', {
                    data: []
                });
                $(""#pMedical"").html("""");

                var sideMenuR = document.getElementById('sideMenuR');
                // 根据菜单的状态切换图标显示
                if (sideMenuR.classList.contains('");
                WriteLiteral(@"folded')) {
                    document.getElementById('toggleSidebarR').click();
                }
            }
            upload.render({
                elem: '#UploadFile'
                , url: '/ReportingManage/PatientManage/UploadFile' // 实际使用时改成您自己的上传接口即可。
                , accept: 'file' // 限制文件类型
                , acceptMime: 'application/pdf,image/jpg,image/png,image/jpeg' // 仅接受 PDF 文件
                , done: function (res) {
                    layer.msg('选择成功');
                    if (res.code == 0) {
                        $(""#fileName"").val(res.data.fileName);
                        $(""#guid"").val(res.data.guid);
                        $(""#newFileName"").val(res.data.newFileName);

                    }
                    else
                        layer.msg(res.msg);
                }
            });

            //监听提交
            form.on('submit(submit)', function (data) {
                var indexs = layer.load(1);
                data.field.newFileName = ");
                WriteLiteral("$(\"#newFileName\").val();\r\n                data.field.guid = $(\"#guid\").val();\r\n                data.field.CRFormId = id;\r\n                data.field.RId = \'");
                Write(
#nullable restore
#line 1469 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DataCollecPC.cshtml"
                                   Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"';
                $.ajax({
                    url: ""/ReportingManage/PatientManage/SaveFile"",
                    type: ""post"",
                    data: data.field,
                    datatype: 'json',
                    success: function (result) {
                        layer.close(indexs);
                        if (result.code == 0) {

                            layer.close(windowsIndex);
                            getTree(businessDomain, id);
                        }
                        else
                            layer.msg(result.msg);

                    }, error: function (res) {
                        layer.msg(""加载统计信息错误："" + res.responseText);
                        layer.close(indexs);
                    }
                });
                return false;
            });
        })
    </script>



");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_15);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"

<div class=""window_wrap"" id=""form_window_load"" style=""display: none;background-color:transparent"">
    <div class=""layui-progress layui-progress-big"" lay-showPercent=""true"" lay-filter=""demo-filter-progress"">
        <div class=""layui-progress-bar"" lay-percent=""0%"">
        </div>

    </div>
    <p style=""text-align:center""> AI数据提取中...</p>
</div>

<div id=""popwrap"" style=""display:none;height:100%;overflow:hidden;"">
    <div class=""layui-row layui-col-space30"" style=""height:100%"">
        <div class=""layui-col-md12"" style=""height:100%"">
            <div style=""background-color: #fff;height: 100%; padding:15px 15px;"">
                <div id=""model_wrapR"" style=""height:100%;width:100%;resize:none;line-height:40px;font-size:20px; overflow:auto;""></div>
                </div>
        </div>
    </div>


</div>
<div class=""window_wrap  layui-form"" id=""previewDiv"" style=""display: none"">
    <div class=""layui-card"">
        <div class=""layui-card-body"">
            <div class=""layui-form l");
            WriteLiteral(@"ayui-form-pane"" style=""height:100%"">
                <div class=""layui-form-item layui-form-text"" style=""height:100%"">
                    <div class=""layui-input-block"" style=""height:100%;display:none"" id=""divOther"">
                        <label class=""layui-form-label"" style=""text-align:center"">原始数据</label>
                        <table id=""tablelistPre"" lay-filter=""tablelistPre""></table>
                    </div>

                    <div class=""layui-input-block"" style=""height:100%;display:none"" id=""divJianYan"">
                        <label class=""layui-form-label"" style=""text-align:center"">原始数据</label>
                        <table id=""tablelistJianYan"" lay-filter=""tablelistJianYan""></table>
                    </div>

                    <div class=""layui-input-block"" style=""height:100%;display:none;"" id=""divMedical"">
                        <label class=""layui-form-label"" style=""text-align:center"">原始数据</label>
                        <div id=""pMedical"" style=""height:100%""></div>
 ");
            WriteLiteral("                   </div>\r\n                </div>\r\n            </div>\r\n\r\n        </div>\r\n\r\n    </div>\r\n</div>\r\n<div class=\"window_wrap\" id=\"form_window\" style=\"display: none;\">\r\n    <form class=\"layui-form\" lay-filter=\"fm\" id=\"fm\"");
            BeginWriteAttribute("action", " action=\"", 69258, "\"", 69267, 0);
            EndWriteAttribute();
            WriteLiteral(@">

        <div class=""layui-form-item"" id=""div10"" style=""margin-top:15px;"">
            <label class=""layui-form-label"" style=""width:124px;"">上传附件：</label>
            <div class=""layui-input-block line_wrap"" style=""margin-left:130px;"">
                <div class=""layui-upload-drag"" id=""UploadFile"">
                    <i class=""layui-icon""></i>
                    <p>点击上传，或将文件拖拽到此处</p>
                    <div class=""layui-hide"" id=""uploadDemoView"">
                        <hr>
                        <input type=""hidden"" id=""newFileName"" />
                        <input type=""hidden"" id=""guid"" />
                    </div>
                </div>
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"" style=""width:124px;"">文件名称：</label>
            <div class=""layui-input-block"" style=""width:300px;margin-left:130px;"">
                <input type=""text"" id=""fileName"" class=""layui-input"" name=""fileName"" />

            </div>
  ");
            WriteLiteral(@"      </div>
        <div class=""layui-form-item"">
            <div class=""layui-input-block"" style=""margin-left:130px;"">
                <button type=""submit"" class=""layui-btn"" lay-submit lay-filter=""submit"">提交</button>
                <button type=""reset"" class=""layui-btn layui-btn-primary"" id=""btn_reset"">重置</button>
            </div>
        </div>
    </form>
</div>

<script id=""NoStructurModel"" type=""text/html"">
    <div class=""record-section"" id=""{{d.divId}}"">
        <p><strong>{{ d.title }}：</strong></p>
        {{#  for (var j=0;j<d.json.length;j++) {}}
        <div class=""subsection-header"">{{d.json[j].fields}}：</div>
        <pre style=""color: #666; font-size: 14px; line-height: 25px; font-family: 'Microsoft YaHei', sans-serif; "">
        {{ d.json[j].fieldsValue}}
    </pre>
        {{# } }}
    </div>
</script>

</html>
");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<AngelwinResearch.Models.ResearchPatient> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
