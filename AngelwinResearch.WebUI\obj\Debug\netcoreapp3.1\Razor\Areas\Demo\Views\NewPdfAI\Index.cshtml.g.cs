#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\NewPdfAI\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "62f9a2af7aba6edd7221ed9f9593e4fa1e0e0ad91a014fae38f11193d3cf61f2"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_Demo_Views_NewPdfAI_Index), @"mvc.1.0.view", @"/Areas/Demo/Views/NewPdfAI/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"62f9a2af7aba6edd7221ed9f9593e4fa1e0e0ad91a014fae38f11193d3cf61f2", @"/Areas/Demo/Views/NewPdfAI/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_Demo_Views_NewPdfAI_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/chatGTPIndex.css?v=v21"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/lib/codemirror.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/theme/pastel-on-dark.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\NewPdfAI\Index.cshtml"
  
    ViewBag.Title = "PDF信息提取";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"zh\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "62f9a2af7aba6edd7221ed9f9593e4fa1e0e0ad91a014fae38f11193d3cf61f26368", async() => {
                WriteLiteral("\r\n    <meta charset=\"UTF-8\">\r\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>chatGTP</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "62f9a2af7aba6edd7221ed9f9593e4fa1e0e0ad91a014fae38f11193d3cf61f26863", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "62f9a2af7aba6edd7221ed9f9593e4fa1e0e0ad91a014fae38f11193d3cf61f28065", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n    <!-- 代码高亮 -->\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "62f9a2af7aba6edd7221ed9f9593e4fa1e0e0ad91a014fae38f11193d3cf61f29292", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "62f9a2af7aba6edd7221ed9f9593e4fa1e0e0ad91a014fae38f11193d3cf61f210495", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "62f9a2af7aba6edd7221ed9f9593e4fa1e0e0ad91a014fae38f11193d3cf61f211698", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        body {
            background-color: #fafafa;
            overflow: hidden;
        }

        .layui-table td, .layui-table th{
            min-width:100px;
        }

        .code_wrap {
            background-color: #3f3f3f;
            border-radius: 6px;
            overflow: hidden;
        }

        .code_top {
            padding: 4px 6px 0;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            background-color: #000;
            color: #c2be9e;
        }

        .code_content {
            padding: 10px;
            border: 10px solid #000;
            color: #c2be9e;
            text-align: left;
            resize: none;
        }

        .layui-btn-xs {
            padding: 0 6px;
        }

        .layui-btn-primary {
            border-color: transparent;
            background-color: transparent;
            color: #fff;
        }

            .layui-btn-primary:ho");
                WriteLiteral(@"ver {
                color: #eee;
                border-color: #eee;
            }

        .message_content {
            flex: 1;
        }

            .message_content p,
            .result_text {
                white-space: pre-line;
            }

        .result_text {
            padding: 10px;
            height: 80px;
            background-color: #3f3f3f;
            font-size: 14px;
            word-wrap: break-word;
            overflow-x: hidden;
            overflow-y: auto;
            border: 10px solid #000;
            border-top: 2px solid #000;
            border-radius: 0;
        }

            .result_text:hover {
                border: 10px solid #000 !important;
                border-top: 2px solid #000 !important;
            }

            .result_text:focus {
                padding: 10px;
                border: 10px solid #000 !important;
                border-top: 2px solid #000 !important;
                color: #c2be9e;
            ");
                WriteLiteral(@"    border-radius: 0;
            }

        .layui-table-view {
            margin:10px;
        }

        .result_bj {
            display: flex;
            flex-direction: row;
            align-items: center;
            background-color: #009688;
            color: #fff;
        }

        .result_icon {
            width: 34px;
            height: 34px;
            border-radius: 10px;
            border: 2px solid #0bbe56;
            overflow: hidden;
            margin-right: 10px;
        }

            .result_icon img {
                vertical-align: top;
                width: 100%;
            }

        .flex_row {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .right_title {
            display: block;
        }

        .layui-btn {
            padding: 0 20px;
        }

        .right_content {
            margin: 10px;
            margin-left: 0;
            backgrou");
                WriteLiteral(@"nd-color: #fff;
            border: 1px solid #eee;
            border-radius: 10px;
            overflow: hidden;
        }

        .left_wrap{
            width:100%;
            margin: 10px;
            margin-left: 0;
            background-color: #fff;
            border: 1px solid #eee;
            border-radius: 10px;
        }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "62f9a2af7aba6edd7221ed9f9593e4fa1e0e0ad91a014fae38f11193d3cf61f217070", async() => {
                WriteLiteral("\r\n    <div class=\"layui-form\">\r\n\r\n\r\n\r\n\r\n\r\n        <div class=\"chat layui-row\">\r\n\r\n            <div class=\"layui-col-xs12 layui-col-md12\">\r\n\r\n                <div class=\"right_title\" style=\"margin-top:10px; \">\r\n");
                WriteLiteral("\r\n                    <form class=\"layui-form flex_row\" lay-filter=\"formModel\" id=\"formModel\"");
                BeginWriteAttribute("action", " action=\"", 4472, "\"", 4481, 0);
                EndWriteAttribute();
                WriteLiteral(">\r\n                        <div class=\"layui-form right_title_select\">\r\n");
                WriteLiteral("                            <div class=\"layui-inline\">\r\n\r\n                                <div class=\"layui-input-inline\">\r\n                                    <select id=\"dept\" name=\"dept\">\r\n                                        <option");
                BeginWriteAttribute("value", " value=\"", 5131, "\"", 5139, 0);
                EndWriteAttribute();
                WriteLiteral(@" selected>全部</option>
                                        <option value=""眼科"">眼科</option>
                                        <option value=""康复科"">康复科</option>
                                        <option value=""病理"">病理</option>
                                    </select>
                                </div>
                            </div>
                            <div class=""layui-inline"">

                                <div class=""layui-input-inline"" style=""width:300px;"">
                                    <input type=""text"" class=""layui-input"" placeholder=""请输入文件名称"" name=""KeyWords"" id=""KeyWords"" />
                                </div>

                            </div>
                            <div class=""layui-inline"">
                                <div class=""layui-input-inline"">
                                    <button type=""button"" class=""layui-btn layui-btn-normal"" id=""btnSearch"">查询</button>
                                </div>
                       ");
                WriteLiteral(@"         <div class=""layui-input-inline"">
                                    <button type=""button"" class=""layui-btn layui-btn-warm"" id=""btnAnalysis"">
                                        AI分析
                                    </button>
                                </div>
                                <div class=""layui-input-inline"">
                                    <button type=""button"" class=""layui-btn"" id=""btnUpload"">上传文件</button>

                                </div>
                            </div>


                        </div>
                        <div>
                            <button id=""popbtn"" type=""button"" class=""layui-btn layui-bg-blue"">
                                <i class=""layui-icon layui-icon-survey""></i>
                            </button>
                        </div>
                    </form>
");
                WriteLiteral("                </div>\r\n\r\n\r\n                <div class=\"layui-row \">\r\n                    <div class=\"layui-col-xs3 layui-col-md3 \" style=\"height:98vh\">\r\n                        <div class=\"left_wrap\">\r\n");
                WriteLiteral(@"                            <div id=""trees"" style=""height:98vh""></div>
                            <div style=""height:48vh;display:none"">
                                <textarea id=""AItips"" class=""layui-textarea"" style=""height:100%"">
                                </textarea>
                            </div>
                        </div>
                    </div>
                    <div class=""layui-col-xs9 layui-col-md9"">
                        <div class=""right_content"" style=""height:50vh"">
                            <textarea id=""resultPdf"" class=""layui-textarea"" style=""height:100%;resize:none""></textarea>
                        </div>
                        <div class=""right_content"" style=""height:39vh;overflow:auto;"">
");
                WriteLiteral(@"
                            <table class=""layui-table"" id=""tableResult"">
                            </table>
                        </div>
                    </div>
                </div>
            </div>



        </div>
    </div>
    </div>



    <div id=""popwrap"" style=""display:none;height:100%;overflow:hidden;"">
        <div class=""layui-row layui-col-space30"" style=""height:100%"">
            <div class=""layui-col-md12"" style=""height:100%"">
                <div style=""background-color: #fff;height: 100%; padding:15px 15px;"">
                    <textarea id=""model_wrapR"" class=""layui-textarea"" style="" height:100%;width:100%;line-height:40px;font-size:20px;resize:none"">

</textarea>
                </div>
            </div>
        </div>


    </div>

    <div id=""resultwrap"" style=""display:none;height:100%;overflow:hidden;"">
        <div class=""layui-row layui-col-space30"" style=""height:100%"">
            <div class=""layui-col-md12"" style=""height:100%"">
         ");
                WriteLiteral(@"       <div style=""background-color: #fff;height: 100%; padding:15px 15px;"">
                    <textarea id=""txtResult"" class=""layui-textarea"" style="" height:100%;width:100%;line-height:40px;font-size:20px;resize:none"">

</textarea>
                </div>
            </div>
        </div>


    </div>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "62f9a2af7aba6edd7221ed9f9593e4fa1e0e0ad91a014fae38f11193d3cf61f222939", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "62f9a2af7aba6edd7221ed9f9593e4fa1e0e0ad91a014fae38f11193d3cf61f224063", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
        layui.use(['jquery', 'layer', 'tree', 'form', 'element', 'upload', 'laytpl', 'table'], function () {
            var layer = layui.layer,
                $ = layui.$,
                form = layui.form,
                laytpl = layui.laytpl,
                element = layui.element,
                tree = layui.tree,
              upload = layui.upload,
                table = layui.table;

            //查询结果表格用的高度  height:""full-"" + table_top_H,
            var table_top_H;
            var windowsIndex;

            $(window).resize(function () {

                arrangement();
            })
            arrangement();
            function arrangement() {
                var winH = $(window).height();

               // $("".right_content"").css(""height"", (winH - 100) + ""px"");

            }
             // 监听下拉框的选择事件
            form.on('select(DeptName)', function (data) {
           // console.log('选择了: ' + data.value);
           // alert('您选择了: ' + data.val");
                WriteLiteral(@"ue);
           if(data.value==""院外数据""||data.value==""病理"")
           {
               $(""#div11"").show();
                $(""#div10"").hide();
           }
           else
           {
                    $(""#div11"").hide();
                    $(""#div10"").show();
           }
        });

        // 初始化表单渲染
        form.render();
            //table.render({
            //    elem: '#tablelist'
            //    , id: 'tablelist'
            //    , url: '/Chat/PdfList'
            //    , page: true
            //    , limit: 20
            //    , height: 'full-110'
            //    , cols: [[
            //        { type: 'checkbox' }
            //        , { field: 'zizeng', title: '', type: 'numbers', width: 40 }
            //        , { field: 'deptName', title: '科室名称', width: 100 }
            //        , { field: 'fileName', title: '文件名称', width: 100 }
            //        , { align: 'center', title: '操作', toolbar: '#tableBar1' }
            //    ]]
            //    ,");
                WriteLiteral(@" done: function (res, curr, count) {
            //        $('.layui-table-view[lay-id=""tablelist""]').children('.layui-table-box')
            //            .children('.layui-table-body').find('table tbody tr[data-index=0]').click();
            //    }
            //});

            $(document).ready(function () {

                $(document).on('click', '#btnUpload', function () {
                    $(""#DeptName"").val(""眼科"");
                    form.render();
                    $(""#fileName"").val("""");
                    $(""#newFileName"").val("""");
                    $(""#guid"").val("""");
                    windowsIndex = layer.open({
                        type: 1,
                        title: '上传PDF文件',
                        area: '600px',
                        resize: true,
                        content: $('#form_window')
                    });

                });
                $(document).on('click', '#btnSearch', function () {

                    //loadTable();
");
                WriteLiteral(@"                   loadTree();

                });


                $(document).on('click', '#btnAnalysis', function () {

                    var selectTrees = tree.getChecked('demo-id-1');
               // console.log(""长度"",selectTrees.length);
                 if (selectTrees.length == 0) {
                     layer.msg(""请选择报告文件！"");
                     return;
                 }
                    if (selectTrees.length>1) {
                        layer.msg(""只能选择同一种类型的报告文件！"");
                        return;
                    }
                      var indexs = layer.load(1);
                    $(""#resultPdf"").val("""");
                     $(""#tableResult"").html("""");
                    var i = 0;
                    var json = JSON.stringify(selectTrees);
                    var source = new EventSource('/Demo/NewPdfAI/AnalysePdf?json=' + encodeURIComponent(json) + ""&prompt="" + encodeURIComponent($(""#model_wrapR"").val()));
                    source.onmessage = function (e");
                WriteLiteral(@"vent) {
                        layer.close(indexs);
                        var result = JSON.parse(event.data);
                     //   console.log(result);
                        if (result.okMsg) {

                                var content = $(""#resultPdf"").val() + result.data;
                                $(""#resultPdf"").val(content);


                            //$("".clearfix[responseCurrent='1'] p"").append(result.data);
                            i = i + 1;
                        }
                        else {
                            layer.msg(result.errorMsg);
                           // $("".clearfix[responseCurrent='1']"").removeAttr(""responseCurrent"");
                            source.close();
                        }
                        resetHistoryscrollTop();
                    };

                    source.addEventListener('end', function (event) {
                          layer.close(indexs);
                        var result = JSON.parse(ev");
                WriteLiteral(@"ent.data);
                        if (result.okMsg) {

                            console.log(result.content);

                            loadTable(result.newContent);

                        }
                        else {
                            layer.msg(result.errorMsg);
                        }

                        // 结束事件源连接
                        source.close();
                    }, false);

                    source.onerror = function (event) {
                        //$("".clearfix[responseCurrent='1']"").removeAttr(""responseCurrent"");
                        layer.close(indexs);
                        source.close();
                    };

                });

                $(""#popbtn"").on(""click"", function () {
                    layer.open({
                        type: 1,
                        area: ['60%', '80%'],
                        resize: false,
                        shadeClose: true,
                        title: '帮助',
         ");
                WriteLiteral(@"               content: $(""#popwrap""),
                        success: function () {
                            // $(""#model_wrapL"").val(modelText);
                        }
                    })
                })

            });


            function loadTips(formId)
            {
                $.get('/Demo/NewPdfAI/GetTip?formId=' + formId, function (res) {
                    if (res.code == 0) {

                        $(""#model_wrapR"").val(res.data);
                    }

                })
            }

           function loadTree(){
                getTree();
           }

           var treeNodeId = 0;
            var patientId = 0;
            function getTree() {
                var dept = $(""#dept"").val();
                var KeyWords = $(""#KeyWords"").val();
                $.get(""/Demo/NewPdfAI/PdfListTree?deptName="" + dept + ""&keyWords="" + KeyWords, function (res) {

                    // 渲染
                    tree.render({
                        ");
                WriteLiteral(@"elem: '#trees',
                        data: res,
                        edit: [""preview""],
                        showCheckbox: true,
                        onlyIconControl: true,  // 是否仅允许节点左侧图标控制展开收缩
                        id: 'demo-id-1',
                        isJump: true, // 是否允许点击节点时弹出新窗口跳转
                        click: function (obj) {
                            var data = obj.data;  //获取当前点击的节点数据
                            //  layer.msg('状态：' + obj.state + '<br>节点数据：' + JSON.stringify(data));
                            console.log(obj);
                            //obj.elem.addClass(""active"");
                            // treeNodeId = data.id;
                            //   patientId = data.patientId;
                            //  $(""#trees"").find("".layui-tree-txt"").removeClass(""active"");
                            //  $(obj.elem).find("".layui-tree-txt"").eq(0).addClass(""active"");


                        },
                        oncheck: function (obj) {
    ");
                WriteLiteral(@"                       // console.log(obj.data); // 得到当前点击的节点数据
                           // console.log(obj.checked); // 节点是否被选中
                            var tips ="""";
                            var tips1 = `   请以JSON格式整理并输出以下内容：“序号”,“患者姓名”:"""",“日期”,“部位”，“平K”，“陡K”，“ΔK”，“平面e²”，“斜面e²”，“8毫米凹陷差距”，“垂直Q”，“陡e”，“陡轴e²”，“角膜表面规则性指数”，“泪膜表面质量”，“平e”，“区域3毫米平面1能力”，“区域3毫米平面2能力”，“区域3毫米斜面1能力”, “区域3毫米斜面2能力”, “区域5毫米平面1能力”, “区域5毫米平面2能力”, “区域5毫米斜面1能力”, “区域5毫米斜面2能力”, “区域7毫米平面1能力”, “区域7毫米平面2能力”, “区域7毫米斜面1能力”, “区域7毫米斜面2能力”, “水平e”, “水平Q”, “中央泪膜表面质量”, “角膜表面非对称性指数”, “IS指数”`;
                            //var tips2 = `   以JSON格式整理并输出以下内容：""步频 (Cadence)"":"""" , ""步长 (Stride Length)"", ""步幅 (Step Length)"", ""骨盆倾斜 (Pelvic Obliquity)"", ""骨盆旋转 (Pelvic Rotation)"", ""髋关节屈伸 (Hip Flex-Extension)"", ""髋关节旋转 (Hip Rotation)"", ""膝关节屈伸 (Knee Flex-Extension)"", ""踝关节背屈 (Ankle Dorsi-Plantarflex)"", ""足部进度 (Foot Progression)"", ""步态周期时间 (Stride Time)"", ""支撑相时间 (Stance Time)"", ""摆动相时间 (Swing Time)"", ""支撑相百分比 (Stance Phase)"", ""摆动相百分比 (Swing Phase)""`;
                 ");
                WriteLiteral(@"           var tips2 = `   以JSON格式整理并输出以下内容：""步频 (Cadence)"":"""" , ""步长 (Stride Length)"", ""步幅 (Step Length)"",  ""髋关节屈伸 (Hip Flex-Extension)"", ""膝关节屈伸 (Knee Flex-Extension)"", ""踝关节背屈 (Ankle Dorsi-Plantarflex)"", ""足部进度 (Foot Progression)"", ""步态周期时间 (Stride Time)"", ""支撑相时间 (Stance Time)"", ""摆动相时间 (Swing Time)"", ""支撑相百分比 (Stance Phase)"", ""摆动相百分比 (Swing Phase)""`;
                            //, ""单支撑相百分比 (Single Support Phase)"", ""双支撑相百分比 (Double Support Phase)"", ""步态偏差指数 (Gait Deviation Index)"", ""步态模式得分 (Gait Profile Score)""
        //                     var tips3 = `   以json格式整理并输出以下内容： ""序号"", ""姓名"",""第几次检查"",""检查日期"",""过渡层面"",""机型"",""滑囊炎"",""附着点炎"",""间隙强化"",""关节积液""，""骨质硬化"",""骨芽""
        // 提取要求:
        // 1.""滑囊炎"",""附着点炎"",""间隙强化"",""关节积液""，""骨质硬化"",""骨芽""项目备注左左右及数值，没有提取到数值默认空；
        // 2.保证提取的完整性
        // 3 按照如下json格式输出内容
        // [
        //     {
        //         ""序号"": """",
        //         ""姓名"": """",
        //         ""第几次检查"": """",
        //         ""检查日期"": """",
        //         ""过渡层面"": """",
        //         ""机型"": """",
   ");
                WriteLiteral(@"     //         ""滑囊炎"": {
        //             ""右"": """",""左"": """"
        //         },
        //         ""附着点炎"": {
        //             ""右"": """",""左"": """"
        //         },
        //         ""间隙强化"": {
        //             ""右"": """",""左"": """"
        //         },
        //         ""关节积液"": {
        //             ""右"": """",""左"": """"
        //         },
        //         ""骨质硬化"": {
        //             ""右"": """",""左"": """"
        //         },
        //         ""骨芽"": {
        //             ""右"": """",""左"": """"
        //         }
        //     },
        //     {

        //            ""序号"": """",
        //            ""姓名"": """",
        //            ""第几次检查"": """",
        //            ""检查日期"": """",
        //            ""过渡层面"": """",
        //            ""机型"": """",
        //            ""滑囊炎"": {
        //                ""右"": """",""左"": """"
        //            },
        //            ""附着点炎"": {
        //                ""右"": """",""左"": """"
        //            },
        //            ""间隙强");
                WriteLiteral(@"化"": {
        //                ""右"": """",""左"": """"
        //            },
        //            ""关节积液"": {
        //                ""右"": """",""左"": """"
        //            },
        //            ""骨质硬化"": {
        //                ""右"": """",""左"": """"
        //            },
        //            ""骨芽"": {
        //                ""右"": """",""左"": """"
        //            }
        //     }
        // ]

        // "" `
                            var tips3 = `   以json格式整理并输出以下内容： ""序号"", ""姓名"",""第几次检查"",""检查日期"",""过渡层面"",""机型"",""滑囊炎"",""附着点炎"",""间隙强化"",""关节积液""，""骨质硬化"",""骨芽""
                提取要求:
                1.""滑囊炎"",""附着点炎"",""间隙强化"",""关节积液""，""骨质硬化"",""骨芽""项目备注左左右及数值，没有提取到数值默认空；
                2.保证提取的完整性
                3 按照如下json格式输出内容
                [
                    {
                        ""第几次检查"": """",
                        ""检查日期"": """",
                        ""过渡层面"": """",
                        ""机型"": """",
                        ""滑囊炎左"": """",
                         ""滑囊炎右"": """",
                        ""附");
                WriteLiteral(@"着点炎左"": """",
                        ""附着点炎右"":"""",
                        ""间隙强化左"": """",
                         ""间隙强化右"": """",
                        ""关节积液左"": """",
                        ""关节积液右"": """",
                        ""骨质硬化左"":"""",
                        ""骨质硬化右"":"""",
                        ""骨芽左"":"""",
                        ""骨芽右"":""""
                    },
                     {
                        ""序号"": """",
                        ""姓名"": """",
                        ""第几次检查"": """",
                        ""检查日期"": """",
                        ""过渡层面"": """",
                        ""机型"": """",
                        ""滑囊炎左"": """",
                         ""滑囊炎右"": """",
                        ""附着点炎左"": """",
                        ""附着点炎右"":"""",
                        ""间隙强化左"": """",
                         ""间隙强化右"": """",
                        ""关节积液左"": """",
                        ""关节积液右"": """",
                        ""骨质硬化左"":"""",
                        ""骨质硬化右"":"""",
                        ""骨芽左"":"""",
  ");
                WriteLiteral(@"                      ""骨芽右"":""""
                    }
                ] `;
                            var tips4 = `
                        提取出如下项目内容：部位（左）、部位（右）、淋巴结个数（左）、淋巴结个数（左）、癌转移（左）、癌转移（右）、部位（甲状腺）、甲状腺被膜状态、BRAF基因、Ki67、CK19、Galectin-3、MC、CD56、CycinD1、报告日期。
        JSON格式输出：[{""项目"":""提取要求中的项目"",""项目值"":""原文提取项目对应的数值或病变描述"",""原文依据"":""简短的原文依据""},{""项目"":""提取要求中的项目"",""项目值"":""原文提取项目对应的数值或病变描述"",""原文依据"":""简短的原文依据""},……]`;
                            if (obj.checked && obj.data.type == ""眼科"")
                            {
                                  $(""#model_wrapR"").val(tips1);
                            }
                          else  if (obj.checked && obj.data.type == ""康复科"") {
                                $(""#model_wrapR"").val(tips2);
                            }
                            else if (obj.checked && obj.data.type == ""院外数据"") {
                                $(""#model_wrapR"").val(tips3);
                            }
                            else if (obj.checked && obj.data.type == """);
                WriteLiteral(@"病理"") {
                                $(""#model_wrapR"").val(tips4);
                            }
                            else if(!obj.checked)
                            {
                                var selectTrees = tree.getChecked('demo-id-1');
                                if(selectTrees.length>0)
                                {
                                    var selectItem = selectTrees[0];
                                    if (selectItem.type == ""眼科"")
                                    {
                                        $(""#model_wrapR"").val(tips1);
                                    }
                                    else if (selectItem.type == ""康复科"") {
                                         $(""#model_wrapR"").val(tips2);
                                    }
                                    else if (selectItem.type == ""院外数据"") {
                                         $(""#model_wrapR"").val(tips3);
                                    }
          ");
                WriteLiteral(@"                          else if (selectItem.type == ""病理"") {
                                        $(""#model_wrapR"").val(tips4);
                                    }
                                }
                                console.log(obj);
                            }

                        }

                        ,

                        operate: function (obj) {
                            var type = obj.type; //得到操作类型：add、edit、del
                            var data = obj.data; //得到当前节点的数据
                            var elem = obj.elem; //得到当前节点元素

                            //Ajax 操作
                            var id = data.id; //得到节点索引
                            if (type === 'preview') { //增加节点
                                //返回 key 值
                                if (data.docmentType == ""文件"") {
                                    layer.open({
                                        type: 2,
                                        title: 'PDF预览',
   ");
                WriteLiteral(@"                                     resize: true,
                                        maxmin: true,
                                        shadeClose: true, // 点击遮罩关闭层
                                        area: ['90%', '90%'], // 设置弹窗大小
                                        content: data.fileUrl
                                    });
                                }
                                else {
                                    layer.msg(""当前目录不支持预览！"");
                                }

                            }
                        }
                    });
                })
            }
            getTree();


            //设置Y轴位置
            function resetHistoryscrollTop() {
                var ele = document.getElementById(""resultPdf"");
                if (ele.scrollHeight > ele.clientHeight) {
                    setTimeout(function () {
                        //设置滚动条到最底部
                        ele.scrollTop = ele.scrollHeight;
             ");
                WriteLiteral(@"       }, 500);
                }
            }

            function hidden() {
                $("".menu_wrap"").css(""display"", ""none"")
                $("".menu_list"").removeClass(""show_menu_list"")
            }

            function load(){
                var n = 0;
                var timer = setInterval(function () {
                    n = n + Math.random() * 10 | 0;
                    if (n > 100) {
                        n = 100;
                        clearInterval(timer);

                    }
                    element.progress('demo-filter-progress', n + '%');
                }, 300 + Math.random() * 4000);
            }


            upload.render({
                elem: '#test10'
                , url: '/Demo/NewPdfAI/UploadFile' // 实际使用时改成您自己的上传接口即可。
                , accept: 'file' // 限制文件类型
                , acceptMime: 'application/pdf' // 仅接受 PDF 文件
                , done: function (res) {
                    layer.msg('上传成功');

                   // conso");
                WriteLiteral(@"le.log(res)
                   if(res.code==0){
                        $(""#fileName"").val(res.data.fileName);
                        $(""#guid"").val(res.data.guid);
                        $(""#newFileName"").val(res.data.newFileName);

                   }
                   else
                      layer.msg(res.msg);
                }
            });

             upload.render({
                elem: '#test11'
                , url: '/Demo/NewPdfAI/UploadFile2' // 实际使用时改成您自己的上传接口即可。
                , accept: 'image/*' // 限制为图片类型
                , acceptMime: 'image/jpg,image/png,image/jpeg' // 限制图片扩展名
                , done: function (res) {
                    layer.msg('上传成功');

                   // console.log(res)
                   if(res.code==0){
                        $(""#fileName"").val(res.data.fileName);
                        $(""#guid"").val(res.data.guid);
                        $(""#newFileName"").val(res.data.newFileName);

                   }
                  ");
                WriteLiteral(@" else
                      layer.msg(res.msg);
                }
            });

            //监听提交
            form.on('submit(submit)', function (data) {
                var indexs = layer.load(1);
                data.field.newFileName = $(""#newFileName"").val();
                data.field.guid = $(""#guid"").val();
                $.ajax({
                    url: ""/Demo/NewPdfAI/SavePdf"",
                    type: ""post"",
                    data:data.field ,
                    datatype: 'json',
                    success: function (result) {
                        layer.close(indexs);
                        if (result.code == 0) {

                            layer.close(windowsIndex);
                            loadTree();
                        }
                        else
                            layer.msg(result.msg);

                    }, error: function (res) {
                        layer.msg(""加载统计信息错误："" + res.responseText);
                        layer.cl");
                WriteLiteral(@"ose(indexs);
                    }
                });
                return false;
            });


            function loadTable(obj)
            {
                try {
                     $(""#tableResult"").html("""");
                     console.log(obj);
                    var data = JSON.parse(obj);
                    if (Array.isArray(data)) {
                        var rowWidth = """";
                        var rowHead = """";
                        var Dta1 = data[0];
                        $.each(Dta1, function (key, value) {

                            rowHead += ""<td>"" + key + ""</td>"";

                            rowWidth += ""<col width='150'>"";
                        });
                        rowHead = ""<thead><tr>"" + rowHead + ""</tr></thead>"";
                        rowWidth = ""<colgroup>"" + rowWidth + ""</colgroup>"";
                        $(""#tableResult"").append(rowWidth);
                        $(""#tableResult"").append(rowHead);

                     ");
                WriteLiteral(@"  var dataRow="""";
                        $.each(data, function (index, item) {
                             var row2 = """";
                            $.each(item, function (key, value) {

                                var newValue = """";
                                if (typeof value === 'string') {
                                    // 如果数据已经是字符串，直接返回
                                    newValue = value;
                                } else if (typeof value === 'object' && value !== null) {
                                    // 如果数据是对象，使用 JSON.stringify 序列化
                                    try {
                                        var jsonString = JSON.stringify(value);
                                        newValue = jsonString;
                                    } catch (e) {

                                    }
                                }
                                else {
                                    newValue = value;
                             ");
                WriteLiteral(@"   }
                                row2 += ""<td>"" + newValue + ""</td>"";
                        });

                            dataRow += ""<tr>"" + row2 + ""</tr>"";

                        });
                        dataRow += ""<tbody>"" + dataRow + ""</tbody>"";
                        $(""#tableResult"").append(dataRow);
                    }
                    else {
                        var rowWidth = """";
                        var rowHead = """";
                        $.each(data, function (key, value) {

                            rowHead += ""<td>"" + key + ""</td>"";

                            rowWidth += ""<col width='150'>"";
                        });
                        rowHead = ""<thead><tr>"" + rowHead + ""</tr></thead>"";
                        rowWidth = ""<colgroup>"" + rowWidth + ""</colgroup>"";
                        $(""#tableResult"").append(rowWidth);
                        $(""#tableResult"").append(rowHead);

                        var row2 = """";
              ");
                WriteLiteral(@"          $.each(data, function (key, value) {
                            var newValue="""";
                            if (typeof value === 'string') {
                                // 如果数据已经是字符串，直接返回
                                newValue = value;
                            } else if (typeof value === 'object' && value !== null) {
                                // 如果数据是对象，使用 JSON.stringify 序列化
                                try {
                                    var jsonString = JSON.stringify(value);
                                   newValue= jsonString;
                                } catch (e) {

                                }
                            }
                            else

                            {
                                newValue = value;
                            }
                            row2 += ""<td>"" + newValue + ""</td>"";


                        });
                        row2 = ""<tbody><tr>"" + row2 + ""</tr></tbody>"";
   ");
                WriteLiteral(@"                     $(""#tableResult"").append(row2);
                    }


                } catch (error) {
                    console.error('JSON字符串格式错误:', error.message);
                    alert('JSON字符串格式错误:' + error.message);
                }
            }


            //table.render({
            //    elem: '#tablelist2'
            //    , id: 'tablelist2',

            //    data: []
            //    , limit: Number.MAX_VALUE
            //    , cols: [[
            //        { field: 'zizeng', title: '', type: 'numbers' }
            //        , { field: 'fields', title: '字段名', width: 110 }
            //        , { field: 'fieldsValue', title: '字段值' }

            //    ]]

            //    , page: false // 关闭分页
            //});
        });
    </script>



");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"

<div class=""window_wrap"" id=""form_window_load"" style=""display: none;background-color:transparent"">
    <div class=""layui-progress layui-progress-big"" lay-showPercent=""true"" lay-filter=""demo-filter-progress"">
        <div class=""layui-progress-bar"" lay-percent=""0%"">
        </div>

    </div>
    <p style=""text-align:center""> AI数据提取中...</p>
</div>

<div class=""window_wrap"" id=""form_window"" style=""display: none;"">
    <form class=""layui-form"" lay-filter=""fm"" id=""fm""");
            BeginWriteAttribute("action", " action=\"", 35815, "\"", 35824, 0);
            EndWriteAttribute();
            WriteLiteral(@">

        <div class=""layui-form-item"" style=""margin-top:15px;"">
            <label class=""layui-form-label"">科室：</label>
            <div class=""layui-input-block"" style=""width:300px;"">
                <select id=""DeptName"" name=""DeptName"" lay-filter=""DeptName"">
                    <option value=""眼科"">眼科</option>
                    <option value=""康复科"">康复科</option>
                    <option value=""院外数据"">院外数据</option>
                    <option value=""病理"">病理</option>
                </select>
            </div>
        </div>
        <div class=""layui-form-item"" id=""div10"">
            <label class=""layui-form-label"">上传附件：</label>
            <div class=""layui-input-block line_wrap"">
                <div class=""layui-upload-drag"" id=""test10"">
                    <i class=""layui-icon""></i>
                    <p>点击上传，或将文件拖拽到此处</p>
                    <div class=""layui-hide"" id=""uploadDemoView"">
                        <hr>

                        <input type=""hidden"" id=""newFileName"" ");
            WriteLiteral(@"/>
                        <input type=""hidden"" id=""guid"" />
                    </div>
                </div>
            </div>
        </div>
        <div class=""layui-form-item"" id=""div11"" style=""display:none"">
            <label class=""layui-form-label"">上传附件：</label>
            <div class=""layui-input-block line_wrap"">
                <div class=""layui-upload-drag"" id=""test11"">
                    <i class=""layui-icon""></i>
                    <p>点击上传，或将文件拖拽到此处</p>
                    <div class=""layui-hide"" id=""uploadDemoView11"">
                        <hr>
                    </div>
                </div>
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">文件名称：</label>
            <div class=""layui-input-block"" style=""width:300px;"">
                <input type=""text"" id=""fileName"" class=""layui-input"" name=""fileName"" />

            </div>
        </div>
        <div class=""layui-form-item"">
            <div cl");
            WriteLiteral(@"ass=""layui-input-block"">
                <button type=""submit"" class=""layui-btn"" lay-submit lay-filter=""submit"">提交</button>
                <button type=""reset"" class=""layui-btn layui-btn-primary"" id=""btn_reset"">重置</button>
            </div>
        </div>
    </form>
</div>
</html>

");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
