#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\PromptInfo\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "eaf8ef158c4bdce8b427ec956f502c23eb8666aa8581aa5dd1eeec880fd39912"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_BasicConfig_Views_PromptInfo_Index), @"mvc.1.0.view", @"/Areas/BasicConfig/Views/PromptInfo/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"eaf8ef158c4bdce8b427ec956f502c23eb8666aa8581aa5dd1eeec880fd39912", @"/Areas/BasicConfig/Views/PromptInfo/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_BasicConfig_Views_PromptInfo_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("charset", new global::Microsoft.AspNetCore.Html.HtmlString("utf-8"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/Kindeditor/kindeditor-all.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/Kindeditor/themes/default/default.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/marked.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\PromptInfo\Index.cshtml"
  
    Layout = null; 

#line default
#line hidden
#nullable disable

            WriteLiteral("<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "eaf8ef158c4bdce8b427ec956f502c23eb8666aa8581aa5dd1eeec880fd399127784", async() => {
                WriteLiteral("\r\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n    <meta charset=\"utf-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>提示词管理</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "eaf8ef158c4bdce8b427ec956f502c23eb8666aa8581aa5dd1eeec880fd399128295", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "eaf8ef158c4bdce8b427ec956f502c23eb8666aa8581aa5dd1eeec880fd399129497", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "eaf8ef158c4bdce8b427ec956f502c23eb8666aa8581aa5dd1eeec880fd3991210699", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "eaf8ef158c4bdce8b427ec956f502c23eb8666aa8581aa5dd1eeec880fd3991211910", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "eaf8ef158c4bdce8b427ec956f502c23eb8666aa8581aa5dd1eeec880fd3991213113", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
   
        .layui-table-view .layui-table-main {
            overflow-x: hidden !important; /* 隐藏横向滚动条 */
        }
  
        ul {
            list-style-type: none; /* 移除默认的列表项样式 */
            padding: 0;
            margin: 0;
        }

            ul li {
                padding: 8px;
                margin: 5px;
                background-color: #f0f0f0; /* 默认背景色 */
                color: #333; /* 默认文字颜色 */
            }

                ul li:hover {
                    background-color: #ddd; /* 鼠标悬停时的背景色 */
                    color: #000; /* 鼠标悬停时的文字颜色 */
                }

        .tooltip {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
        }
        /*弹窗*/
        .window_wrap {
            padding: 15px;
        }

        .prompt {
            margin: 5px 15px;
    ");
                WriteLiteral(@"        display: flex;
            flex-direction: row;
            align-items: center;
        }

        .btn_wrap {
            position: absolute;
            bottom: 0;
            right: 15px;
        }

        .dialog_box {
            padding: 10px;
            overflow-y: auto;
            font-size: 20px;
        }

        .operating {
            width: 100%;
            position: fixed;
            bottom: 0;
        }

        .search_wrap {
            background-color: #f0f0f0;
        }

        .line_wrap {
            padding: 10px 15px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        #detail_window .layui-form-label {
            width: 100px;
        }

        #detail_window .layui-form-val {
            padding: 9px 15px;
        }

        #detail_window .mindow_icon .title_icon {
            display: inline-block;
        }

        #detail_window .mindow_icon {
");
                WriteLiteral(@"            text-align: center;
            padding: 20px 0;
        }

        #detail_window .layui-form-item {
            margin-bottom: 0;
        }
        /* 定义表头样式 */
        .layui-table-header .layui-table-cell {
            font-weight: bold; /* 加粗 */
            font-size: 14px; /* 可选：调整字体大小以提升可读性 */
            color: #333; /* 可选：调整字体颜色 */
        }

        .table_wrap {
            overflow: hidden;
        }

        .input_wrap {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin: 5px 15px;
            background-color: #fff;
            border: 1px solid #7a4d7b;
            border-radius: 6px;
            overflow: hidden;
        }

            .input_wrap .layui-icon {
                color: #7a4d7b;
                padding: 12px;
            }

            .input_wrap .layui-input {
                border: none;
                padding-left: 0;
            }

                .input_wrap .l");
                WriteLiteral("ayui-input:hover {\r\n                    border: none;\r\n                }\r\n        .item_bottom{\r\n            display:flex;\r\n            flex-direction:row;\r\n            align-items:flex-end;\r\n        }\r\n    </style>\r\n\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "eaf8ef158c4bdce8b427ec956f502c23eb8666aa8581aa5dd1eeec880fd3991218356", async() => {
                WriteLiteral(@"

    <div class=""layui-col-md12"">

        <div class=""layui-card"">
            <div class=""line_wrap search_wrap"">
                <div>
                    <div class=""layui-inline"">
                        <label class=""layui-form-label"">搜索条件</label>
                        <div class=""layui-input-inline"">
                            <input type=""text"" name=""keyWordPropmtIdentifier"" id=""keyWordPropmtIdentifier"" placeholder=""请输入提示词常量"" autocomplete=""off"" class=""layui-input"">
                        </div>
                        <div class=""layui-input-inline"">
                            <input type=""text"" name=""keyWordPropmtName"" id=""keyWordPropmtName"" placeholder=""请输入提示词简称"" autocomplete=""off"" class=""layui-input"">
                        </div>
                        <button class=""layui-btn layui-btn-primary layui-border-green"" id=""Search""><i class=""layui-icon layui-icon-search""></i> </button>
                    </div>
                </div>

                <div>
                  ");
                WriteLiteral(@"  <button type=""button"" class=""layui-btn layui-bg-blue"" id=""addField""><i class=""layui-icon layui-icon-add-1""></i></button>
                </div>
            </div>
            <div class=""edit_area"">
                <div class=""line_wrap layui-card layui-colla-content layui-show"">
                    <div class=""layui-row form_wrap layui-form"">
                        <div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"">
                            <div class=""layui-form-item"">
                                <label class=""layui-form-label"">控制器名</label>
                                <div class=""layui-input-block "">
                                    <input type=""text"" name=""Id"" id=""Id"" style=""display:none;"" />
                                    <input type=""text"" name=""PropmtIdentifier"" id=""PropmtIdentifier""  placeholder=""请输入控制器名"" autocomplete=""off"" class=""layui-input"">
                                </div>
                            </div>
                        </div>

            ");
                WriteLiteral(@"            <div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"">
                            <div class=""layui-form-item"">
                                <label class=""layui-form-label"">提示词简称</label>
                                <div class=""layui-input-block "">
                                    <input type=""text"" name=""PropmtName"" id=""PropmtName""  placeholder=""请输入提示词简称"" autocomplete=""off"" class=""layui-input"">
                                </div>
                            </div>
                        </div>
                        <div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"">
                            <div class=""form_item"">
                                <label class=""layui-form-label"">大模型</label>
                                <div class=""layui-input-block "">
                                    <select name=""ModelName"" id=""ModelName""");
                BeginWriteAttribute("lay-search", " lay-search=\"", 6876, "\"", 6889, 0);
                EndWriteAttribute();
                WriteLiteral(@">
                                        <option value=""通用"" selected>通用</option>
                                        <option value=""glm-4"">glm-4</option>
                                        <option value=""deepseek"">deepseek</option>
                                        <option value=""qwq"">qwq</option>
                                        <option value=""其他"">其他</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"" style=""display: none"">
                            <div class=""layui-form-item"">
                                <label class=""layui-form-label"">科室编码</label>
                                <div class=""layui-input-block "">
                                    <input type=""text"" name=""KSBMKId"" id=""KSBMKId"" placeholder=""请输入科室编码"" autocomplete=""off"" class=""layui-input"">
                         ");
                WriteLiteral(@"       </div>
                            </div>
                        </div>
                        <div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"" style=""display: none"">
                            <div class=""layui-form-item"">
                                <label class=""layui-form-label"">科室名称</label>
                                <div class=""layui-input-block "">
                                    <input type=""text"" name=""DepName"" id=""DepName"" placeholder=""请输入科室名称"" autocomplete=""off"" class=""layui-input"">
                                </div>
                            </div>
                        </div>
                        <div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"" style=""display: none"">
                            <div class=""layui-form-item"">
                                <label class=""layui-form-label"">检查类型</label>
                                <div class=""layui-input-block "">
                                    <input type=""text"" name=""ExamName"" id=""");
                WriteLiteral(@"ExamName"" placeholder=""请输入检查类型"" autocomplete=""off"" class=""layui-input"">
                                </div>
                            </div>
                        </div>
                        <div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"" style=""display: none"">
                            <div class=""layui-form-item"">
                                <label class=""layui-form-label"">疾病编码</label>
                                <div class=""layui-input-block "">
                                    <input type=""text"" name=""DiseaseCode"" id=""DiseaseCode"" placeholder=""请输入疾病编码"" autocomplete=""off"" class=""layui-input"">
                                </div>
                            </div>
                        </div>
                        <div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"">
                            <div class=""layui-form-item"">
                                <label class=""layui-form-label"">是否启用</label>
                                <div class=""layui-input-b");
                WriteLiteral(@"lock "">
                                    <input type=""checkbox"" name=""IsUsed"" id=""IsUsed"" title=""是|否"" lay-skin=""switch"">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class=""layui-row  item_bottom"">
                        <div class=""layui-col-xs6 layui-col-sm6 layui-col-md6"" style=""border:0px red solid"">
                            <div class=""layui-form-item"">
                                <label class=""layui-form-label"">提示词</label><span style=""color:cornflowerblue"" id=""mySpan""> {X}引用变量</span>
                                <div id=""tooltip"" class=""tooltip"" style=""display:none"">
                                    <ul id=""RV"">
                                        <li>{{报告内容}}</li>
                                        <li>{{提取格式要求}}</li>
                                        <li>{{提取特征要求}}</li>
                                        <li>{{用户输入提示词}}</li>
              ");
                WriteLiteral("                      </ul>\r\n\r\n                                </div>\r\n");
                WriteLiteral(@"                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <i class=""layui-icon layui-icon-dialogue"" id=""ChatBtn""></i>
                                <div class=""layui-input-block "">
                                    <div name=""Prompt"" id=""Prompt"" class=""layui-textarea"" style=""height:400px;"" contenteditable=""true""></div>
                                </div>
                            </div>
                        </div>
                        <div class=""layui-col-xs5 layui-col-sm5 layui-col-md5"" style=""border:0px red solid"">
                            <div class=""layui-form-item"">
                                <label class=""layui-form-label"">说明</label>

                                <div class=""layui-input-block "">
                                    <textarea name=""Note"" id=""Note"" class=""layui-textarea"" style=""resize: none; height: 340px;margin-top:20px;""></textarea>
                                </div>
                            </div>
    ");
                WriteLiteral(@"                    </div>
                        <div class=""layui-col-xs1 layui-col-sm1 layui-col-md1"">
                            <div class=""btnwrap layui-form-item"" style=""text-align:right;margin-top:0px;"">
                                <button class=""layui-btn"" lay-submit lay-filter=""submit"">保存</button>
                            </div>
                        </div>
                        </div>
                 
                    </div>

            </div>

            <div class=""layui-card-body table_wrap"">
                <!--编辑区-->

                <table id=""tablelist"" lay-filter=""tablelist""></table>
                <script type=""text/html"" id=""tableBar"">

                    <a class=""layui-btn layui-btn-danger layui-btn-xs"" lay-event=""del"" style=""text-decoration:none""><i class=""layui-icon layui-icon-delete""></i>删除</a>
                </script>
            </div>
        </div>


    </div>



    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "eaf8ef158c4bdce8b427ec956f502c23eb8666aa8581aa5dd1eeec880fd3991228499", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "eaf8ef158c4bdce8b427ec956f502c23eb8666aa8581aa5dd1eeec880fd3991229623", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "eaf8ef158c4bdce8b427ec956f502c23eb8666aa8581aa5dd1eeec880fd3991230747", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "eaf8ef158c4bdce8b427ec956f502c23eb8666aa8581aa5dd1eeec880fd3991231871", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"

    <script>
        var windowsIndex;
        var isMouseOverTooltip;
        var Prompt;
        KindEditor.ready(function (K) {
        
            Prompt = K.create('#Prompt', {
                cssData: 'body{font: 16px/1.5 ""sans serif"",tahoma,verdana,helvetica;}',
              
                
                afterChange: function ( ) {

                   
                  //  this.html(this.formatHtml(html));
                   console.log(this.html());
                }

            });
           // Prompt.exec('fontsize', '16px');

 
            $('#RV').on('click', 'li', function () {
                var startOffset = Prompt.cmd.range.startOffset;
                var htmlContent = `<span style='color:blue'> ` + $(this).text() + `</span>  `;
               // var old = `<span style='color:black'></span>  `  ;
                Prompt.insertHtml(htmlContent);
                //Prompt.appendHtml(old);
            });
            //K(""#RV"").change(function () {


  ");
                WriteLiteral(@"          //    var index = this.selectedIndex;
            //    if (this.options[index].text == """") return;
            //    var startOffset = Prompt.cmd.range.startOffset;//kindEditor中当前光标的位置索引值startOffset
            //    var htmlContent = ` <span style='color:blue'> ` + this.options[index].text +`</span>`;
            //     //var old = '<span style=""color:black"">1</span>';
            //  //  KindEditor.insertHtml(Prompt, htmlContent + old);
               
            //    Prompt.insertHtml(htmlContent );
                
                 

                
                 
            //});
        });

        $(document).ready(function () {

            var elements = document.querySelectorAll('.ke-toolbar');
            document.querySelector('.ke-toolbar').style.display = 'none';
            elements = document.querySelectorAll('.ke-statusbar');
            document.querySelector('.ke-statusbar').style.display = 'none';

           // var startOffset = Prompt.cmd.range.s");
                WriteLiteral(@"tartOffset;//kindEditor中当前光标的位置索引值startOffset
           // Prompt.insertHtml('<span style=""color:red"">pppp</span>');

            var span = document.getElementById('mySpan');
            var tooltip = document.getElementById('tooltip');

        
    

            tooltip.addEventListener('mouseenter', function () {
                tooltip.style.display = 'block';
                isMouseOverTooltip = true; // 鼠标进入tooltip区域
            });

            tooltip.addEventListener('mouseleave', function () {
                  isMouseOverTooltip = false;
                    document.getElementById('tooltip').style.display = 'none';
           
            });

            // 鼠标悬停时改变光标样式
            span.addEventListener('mouseover', function () {
                this.style.cursor = 'pointer';

                var ChatBtn = document.getElementById('ChatBtn');
                var tooltip = document.getElementById('tooltip');
                tooltip.style.display = 'block';
               ");
                WriteLiteral(@" tooltip.style.left = span.offsetLeft + 30 + 'px'; // 调整位置
                tooltip.style.top = span.offsetTop - 10 + 'px'; // 调整位置
                isMouseOverTooltip = true;
            });

            // 鼠标离开时改变光标样式
            span.addEventListener('mouseout', function () {
                this.style.cursor = 'default'; // 或者其他你想要的默认样式

                if (isMouseOverTooltip == false) {
                    document.getElementById('tooltip').style.display = 'none';
                }
                
               
                
            });

        });


    </script>


    <script>


        layui.use(['element', 'layer', 'table', 'laydate', 'form'], function () {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , laydate = layui.laydate
                , $ = layui.$
                , form = layui.form;

            var areawrap = $("".edit_area"").height();
            areawrap +");
                WriteLiteral(@"= 85;



                table.render({
                    elem: '#tablelist'
                    , id: 'tablelist'
                    , page: false
                    , limit: Number.MAX_VALUE
                    , height: 'full-' + areawrap
                , cols: [[
                    { field: 'No', title: '序号', type: 'numbers', fixed: 'left' }
                    , { field: 'Id', title: '表单Id', hide: true }
                    , { field: 'PropmtIdentifier', title: '控制器名', width: 180 }
                    , { field: 'PropmtName', title: '提示词简称', width: 180 }
                    , { field: 'ModelName', title: '模型', width: 120 }
                    , {
                        field: 'IsUsed', title: '是否启用', width: 100, templet: function (d) {
                            return '<input type=""checkbox"" name=""center"" lay-filter=""centerSwitch"" data-id=""' + d.Id + '"" title=""是|否"" lay-skin=""switch"" ' + (d.IsUsed ? 'checked' : '') + '>';
                        }
                    }
      ");
                WriteLiteral(@"              , { field: 'Prompt', title: '提示词', width: 400 }
                    , { field: 'CreateUser', title: '创建者', width: 120 }
                    , { field: 'CreatedTime', title: '创建时间', width: 200 }
                    , { field: 'Note', title: '说明' , width: 180 }
                    , { title: '操作', toolbar: '#tableBar', width: 160, minWidth: 160, fixed: 'right' }
                    ]]
                   
                , done: function (res, curr, count) {
                  //  document.querySelector('.layui-table-main').style.minHeight = '100px';
                    $('.layui-laypage').remove();
                    form.render();
                }
            });

        



            //$(document).on('change', '#RV', function (event) {

      
            //    var index = this.selectedIndex;
            //    if (this.options[index].text == """") return;
            //    var startOffset = Prompt.cmd.range.startOffset;//kindEditor中当前光标的位置索引值startOffset
            //  ");
                WriteLiteral(@"  var htmlContent = '<span style=""color:blue"">' + this.options[index].text + '</span>';
            //  //  KindEditor.insertHtml(Prompt, htmlContent);
            //    Prompt.insertHtml(htmlContent);
            //    $('#Prompt').caret(startOffset);
            //    $('#Prompt').focus();
             
                 
               
            //});
      

            table.on('tool(tablelist)', function (obj) {
                var data = obj.data;
                if (obj.event === 'del') {
                    layer.confirm('确定要删除名为【' + data.PropmtIdentifier + '】的相关信息吗？', {
                        title: '',
                        btn: ['确定', '取消'], //按钮
                        resize: false
                    }, function (index) {
                        $.post('/BasicConfig/PromptInfo/Del', { id: data.Id }, function (result) {
                            if (result.okMsg) {
                                layer.msg(result.okMsg);
                                currentIndex =");
                WriteLiteral(@" -1;
                                table.reload('tablelist'); //重载表格
                                EmptyData();
                            } else {
                                layer.msg(result.errorMsg);
                            }
                        }, 'json');
                        layer.close(index);
                    });
                }
            });


            table.on('row(tablelist)', function (obj) {
                var data = obj.data;
                if (data) {
                    $(""#PropmtIdentifier"").attr(""readonly"", true)
                }

                $(""#PropmtIdentifier"").val(data.PropmtIdentifier);
                $(""#PropmtName"").val(data.PropmtName);
                $(""#ModelName"").val(data.ModelName).change();
                Prompt.html(ReplaceStr(marked.parse(data.Prompt) ) );
                $(""#Note"").val(data.Note);
                // 设置开关组件的状态
                $(""#IsUsed"").prop(""checked"", data.IsUsed);
                $(""#Id"")");
                WriteLiteral(@".val(data.Id);
                // 重新渲染表单
                form.render();
            });
            $(document).on('click', '#addField', function () {
                EmptyData();
            });

            $(document).ready(function () {
                $(""#IsUsed"").prop(""checked"",true);
                getData();
                $(document).on('click', '#Search', function () {
                    getData();
                })
            });
            function getData() {
                table.reload('tablelist', {
                    page: {
                        curr: 1
                    },
                    url: '/BasicConfig/PromptInfo/List',
                    where: {
                        'PropmtIdentifier': $.trim($(""#keyWordPropmtIdentifier"").val()),
                        'PropmtName': $.trim($(""#keyWordPropmtName"").val()) 
                    }
                });
            }

            function ReplaceStr(xPrompt) {

                let match = xPr");
                WriteLiteral(@"ompt.match(/{{(.*?)}}/g);
                if (match) {
                    for (let i = 0; i < match.length; i++) {
                        let content = match[i];
                       

                        xPrompt = xPrompt.replaceAll(content, ""<span style='color:blue'>"" + content + ""</span>"");
                    }
                }


                if (xPrompt.indexOf(""{{报告内容}}"")>=0) {
                    xPrompt = xPrompt.replaceAll(""{{报告内容}}"", ""<span style='color:blue'>{{报告内容}}</span>"");
                    console.log(xPrompt);
                }
                if (xPrompt.indexOf(""{{提取格式要求}}"")>=0) {
                    xPrompt = xPrompt.replaceAll(""{{提取格式要求}}"", ""<span style='color:blue'>{{提取格式要求}}</span>"");
                }
                if (xPrompt.indexOf(""{{提取特征要求}}"") >=0) {
                    xPrompt = xPrompt.replaceAll(""{{提取特征要求}}"", ""<span style='color:blue'>{{提取特征要求}}</span>"");

                } 

                if (xPrompt.indexOf(""{{用户输入提示词}}"")>=0) {
      ");
                WriteLiteral(@"              xPrompt = xPrompt.replaceAll(""{{用户输入提示词}}"", ""<span style='color:blue'>{{用户输入提示词}}</span>"");
                }
             
               
                return xPrompt
            }


            function EmptyData() {
                $('#Id').val('');
                $('#PropmtIdentifier').val('');
                $('#PropmtName').val('');
                $(""#Note"").val('');
                Prompt.html('');
                $('#IsUsed').prop('checked', true);
                $(""#PropmtIdentifier"").attr(""readonly"", false)
                form.render();
            }

            //监听提交
            form.on('submit(submit)', function (data) {
                var indes = layer.load(1);
                data.field.IsUsed = $(""#IsUsed"").is(':checked');
                data.field.PropmtIdentifier = $(""#PropmtIdentifier"").val();
                data.field.PropmtName = $(""#PropmtName"").val();
                data.field.ModelName = $(""#ModelName"").val();
                data.fi");
                WriteLiteral(@"eld.Prompt = Prompt.html();
                data.field.Note = $(""#Note"").val();
                data.field.Id = $(""#Id"").val();

                //提交 Ajax 成功后，关闭当前弹层并重载表格
                $.ajax({
                    url: '/BasicConfig/PromptInfo/Save',
                    type: ""post"",
                    data: { 'node': data.field, 'tempPrompt': Prompt.text()},
                    datatype: 'json',
                    success: function (data) {
                        if (data.okMsg) {
                            layer.msg(data.okMsg);
                            table.reload('tablelist'); //重载表格
                            if (data.code == ""0"") {
                                EmptyData();
                            }
                           
                        }
                        else {
                            layer.msg(data.errorMsg);
                        }
                        layer.close(indes);
                    }, error: function (res) {
            ");
                WriteLiteral(@"            layer.msg(""加载统计信息错误："" + res.responseText);
                        layer.close(indes);
                    }
                });
                return false;
            });


            form.on('switch(centerSwitch)', function (data) {
                var id = data.elem.getAttribute('data-id'); // 获取唯一ID
                var checked = data.elem.checked;
                $.ajax({
                    url: ""/BasicConfig/PromptInfo/UpdateStatus"",
                    type: ""post"",
                    data: { 'id': id, 'status': checked },
                    datatype: 'json',
                    success: function (data) {
                        if (data.okMsg) {
                            layer.msg(data.okMsg);
                            table.reload('tablelist'); //重载表格
                        }
                        else {
                            layer.msg(data.errorMsg);
                        }
                        layer.close(indes);
                    }, er");
                WriteLiteral(@"ror: function (res) {
                        layer.msg(""加载统计信息错误："" + res.responseText);
                        layer.close(indes);
                    }
                });

            });

            

            $(""#ChatBtn"").on(""click"", function () {

                if ($.trim(Prompt.text()) == """") {
                    layer.msg(""请输入优化的内容！"");
                    return;
                }

                var indexs = layer.load();
                $.ajax({
                    url: '/BasicConfig/PromptInfo/GetPrompt',
                    type: ""post"",

                    data: {
                        Speech:   Prompt.text() ,
                        ModelName: $(""#ModelName"").val(),

                    },
                    success: function (event) {
                        
                        if (event.code == ""0"") {
                            Prompt.exec('fontsize', '16px');
                            var htmlContent = marked.parse(event.data);

        ");
                WriteLiteral(@"                    Prompt.html(ReplaceStr(htmlContent));
                        }
                        else {
                            layer.msg(""获取失败！"" + event.msg);
                        }
                        layer.close(indexs);
                    }, error: function () {
                        layer.close(indexs);
                        layer.msg(""获取失败！"");
                    }
                })
               
            })

         

            function adjustWidth() {
               var textarea = document.getElementById('Prompt');
              ///  var Width = document.getElementById('Note').style.width; //
             //   textarea.style.width ='10%'; //  （px单位）
            }

            function setTableH() {
                var winH = $(window).height();
                var navH =  $("".layui-tab-brief"").height();
                var searchH = $("".search_wrap"").height();
                var editAreaH =  $("".edit_area"").height();
                var t");
                WriteLiteral(@"ableH = winH - (navH + searchH + editAreaH) - 190 + ""px"";
                console.log(tableH)
                $("".table_wrap"").css(""height"", tableH);
                adjustWidth();
            };
            setTableH();
            $(window).resize(function () {
                setTableH()
            });

        });
    </script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"

<div class=""layui-form"" id=""popwrap"" style=""display: none"">
    <div class=""layui-form-item layui-form-text"">
        <label class=""layui-form-label"">提示词</label>
        <div class=""layui-input-block"">
            <textarea placeholder=""请输入内容"" class=""layui-textarea""></textarea>
        </div>
    </div>

    <div class=""layui-form-item layui-form-text"">
        <label class=""layui-form-label"">返回结果</label>
        <div class=""layui-input-block"">
            <textarea class=""layui-textarea""></textarea>
        </div>
    </div>
    

    <div class=""layui-form-item"">
        <div class=""layui-input-block"">
            <button class=""layui-btn"" lay-submit lay-filter=""*"">优化</button>
            <button class=""layui-btn layui-btn-normal"" id=""btnSure"">确定</button>
            <button class=""layui-btn layui-btn-primary"" id=""btnCancel"">取消</button>
        </div>
    </div>
</div>
</html>
");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
