#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\CRFormsPad\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "4a97dd61fd9abfa79f901565d270edc25c1df054968d4eb47bf3eaff74ab2c66"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_BasicConfig_Views_CRFormsPad_Index), @"mvc.1.0.view", @"/Areas/BasicConfig/Views/CRFormsPad/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"4a97dd61fd9abfa79f901565d270edc25c1df054968d4eb47bf3eaff74ab2c66", @"/Areas/BasicConfig/Views/CRFormsPad/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_BasicConfig_Views_CRFormsPad_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/touchslider/touchslider.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin\\layui\\font\\web_font\\iconfont.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/paddemo_style.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("type", new global::Microsoft.AspNetCore.Html.HtmlString("text/javascript"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/touchslider/touchslider.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\CRFormsPad\Index.cshtml"
  
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4a97dd61fd9abfa79f901565d270edc25c1df054968d4eb47bf3eaff74ab2c667093", async() => {
                WriteLiteral("\r\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n    <meta charset=\"utf-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>表单管理</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "4a97dd61fd9abfa79f901565d270edc25c1df054968d4eb47bf3eaff74ab2c667603", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "4a97dd61fd9abfa79f901565d270edc25c1df054968d4eb47bf3eaff74ab2c668805", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "4a97dd61fd9abfa79f901565d270edc25c1df054968d4eb47bf3eaff74ab2c6610007", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "4a97dd61fd9abfa79f901565d270edc25c1df054968d4eb47bf3eaff74ab2c6611210", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        html {
            background-color: #fff;
        }
        body{
            background-color: #fff;
            overflow:hidden;
        }
        /* 移动端头部 */
        .headrow {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            background-color: #f6f6f6;
            height: 38px;
            line-height: 38px;
        }

            .headrow .layui-btn-primary {
                background-color: transparent;
                border: none;
                color: #333;
            }


        /* 移动端头部 */


        .contents {
            position: relative;
        }

        .CRF_list {
            padding: 0 10px 10px;
        }

        .CRF_item {
            background-color: #fff;
            display: flex;
            flex-direction: row;
            padding: 10px;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .crf_img {
 ");
                WriteLiteral(@"           overflow: hidden;
            width: 100px;
            height: 100px;
            border-radius: 10px;
            border:1px solid #eee;
        }

            .crf_img img {
                width: 100%;
                height: 100%;
            }

        .crf_info {
            flex: 1;
            padding-left: 15px;
        }

        .crf_title {
            font-size: 16px;
            font-weight: bold;
            line-height: 40px;
        }

        .crf_subtitle {
            min-height:40px;
        
        }

        .state{
            display:flex;
            flex-direction:row;
            justify-content:space-between;
        }

        .state_type{
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
        }

        .state p{
            padding-right:10px;
            color: #7A4D7B;
        }
        .text-limit-2-lines {
            display: -webkit-box;
            -webkit-box");
                WriteLiteral("-orient: vertical;\r\n            -webkit-line-clamp: 2;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n        }\r\n\r\n        .input_group {\r\n            margin: 0 auto;\r\n        }\r\n    </style>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4a97dd61fd9abfa79f901565d270edc25c1df054968d4eb47bf3eaff74ab2c6615469", async() => {
                WriteLiteral("\r\n    <div class=\"wrap\">\r\n\r\n");
                WriteLiteral("        <header class=\"aui-navBar aui-navBar-fixed\">\r\n        <div class=\"toptools layui-form\">\r\n            <div class=\"search_wrap\">\r\n        \r\n                    <div class=\"layui-inline\">\r\n                        <div");
                BeginWriteAttribute("class", " class=\"", 3679, "\"", 3687, 0);
                EndWriteAttribute();
                WriteLiteral(@" style=""width:68vw;"">
                            <div id=""xmDeptsList""></div>
                        </div>
                    </div>
                    <div class=""layui-input-inline"">
                        <button class=""layui-btn layui-btn-primary"" id=""Search"">
                            <i class=""layui-icon layui-icon-search""></i>
                        </button>
                    </div>
           
            </div>

        </div>
        </header>
        <div style=""height:95px;""></div>
        <div class=""contents"">
            <div class=""find_nav"">
                <div class=""find_nav_left"">
                    <div class=""find_nav_list"">
                        <ul id=""PageNavi1"">
");
                WriteLiteral("\r\n                        </ul>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div id=\"slider1\" class=\"swipe\">\r\n                <ul class=\"box_list\" id=\"box_list\">\r\n");
                WriteLiteral(@"                </ul>
            </div>
        </div>


        <div style=""height:60px;""></div>
        <footer class=""aui-footer aui-footer-fixed"">
            <a href=""javascript:;"" class=""aui-tabBar-item "" id=""yxzx"">
                <span class=""aui-tabBar-item-icon"">
                    <i class=""icon layui-icon icon-login_organization_icon""></i>
                </span>
                <span class=""aui-tabBar-item-text"">医学中心</span>
            </a>
            <a href=""javascript:;"" class=""aui-tabBar-item aui-tabBar-item-active"" id=""bdgl"">
                <span class=""aui-tabBar-item-icon"">
                    <i class=""icon layui-icon icon-biaodanguanli""></i>
                </span>
                <span class=""aui-tabBar-item-text "">eCRF</span>
            </a>
            <a href=""javascript:;"" class=""aui-tabBar-item "" id=""sjcj"">
                <span class=""aui-tabBar-item-icon"">
                    <i class=""icon layui-icon icon-shujucaiji""></i>
                </span>
     ");
                WriteLiteral(@"           <span class=""aui-tabBar-item-text"">数据采集</span>
            </a>
            <div class=""no-style aui-tabBar-item "">
                <span class=""aui-tabBar-item-icon"">
                    <i class=""icon layui-icon icon-wode-copy""></i>
                </span>
                <span class=""aui-tabBar-item-text"">我的</span>
            </div>
        </footer>
    
    
    </div>







    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4a97dd61fd9abfa79f901565d270edc25c1df054968d4eb47bf3eaff74ab2c6618795", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4a97dd61fd9abfa79f901565d270edc25c1df054968d4eb47bf3eaff74ab2c6620006", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4a97dd61fd9abfa79f901565d270edc25c1df054968d4eb47bf3eaff74ab2c6621217", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script type=""text/javascript"">
        layui.use(['jquery', 'layer', 'form', 'element', 'code', 'laytpl', 'table'], function () {
            var layer = layui.layer,
                $ = layui.$,
                form = layui.form,
                laytpl = layui.laytpl,
                element = layui.element,
                table = layui.table;

            //头部返回按钮
            if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
                $(""#previous"").show();
            }
            else {
                $(""#previous"").hide();
            }
            $(""#previous"").click(function () {
                window.location.href = ""/Home/ipadIndex"";
            })

            $(document).ready(function () {




                $(""#yxzx"").on(""click"", function () {
                    window.location.href = ""/BasicConfig/CRFormsPad/MedicalCente");
                WriteLiteral(@"rManage"";
                })

                $(""#bdgl"").on(""click"", function () {
                    window.location.href = ""/BasicConfig/CRFormsPad/Index"";
                })

                $(""#sjcj"").on(""click"", function () {
                    window.location.href = ""/BasicConfig/CRFormsPad/DataProfiling"";
                })










                GetDeptsTree();
                $(document).on('click', '#Search', function () {
                    GetList();
                });

                var xmDeptsList = xmSelect.render({
                    el: '#xmDeptsList',
                    model: { label: { type: 'text' } },
                    prop: {
                        name: 'title',
                        value: 'id',
                    },
                    minWidth: 200,
                    radio: true,
                    filterable: true,
                    clickClose: true,
                    //树
                    tree: {
                     ");
                WriteLiteral(@"   show: true,
                        //非严格模式
                        strict: false,
                        //默认展开节点
                        expandedKeys: [-1],
                    },
                    data: []
                });

                function GetDeptsTree() {
                    $.ajax({
                        url: '/CommAPI/GetOrgsTreeList',
                        type: ""post"",
                        datatype: 'json',
                        success: function (result) {
                            xmDeptsList.update({
                                data: result
                            });
                            if (result[0].id) {
                                var arr = new Array();
                                arr.push(result[0].id);
                                xmDeptsList.setValue(arr);
                                setTimeout(function () {
                                    GetList();
                                }, 1000);

          ");
                WriteLiteral(@"                  }

                        }, error: function () {
                            layer.msg(""获取失败！"");
                        }
                    })
                };

                function GetList() {
                    var indexs = layer.load();
                    var hospitalDeptId = xmDeptsList.getValue('valueStr');
                    $.ajax({
                        url: ""/BasicConfig/CRFormsPad/GetList"",
                        type: ""post"",
                        data: { ""hospitalDeptId"": hospitalDeptId },
                        success: function (res) {
                            if (res.code == 0) {
                                getDataMenu(res);
                                getData(res);
                                touchslider();
                                //跳转详情页
                                $("".CRF_list"").on(""click"", "".CRF_item"", function () {
                                    var fId = $(this).attr('fromId');
                   ");
                WriteLiteral(@"                 var formName = $(this).attr('formName');
                                    var formdis = $(this).attr('formdis');
                                    //console.log(fId);
                                    var url = '/BasicConfig/CRFormsPad/detail?fId=' + fId + '&formName=' + formName + '&formdis=' + formdis;
                                    //parent.layui.index.openTabsPage(url, ""CRF报告详情"");
                                    window.location.href = url;
                                })
                            } else {

                            }
                            layer.close(indexs);

                        }
                    });
                }

                function getDataMenu(res) {
                    var PageNavi1 = '';
                    var box_list = '';
                    $(""#PageNavi1"").html("""");
                    $(""#box_list"").html("""");

                    for (var i = 0; i < res.data.length; i++) {
                   ");
                WriteLiteral(@"     if (res.data[i].LevelType == 1) {
                            if (res.data[i].ParentId == 0) {
                                PageNavi1 += '<li><a href=""#"" class=""active"">全部</a></li>'
                                box_list += '<li class=""li_list""><div class=""CRF_list"" id=""CRF_list' + res.data[i].ParentId + '""></div></li>'
                            }
                            else {
                                PageNavi1 += '<li><a href=""#"">' + res.data[i].FormName + '</a></li>'
                                box_list += '<li class=""li_list""><div class=""CRF_list"" id=""CRF_list' + res.data[i].Id + '""></div></li>'
                            }
                        }
                    }
                    PageNavi1 += '<li class=""sideline""></li>';
                    $(""#PageNavi1"").append(PageNavi1);
                    $(""#box_list"").append(box_list);
                    form.render();
                }


                function getData(res) {
                    var CRF");
                WriteLiteral(@"_list = '';
                    for (var i = 0; i < res.data.length; i++) {
                        if (res.data[i].LevelType == 2) {

                            var subtitle = res.data[i].Remark

                            if (subtitle == null) {
                                subtitle=""暂无描述""
                            }

                            CRF_list += '<div class=""CRF_item"" fromId=""' + res.data[i].FormId + '"" formName=""' + res.data[i].FormName + '"" formdis=""' + res.data[i].Remark + '""><div class=""crf_img"">'
                                + '<img src=""../../images/CRFormsPad/eCRF_img/' + res.data[i].FormId + '_t.jpg"" /> </div>'
                                + '<div class=""crf_info""><div class=""crf_title text-limit-1-lines"">' + res.data[i].FormName + '</div>'
                                + '<p class=""crf_subtitle text-limit-2-lines"">' + subtitle + '</p> '
                                + '<div class=""state""><div class=""state_type""><p>已填报</p><p>已发布</p></div><div>负责人：刘班</div></");
                WriteLiteral(@"div></div></div>';

                            if (i + 1 == res.data.length) {
                                $(""#CRF_list"" + res.data[i].ParentId).append(CRF_list);
                                $(""#CRF_list0"").append(CRF_list);

                            }
                            else {
                                if (res.data[i].ParentId != res.data[i + 1].ParentId) {
                                    $(""#CRF_list"" + res.data[i].ParentId).append(CRF_list);
                                    $(""#CRF_list0"").append(CRF_list);
                                    CRF_list = '';
                                }
                            }


                        }
                    }
                    form.render();
                }

            });



            function touchslider() {
                //滑动导航start
                //给li定义高度，配合css使li独立滚动
                var $window = $(window);
                var initialWindowHeight = null;
             ");
                WriteLiteral(@"   $window.resize(function () {
                    sliderHeight();
                });
                sliderHeight();
                function sliderHeight() {
                    var wHeight = $(window).height();
                    var sliderHeight = wHeight - 70;
                    $("".swipe li"").height(sliderHeight);
                };
                $("".find_nav_list"").css(""left"", 0);

                $("".find_nav_list li"").each(function () {
                    $("".sideline"").css({ left: 0 });
                    $("".find_nav_list li"").eq(0).addClass(""find_nav_cur"").siblings().removeClass(""find_nav_cur"");
                });
                var nav_w = $("".find_nav_list li"").first().width();
                $("".sideline"").width(nav_w);
                $("".find_nav_list li"").on('click', function () {
                    nav_w = $(this).width();
                    $("".sideline"").stop(true);
                    $("".sideline"").animate({ left: $(this).position().left }, 300);
      ");
                WriteLiteral(@"              $("".sideline"").animate({ width: nav_w });
                    $(this).addClass(""find_nav_cur"").siblings().removeClass(""find_nav_cur"");
                    var fn_w = ($("".find_nav"").width() - nav_w) / 2;
                    var fnl_l;
                    var fnl_x = parseInt($(this).position().left);
                    if (fnl_x <= fn_w) {
                        fnl_l = 0;
                    } else if (fn_w - fnl_x <= flb_w - fl_w) {
                        fnl_l = flb_w - fl_w;
                    } else {
                        fnl_l = fn_w - fnl_x;
                    }
                    $("".find_nav_list"").animate({
                        ""left"": fnl_l
                    }, 300);
                });
                var fl_w = $("".find_nav_list"").width();
                var flb_w = $("".find_nav_left"").width();
                $("".find_nav_list"").on('touchstart', function (e) {
                    var touch1 = e.originalEvent.targetTouches[0];
                    x");
                WriteLiteral(@"1 = touch1.pageX;
                    y1 = touch1.pageY;
                    ty_left = parseInt($(this).css(""left""));
                });
                $("".find_nav_list"").on('touchmove', function (e) {
                    var touch2 = e.originalEvent.targetTouches[0];
                    var x2 = touch2.pageX;
                    var y2 = touch2.pageY;
                    if (ty_left + x2 - x1 >= 0) {
                        $(this).css(""left"", 0);
                    } else if (ty_left + x2 - x1 <= flb_w - fl_w) {
                        $(this).css(""left"", flb_w - fl_w);
                    } else {
                        $(this).css(""left"", ty_left + x2 - x1);
                    }
                    if (Math.abs(y2 - y1) > 0) {
                        e.preventDefault();
                    }
                });
                for (n = 1; n < 9; n++) {
                    var page = 'PageNavi' + n;
                    var mslide = 'slider' + n;
                    var mtitle = ");
                WriteLiteral(@"'emtitle' + n;
                    arrdiv = 'arrdiv' + n;
                    var as = $(""#"" + page + """").find(""a"");
                    var tt = new TouchSlider({
                        id: mslide, 'auto': '-1', fx: 'ease-out', direction: 'left', speed: 600, timeout: 5000, 'before': function (index) {
                            var as = document.getElementById(this.page).getElementsByTagName('a');
                            as[this.p].className = '';
                            this.p = index;
                            fnl_x = parseInt($("".find_nav_list li"").eq(this.p).position().left);
                            nav_w = $("".find_nav_list li"").eq(this.p).width();
                            $("".sideline"").stop(true);
                            $("".sideline"").animate({ left: $("".find_nav_list li"").eq(this.p).position().left }, 300);
                            $("".sideline"").animate({ width: nav_w }, 100);
                            $("".find_nav_list li"").eq(this.p).addClass(""find_nav_cur");
                WriteLiteral(@""").siblings().removeClass(""find_nav_cur"");
                            var fn_w = ($("".find_nav"").width() - nav_w) / 2;
                            var fnl_l;
                            if (fnl_x <= fn_w) {
                                fnl_l = 0;
                            } else if (fn_w - fnl_x <= flb_w - fl_w) {
                                fnl_l = flb_w - fl_w;
                            } else {
                                fnl_l = fn_w - fnl_x;
                            };
                            $("".find_nav_list"").animate({
                                ""left"": fnl_l
                            }, 300);
                        }
                    });
                    tt.page = page;
                    tt.p = 0;
                    for (var i = 0; i < as.length; i++) {
                        (function () {
                            var j = i;
                            as[j].tt = tt;
                            as[j].onclick = function () {
          ");
                WriteLiteral(@"                      this.tt.slide(j);
                                return false;
                            }
                        })();
                    }
                };
                //滑动导航end
            }
        })
    </script>


");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
