#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\HospitalDepts\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "54cc3e48c38d69862e3cfcf42440a8e25a30f056e24c812dd51471fb95d1bf6d"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_BasicConfig_Views_HospitalDepts_Index), @"mvc.1.0.view", @"/Areas/BasicConfig/Views/HospitalDepts/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"54cc3e48c38d69862e3cfcf42440a8e25a30f056e24c812dd51471fb95d1bf6d", @"/Areas/BasicConfig/Views/HospitalDepts/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_BasicConfig_Views_HospitalDepts_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/jquery/dist/jquery.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\HospitalDepts\Index.cshtml"
  
    ViewBag.Title = "部门管理";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"en\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "54cc3e48c38d69862e3cfcf42440a8e25a30f056e24c812dd51471fb95d1bf6d6015", async() => {
                WriteLiteral(@"
    <meta charset=""UTF-8"">
    <meta name=""viewport""
          content=""width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"">
    <meta http-equiv=""X-UA-Compatible"" content=""ie=edge"">
    <title>科研机构管理</title>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "54cc3e48c38d69862e3cfcf42440a8e25a30f056e24c812dd51471fb95d1bf6d6567", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "54cc3e48c38d69862e3cfcf42440a8e25a30f056e24c812dd51471fb95d1bf6d7769", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "54cc3e48c38d69862e3cfcf42440a8e25a30f056e24c812dd51471fb95d1bf6d8971", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "54cc3e48c38d69862e3cfcf42440a8e25a30f056e24c812dd51471fb95d1bf6d10094", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "54cc3e48c38d69862e3cfcf42440a8e25a30f056e24c812dd51471fb95d1bf6d11218", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        /*弹窗*/
        .window_wrap {
            padding: 15px;
        }

        #form_window .layui-form-label {
            width: 100px;
        }

        #form_window .layui-form-val {
            padding: 9px 15px;
        }

        #form_window .layui-form-item {
            margin-bottom: 0;
        }

        .layui-form-item {
            margin-bottom: 5px;
        }
        /* 定义表头样式 */
        .layui-table th {
            font-weight: bold; /* 加粗 */
            font-size: 14px; /* 调整字体大小 */
            color: #333; /* 字体颜色，可根据需要调整 */
        }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "54cc3e48c38d69862e3cfcf42440a8e25a30f056e24c812dd51471fb95d1bf6d13674", async() => {
                WriteLiteral(@"
    <div class=""layui-col-md12"">
        <div class=""layui-card""> 
                 
            <div class=""layui-card-header"">
                <div class=""layui-inline"">
                    <input type=""text"" class=""layui-input"" name=""KeyWords"" placeholder=""关键字"" style=""width:250px;"" id=""KeyWords"" />
                </div>
                <div class=""layui-inline"">
                    <div class=""layui-input-block"">
                        <button class=""layui-btn layui-btn-normal fr"" id=""Search"">查 &nbsp;&nbsp;询</button>
                         
                    </div>
                </div>
            </div>
            <div class=""layui-card-body"">
                <table class=""layui-table layui-form"" id=""menuTreeTb""></table>
                <script type=""text/html"" id=""tableBar1"">
                    <a class=""layui-btn  layui-btn-xs"" lay-event=""add""><i class=""layui-icon layui-icon-add-circle""></i>新增</a>
                    <a class=""layui-btn layui-btn-normal layui-btn-xs"" lay-ev");
                WriteLiteral(@"ent=""edit""><i class=""layui-icon layui-icon-edit""></i>编辑</a>
                    {{# if(d.sonNum == 0){ }}
                    <a class=""layui-btn layui-btn-danger layui-btn-xs"" lay-event=""del""><i class=""layui-icon layui-icon-delete""></i>删除</a>
                    {{# } }}
                </script>
            </div>
        </div>

        <script>
            layui.config({
                base: '/layuiadmin/layuiextend/'
            }).use(['element', 'layer', 'treeTable', 'table', 'form'], function () {
                var element = layui.element
                    , layer = layui.layer
                    , table = layui.table//表格
                    , tree = layui.tree
                    , treeTable = layui.treeTable //表格
                    , $ = layui.$
                    , laytpl = layui.laytpl
                    , form = layui.form;


                var windowsIndex; 
                var url = '';


               
                $(document).ready(function () { 
    ");
                WriteLiteral(@"                //檢索
                    $(""#Search"").on('click', function () {
                        menuTreeTb.filterData($.trim($(""#KeyWords"").val()));
                    });

                });


                // 渲染表格
                var menuTreeTb = treeTable.render({
                    elem: '#menuTreeTb',
                    url: ""/BasicConfig/HospitalDepts/TreeList"",
                    tree: {
                        idName: 'Id',
                        pidName: 'ParentId',
                        haveChildName: 'haveChildName',
                        iconIndex: 1,    // 折叠图标显示在第几列
                        isPidData: true,  // 是否是pid形式数据
                        arrowType: 'arrow2',
                        getIcon: 'ew-tree-icon-style2'
                    },
                    cols: [[
                        { type: 'numbers', fixed: 'left' },
                        { field: 'DeptName', title: '名称', fixed: ""left""},
                        { field: 'DeptCode', title:");
                WriteLiteral(@" '代码', width: 250 },
                       /* { field: 'Level', title: '层级' },*/
                        { field: 'DeptIntro', title: '简介', },
                        { fixed: 'right', align: 'center', title: '操作', width: 220, toolbar: '#tableBar1' }
                    ]],
                    done: function () {
                        menuTreeTb.expand(1);
                    }
                });

                // 工具列点击事件
                treeTable.on('tool(menuTreeTb)', function (obj) {
                    var event = obj.event;
                    var data = obj.data;
                    if (data.haveChildName) {
                        $(""#ParentId"").attr(""haveChildName"", ""1"");
                    }
                    else {
                        $(""#ParentId"").attr(""haveChildName"", ""0"");
                    }
                    if (event === 'add') {

                      
                        $(""#fm"")[0].reset();
                        $('#Id').val(0);
             ");
                WriteLiteral(@"           $('#ParentId').val(data.Id); 
                        $('#ParentName').val(data.DeptName); 
                        layui.form.render();
                          $(""#ParentId"").attr(""reload"", ""0"");
                        windowsIndex = layer.open({
                            type: 1,
                            title: '新增【'+data.DeptName+'】下级部门',
                            area: ['600px', '500px'],
                            resize: true,
                            content: $('#form_window_Menu')
                        });
                        url = '/BasicConfig/HospitalDepts/AddDepts';
                    }
                    else if (event === 'edit') {
                        $('#menuName').attr(""readonly"", ""readonly"");

                        form.render('select'); 
                        form.val('fm', data);
                        $(""#ParentId"").attr(""reload"", ""1"");
 
                        windowsIndex = layer.open({
                            type: 1,
");
                WriteLiteral(@"                            title: '修改部门【' + data.DeptName + '】',
                            area: ['600px', '500px'],
                            content: $('#form_window_Menu')
                        });
                        url = '/BasicConfig/HospitalDepts/EditDepts';
                    }
                    else if (event === 'del') {
                        layer.confirm('确定要删除部门【' + data.DeptName + '】吗？', {
                            title: '',
                            btn: ['确定', '取消'], //按钮
                            resize: false
                        }, function (index) {
                            $.post('/BasicConfig/HospitalDepts/DelDepts', { id: data.Id }, function (result) {
                                if (result.okMsg) {
                                    layer.msg(result.okMsg);
                                    currentIndex = -1;
                                    //menuTreeTb.reload(); //重载表格
                                    if (data.ParentId === '");
                WriteLiteral(@"' || data.ParentId == undefined || data.ParentId === 0) {
                                        menuTreeTb.reload({ url: ""/BasicConfig/HospitalDepts/TreeList"", where: { 'pid': -1 } }); //重载表格
                                    }
                                    else {
                                        menuTreeTb.refresh(data.ParentId); //重载表格
                                    }
                                } else {
                                    layer.msg(result.errorMsg);
                                }
                            }, 'json');
                            layer.close(index);
                        });
                    }
                });




                //自定义验证规则
                form.verify({
                    intVer: [
                        /^[1-9][0-9]{0,}$/
                        , '请输入正整数'
                    ]
                });

                //监听提交
                form.on('submit(MenuSubmit)', function (data) {
      ");
                WriteLiteral(@"              console.log(data);
                    var indes = layer.load(1);
                    //提交 Ajax 成功后，关闭当前弹层并重载表格
                    $.ajax({
                        url: url,
                        type: ""post"",
                        data: { 'DeptsModel': data.field },
                        datatype: 'json',
                        success: function (result) {
                            if (result.okMsg) {
                                debugger; 
                                layer.msg(result.okMsg);
                                layer.close(windowsIndex);//关闭弹出层
                                if ($(""#ParentId"").attr(""reload"") == ""1"") {
                                    menuTreeTb.refresh(parseInt(data.field.ParentId)); //重载表格
                                }
                                else {
                                    if ($(""#ParentId"").attr(""haveChildName"") == ""1"") {
                                        menuTreeTb.refresh(parseInt(data.field.P");
                WriteLiteral(@"arentId)); //重载表格
                                    }
                                    else {
                                        menuTreeTb.reload({ url: ""/BasicConfig/HospitalDepts/TreeList"", where: { 'pid': -1 } }); //重载表格
                                    }
                                }
                            }
                            else {
                                layer.msg(result.errorMsg);
                            }
                            layer.close(indes);
                        }, error: function (res) {
                            layer.msg(""加载统计信息错误："" + res.responseText);
                            layer.close(indes);
                        }
                    });
                    return false;
                });

            });
        </script>
    </div>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n \r\n \r\n\r\n<!--新增/修改模块-->\r\n<div class=\"window_wrap\" id=\"form_window_Menu\" style=\"display: none;\">\r\n    <form class=\"layui-form\" lay-filter=\"fm\" id=\"fm\"");
            BeginWriteAttribute("action", " action=\"", 10516, "\"", 10525, 0);
            EndWriteAttribute();
            WriteLiteral(">\r\n");
            WriteLiteral(@"            <div class=""layui-form-item"">
                <label class=""layui-form-label"">代码:</label>
                <div class=""layui-input-block"">
                    <input type=""text"" name=""Id"" id=""Id"" style=""display:none;"" />
                    <input type=""text"" name=""ParentId"" id=""ParentId"" style=""display:none;"" haveChildName=""0"" reload=""0"" />
                  
                    <input type=""text"" name=""DeptCode"" id=""DeptCode"" required lay-verify=""required"" autocomplete=""off"" class=""layui-input"">
                </div>
            </div>


            <div class=""layui-form-item"">
                <label class=""layui-form-label"">名称:</label>
                <div class=""layui-input-block"">
                    <input type=""text"" name=""DeptName"" id=""DeptName"" required lay-verify=""required"" autocomplete=""off"" class=""layui-input"" />
                </div>
            </div>

            <div class=""layui-form-item"">
                <label class=""layui-form-label"">简介:</label>
         ");
            WriteLiteral(@"       <div class=""layui-input-block"">
                    <textarea name=""DeptIntro"" id=""DeptIntro"" class=""layui-textarea"" style=""resize: none""></textarea>
                </div>
            </div>
            <div class=""layui-form-item"">
                <div class=""layui-input-block"">
                    <button type=""submit"" class=""layui-btn"" lay-submit lay-filter=""MenuSubmit"">提交</button>
                </div>
            </div>
</form>
</div>

</html>
");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
