#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\MultiCentersManage\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "54693b6cf457eabce2838f4353fa14015bab2e7ae9e600945265206bdb75bf0d"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_BasicConfig_Views_MultiCentersManage_Index), @"mvc.1.0.view", @"/Areas/BasicConfig/Views/MultiCentersManage/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"54693b6cf457eabce2838f4353fa14015bab2e7ae9e600945265206bdb75bf0d", @"/Areas/BasicConfig/Views/MultiCentersManage/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_BasicConfig_Views_MultiCentersManage_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\MultiCentersManage\Index.cshtml"
  
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "54693b6cf457eabce2838f4353fa14015bab2e7ae9e600945265206bdb75bf0d6376", async() => {
                WriteLiteral("\r\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n    <meta charset=\"utf-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>多中心管理</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "54693b6cf457eabce2838f4353fa14015bab2e7ae9e600945265206bdb75bf0d6887", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "54693b6cf457eabce2838f4353fa14015bab2e7ae9e600945265206bdb75bf0d8089", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        /*弹窗*/
        .window_wrap {
            padding: 15px;
        }
        .search_wrap {
            background-color: #f0f0f0;
        }

        .line_wrap {
            padding: 10px 15px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }
        #detail_window .layui-form-label {
            width: 100px;
        }

        #detail_window .layui-form-val {
            padding: 9px 15px;
        }

        #detail_window .mindow_icon .title_icon {
            display: inline-block;
        }

        #detail_window .mindow_icon {
            text-align: center;
            padding: 20px 0;
        }

        #detail_window .layui-form-item {
            margin-bottom: 0;
        }
        /* 定义表头样式 */
        .layui-table-header .layui-table-cell {
            font-weight: bold; /* 加粗 */
            font-size: 14px; /* 可选：调整字体大小以提升可读性 */
            color: #333; /* 可选：调整字体颜色 */
   ");
                WriteLiteral("     }\r\n    </style>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "54693b6cf457eabce2838f4353fa14015bab2e7ae9e600945265206bdb75bf0d11081", async() => {
                WriteLiteral(@"
    <div class=""layui-col-md12"">
        <div class=""layui-card"">
            <div class=""line_wrap search_wrap"">
                <div>
                    <div class=""layui-inline"">
                        <label class=""layui-form-label"">搜索条件</label>
                        <div class=""layui-input-inline"">
                            <input type=""text"" name=""keyWord"" id=""keyWord"" placeholder=""请输入中心名称"" autocomplete=""off"" class=""layui-input"">
                        </div>
                        <button class=""layui-btn layui-btn-primary layui-border-green"" id=""Search""><i class=""layui-icon layui-icon-search""></i> </button>
                    </div>
                </div>
                <div>
                    <button type=""button"" class=""layui-btn layui-bg-blue"" id=""addField""><i class=""layui-icon layui-icon-add-1""></i></button>
                </div>
            </div>
            <div class=""layui-card-body"">
                <!--编辑区-->
                <div class="" edit_area"">
       ");
                WriteLiteral(@"             <div class=""line_wrap layui-card layui-colla-content layui-show"">
                        <div class=""layui-row form_wrap layui-form"">
                            <div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"">
                                <div class=""layui-form-item"">
                                    <label class=""layui-form-label"">中心名称</label>
                                    <div class=""layui-input-block "">
                                        <input type=""text"" name=""Id"" id=""Id"" style=""display:none;"" />
                                        <input type=""text"" name=""CenterName"" id=""CenterName"" required lay-verify=""required"" placeholder=""请输入中心名称"" autocomplete=""off"" class=""layui-input"">
                                    </div>
                                </div>
                            </div>
                            <div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"">
                                <div class=""layui-form-item"">
                ");
                WriteLiteral(@"                    <label class=""layui-form-label"">是否为中心</label>
                                    <div class=""layui-input-block "">
                                        <input type=""checkbox"" name=""IsCenter"" id=""IsCenter"" title=""是|否"" lay-skin=""switch"">
                                    </div>
                                </div>
                            </div>

                            <div class=""layui-col-xs3 layui-col-sm3 layui-col-md3"">
                                <div class=""layui-form-item"">
                                    <label class=""layui-form-label"">联系人</label>
                                    <div class=""layui-input-block "">
                                        <input type=""text"" name=""Contact"" id=""Contact"" placeholder=""请输入联系人"" autocomplete=""off"" class=""layui-input"">
                                    </div>
                                </div>
                            </div>
                            <div class=""layui-col-xs3 layui-col-sm3 layu");
                WriteLiteral(@"i-col-md3"">
                                <div class=""layui-form-item"">
                                    <label class=""layui-form-label"">联系电话</label>
                                    <div class=""layui-input-block "">
                                        <input type=""text"" name=""Telephone"" id=""Telephone"" placeholder=""请输入联系电话"" autocomplete=""off"" class=""layui-input"">
                                    </div>
                                </div>
                            </div>
                            <div class=""layui-col-xs6 layui-col-sm6 layui-col-md6"">
                                <div class=""layui-form-item"">
                                    <label class=""layui-form-label"">地址</label>
                                    <div class=""layui-input-block "">
                                        <input type=""text"" name=""Address"" id=""Address"" lay-verify=""required"" placeholder=""请输入地址"" autocomplete=""off"" class=""layui-input"">
                                    </div>
          ");
                WriteLiteral(@"                      </div>
                            </div>
                            <div class=""layui-col-xs6 layui-col-sm6 layui-col-md6"">
                                <div class=""form_item"">
                                    <label class=""layui-form-label"">简介</label>
                                    <div class=""layui-input-block "">
                                        <textarea name=""CenterIntro"" id=""CenterIntro"" class=""layui-textarea"" style=""resize: none""></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class=""btnwrap"" style=""text-align:right;margin-top:10px;"">
                            <button class=""layui-btn"" lay-submit lay-filter=""submit"">保存</button>
                        </div>
                    </div>
                </div>
                <table id=""tablelist"" lay-filter=""tablelist""></table>
                <script type=");
                WriteLiteral("\"text/html\" id=\"tableBar\">\r\n");
                WriteLiteral("                    <a class=\"layui-btn layui-btn-danger layui-btn-xs\" lay-event=\"del\" style=\"text-decoration:none\"><i class=\"layui-icon layui-icon-delete\"></i>删除</a>\r\n                </script>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "54693b6cf457eabce2838f4353fa14015bab2e7ae9e600945265206bdb75bf0d17178", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "54693b6cf457eabce2838f4353fa14015bab2e7ae9e600945265206bdb75bf0d18302", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "54693b6cf457eabce2838f4353fa14015bab2e7ae9e600945265206bdb75bf0d19426", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "54693b6cf457eabce2838f4353fa14015bab2e7ae9e600945265206bdb75bf0d20550", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"

    <script>
        layui.use(['element', 'layer', 'table', 'laydate', 'form'], function () {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , laydate = layui.laydate
                , $ = layui.$
                , form = layui.form;

            var url = ''

            table.render({
                elem: '#tablelist'
                , id: 'tablelist'
                , page: true
                , limit: 20
                , height: 'full-110'
                , cols: [[
                    { field: 'No', title: '序号', type: 'numbers', fixed: 'left' }
                    , { field: 'Id', title: '表单Id',hide:true }
                    , { field: 'CenterName', title: '中心名称', width: 120 }
                    , {
                        field: 'IsCenter', title: '是否为中心', width: 100, templet: function (d) {
                            return '<input type=""checkbox"" name=""center"" lay-filter=""centerSw");
                WriteLiteral(@"itch"" data-id=""' + d.Id + '"" title=""是|否"" lay-skin=""switch"" ' + (d.IsCenter ? 'checked' : '') + '>';
                        }
                       }
                    , { field: 'Contact', title: '联系人', width: 90}
                    , { field: 'Telephone', title: '联系电话', width: 120 }
                    , { field: 'Address', title: '地址', width: 200 }
                    , { field: 'CenterIntro', title: '简介'/*, width: 300*/ }
                    , { title: '操作', toolbar: '#tableBar', width: 160, minWidth: 160, fixed: 'right' }
                ]]
                , done: function (res, curr, count) {
                    // 重新渲染表单
                    form.render();
                }
            });

            //监听tablelist工具条
            table.on('tool(tablelist)', function (obj) {
                var data = obj.data;
                if (obj.event === 'del') {
                    layer.confirm('确定要删除中心名为【' + data.CenterName + '】的相关信息吗？', {
                        title: '',
           ");
                WriteLiteral(@"             btn: ['确定', '取消'], //按钮
                        resize: false
                    }, function (index) {
                        $.post('/BasicConfig/MultiCentersManage/Del', { id: data.Id }, function (result) {
                            if (result.okMsg) {
                                layer.msg(result.okMsg);
                                currentIndex = -1;
                                table.reload('tablelist'); //重载表格
                            } else {
                                layer.msg(result.errorMsg);
                            }
                        }, 'json');
                        layer.close(index);
                    });
                }
            });

            //触发行单击事件
            table.on('row(tablelist)', function (obj) {
                var data = obj.data;
                $(""#CenterName"").val(data.CenterName);
                $(""#Contact"").val(data.Contact);
                $(""#Telephone"").val(data.Telephone);
                $");
                WriteLiteral(@"(""#Address"").val(data.Address);
                $(""#CenterIntro"").val(data.CenterIntro);
                // 设置开关组件的状态
                $(""#IsCenter"").prop(""checked"", data.IsCenter);
                $(""#Id"").val(data.Id);
                // 重新渲染表单
                form.render('checkbox');
            });

            $(document).ready(function () {
                getData();
                $(document).on('click', '#Search', function () {
                    getData();
                })
            });


            function getData() {
                table.reload('tablelist', {
                    page: {
                        curr: 1
                    },
                    url: '/BasicConfig/MultiCentersManage/List',
                    where: {
                        'centerName': $.trim($(""#keyWord"").val()),
                    }
                });
            }

            $(document).on('click', '#addField', function () {
                EmptyData();
            });");
                WriteLiteral(@"

            function EmptyData() {
                $('#Id').val('');
                $('#CenterName').val('');
                $('#Contact').val('');
                $('#Telephone').val('');
                $('#Address').val('');
                $('#CenterIntro').val('');
                $('#IsCenter').prop('checked', false);
                form.render();
            }

            //监听提交
            form.on('submit(submit)', function (data) {
                var indes = layer.load(1);
                data.field.IsCenter = $(""#IsCenter"").is(':checked');
                data.field.CenterName = $(""#CenterName"").val();
                data.field.Contact = $(""#Contact"").val();
                data.field.Telephone = $(""#Telephone"").val();
                data.field.Address = $(""#Address"").val();
                data.field.CenterIntro = $(""#CenterIntro"").val();
                data.field.Id = $(""#Id"").val();
                console.log(data);
                //提交 Ajax 成功后，关闭当前弹层并重载表格
   ");
                WriteLiteral(@"             $.ajax({
                    url: '/BasicConfig/MultiCentersManage/Save',
                    type: ""post"",
                    data: { 'node': data.field },
                    datatype: 'json',
                    success: function (data) {
                        if (data.okMsg) {
                            layer.msg(data.okMsg);
                            table.reload('tablelist'); //重载表格
                        }
                        else {
                            layer.msg(data.errorMsg);
                        }
                        layer.close(indes);
                    }, error: function (res) {
                        layer.msg(""加载统计信息错误："" + res.responseText);
                        layer.close(indes);
                    }
                });
                return false;
            });

           
            form.on('switch(centerSwitch)', function (data) {
                var id = data.elem.getAttribute('data-id'); // 获取唯一ID
                ");
                WriteLiteral(@"var checked = data.elem.checked;
                $.ajax({
                    url: ""/BasicConfig/MultiCentersManage/UpdateStatus"",
                    type: ""post"",
                    data: { 'id': id, 'status': checked },
                    datatype: 'json',
                    success: function (data) {
                        if (data.okMsg) {
                            layer.msg(data.okMsg);
                            table.reload('tablelist'); //重载表格
                        }
                        else {
                            layer.msg(data.errorMsg);
                        }
                        layer.close(indes);
                    }, error: function (res) {
                        layer.msg(""加载统计信息错误："" + res.responseText);
                        layer.close(indes);
                    }
                });

            });

            form.verify({
                mobileck: [
                    /^(086|\+86|17951)?(13|14|15|17|18)\d{9}$/
                  ");
                WriteLiteral(@"  , '手机号码格式不正确。'
                ]
            });
            function setTableH() {
                var winH = $(window).height();
                var navH = $("".layui-tab-brief"").height();
                var searchH = $("".search_wrap"").height();
                var editAreaH = $("".edit_area"").height();
                var tableH = winH - (navH + searchH + editAreaH) - 55 + ""px"";
                console.log(tableH)
                $("".table_wrap"").css(""height"", tableH);

            };
            setTableH();
            $(window).resize(function () {
                setTableH()
            });
        });
    </script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n<!--新增/修改弹框-->\r\n<div class=\"window_wrap\" id=\"form_window\" style=\"display: none\">\r\n");
            WriteLiteral("</div>\r\n</html>\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
