#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\PatientDiscoveryManage\Views\PatientDiscovery\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_PatientDiscoveryManage_Views_PatientDiscovery_Index), @"mvc.1.0.view", @"/Areas/PatientDiscoveryManage/Views/PatientDiscovery/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b", @"/Areas/PatientDiscoveryManage/Views/PatientDiscovery/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_PatientDiscoveryManage_Views_PatientDiscovery_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/css/bootstrap.min.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/css/chosen.min.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/css/awesome-bootstrap-checkbox.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/css/bootstrap-slider.min.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/css/selectize.bootstrap5.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/css/bootstrap-icons.min.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/css/query-builder.default.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/popper.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_13 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/bootstrap.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_14 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/chosen.jquery.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_15 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/bootstrap-slider.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_16 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/selectize.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_17 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/jquery-extendext.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_18 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/sql-parser.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_19 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/interact.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_20 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/bootbox.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_21 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/query-builder.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_22 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/query-builder.zh-cn.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_23 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\PatientDiscoveryManage\Views\PatientDiscovery\Index.cshtml"
  
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b12462", async() => {
                WriteLiteral("\r\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n    <meta charset=\"utf-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>患者发现</title>\r\n\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b12977", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n");
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b14219", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b15422", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b16625", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b17828", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b19031", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b20234", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b21437", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b22640", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    <style>\r\n        .rectangle {\r\n            width: 200px; /* 宽度 */\r\n       \r\n            float:left;\r\n            margin-left:20px;\r\n           padding:40px 15px;\r\n           \r\n            \r\n          \r\n        }\r\n    </style>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b24727", async() => {
                WriteLiteral("\r\n    <div class=\"layui-col-md12\">\r\n        <div class=\"layui-card\">\r\n            <div class=\"layui-card-body layui-form\">\r\n                <div class=\"layui-inline\">\r\n\r\n\r\n                    <label class=\"layui-form-label\"");
                BeginWriteAttribute("style", " style=\"", 1525, "\"", 1533, 0);
                EndWriteAttribute();
                WriteLiteral(">状态：</label>\r\n                    <div class=\"layui-input-inline\">\r\n                        <select id=\"status\">\r\n                            <option");
                BeginWriteAttribute("value", " value=\"", 1683, "\"", 1691, 0);
                EndWriteAttribute();
                WriteLiteral(@">全部</option>
                            <option value=""1"">已入组</option>
                             <option value=""0"">待定</option>
                            <option value=""-1"">不符合</option>
                        </select>
                    </div>

                </div>
                <div class=""layui-inline"">
                    <div class=""layui-input-inline"" style=""width:300px;"">
                        <input type=""text""  class=""layui-input"" name=""keyWord""  placeholder=""请输入病历号/患者姓名"" id=""keyWord"" />
                    </div>
                  
                </div>
               
                <div class=""layui-inline"">
                    <button class=""layui-btn"" id=""Search2"">查询</button>

                </div>
                <div class=""layui-inline"">
                    <button class=""layui-btn layui-btn-normal fr"" id=""Search"">高级查询</button>

                </div>
            </div>
            <div class=""layui-card-body"">
                <table id=""tablelist"" lay");
                WriteLiteral("-filter=\"tablelist\"></table>\r\n                <script type=\"text/html\" id=\"tableBar1\">\r\n                    <a class=\"layui-btn layui-btn-normal layui-btn-xs\" lay-event=\"rz\" style=\"text-decoration:none\"><i class=\"layui-icon\"></i>查看</a>\r\n\r\n");
                WriteLiteral("\r\n                </script>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b27205", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b28330", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b29455", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b30579", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b31708", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b32837", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_14);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b33962", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_15);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b35087", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_16);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b36212", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_17);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b37337", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_18);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b38462", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_19);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b39587", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_20);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b40712", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_21);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "531c8e71e156a79a68937f270d70ad8822f9a59b9712a72a7530db7baf95080b41837", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_22);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
        layui.use(['element', 'layer', 'table', 'laydate', 'form'], function () {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , laydate = layui.laydate
                , $ = layui.$
                , form = layui.form;
            ;


            table.render({
                elem: '#tablelist'
                , id: 'tablelist'
                , url: '/PatientDiscoveryManage/PatientDiscovery/GetData'
                , page: true
                , limit: 20
                , height: 'full-110'
                , cols: [[
                    { field: 'No', title: '序号', width: 60, type: 'numbers' }
                    , { field: 'BLH', title: '病历号', width: 100 }
                    , { field: 'HZXM', title: '患者姓名', width: 120 }
                    , { field: 'NL', title: '年龄', width: 80 }
                    , { field: 'RYZDMC', title: '入院诊断', width: 200 }
                    , { fie");
                WriteLiteral(@"ld: 'CYZDMC', title: '出院诊断', width: 200 }
                    , { field: 'SSMC', title: '手术', width: 200 }
                    , { field: 'CSZZ', title: '初始症状', width: 110 }
                    , { field: 'CBZD', title: '初步诊断', width: 110 }
                    , { field: 'CBZL', title: '初步治疗', width: 110 }
                    , { field: 'TW', title: '体温', width: 110 }
                         , { field: 'MB', title: '脉搏', width: 110 }
                    , { field: 'HXL', title: '呼吸率', width: 110 }
                      , { field: 'TGJC', title: '体格检查', width: 200 }
                        , { field: 'XBS', title: '现病史', width: 200 }
                    , { field: 'CWDM', title: '床位代码', width: 100 }
                    , { field: 'KSDM', title: '科室代码', width: 100 }
                    , { field: 'KSMC', title: '科室名称', width: 100 }
                    , { field: 'BQDM', title: '病区代码', width: 100 }
                    , { field: 'BQMC', title: '病区名称', width: 100 }
                    , { field: '");
                WriteLiteral(@"ZXKSDM', title: '执行科室', width: 100 }
                    , { field: 'YLZDM', title: '医疗组代码', width: 100 }
                    , { field: 'YLZMC', title: '医疗组名称', width: 100 }
                   
                    , {
                        field: 'Status', title: '状态', width: 100, fixed: 'right', templet: function (d) {
                            if (d.Status == 1)
                                return ""<span  style='background-color: #00FF00'>已入组</span>"";
                            else if (d.Status == -1)
                                return ""<span  style='background-color: #FF9933'>不符合</span>"";
                            else
                                return '待定';
                          
                        }
                    }
                    , { field: 'KYRZ', title: '科研入组', width: 200,fixed: 'right' }
                    , {
                        title: '操作',
                        minWidth: 160,
                         fixed: 'right',
               ");
                WriteLiteral(@"         toolbar: '#tableBar1'
                    }
                ]]
            });


            //监听tablelist工具条
            table.on('tool(tablelist)', function (obj) {
                var data = obj.data;
                if (obj.event === 'rz') {
                   if(data.KYRZ)
                   {
                       var arr = data.KYRZ.split(',');

                        demo1.setValue(arr);
                   }
                   else
                   {
                        demo1.setValue([]);
                   }
                    windowsIndex = layer.open({
                        type: 1,
                        //title: '患者【' + data.HZXM + '】入组',
                        title:'MDT查看',
                        area: ['600px', '700px'],
                        resize: true,
                        content: $('#form_window_rz'),
                        btn: ['入组', '不符合'],
                        yes: function (index, layero) {
                            var");
                WriteLiteral(@" name = demo1.getValue('valueStr');
                            if (!name) {
                                layer.msg(""请选择科研组！"");
                                return;
                            }
                            $.post('/PatientDiscoveryManage/PatientDiscovery/Save', { id: data.Id, name:name  },function(res){
                              if(res.code==0) {
                                    layer.close(index);
                                    table.reload('tablelist');
                              }
                              else
                                 layer.msg(res.msg);
                            })
                        }
                        , btn2: function (index, layero) {
                            //layer.close(windowsIndex);
                            $.post('/PatientDiscoveryManage/PatientDiscovery/UpdateStatus', { id: data.Id }, function (res) {
                                if (res.code == 0) {
                                    lay");
                WriteLiteral(@"er.close(index);
                                    table.reload('tablelist');
                                }
                                else
                                    layer.msg(res.msg);
                            })
                        },
                        success: function () {
                            $('#builder').queryBuilder(options);
                        }

                    });
                }
                else if (obj.event === ""sy"") {
                    var url ='");
                Write(
#nullable restore
#line 219 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\PatientDiscoveryManage\Views\PatientDiscovery\Index.cshtml"
                               ViewBag.url

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"';
                   /// var Url = url+""?PID="" + data.BLH;
                    var Url = ""/vform/traceability?PID="" + data.BLH;
                    parent.layui.index.openTabsPage(Url, ""溯源"");
                }

            });

            var demo1 = xmSelect.render({
                el: '#demo1',
                data: [
                    { name: '甲状腺', value: '甲状腺' },
                    { name: '肾移植', value: '肾移植' },
                    { name: '脓毒症', value: '脓毒症' },
                    { name: '泌尿系结石', value: '泌尿系结石' }
                ]
            })

            $(document).on('click', '#Search2', function () {
                table.reload('tablelist', {
                    page: {
                        curr: 1
                    },
                    where: {
                        'keyWord': $.trim($(""#keyWord"").val()),
                        'keySql':'',
                        'status': $(""#status"").val()
                    }
                });
            })
");
                WriteLiteral(@"

            $(document).on('click', '#Search', function () {
                windowsIndex = layer.open({
                    type: 1,
                    title: '高级查询',
                    area: ['950px', '600px'],
                    resize: true,
                    content: $('#form_window'),
                    btn: ['查询', '取消'],
                    yes: function (index, layero) {
                        var res = $('#builder').queryBuilder('getSQL', $(this).data('stmt'), false);
                        console.log(res);
                        layer.close(index);
                        table.reload('tablelist', {
                            page: {
                                curr: 1
                            },
                            where: {
                                 'keyWord':'',
                                'keySql': res.sql,
                                'status':''
                            }
                        });
                    }
     ");
                WriteLiteral(@"               , btn2: function (index, layero) {
                        layer.close(windowsIndex);
                    },
                    success: function () {
                        $('#builder').queryBuilder(options);
                    }

                });
            });
             $(document).on('click', '.rectangle', function () {
              var names=  $.trim($(this).text());
              var res="""";
              var type=1;
              var content= $('#form_window_res');
              if(names==""体格检查"")
              {
                  type=1;
                  content= $('#form_window_res');
                    res = `患者整体情况良好。体温为36℃，脉搏79次/分，呼吸16次/分，血压暂未测量。患者神志清晰，面色无病容，自动取位，眼睑无浮肿，结膜无水肿，巩膜无黄染，瞳孔等大、等圆，唇无紫绀，甲状腺、颈部血管和颈动脉无异常，双肺听诊无异常，心脏听诊无异常，心脏搏动无异常，心率79次/分，心音正常，A2>P2，肝脾肋下未触及，双下肢无水肿。`
                   
              }
              else if(names==""现病史"")
              {
                    type = 1;
                    content= $('#form_window_res');
          ");
                WriteLiteral(@"          res = `患者在2023年9月20日出现心悸等症状，病程约10小时。初始症状包括心悸，与该症状相关的其他症状包括无黑朦、无晕厥、无胸闷和无胸痛。患者曾于房山区良乡医院就诊，初步诊断为心房颤动，接受胺碘酮治疗，治疗效果良好，已转为窦律。此外，患者进行了冠脉造影，发现冠状动脉粥样硬化，建议择期进行射频消融治疗。患者在出院后继续服用利伐沙班和胺碘酮，最近的心电图检查显示窦性心律。目前，患者计划接受房颤射频消融手术治疗。`;
              }
              else if(names==""CT报告"")
              {
                  type=2;
                  content=""/images/CT2.pdf"";
              }
              else
              {
                  return;
              }
               $(""#divRes"").html(res);
                windowsIndex2 = layer.open({
                    type: type,
                    title: names,
                    area: ['950px', '600px'],
                    resize: true,
                    content: content,
                    maxmin:true,
                  //  btn: ['确认', '取消'],
                    yes: function (index, layero) {
                        var res = $('#builder').queryBuilder('getSQL', $(this).data('stmt'), false);
                        console.log(res);
        ");
                WriteLiteral(@"                layer.close(index);
                        table.reload('tablelist', {
                            page: {
                                curr: 1
                            },
                            where: {
                                'keyWord': '',
                                'keySql': res.sql,
                                'status': ''
                            }
                        });
                    }
                    , btn2: function (index, layero) {
                        layer.close(windowsIndex2);
                    },
                    success: function (layero, index) {
                        if (names == ""CT报告"")
                            layer.full(index); // 最大化窗口
                    }

                });
             })

        });
      
        var $b = $('#builder');

        var options = {
            allow_empty: true,

            //default_filter: 'name',
            sort_filters: true,

          ");
                WriteLiteral(@"  optgroups: {
                core: {
                    en: 'Core',
                    fr: 'Coeur'
                }
            },

            plugins: {
                'bt-tooltip-errors': { delay: 100 },
                'sortable': null,
                'filter-description': { mode: 'bootbox' },
                //      'chosen-selectpicker': null,
                'unique-filter': null,
                'bt-checkbox': { color: 'primary' },
                'invert': null,
                'not-group': null
            },

            // standard operators in custom optgroups
            operators: [
                { type: 'equal', optgroup: 'basic' },
                { type: 'not_equal', optgroup: 'basic' },
                { type: 'in', optgroup: 'basic' },
                { type: 'not_in', optgroup: 'basic' },
                { type: 'less', optgroup: 'numbers' },
                { type: 'less_or_equal', optgroup: 'numbers' },
                { type: 'greater', optgroup: 'num");
                WriteLiteral(@"bers' },
                { type: 'greater_or_equal', optgroup: 'numbers' },
                { type: 'between', optgroup: 'numbers' },
                { type: 'not_between', optgroup: 'numbers' },
                { type: 'begins_with', optgroup: 'strings' },
                { type: 'not_begins_with', optgroup: 'strings' },
                { type: 'contains', optgroup: 'strings' },
                { type: 'not_contains', optgroup: 'strings' },
                { type: 'ends_with', optgroup: 'strings' },
                { type: 'not_ends_with', optgroup: 'strings' },
                { type: 'is_empty' },
                { type: 'is_not_empty' },
                { type: 'is_null' },
                { type: 'is_not_null' }
            ],

            filters: [
                /*
                 * string with separator
                 */
                {
                    id: 'HZXM',
                    field: 'HZXM',
                    label: {
                        en: '患者姓名',
   ");
                WriteLiteral(@"                 },
                    icon: 'bi-person-fill',
                    value_separator: ',',
                    type: 'string',
                    optgroup: '基本信息',
                    default_value: '',
                    size: 30,
                    validation: {
                        allow_empty_value: false
                    },
                    unique: true
                },
                {
                    id: 'BLH',
                    field: 'BLH',
                    label: {
                        en: '病历号',
                    },
                    icon: 'bi-person-fill',
                    value_separator: ',',
                    type: 'string',
                    optgroup: '基本信息',
                    default_value: '',
                    size: 30,
                    validation: {
                        allow_empty_value: false
                    },
                    unique: true
                },

                /*
         ");
                WriteLiteral(@"        * integer with separator for 'in' and 'not_in'
                 */
                {
                    id: 'NL',
                    field: 'NL',
                    label: {
                        en: '年龄',
                    },
                    icon: 'bi-calendar3',
                    type: 'integer',
                    input: 'text',
                    value_separator: '|',
                    optgroup: '基本信息',
                    description: function (rule) {
                        if (rule.operator && ['in', 'not_in'].indexOf(rule.operator.type) !== -1) {
                            return 'Use a pipe (|) to separate multiple values with ""in"" and ""not in"" operators';
                        }
                    }
                },
                /*
                 * select
                 */
                {
                    id: 'KSDM',
                    field: 'KSDM',
                    label: '所在科室',
                    icon: 'bi-list-task',
  ");
                WriteLiteral(@"                  type: 'string',
                    input: 'select',
                    optgroup: '基本信息',
                    values: [
                        {
                            label: '重症医学三科',
                            value: '1218',
                        },
                        {
                            label: '感染性疾病科',
                            value: '9327',
                        },
                        {
                            label: '内分泌科',
                            value: '1218',
                        },
                        {
                            label: '重症医学三科',
                            value: '1105',
                        },
                        {
                            label: '血液科',
                            value: '1112',
                        },
                        {
                            label: '儿外科',
                            value: '1207',
                        },
                   ");
                WriteLiteral(@"     {
                            label: '神经内科',
                            value: '1850',
                        }
                    ],
                    operators: ['equal', 'not_equal', 'is_null', 'is_not_null']
                },

                /*
                * textarea
                */
                {
                    id: 'RYZDMC',
                    field: 'RYZDMC',
                    label: '入院诊断',
                    icon: 'bi-qr-code',
                    type: 'string',
                    input: 'textarea',
                    optgroup: '疾病信息',
                    operators: ['contains'],
                    size: 30,
                    rows: 3
                },
                {
                    id: 'CYZDMC',
                    field: 'CYZDMC',
                    label: '出院诊断',
                    icon: 'bi-qr-code',
                    type: 'string',
                    input: 'textarea',
                    optgroup: '疾病信息',
          ");
                WriteLiteral(@"          operators: ['contains'],
                    size: 30,
                    rows: 3
                },

                {
                    id: 'SSMC',
                    field: 'SSMC',
                    label: '手术名称',
                    icon: 'bi-qr-code',
                    type: 'string',
                    input: 'textarea',
                    optgroup: '疾病信息',
                    operators: ['contains'],
                    size: 30,
                    rows: 3
                }]
        };

        // init


        $('#builder').on('afterCreateRuleInput.queryBuilder', function (e, rule) {
            if (rule.filter.plugin == 'selectize') {
                rule.$el.find('.rule-value-container').css('min-width', '200px')
                    .find('.selectize-control').removeClass('form-control');
            }
        });
    </script>
    <div");
                BeginWriteAttribute("class", " class=\"", 21891, "\"", 21899, 0);
                EndWriteAttribute();
                WriteLiteral(" id=\"form_window\" style=\"display: none;\">\r\n        <div");
                BeginWriteAttribute("class", " class=\"", 21955, "\"", 21963, 0);
                EndWriteAttribute();
                WriteLiteral(" style=\"padding-top:15px;\">\r\n            <form id=\"fm_import\"");
                BeginWriteAttribute("action", " action=\"", 22025, "\"", 22034, 0);
                EndWriteAttribute();
                WriteLiteral(">\r\n                <div id=\"builder\"></div>\r\n            </form>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"window_wrap\" id=\"form_window_rz\" style=\"display: none;\">\r\n\r\n        <form class=\"layui-form\" id=\"fm_rz\"");
                BeginWriteAttribute("action", " action=\"", 22250, "\"", 22259, 0);
                EndWriteAttribute();
                WriteLiteral(@">
          
            <div class=""layui-form-item"" style=""padding-top:15px"">
                <label class=""layui-form-label"" style=""width:130px"">入组专病库：</label>
                <div class=""layui-input-block"">
                    <div id=""demo1"" class=""xm-select-demo"" style=""width:300px""></div>
                </div>
            </div>
            <div class=""layui-form-item"" style=""padding-top:15px"">
                <label class=""layui-form-label"" style=""width:130px"">就诊诊断：</label>
                <div class=""layui-input-block"" style=""margin-top:7px;"">
                   感冒
                </div>
            </div>
            <div class=""layui-form-item"" style=""padding-top:15px"">
                <label class=""layui-form-label"" style=""width:130px"">药物过敏史：</label>
                <div class=""layui-input-block"" style=""margin-top:7px;"">
                    无
                </div>
            </div>
             <div class=""layui-form-item"" style=""padding-top:15px"">
              
         ");
                WriteLiteral(@"       <div class=""rectangle"" style=""background-color:#FFC0CB"">
                    体格检查
                </div>
                <div class=""rectangle"" style=""background-color:#b2c4d1"">
                    现病史
                </div>
            </div>
            <div class=""layui-form-item"" style=""padding-top:15px"">

                <div class=""rectangle"" style=""background-color:#b2c4d1"">
                    CT报告
                </div>
                <div class=""rectangle"" style=""background-color:#18f5c5"">
                    临床医嘱
                </div>
            </div>
             <div class=""layui-form-item"" style=""padding-top:15px"">

                <div class=""rectangle"" style=""background-color:#18f5c5"">
                    生命体征
                </div>
                <div class=""rectangle"" style=""background-color:#FFC0CB"">
                    手术记录
                </div>
            </div>
            <div class=""layui-form-item"" style=""padding-top:15px"">

                <di");
                WriteLiteral(@"v class=""rectangle"" style=""background-color:#18f5c5"">
                    MDT记录
                </div>
                <div class=""rectangle"" style=""background-color:#FFC0CB"">
                    补录资料
                </div>
            </div>
        </form>

    </div>

    <div class=""window_wrap"" id=""form_window_res"" style=""display: none;"">

        <form class=""layui-form"" id=""fm_res""");
                BeginWriteAttribute("action", " action=\"", 24712, "\"", 24721, 0);
                EndWriteAttribute();
                WriteLiteral(@">

            <div class=""layui-form-item"" style=""padding-top:15px"">
              
                <div class=""layui-input-block"">
                   检查结果：
                </div>
            </div>
            <div class=""layui-form-item"" style=""padding-top:15px"">

                <div class=""layui-input-block"" id=""divRes"">
                  
                </div>
            </div>
        </form>

    </div>

");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_23);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n\r\n\r\n</html>\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
