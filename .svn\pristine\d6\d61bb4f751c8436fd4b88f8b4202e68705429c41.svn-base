﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace AngelwinResearch.Models
{
    public partial class CRFormFieldSet
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [ForeignKey("CRForm")]
        public int CRFormId { get; set; } ////表单Id
        public virtual CRForm CRForm { get; set; }

        [MaxLength(500)]
        public string FieldName { get; set; }//字段名称
        [MaxLength(500)]
        public string FieldComment { get; set; }//字段名称

        [DefaultValue(1)]
        public int Orderby { get; set; }  //排序号 addbyzolf 250416

        [MaxLength(200)]
        public string GroupName { get; set; }//分组名称 addbyzolf 250722
        [MaxLength(500)]
        public string GroupDesc { get; set; }//分组描述 addbyzolf 250722


        [MaxLength(1000)]
        public string ExtractSet { get; set; }//提取配置

        [MaxLength(50)]
        public string MinRangeValue { get; set; }//最小区间值   //addbyzolf 20240903

        [MaxLength(50)]
        public string MaxRangeValue { get; set; }//最大区间值

        [MaxLength(50)]
        public string CreateUserName { get; set; }
        public DateTime CreatedTime { get; set; }
    }
}
