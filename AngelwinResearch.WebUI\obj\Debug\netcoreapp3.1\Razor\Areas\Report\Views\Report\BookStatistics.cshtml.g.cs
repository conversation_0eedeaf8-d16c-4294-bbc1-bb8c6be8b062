#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Report\Views\Report\BookStatistics.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "044ac054e93e319c0e206e41d6149a6699de7177743ae69bbb197d911b310d7b"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_Report_Views_Report_BookStatistics), @"mvc.1.0.view", @"/Areas/Report/Views/Report/BookStatistics.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"044ac054e93e319c0e206e41d6149a6699de7177743ae69bbb197d911b310d7b", @"/Areas/Report/Views/Report/BookStatistics.cshtml")]
    #nullable restore
    internal sealed class Areas_Report_Views_Report_BookStatistics : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
            WriteLiteral("<!DOCTYPE html>\r\n<html lang=\"zh-CN\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "044ac054e93e319c0e206e41d6149a6699de7177743ae69bbb197d911b310d7b3077", async() => {
                WriteLiteral(@"
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <title>预约统计</title>
    <style>
        body {
            background: #f4f6fa;
            font-family: 'Segoe UI', 'PingFang SC', 'Hiragino Sans', Arial, sans-serif;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        .container {
            width: 100%;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        .title {
            text-align: center;
            font-size: 2.2rem;
            font-weight: bold;
            margin: 0 0 8px 0;
            padding: 8px 0;
            color: #2563eb;
            letter-spacing: 2px;
        }
        .main-content {
            display: flex;
            gap: 8px;
            margin: 0 0 8px 0;
            padding: 0;
            width: 100%;
            box-sizing: border-box;
        }
        .panel {
            background: #fff");
                WriteLiteral(@";
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            padding: 8px;
            flex: 1 1 0;
            min-width: 0;
            min-height: 520px;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            overflow: hidden;
        }
        .panel-title {
            font-size: 1.1rem;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 8px;
            text-align: left;
        }
        .chart-area {
            flex: 1 1 0;
            min-height: 420px;
            width: 100%;
        }
        .analysis-box {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            padding: 8px;
            margin: 0 0 8px 0;
            width: 100%;
            box-sizing: border-box;
        }
        .analysis-title {
            font-size: 1.1rem;
            font-weight: bold;
   ");
                WriteLiteral("         color: #2563eb;\r\n            margin-bottom: 8px;\r\n        }\r\n        .analysis-content {\r\n            color: #334155;\r\n            font-size: 1rem;\r\n            line-height: 1.8;\r\n        }\r\n        ");
                WriteLiteral(@"@media (max-width: 900px) {
            .main-content {
                flex-direction: column;
                gap: 8px;
                width: 100%;
            }
            .panel {
                padding: 12px;
                min-height: 380px;
                width: 100%;
            }
            .container {
                padding: 0;
                width: 100%;
            }
            .chart-area {
                min-height: 280px;
                width: 100%;
            }
        }
        ");
                WriteLiteral(@"@media (max-width: 480px) {
            .panel {
                padding: 8px;
                min-height: 240px;
                width: 100%;
            }
            .chart-area {
                min-height: 180px;
                width: 100%;
            }
            .container {
                padding: 0;
                width: 100%;
            }
            .title {
                font-size: 1.4rem;
                margin-bottom: 12px;
                padding: 12px 0;
            }
        }
        .search-box {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            padding: 12px 8px;
            margin: 0 0 8px 0;
            width: 100%;
            box-sizing: border-box;
        }
        .search-form {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
        }
        .search-form label {
            color: #2563eb;
         ");
                WriteLiteral(@"   font-size: 15px;
            font-weight: 500;
        }
        .search-form select, .search-form input {
            min-width: 120px;
            max-width: 220px;
            height: 36px;
            padding: 0 12px;
            border-radius: 8px;
            border: 1px solid #cbd5e1;
            font-size: 15px;
            background: #fff;
            outline: none;
            transition: border 0.2s;
        }
        .search-form button {
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 8px;
            height: 36px;
            padding: 0 24px;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
        }
        .search-form button:hover {
            background: #1d4ed8;
        }
        .marquee-bar {
            width: 100%;
            background: #2563eb;
            color: #fff;
            overflow: hidden;
     ");
                WriteLiteral(@"       position: fixed;
            left: 0;
            bottom: 0;
            z-index: 999;
            height: 38px;
            display: flex;
            align-items: center;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.06);
        }
        .marquee-content {
            white-space: nowrap;
            display: inline-block;
            padding-left: 100%;
            animation: marquee 12s linear infinite;
            font-size: 1rem;
            font-weight: 500;
            letter-spacing: 1px;
        }
        ");
                WriteLiteral("@keyframes marquee {\r\n            0% { transform: translateX(0); }\r\n            100% { transform: translateX(-100%); }\r\n        }\r\n        ");
                WriteLiteral("@media (max-width: 900px) {\r\n            .marquee-bar { height: 32px; font-size: 0.95rem; }\r\n            .marquee-content { font-size: 0.95rem; }\r\n        }\r\n        ");
                WriteLiteral(@"@media (max-width: 480px) {
            .marquee-bar { height: 28px; font-size: 0.9rem; }
            .marquee-content { font-size: 0.9rem; }
        }
    </style>
    <script src=""https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js""></script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "044ac054e93e319c0e206e41d6149a6699de7177743ae69bbb197d911b310d7b10379", async() => {
                WriteLiteral(@"
    <div class=""container"">
        <div class=""title"">预约统计</div>
        <div class=""search-box"">
            <form class=""search-form"" onsubmit=""return false;"">
                <label for=""searchType"">筛选类型：</label>
                <select id=""searchType"">
                    <option");
                BeginWriteAttribute("value", " value=\"", 6311, "\"", 6319, 0);
                EndWriteAttribute();
                WriteLiteral(@">全部</option>
                    <option value=""ct"">CT</option>
                    <option value=""mr"">MR</option>
                    <option value=""us"">超声</option>
                </select>
                <label for=""searchText"">关键字：</label>
                <input type=""text"" id=""searchText"" placeholder=""请输入关键字"" />
                <button type=""button"" id=""searchBtn"">检索</button>
            </form>
        </div>
        <div class=""main-content"">
            <div class=""panel"">
                <div class=""panel-title"">CT科室各检查室不同时段预约热力图</div>
                <div id=""heatmap"" class=""chart-area""></div>
            </div>
            <div class=""panel"">
                <div class=""panel-title"">CT各检查室预约等待时长分布</div>
                <div id=""violin"" class=""chart-area""></div>
            </div>
        </div>
        <div class=""analysis-box"">
            <div class=""analysis-title"">基于以上数据的分析及提醒建议</div>
            <div class=""analysis-content"">
                1. 预约高峰主要集中在上午10-12点，部分检查室预约");
                WriteLiteral(@"量明显高于其他时段，建议合理安排设备与人力资源。<br>
                2. 等待时长分布显示部分检查室存在极端等待情况，需关注流程瓶颈，优化预约分配。<br>
                3. 建议定期复盘预约数据，及时发现异常波动，辅助管理决策。
            </div>
        </div>
    </div>
    <div class=""marquee-bar"">
        <div class=""marquee-content"">提醒：预约等待时长最久的科室为影像楼CT1（16排），请关注高峰时段资源调配。</div>
    </div>
    <script>
        // 热力图数据示例
        var heatmapData = {
            xAxis: ['08','09','10','11','12','13','14','15','16','17','18'],
            yAxis: [
                'CT5室(周二)','CT2室(周四至周六)','CT5室(周一至周三)','10号楼双源CT',
                'CT5室(周四至周六)','CT2室(周一至周三)','影像楼CT2（64排）','影像楼CT1（16排）','CT1室'
            ],
            data: [
                // [x, y, value]
                [0,0,0],[1,0,0],[2,0,0],[3,0,0],[4,0,22],[5,0,0],[6,0,0],[7,0,0],[8,0,0],[9,0,0],[10,0,0],
                [0,1,165],[1,1,175],[2,1,101],[3,1,128],[4,1,83],[5,1,114],[6,1,94],[7,1,85],[8,1,0],[9,1,0],[10,1,0],
                [0,2,191],[1,2,202],[2,2,104],[3,2,96],[4,2,93],[5,2,146],[6,2,81],[7,2,127],[8,2,0");
                WriteLiteral(@"],[9,2,0],[10,2,0],
                [0,3,201],[1,3,255],[2,3,58],[3,3,260],[4,3,216],[5,3,205],[6,3,0],[7,3,0],[8,3,0],[9,3,0],[10,3,0],
                [0,4,134],[1,4,90],[2,4,111],[3,4,88],[4,4,131],[5,4,117],[6,4,141],[7,4,139],[8,4,141],[9,4,92],[10,4,86],
                [0,5,190],[1,5,126],[2,5,182],[3,5,110],[4,5,182],[5,5,179],[6,5,134],[7,5,86],[8,5,127],[9,5,83],[10,5,116],
                [0,6,237],[1,6,163],[2,6,385],[3,6,240],[4,6,235],[5,6,304],[6,6,0],[7,6,0],[8,6,0],[9,6,0],[10,6,0],
                [0,7,388],[1,7,263],[2,7,631],[3,7,368],[4,7,345],[5,7,454],[6,7,0],[7,7,0],[8,7,0],[9,7,0],[10,7,0],
                [0,8,416],[1,8,268],[2,8,414],[3,8,245],[4,8,419],[5,8,377],[6,8,429],[7,8,280],[8,8,382],[9,8,291],[10,8,323]
            ]
        };
        var heatmapChart = echarts.init(document.getElementById('heatmap'));
        heatmapChart.setOption({
            tooltip: { position: 'top' },
            grid: { left: 80, right: 40, top: 40, bottom: 40, containLabel: true },
");
                WriteLiteral(@"            xAxis: { type: 'category', data: heatmapData.xAxis, splitArea: { show: true }, name: '时间', nameLocation: 'middle', nameGap: 28 },
            yAxis: { type: 'category', data: heatmapData.yAxis, splitArea: { show: true }, name: '检查项', nameLocation: 'middle', nameGap: 40 },
            visualMap: {
                min: 0, max: 650,
                calculable: true,
                orient: 'vertical',
                right: 0, top: 'center',
                inRange: { color: ['#e0ecf7','#60a5fa','#1e40af'] },
                text: ['高','低'],
                textStyle: { color: '#2563eb' }
            },
            series: [{
                name: '预约数',
                type: 'heatmap',
                data: heatmapData.data,
                label: { show: true, color: '#222', fontSize: 13 },
                emphasis: { itemStyle: { shadowBlur: 10, shadowColor: 'rgba(37,99,235,0.18)' } }
            }]
        });

        // 小提琴图（用箱线图+散点模拟）
        var violinChart = echarts.init");
                WriteLiteral(@"(document.getElementById('violin'));
        violinChart.setOption({
            title: {
                text: '2019年1月数据分析 | 平均等待时间: 24分钟 | 样本数: 12297条记录',
                left: 'center',
                top: 8,
                textStyle: { fontSize: 14, color: '#334155', fontWeight: 'normal' }
            },
            tooltip: { trigger: 'axis' },
            grid: { left: 60, right: 40, top: 48, bottom: 40, containLabel: true },
            xAxis: {
                type: 'category',
                data: [
                    'CT5室(周二)','影像楼CT1（16排）','CT1室','CT2室(周一至周三)','CT2室(周四至周六)',
                    'CT5室(周一至周三)','CT5室(周四至周六)','影像楼CT2（64排）','10号楼双源CT'
                ],
                axisLabel: { rotate: 30, fontSize: 12 }
            },
            yAxis: {
                type: 'value',
                name: '分钟',
                min: 0,
                max: 200,
                splitLine: { lineStyle: { color: '#e5e7eb' } }
            },
            series: [
      ");
                WriteLiteral(@"          {
                    name: '等待时长分布',
                    type: 'boxplot',
                    data: [
                        [5, 10, 15, 20, 40],
                        [6, 12, 18, 25, 60],
                        [4, 9, 14, 22, 55],
                        [7, 13, 19, 27, 70],
                        [8, 14, 20, 28, 80],
                        [6, 11, 17, 23, 50],
                        [7, 12, 18, 24, 65],
                        [5, 10, 16, 21, 45],
                        [9, 15, 22, 30, 90]
                    ],
                    itemStyle: { color: '#e0e7ff', borderColor: '#a78bfa' }
                },
                {
                    name: '平均值',
                    type: 'scatter',
                    data: [24, 28, 22, 26, 30, 23, 25, 20, 32],
                    symbolSize: 16,
                    itemStyle: { color: '#facc15', borderColor: '#fff', borderWidth: 2 }
                }
            ]
        });
    </script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html> ");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
