@{
    ViewBag.Title = "首页";
    Layout = null;
}
<!DOCTYPE html>
<html>

<head>
    <title>欢迎使用</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="description" content="Awesome Bubble Navigation with jQuery" />
    <meta name="keywords" content="jquery, circular menu, navigation, round, bubble" />
    <link rel="stylesheet" href="/css/welcome_style.css" type="text/css" media="screen" />
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css">
    @*<link rel="stylesheet" href="/css/font/eyes_icon/eye_icon.css">*@

    <style>
			* {
				margin: 0;
				padding: 0;
			}

			body {
				font-family: Arial;
				background-color: #fff;
				background-image: url(/images/welcome/bg1.jpg);
				background-repeat: no-repeat;
				background-position: left bottom;
				background-size: cover;
				overflow: hidden;
			}

			.title {
				width: 460px;
				height: 119px;
				position: fixed;
				bottom: 30px;
				left: 30px;
				background: transparent url(/images/welcome/title.png) no-repeat top left;
			}

			a.back {
				background: transparent url(back.png) no-repeat top left;
				position: fixed;
				width: 150px;
				height: 27px;
				outline: none;
				bottom: 0px;
				left: 0px;
			}

			/* 侧边信息栏 */
			.right_info {
				margin-right: 2px;
				margin-left: 10px;

			}

			.info_list>li {
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
				padding: 0 15px;
				line-height: 40px;
				border-bottom: 1px dashed #b16897;
			}

			.info_title {
				font-size: 18px;
				font-weight: bold;
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
			}

			/* 页脚 */
			.footer {
				width: 100%;
				padding-bottom: 15px;
				position: absolute;
				bottom: 0;
			}

			.foot_content {

				display: flex;
				flex-direction: row;
				justify-content: flex-end;
				align-items: center;
				margin-right: 15px;
			}

			.logo1 {
				width: 112px;
				height: 50px;
				margin-right: 10px;
			}

			.logo1 img {
				width: 100%;
			}

			.cp_info {
				line-height: 24px;
				color: #585d5e;
			}

    </style>
</head>
<body>

    <div class="layui-row">
        <div class="layui-col-md12">
            <div id="content">
                <div class="title"></div>
                <div class="navigation" id="nav">
                    <div class="item user" lay-href="/ReportingManage/PatientManage/Index">
                        <img src="/images/welcome/bg_user.png" alt="" width="300" height="300" class="circle" />
                        <a class="icon"></a>
                        <h2>科研患者管理</h2>
                    </div>
                    <div class="item home" lay-href="/PatientDiscoveryManage/CRFDataDetails/Index">
                        <img src="/images/welcome/bg_home.png" alt="" width="300" height="300" class="circle" />
                        <a class="icon"></a>
                        <h2>填报结果下载</h2>
                    </div>
                    <div class="item shop">
                        <img src="/images/welcome/bg_shop.png" alt="" width="300" height="300" class="circle" />
                        <a class="icon "></a>
                        <h2>电子病历</h2>
                    </div>
                    <div class="item camera" >
                        <img src="/images/welcome/bg_camera.png" alt="" width="300" height="300" class="circle" />
                        <a class="icon"></a>
                        <h2>检验样本管理</h2>
                    </div>
					<div class="item consultation">
					    <img src="/images/welcome/bg_consultation.png" alt="" width="300" height="300" class="circle" />
					    <a class="icon"></a>
                        <h2>随访管理</h2>
					</div>
                </div>
            </div>

        </div>

    </div>


    <!-- 			<div class="footer">

            <div class="foot_content">
                <div class="logo1">
                    <img src="../images/color.png" alt="">
                </div>
                <div class="cp_info">
                    <p>技术支持：北京天助盈通技术有限公司V1.0</p>
                    <p>Beijing Angelwin Technology Co.,Ltd.</p>

                </div>
            </div>

        </div> -->
    <!-- The JavaScript -->
    <script type="text/javascript" src="/js/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="/js/jquery.easing.1.3.js"></script>
    <script type="text/javascript" src="/layuiadmin/layui/layui.js"></script>



    <script>
        layui.config({
            base: '/layuiadmin/' //静态资源所在路径
        }).extend({
            index: 'lib/index' //主入口模块
        }).use(['index', 'flow', 'jquery', 'layer', 'form', 'element', 'carousel'], function () {
            var flow = layui.flow,
                $ = layui.$,
                layer = layui.layer,
                form = layui.form,
                element = layui.element,
                carousel = layui.carousel;
            var showflag = false;
            var showflag1 = false;
            $('#showbtn').on('click', function () {
                if (showflag) {
                    showflag = false
                    $("#showbtn").attr("class", "iconfont icon-yanjing_bi ");
                }
                else {
                    showflag = true;
                    $("#showbtn").attr("class", "iconfont icon-browse");

                }
                GetReportShowLis();
              
            });
            $('#showbtn1').on('click', function () {
                if (showflag1) {
                    showflag1 = false
                    $("#showbtn1").attr("class", "iconfont icon-yanjing_bi ");
                }
                else {
                    showflag1 = true;
                    $("#showbtn1").attr("class", "iconfont icon-browse");

                }
               
                //GetNotifyMessage();
            });
           
            function getUlH() {
                var cadHeader = $(".layui-card-header").height();
                var windowH = $(window).height();
                var ulH = (windowH / 2) - cadHeader - 50
                $(".info_wrap").css("height", ulH + "px");
            }
            getUlH();
            function changeHeight() {
                var winH = $(window).height();
                $("#content").css("height", winH + "px");
            }
            changeHeight();
            $(".right_info").css("display", "");
            $(".fav").find('img').stop().animate({
                'width': '200px',
                'height': '200px',
                'top': '-25px',
                'left': '-25px',
                'opacity': '1.0'
            }, 500, 'easeOutBack', function () {

            });
            $('#nav > div').hover(
                function () {
                    var $this = $(this);
                    $this.find('img').stop().animate({
                        'width': '200px',
                        'height': '200px',
                        'top': '-25px',
                        'left': '-25px',
                        'opacity': '1.0'
                    }, 500, 'easeOutBack', function () {

                    });

                    $this.find('a:first,h2').addClass('active');
                },
                function () {
                    var $this = $(this);
                 
                    $this.find('ul').fadeOut(500);

                    $this.find('img').stop().animate({
                        'width': '52px',
                        'height': '52px',
                        'top': '0px',
                        'left': '0px',
                        'opacity': '0.1'
                    }, 5000, 'easeOutBack');

                    $this.find('a:first,h2').removeClass('active');
                });
           
            $(window).resize(function () {
                getUlH();
                changeHeight();
            });

            //报告隐藏显示通知
            function GetReportShowLis() {

                $.ajax({
                    url: "/Home/GetReportShowLis",
                    type: "post",
                    datatype: 'json',
                    data: { "ShowFlag": showflag },
                    success: function (re) {
                        $("#test2").html("");
                        //<li>
                        //    <p>0010019 张** 报告可见 <b>2022-10-20</b></p>
                        //    <dutton class="layui-btn layui-btn-xs">已知晓</dutton>
                        //</li>
                        var htmlstr = '<div carousel-item>';
                        if (re.code == 0) {
                            if (re.result != null && re.result != undefined && re.result.length > 0) {

                                for (var i = 0; i < re.result.length; i++) {

                                    if (i == 0 || i == 10 || i == 20) {
                                        htmlstr += '<ul class="info_list"> ';
                                    }
                                    htmlstr += '<li><p>' + re.result[i].patientID + " " + re.result[i].contents + '至 <b>' + re.result[i].crontime+'</b></p><dutton class="layui-btn layui-btn-xs">已知晓</dutton></li>'
                                    if (i == 9 || i == 19 || i == 29) {
                                        htmlstr += '</ul> ';
                                    }
                                }

                            }
                            else { htmlstr += '<ul ></ul>  '; }
                        }
                        else {
                            htmlstr += '<ul ></ul>  ';
                        }
                        
                       
                        htmlstr += '</div>';
                        $("#test2").html(htmlstr);
                        carousel.render({
                            elem: '#test2',
                            width: '100%' //设置容器宽度
                            ,
                            arrow: 'none' //始终显示箭头
                            ,
                            anim: 'default' //切换动画方式
                            ,
                            'autoplay': true
                        });
                        getUlH();
                        form.render();
                    }, error: function () {
                        layer.msg("获取失败！");
                    }
                })
            }
            //随访通知
            function GetNotifyMessage() {

                $.ajax({
                    url: "/Home/GetNotifyMessage",
                    type: "post",
                    datatype: 'json',
                    data: { "ShowFlag": showflag1 },
                    success: function (re) {
                        $("#test1").html("");
                        //<li>
                        //    <p>0010019 张** 报告可见 <b>2022-10-20</b></p>
                        //    <dutton class="layui-btn layui-btn-xs">已知晓</dutton>
                        //</li>
                        var htmlstr = '<div carousel-item>';
                        if (re.code == 0) {
                            if (re.result != null && re.result != undefined && re.result.length > 0) {

                                for (var i = 0; i < re.result.length; i++) {

                                    if (i == 0 || i == 10 || i == 20) {
                                        htmlstr += '<ul class="info_list"> ';
                                    }
                                    htmlstr += '<li><p>' + re.result[i].patientID + " " + re.result[i].contents + ' <b>' + re.result[i].crontime + '</b></p><dutton class="layui-btn layui-btn-xs">已知晓</dutton></li>'
                                    if (i == 9 || i == 19 || i == 29) {
                                        htmlstr += '</ul> ';
                                    }
                                }

                            }
                            else { htmlstr += '<ul ></ul>  '; }
                        }
                        else {
                            htmlstr += '<ul ></ul>  ';
                        }


                        htmlstr += '</div>';
                        $("#test1").html(htmlstr);
                        carousel.render({
                            elem: '#test1',
                            width: '100%' //设置容器宽度
                            ,
                            arrow: 'none' //始终显示箭头
                            ,
                            anim: 'default' //切换动画方式
                            ,
                            'autoplay': true
                        });
                        getUlH();
                        form.render();
                    }, error: function () {
                        layer.msg("获取失败！");
                    }
                })
            }
            //GetReportShowLis();
            //GetNotifyMessage();
            



        });
    </script>


    <script>
        // 参数加*函数
        function secretinfo(text, text_type) {
            // 1.姓名 2.出生日期 3.身份证 4.联系方式
            var testlen = text.length;
            var star = ""
            if (text_type == 1) { // 1.姓名
                for (var i = 0; i < testlen - 1; i++) {
                    star += "*"
                }
                var text = text.substring(1, -1) + star
                console.log(text)
                return text

            } else if (text_type == 2) { //2.出生日期
                for (var i = 0; i < 4; i++) {
                    star += "*"
                }
                text = star + text.slice(4)
                console.log(text)
                return text

            } else if (text_type == 3) { //3.身份证
                for (var i = 0; i < testlen - 6; i++) {
                    star += "*"
                }
                text = text.substring(6, -1) + star
                console.log(text)
                return text

            } else if (text_type == 4) { //4.联系方式
                for (var i = 0; i < 4; i++) {
                    star += "*"
                }
                text = text.slice(0, -4) + star
                console.log(text)
                return text

            } else {
                console.log(text)
                return text
            }
        }
        /*secretinfo("13072168888", 4)*/
    </script>


</body>









</html>
