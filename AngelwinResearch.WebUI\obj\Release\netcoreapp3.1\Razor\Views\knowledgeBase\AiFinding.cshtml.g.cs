#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\knowledgeBase\AiFinding.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "fd5897583b9310a609f867ae541dec7156a8769df045b8969f6798f9de43f8ad"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_knowledgeBase_AiFinding), @"mvc.1.0.view", @"/Views/knowledgeBase/AiFinding.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI

#nullable disable
    ;
#nullable restore
#line 2 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"fd5897583b9310a609f867ae541dec7156a8769df045b8969f6798f9de43f8ad", @"/Views/knowledgeBase/AiFinding.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"a5bcd83cf27c8ffb8baf597d24314352bf7b52b5a9bb5ee83e83e9d0089a628e", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_knowledgeBase_AiFinding : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("layui-layout-body"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\knowledgeBase\AiFinding.cshtml"
  
    ViewBag.Title = "AI检索";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "fd5897583b9310a609f867ae541dec7156a8769df045b8969f6798f9de43f8ad6458", async() => {
                WriteLiteral("\r\n    <meta charset=\"utf-8\">\r\n    <title> ");
                Write(
#nullable restore
#line 9 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\knowledgeBase\AiFinding.cshtml"
             ViewBag.SiteTitle

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</title>
    <meta name=""renderer"" content=""webkit"">
    <meta http-equiv=""X-UA-Compatible"" content=""IE=edge,chrome=1"">
    <meta name=""viewport""
          content=""width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0"">
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "fd5897583b9310a609f867ae541dec7156a8769df045b8969f6798f9de43f8ad7352", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "fd5897583b9310a609f867ae541dec7156a8769df045b8969f6798f9de43f8ad8554", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "fd5897583b9310a609f867ae541dec7156a8769df045b8969f6798f9de43f8ad9756", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        .tree_wrap,.result_wrap {
            background-color: #fff;
        }
        .result_wrap{
            margin-left:5px;
            padding-bottom:6px;
        }
        .line_wrap {
            margin: 0 16%;
            display: flex;
            flex-direction: row;
        }
        .layui-input-block{
            position:relative;
            width:100%;
            margin-left:0;

        }
        .layui-input-block i{
            position:absolute;
            top:12px;
            left:12px;
        }
        .layui-input{
            padding-left:40px;
        }
        .layui-card-header{
            height:auto;
            line-height:normal;
        }
        .morebtn{
            text-align:right;
            padding-right:15px;
        }
        .result_h{
            overflow-y:auto;
        }
        .tertiary_structure {
            box-shadow: 0px 0px 5px #e2e2e2;
        }
        .tertiary_structure_list{
            pad");
                WriteLiteral(@"ding-top:10px;

        }
        .tertiary_structure_item {
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 10px;
            text-indent: 2em;
        }

        .active {
            /* border-bottom:1px solid #666;*/
            text-decoration: underline;
        }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "fd5897583b9310a609f867ae541dec7156a8769df045b8969f6798f9de43f8ad12980", async() => {
                WriteLiteral(@"

    <div class=""lay_t_head"">
        <div class=""layui-card"" style=""margin-bottom:5px;"">
            <div class=""layui-form"" style=""padding:15px;"">
                <div class=""line_wrap "">
                    <div class=""layui-input-block"">
                        <i class=""layui-icon layui-icon-search""></i>
                        <input type=""text"" name=""keyWords"" id=""keyWords"" autocomplete=""off"" class=""layui-input"">
                    </div>
                    <button class=""layui-btn"" id=""search"" style=""margin-left:10px"">查找</button>
                </div>
            </div>
        </div>
    </div>
    <div class=""layui-row"">
        <div class=""layui-col-md2"">
            <div class=""tree_wrap"">
                <div id=""menuTree""></div>
            </div>
        </div>
        <div class=""layui-col-md10"">
            <div class=""result_wrap"">
                <!--二级循环体-->
                <div class=""layui-card second_stage"">
                    <div class=""layui-card-header""");
                WriteLiteral(@">内科</div>  <!--二级标题-->
                    <div class=""layui-card-body result_h"" style=""height:100%"">
                        <!--三级循环体-->
                        <div class=""layui-card tertiary_structure"">
                            <div class=""layui-card-header"">风湿免疫科 </div>  <!--三级标题-->
                            <div class=""layui-card-body"">
                                <div class=""layui-row layui-col-space10 tertiary_structure_list"">
                                    <div class=""layui-col-md4 "">
                                        <div class=""tertiary_structure_item"">
                                            <p");
                BeginWriteAttribute("class", " class=\"", 3698, "\"", 3706, 0);
                EndWriteAttribute();
                WriteLiteral(@">搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果</p>
                                        </div>
                                    </div>
                                    <div class=""layui-col-md4 "">
                                        <div class=""tertiary_structure_item"">
                                            <p");
                BeginWriteAttribute("class", " class=\"", 4081, "\"", 4089, 0);
                EndWriteAttribute();
                WriteLiteral(@">搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果</p>
                                        </div>
                                    </div>
                                    <div class=""layui-col-md4 "">
                                        <div class=""tertiary_structure_item"">
                                            <p");
                BeginWriteAttribute("class", " class=\"", 4464, "\"", 4472, 0);
                EndWriteAttribute();
                WriteLiteral(@">搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果</p>
                                        </div>
                                    </div>

                                </div>

                                <div class=""morebtn"">
                                    <button type=""button"" class=""layui-btn layui-btn-sm"">加载更多</button>
                                </div>
                            </div>
                        </div>


                        <!--三级循环体-->
                        <div class=""layui-card tertiary_structure"">
                            <div class=""layui-card-header"">心血管内科</div>  <!--三级标题-->
                            <div class=""layui-card-body"">
                                <div class=""layui-row layui-col-space10 tertiary_structure_list"">
                                    <div class=""layui-col-md4 "">
                                        <div class=""tertiary_structure_item"">
                               ");
                WriteLiteral("             <p");
                BeginWriteAttribute("class", " class=\"", 5512, "\"", 5520, 0);
                EndWriteAttribute();
                WriteLiteral(@">搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果</p>
                                        </div>
                                    </div>
                                    <div class=""layui-col-md4 "">
                                        <div class=""tertiary_structure_item"">
                                            <p");
                BeginWriteAttribute("class", " class=\"", 5895, "\"", 5903, 0);
                EndWriteAttribute();
                WriteLiteral(@">搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果</p>
                                        </div>
                                    </div>
                                    <div class=""layui-col-md4 "">
                                        <div class=""tertiary_structure_item"">
                                            <p");
                BeginWriteAttribute("class", " class=\"", 6278, "\"", 6286, 0);
                EndWriteAttribute();
                WriteLiteral(@">搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果</p>
                                        </div>
                                    </div>

                                </div>
                                <div class=""morebtn"">
                                    <button type=""button"" class=""layui-btn layui-btn-sm"">加载更多</button>
                                </div>
                            </div>
                        </div>

                        <!--三级循环体-->
                        <div class=""layui-card tertiary_structure"">
                            <div class=""layui-card-header"">消化内科</div>  <!--三级标题-->
                            <div class=""layui-card-body"">
                                <div class=""layui-row layui-col-space10 tertiary_structure_list"">
                                    <div class=""layui-col-md4 "">
                                        <div class=""tertiary_structure_item"">
                                    ");
                WriteLiteral("        <p");
                BeginWriteAttribute("class", " class=\"", 7321, "\"", 7329, 0);
                EndWriteAttribute();
                WriteLiteral(@">搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果</p>
                                        </div>
                                    </div>
                                    <div class=""layui-col-md4 "">
                                        <div class=""tertiary_structure_item"">
                                            <p");
                BeginWriteAttribute("class", " class=\"", 7704, "\"", 7712, 0);
                EndWriteAttribute();
                WriteLiteral(@">搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果</p>
                                        </div>
                                    </div>
                                    <div class=""layui-col-md4 "">
                                        <div class=""tertiary_structure_item"">
                                            <p");
                BeginWriteAttribute("class", " class=\"", 8087, "\"", 8095, 0);
                EndWriteAttribute();
                WriteLiteral(@">搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果</p>
                                        </div>
                                    </div>

                                </div>
                                <div class=""morebtn"">
                                    <button type=""button"" class=""layui-btn layui-btn-sm"">加载更多</button>
                                </div>
                            </div>
                        </div>

                        <!--三级循环体-->
                        <div class=""layui-card tertiary_structure"">
                            <div class=""layui-card-header"">感染科</div>  <!--三级标题-->
                            <div class=""layui-card-body"">
                                <div class=""layui-row layui-col-space10 tertiary_structure_list"">
                                    <div class=""layui-col-md4 "">
                                        <div class=""tertiary_structure_item"">
                                     ");
                WriteLiteral("       <p");
                BeginWriteAttribute("class", " class=\"", 9129, "\"", 9137, 0);
                EndWriteAttribute();
                WriteLiteral(@">搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果</p>
                                        </div>
                                    </div>
                                    <div class=""layui-col-md4 "">
                                        <div class=""tertiary_structure_item"">
                                            <p");
                BeginWriteAttribute("class", " class=\"", 9512, "\"", 9520, 0);
                EndWriteAttribute();
                WriteLiteral(@">搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果</p>
                                        </div>
                                    </div>
                                    <div class=""layui-col-md4 "">
                                        <div class=""tertiary_structure_item"">
                                            <p");
                BeginWriteAttribute("class", " class=\"", 9895, "\"", 9903, 0);
                EndWriteAttribute();
                WriteLiteral(@">搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果</p>
                                        </div>
                                    </div>

                                </div>
                                <div class=""morebtn"">
                                    <button type=""button"" class=""layui-btn layui-btn-sm"">加载更多</button>
                                </div>
                            </div>
                        </div>

                        <!--三级循环体-->
                        <div class=""layui-card tertiary_structure"">
                            <div class=""layui-card-header"">呼吸内科</div>  <!--三级标题-->
                            <div class=""layui-card-body"">
                                <div class=""layui-row layui-col-space10 tertiary_structure_list"">
                                    <div class=""layui-col-md4 "">
                                        <div class=""tertiary_structure_item"">
                                    ");
                WriteLiteral("        <p");
                BeginWriteAttribute("class", " class=\"", 10938, "\"", 10946, 0);
                EndWriteAttribute();
                WriteLiteral(@">搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果</p>
                                        </div>
                                    </div>
                                    <div class=""layui-col-md4 "">
                                        <div class=""tertiary_structure_item"">
                                            <p");
                BeginWriteAttribute("class", " class=\"", 11321, "\"", 11329, 0);
                EndWriteAttribute();
                WriteLiteral(@">搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果</p>
                                        </div>
                                    </div>
                                    <div class=""layui-col-md4 "">
                                        <div class=""tertiary_structure_item"">
                                            <p");
                BeginWriteAttribute("class", " class=\"", 11704, "\"", 11712, 0);
                EndWriteAttribute();
                WriteLiteral(@">搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果搜索结果</p>
                                        </div>
                                    </div>

                                </div>
                                <div class=""morebtn"">
                                    <button type=""button"" class=""layui-btn layui-btn-sm"">加载更多</button>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
      
            </div>

        </div>
    </div>




        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "fd5897583b9310a609f867ae541dec7156a8769df045b8969f6798f9de43f8ad26119", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "fd5897583b9310a609f867ae541dec7156a8769df045b8969f6798f9de43f8ad27247", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
        <script>
            layui.use(['element', 'layer', 'table', 'laydate', 'form', 'tree'], function () {
                var element = layui.element
                    , layer = layui.layer
                    , table = layui.table//表格
                    , laydate = layui.laydate
                    , $ = layui.$
                    , form = layui.form
                    , tree = layui.tree;

           var treeNodeId = """";
          var  treeNodeName = """";
                function getTree()
                {
                    $.get('/Chat/GetTreeList', function (res) {
                        if(res.code==0){
                        //渲染
                        var menulist = tree.render({
                            elem: '#menuTree'  //绑定元素
                            , showCheckbox: false
                            , showLine: true
                            , onlyIconControl: true

                            , data: res.data,
                            click: func");
                WriteLiteral(@"tion (obj) {
                                var data = obj.data;  //获取当前点击的节点数据
                                //  layer.msg('状态：' + obj.state + '<br>节点数据：' + JSON.stringify(data));
                                console.log(obj);
                                //obj.elem.addClass(""active"");
                                treeNodeId = data.id;
                                treeNodeName = data.title;
                                $(""#menuTree"").find("".layui-tree-txt"").removeClass(""active"");
                                $(obj.elem).find("".layui-tree-txt"").eq(0).addClass(""active"");
                            }
                        });
                        }
                  
                    })
                }

                getTree();

                $(window).resize(function () {
                    treeHeight();
                });


                treeHeight();
                function treeHeight() {
                    var winH = $(window).height();
   ");
                WriteLiteral(@"                 var serH = $("".lay_t_head"").outerHeight(true);
                    var contentH = winH - serH - 20;
                    var resultH = contentH - 65;
                    $("".tree_wrap"").css(""height"", contentH + ""px"");
                    $("".result_h"").css(""height"", resultH + ""px"");
                }



            $(""#search"").on('click', function () {
                if (!treeNodeId)
                {
                    layer.msg(""请选择知识库！"");
                    return;
                }
                $.post('/Chat/SearchWiki', { wikiId: treeNodeId, prompt: $(""#keyWords"").val() },function(res){
                
                
                })
               
            })



            })



        </script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html >\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
