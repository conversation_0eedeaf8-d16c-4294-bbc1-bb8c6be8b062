﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AngelwinResearch.Models
{
    public partial class ExternalReport
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        [MaxLength(200)]
        public string ReportName { get; set; }


        [ForeignKey("ResearchPatient")]
        public int ResearchPatientId { get; set; }
        public virtual ResearchPatient ResearchPatient { get; set; }

        [ForeignKey("CRForm")]
        public int? CRFormId { get; set; }
        public virtual CRForm CRForm { get; set; }

        [ForeignKey("HospitalCRForm")]
        public int? HospitalCRFormId { get; set; }
        public virtual HospitalCRForm HospitalCRForm { get; set; }

        /// <summary>
        /// 报告文件类型：图片，pdf
        /// </summary>
        public string ReportType { get; set; }

        [Required]
        [MaxLength(1000)]
        public string ReportURL { get; set; }

        public string ReportContent { get; set; }

        public string AIContent { get; set; }

        /// <summary>
        /// 是否删除 false:未删除。true：逻辑删除
        /// </summary>
        [DefaultValue(false)]
        public bool IsDel { get; set; }

        [MaxLength(50)]
        public string CreateUserName { get; set; }
        public DateTime CreatedTime { get; set; }
    }
}
