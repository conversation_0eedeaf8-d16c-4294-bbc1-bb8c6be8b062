﻿@{
    ViewBag.Title = "科研患者管理";
    Layout = null;
}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>科研患者管理</title>
    <link href="~/layuiadmin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/layuiadmin/style/admin.css" rel="stylesheet" />
    <link href="~/layuiadmin/layui/font/eyes_icon/iconfont.css" rel="stylesheet" />
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <style>
        .layui-progress-text {
            text-wrap: nowrap;
        }
        /* 移动端头部 */
        .headrow {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            background-color: #f6f6f6;
        }

            .headrow .layui-btn-primary {
                background-color: transparent;
                border: none;
                color: #333;
            }

        .search_wrap {
            background-color: #fff;
            display: flex;
            flex-direction: row;
            padding: 10px 4px;
        }
        /* 移动端头部 */
        .layui-layout-admin .layui-body {
            background-color: #fff;
        }

        .patient_item {
            margin-bottom: 10px;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 4px;
        }

        .info_left {
            flex: 1;
        }

        .patient_pic {
            width: 100px;
            height: 100px;
            text-align: center;
        }

            .patient_pic img {
                width: 100%;
                height: 100%;
            }

        .flex-row {
            display: flex;
            flex-direction: row;
        }


        .flex-column {
            display: flex;
            flex-direction: column
        }

        .layui-inline {
            margin-left: 10px;
        }

        .content-inline {
            display: flex;
            flex-direction: row;
        }

        .user_name {
            line-height: 30px;
            font-size: 18px;
            font-weight: bold;
        }

        .user_value {
            color: #000;
        }

        .card_type {
            font-size: 18px;
            color: #000;
            line-height: 30px;
        }

        .patient_info {
            border-bottom: 1px solid #eee;
        }

        .patient_btn_1 {
            width: 80px;
            padding-top: 5px;
            text-align: center;
        }

            .patient_btn_1 .layui-btn {
                width: 100%;
                margin-left: 0;
                margin-bottom: 5px;
            }



        .patient_btn_2 {
            padding-top: 10px;
            text-align: center;
            width: 100%;
        }

            .patient_btn_2 .layui-btn {
                width: 30%;
            }

        .layui-border-blue {
            border-color: #1E9FFF !important;
            color: #1E9FFF !important;
        }

        .patient_list {
            overflow-y: auto;
        }

        .page_size {
            width: 100px;
            height: 36px;
            align-items: center;
            justify-content: center;
        }

        .pagebtn {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .sex {
            font-size: 14px;
            font-weight: normal;
        }

        .sex0 {
            color: #FF5722;
        }

        .sex1 {
            color: #1E9FFF;
        }

        .progress_title {
            padding-left: 10px;
        }

        .layui-form-label {
            padding: 9px 0;
        }

        .patient_btn .layui-btn {
            border-radius: 15px;
        }

        .user_name .layui-btn {
            border-radius: 15px;
            margin-left: 15px;
        }

        /* 弹窗样式 */
        .add_patient {
            padding: 10px 15px;
        }
    </style>

</head>
<body style="padding:5px;">

    <div class="wrap">

        <div class="head_wrap screening" id="IpadHead">
            <div class="headrow">
                <div class="left">
                    <button id="previous" type="button" class="layui-btn layui-btn-primary"><i class="layui-icon layui-icon-return"></i></button>
                </div>
                <div class="middle">
                </div>
                <div class="right">
                    <button class="layui-btn layui-btn-primary" id="addBtn"><i class="layui-icon layui-icon-addition"></i></button>
                </div>
            </div>
            <div class="search_wrap">
                <div class="layui-inline" style="width:235px">

                    <div id="xmDeptsList" xm-select="xmDeptsList"></div>

                </div>
                <div class="layui-inline" style="width:240px">

                    <div id="xmDeptsList3"></div>

                </div>
                <div class="layui-inline" style="width:180px">
                    <div id="xmCentersList"></div>

                </div>
                <div class="layui-input-inline" style="margin-left: 10px; width: 22vw;">
                    <input type="text" class="layui-input" name="keyWord" placeholder="请输入科研编号/姓名" id="keyWord" />
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-primary" id="Search2">
                        <i class="layui-icon layui-icon-search"></i>
                    </button>
                </div>
            </div>
        </div>



@*         <div class="layui-form screening">
            <div class="layui-card">
                <div class="layui-card-body ">
                    <button id="previous" class="layui-btn layui-btn-primary layui-border-green" style="margin-right:15px;display:none"><i class="layui-icon layui-icon-return"></i> 返回</button>
                    <div class="layui-inline" style="width:25vw;">
                        <div class="" style=" margin-left: 5px; min-height: 36px;">
                            <div id="xmDeptsList"></div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-input-inline" style="width: 22vw; margin-right: 26px;">
                            <input type="text" class="layui-input" name="keyWord" placeholder="请输入科研编号/姓名" id="keyWord" />
                        </div>

                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" id="Search2">查询</button>
                        <button class="layui-btn layui-btn-normal" id="addBtn">新增患者</button>
                    </div>
                </div>
            </div>
        </div> *@




        <div class="patient_wrap layui-card">
            <div class="patient_list layui-card-body layui-row " id="PatientList">
                <!--科研患者卡片-->
            </div>
        </div>

        <!--翻页按钮-->
        <div class="pagebtn">
            <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" id="previousPage" style="display:none">
                <i class="layui-icon layui-icon-left"></i>
                上一页
            </button>
            <div class="page_size flex-row">
                <p id="pageSize">1</p>/
                <p id="pageCount">1</p>
            </div>
            <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" id="nextPage">
                下一页
                <i class="layui-icon layui-icon-right"></i>
            </button>
        </div>


    </div>


    <!--新增患者弹窗-->
    <div id="addPatient" style="display:none;">
        <div class="add_patient ">
            <form class="layui-form" lay-filter="fm" id="fm" action="">
                <div class="layui-form-item">
                    <label class="layui-form-label">医学中心</label><span style="color: red;">*</span>
                    <div class="layui-input-block">
                        <div id="xmDeptsList2"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">专病组</label><span style="color: red;">*</span>
                    <div class="layui-input-block">
                        <div id="xmDeptsList4"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">身份证号</label><span style="color: red;">*</span>
                    <div class="layui-input-block">
                        <input type="text" name="IDCardNo" id="IDCardNo" placeholder="请输入" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">出生日期</label><span style="color: red;">*</span>
                    <div class="layui-input-block">
                        <input name="BrithDay" id="birthday" placeholder="选择日期" type="text" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">受试年龄</label><span style="color: red;">*</span>
                    <div class="layui-input-block">
                        <input type="text" name="ParticipatAge" id="participatAge" placeholder="自动计算" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">科研编号</label><span style="color: red;">*</span>
                    <div class="layui-input-block">
                        <input type="text" name="Id" id="Id" style="display:none;" />
                        <input type="text" name="ResearchID" id="ResearchID" placeholder="请输入" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">患者ID</label>
                    <div class="layui-input-block">
                        <input type="text" name="HisPatientId" id="HisPatientId" placeholder="请输入" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">住院号</label>
                    <div class="layui-input-block">
                        <input type="text" name="BLH" id="BLH" placeholder="请输入" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">卡号</label>
                    <div class="layui-input-block">
                        <input type="text" name="BRKH" id="BRKH" placeholder="请输入" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">患者姓名</label><span style="color: red;">*</span>
                    <div class="layui-input-block">
                        <input type="text" name="PatientName" id="PatientName" placeholder="请输入" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">患者来源</label>
                    <div class="layui-input-block">
                        <input type="radio" name="HisPatientSource" value="O" title="门诊">
                        <input type="radio" name="HisPatientSource" value="I" title="住院">
                        <input type="radio" name="HisPatientSource" value="E" title="急诊">
                        <input type="radio" name="HisPatientSource" value="P" title="体检">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">性别</label>
                    <div class="layui-input-block">
                        <input type="radio" name="Sex" value="1" title="男">
                        <input type="radio" name="Sex" value="0" title="女">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">入组日期</label><span style="color: red;">*</span>
                    <div class="layui-input-block">
                        <input type="text" name="JoinDate" id="joinDate" placeholder="选择日期" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">联系方式</label>
                    <div class="layui-input-block">
                        <input type="text" name="Telephone" id="Telephone" placeholder="请输入" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button type="button" class="layui-btn" lay-submit lay-filter="submit">提交</button>
                        <button type="reset" class="layui-btn layui-btn-primary" id="btn_reset">重置</button>
                    </div>
                </div>
            </form>
        </div>

    </div>


    <script src="~/layuiadmin/layui/layui.js"></script>
    <script src="~/layuiadmin/layuiextend/xm-select.js"></script>
    @*<script src="~/js/jquery-3.5.1.min.js"></script>*@

<script>
    layui.use(['element', 'layer', 'table', 'laydate', 'form'], function () {
        var element = layui.element
            , layer = layui.layer
            , table = layui.table//表格
            , laydate = layui.laydate
            , $ = layui.$
            , form = layui.form;
        var page = 1;
        var pageCount = 1;
        var url = "";
        var isPC;

        if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {

            $("#previous").show();
            isPC = false;

        }
        else {
            $("#previous").hide();
            isPC = true;
        }

        $("#previous").click(function () {
            window.location.href = "/Home/ipadIndex";
        })
        // 初始化日期选择器
        laydate.render({
            elem: '#birthday' // 绑定元素
            , type: 'date'
            , format: 'yyyy-MM-dd' // 设置日期格式
            , done: function (value, date) {
                // done 事件在日期被选中后触发
                calculateAge(date.year, date.month, date.date);
            }
        });
        // 初始化日期选择器
        laydate.render({
            elem: '#joinDate' // 绑定元素
            , type: 'date'
            , format: 'yyyy-MM-dd' // 设置日期格式
        });

        // 监听身份证号码输入框的变化
        $('#IDCardNo').on('input propertychange', function () {
            var idCard = $(this).val();
            var birthDate;

            // 检查身份证号码长度是否正确
            if (idCard.length === 18) {
                // 提取出生日期部分
                var birthStr = idCard.substr(6, 8);

                // 尝试将字符串转换为日期
                if (/^\d{4}(0[1-9]|1[012])(0[1-9]|[12][0-9]|3[01])$/.test(birthStr)) {
                    birthDate = new Date(birthStr.slice(0, 4), parseInt(birthStr.slice(4, 6)) - 1, birthStr.slice(6, 8));
                    // 格式化日期为 YYYY-MM-DD
                    //var formattedDate = birthDate.toISOString().substring(0, 10);
                    // 手动格式化日期为 YYYY-MM-DD
                    var year = birthDate.getFullYear();
                    var month = ('0' + (birthDate.getMonth() + 1)).slice(-2); // 补零
                    var day = ('0' + birthDate.getDate()).slice(-2); // 补零
                    var formattedDate = year + '-' + month + '-' + day;

                    $.ajax({
                        url: "/ReportingManage/PatientManage/AIExtractByIDCard",
                        type: "post",
                        data: { "IDCard": idCard },
                        success: function (res) {
                            if (res.code == 0) {
                                $('#HisPatientId').val(res.data[0].patientId);
                                $('#PatientName').val(res.data[0].patientName);
                                $('#Telephone').val(res.data[0].telephone);
                                $('[name="HisPatientSource"][value="' + res.data[0].patientSource + '"]').prop('checked', true);
                                form.render(); // 更新表单视图

                            } else {

                            }
                        }
                    });
                    // 设置出生日期输入框的值
                    $('#birthday').val(formattedDate);
                    calculateAge(birthStr.slice(0, 4), parseInt(birthStr.slice(4, 6)) - 1, birthStr.slice(6, 8))
                    form.render(); // 更新表单视图
                } else {
                    alert('身份证号码中的日期部分无效！');
                }
            }
        });

        // 监听住院号输入框的变化
        $('#BLH').on('input propertychange', function () {
            var blh = $(this).val();
            var name = $('#PatientName').val();
            var birthDate;

            // 检查身份证号码长度是否正确
            if (blh != "" && name != "") {
                $.ajax({
                    url: "/ReportingManage/PatientManage/GetPatientInfoByInpNo",
                    type: "post",
                    data: { "blh": blh, "name": name },
                    success: function (res) {
                        if (res.code == 0) {
                            $('#HisPatientId').val(res.data[0].patientId);
                            $('#PatientName').val(res.data[0].patientName);
                            $('#Telephone').val(res.data[0].telephone);
                            $('#IDCardNo').val(res.data[0].iDCardNo);
                            var idCard = res.data[0].iDCardNo;
                            if (idCard.length === 18) {
                                var birthStr = idCard.substr(6, 8);
                                if (/^\d{4}(0[1-9]|1[012])(0[1-9]|[12][0-9]|3[01])$/.test(birthStr)) {
                                    birthDate = new Date(birthStr.slice(0, 4), parseInt(birthStr.slice(4, 6)) - 1, birthStr.slice(6, 8));
                                    // 格式化日期为 YYYY-MM-DD
                                    //var formattedDate = birthDate.toISOString().substring(0, 10);
                                    // 手动格式化日期为 YYYY-MM-DD
                                    var year = birthDate.getFullYear();
                                    var month = ('0' + (birthDate.getMonth() + 1)).slice(-2); // 补零
                                    var day = ('0' + birthDate.getDate()).slice(-2); // 补零
                                    var formattedDate = year + '-' + month + '-' + day;
                                    // 设置出生日期输入框的值
                                    $('#birthday').val(formattedDate);
                                    calculateAge(birthStr.slice(0, 4), parseInt(birthStr.slice(4, 6)) - 1, birthStr.slice(6, 8))
                                }
                            }
                            
                            $('[name="HisPatientSource"][value="' + res.data[0].patientSource + '"]').prop('checked', true);
                            form.render(); // 更新表单视图

                        } else {

                        }
                    }
                });
            }
        });
        // 监听患者姓名输入框的变化
        $('#PatientName').on('input propertychange', function () {
            var name = $(this).val();
            var blh = $('#BLH').val();
            var birthDate;

            // 检查身份证号码长度是否正确
            if (blh != "" && name != "") {
                $.ajax({
                    url: "/ReportingManage/PatientManage/GetPatientInfoByInpNo",
                    type: "post",
                    data: { "blh": blh, "name": name },
                    success: function (res) {
                        if (res.code == 0) {
                            $('#HisPatientId').val(res.data[0].patientId);
                            $('#PatientName').val(res.data[0].patientName);
                            $('#Telephone').val(res.data[0].telephone);
                            $('#IDCardNo').val(res.data[0].iDCardNo);
                            var idCard = res.data[0].iDCardNo;
                            if (idCard.length === 18) {
                                var birthStr = idCard.substr(6, 8);
                                if (/^\d{4}(0[1-9]|1[012])(0[1-9]|[12][0-9]|3[01])$/.test(birthStr)) {
                                    birthDate = new Date(birthStr.slice(0, 4), parseInt(birthStr.slice(4, 6)) - 1, birthStr.slice(6, 8));
                                    // 格式化日期为 YYYY-MM-DD
                                    //var formattedDate = birthDate.toISOString().substring(0, 10);
                                    // 手动格式化日期为 YYYY-MM-DD
                                    var year = birthDate.getFullYear();
                                    var month = ('0' + (birthDate.getMonth() + 1)).slice(-2); // 补零
                                    var day = ('0' + birthDate.getDate()).slice(-2); // 补零
                                    var formattedDate = year + '-' + month + '-' + day;
                                    // 设置出生日期输入框的值
                                    $('#birthday').val(formattedDate);
                                    calculateAge(birthStr.slice(0, 4), parseInt(birthStr.slice(4, 6)) - 1, birthStr.slice(6, 8))
                                }
                            }

                            $('[name="HisPatientSource"][value="' + res.data[0].patientSource + '"]').prop('checked', true);
                            form.render(); // 更新表单视图

                        } else {

                        }
                    }
                });
            }
        });
        //患者列表的高度
        function setPatientListH() {
            var winH = $(window).height();
            var screeH = $(".screening").height();
            var pagebtnH = $(".pagebtn").height();
            var listH = winH - (screeH + pagebtnH) - 50 + "px";
            $(".patient_list").css("height", listH);
        }
        setPatientListH();

        $(window).resize(function () {
            setPatientListH();
        });
        $(document).ready(function () {
            GetDeptsTree();
            GetCentersTree();
            // 初始判断横竖屏
            checkOrientation();
            //ResearchPatList(page);
        });



        // 添加监听器，当屏幕方向改变时触发
        window.addEventListener("orientationchange", checkOrientation);



        function calculateAge(year, month, day) {
            var now = new Date();
            var birthDate = new Date(year, month - 1, day); // 注意月份是从0开始的
            var age = now.getFullYear() - birthDate.getFullYear();

            // 如果当前月份小于出生月份，或者当前月份等于出生月份但当前日小于出生日，则年龄减一
            if (now.getMonth() < birthDate.getMonth() ||
                (now.getMonth() === birthDate.getMonth() && now.getDate() < birthDate.getDate())) {
                age--;
            }

            $('#participatAge').val(age);
        }


        function checkOrientation() {
            var orientation = window.orientation;
            if (orientation === 0 || orientation === 180) {
                // 竖屏状态
                $(".patient_item").parent().removeClass().addClass("layui-col-xs12 layui-col-sm12 layui-col-md12");
            } else if (orientation === 90 || orientation === -90) {
                // 横屏状态
                $(".patient_item").parent().removeClass().addClass("layui-col-xs6 layui-col-sm6 layui-col-md6");
                $(".layui-input-inline").css("width", "200px");
            }
        }

        function ResearchPatList(page) {
            var indexs = layer.load();
            var hospitalDeptId = xmDeptsList.getValue('valueStr');
            var groupid = xmDeptsList3.getValue('valueStr');
            var centerId = xmCentersList.getValue('valueStr');
            if ($.trim(hospitalDeptId) == "") {
                layer.alert('请选择科研机构!');
                layer.close(indexs);
                return;
            }
            $.ajax({
                url: "/ReportingManage/PatientManage/ResearchPatList",
                type: "post",
                data: { "page": page, "limit": 8, "hospitalDeptId": hospitalDeptId, "diseaseSpecificGroupId": groupid, "keyWords": $("#keyWord").val(), "centerId": centerId },
                success: function (res) {
                    if (res.code == 0) {
                        GetList(res);
                    } else {

                    }
                    layer.close(indexs);
                }
            });
        }

        function GetList(res) {
            var PatientItem = '';
            $("#PatientList").html("");
            pageCount = Math.ceil(res.count / 8);
            if (pageCount == 1) {
                // $("#nextPage").css("opacity", "0")
                $("#nextPage").css("display", "none")
            }
            $("#pageCount").html(pageCount);
            $("#pageSize").html(page);

            for (var i = 0; i < res.data.length; i++) {
                var gender = res.data[i].Sex == '男' ? '1' : '0'
                PatientItem += '<div class="layui-col-xs12 layui-col-sm12 layui-col-md6">'
                    + '<div class="patient_item"><div class="patient_info flex-row">'
                    + '<div class="info_left"><div class="patient_content">'
                    + '<div class="user_name">' + res.data[i].PatientName + '  <span class="sex sex' + gender + '">' + res.data[i].Sex + '</span>'
                    + '<button type="button" class="layui-btn layui-btn-sm toUpdate" '
                    + 'Id = "' + res.data[i].Id + '" PatientName = "' + res.data[i].PatientName + '" '
                    + 'Sex = "' + res.data[i].Sex + '" ResearchID = "' + res.data[i].ResearchID + '" '
                    + 'Telephone = "' + res.data[i].Telephone + '" IDCardNo = "' + res.data[i].IDCardNo + '" '
                    + 'HisPatientId = "' + res.data[i].HisPatientId + '" HisPatientSource = "' + res.data[i].HisPatientSource + '" '
                    + 'ParticipatAge = "' + res.data[i].ParticipatAge + '" BrithDay = "' + res.data[i].BrithDay + '" '
                    + 'GroupId = "' + res.data[i].GroupId + '" JoinDate = "' + res.data[i].JoinDate + '" '
                    + 'BLH = "' + res.data[i].BLH + '" BRKH = "' + res.data[i].BRKH + '" '
                    + 'HospitalDeptId = "' + res.data[i].HospitalDeptId + '" ><i class="layui-icon layui-icon-edit "></i></button > '
                    + '<button type="button" class="layui-btn layui-bg-blue layui-btn-sm toSee" ResearchID="' + res.data[i].Id + '"><i class="layui-icon icon-shuyi_yanjing-xianshi"></i></button >'
                    + '</div>'
                    + '<div class="content-inline">编号：<p class="user_value">' + res.data[i].ResearchID + '</p></div>'
                    + '<div class="content-inline" style="justify-content: space-between;padding-right: 15px;">'
                    + '<div class="content-inline">所属中心：<p class="user_value">' + res.data[i].CenterName + '</p></div>'
                    + '<div class="content-inline">专病组：<p class="user_value">' + res.data[i].GroupName + '</p></div>'
                    + '</div>'
                    + '</div></div><div class="info_right"><div class="patient_btn  patient_btn_1">'
                    + '<button type="button" class="layui-btn layui-btn-sm layui-btn-normal toDataCollect" ResearchID="' + res.data[i].Id + '" DeptName="' + res.data[i].DeptName + '">数据采集</button>'



                    + '<button type="button" class="layui-btn layui-btn-danger layui-btn-sm toDelete" ResearchID="' + res.data[i].Id + '"><i class="layui-icon layui-icon-delete"></i></button>'
                    + '</div></div></div><div class="progress"><p class="progress_title">填写进度</p>'
                    + '<div class="layui-progress layui-progress-big" lay-showPercent="true" lay-filter="progress_update">'
                    + '<div class="layui-progress-bar layui-bg-orange" lay-percent="' + res.data[i].Progress + '"></div>'
                    + '</div></div></div></div>';
            }
            $("#PatientList").append(PatientItem);
            form.render();

            // 渲染进度条组件
            element.render('progress', 'progress_update');
            $(".toDataCollect").on("click", function () {
                var pId = $(this).attr("ResearchID");
                var DeptName = $(this).attr("DeptName");

                if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {

                    if (DeptName == "康复医学中心") {
                        url = "/ReportingManage/RehabilitationDept/Index?ResearchID=" + pId;
                    }
                    else {
                        url = "/ReportingManage/PatientManage/DataCollect?ResearchID=" + pId;
                    }
                    window.location.href = url;
                    //  parent.layui.index.openTabsPage(url, "数据采集");
                }
                else {
                    if (DeptName == "康复医学中心" || DeptName == "中医科") {
                        url = "/ReportingManage/RehabilitationDept/Index?ResearchID=" + pId;
                    }
                    else
                        url = "/ReportingManage/PatientManage/DataCollecPC?ResearchID=" + pId;
                    //  window.location.href = url;
                    parent.layui.index.openTabsPage(url, "数据采集");
                }
            })

            $(".toSee").on("click", function () {
                var pId = $(this).attr("ResearchID");
                if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
                    url = "/Demo/CRFormTraceabilityNew/Index?ResearchPatientID=" + pId;
                    window.location.href = url;
                    //  parent.layui.index.openTabsPage(url, "数据采集");
                }
                else {
                    url = "/Demo/CRFormTraceabilityNew/Index?ResearchPatientID=" + pId;
                    //  window.location.href = url;
                    parent.layui.index.openTabsPage(url, "数据溯源");
                }
            })

            //$(".toDelete").on("click", function () {
            //    var indexs = layer.load(1);
            //    var pId = $(this).attr("ResearchID");
            //    url = "/ReportingManage/PatientManage/Del";
            //    //提交 Ajax 成功后，关闭当前弹层并重载表格
            //    $.ajax({
            //        url: url,
            //        type: "post",
            //        data: { 'id': pId },
            //        datatype: 'json',
            //        success: function (result) {
            //            if (result.okMsg) {
            //                layer.msg(result.okMsg);
            //                ResearchPatList(page);
            //            }
            //            else {
            //                layer.msg(result.errorMsg);
            //            }
            //            layer.close(indexs);
            //        }, error: function (res) {
            //            layer.msg("删除患者信息错误：" + res.responseText);
            //            layer.close(indexs);
            //        }
            //    });
            //    return false;

            //})
            $(".toDelete").on("click", function () {
                var pId = $(this).attr("ResearchID");

                // 添加确认提示框
                layer.confirm('确认删除该患者信息吗？', {
                    btn: ['确定', '取消'], // 按钮
                    icon: 3, // 问号图标
                    title: '删除确认'
                }, function (index) {
                    // 用户点击“确定”后执行删除操作
                    var indexs = layer.load(1); // 显示加载动画
                    var url = "/ReportingManage/PatientManage/Del";

                    $.ajax({
                        url: url,
                        type: "post",
                        data: { 'id': pId },
                        dataType: 'json', // 注意这里应该是 dataType 而不是 datatype
                        success: function (result) {
                            if (result.okMsg) {
                                layer.msg(result.okMsg);
                                ResearchPatList(page); // 刷新表格数据
                            } else {
                                layer.msg(result.errorMsg);
                            }
                            layer.close(indexs);
                        },
                        error: function (res) {
                            layer.msg("删除患者信息错误：" + res.responseText);
                            layer.close(indexs);
                        }
                    });

                    layer.close(index); // 关闭 confirm 弹窗
                }, function (index) {
                    // 用户点击“取消”不做任何事
                    layer.close(index); // 关闭 confirm 弹窗
                });

                return false;
            });
            $(".toUpdate").on("click", function () {
                var hDepid = $(this).attr("HospitalDeptId");
                var groupid = $(this).attr("GroupId");
                var sex = $(this).attr("Sex") == "男" ? "1" : "0";
                var source = $(this).attr("HisPatientSource");
                $('#Id').val($(this).attr("Id"));
                $('#ResearchID').val($(this).attr("ResearchID"));
                $('#HisPatientId').val($(this).attr("HisPatientId"));
                $('#PatientName').val($(this).attr("PatientName"));
                $('#Telephone').val($(this).attr("Telephone"));
                $('#IDCardNo').val($(this).attr("IDCardNo"));
                $('#BLH').val($(this).attr("BLH"));
                $('#BRKH').val($(this).attr("BRKH"));
                var joindate = $(this).attr("JoinDate");
                var brithDay = $(this).attr("BrithDay");

                var date = new Date(brithDay);
                var year = date.getFullYear();
                var month = ("0" + (date.getMonth() + 1)).slice(-2); // 加1是因为月份是从0开始的
                var day = ("0" + date.getDate()).slice(-2);
                var formattedDate = year + "-" + month + "-" + day;

                var jdate = new Date(joindate);

                var jyear = jdate.getFullYear();
                var jmonth = ("0" + (jdate.getMonth() + 1)).slice(-2);
                var jday = ("0" + jdate.getDate()).slice(-2);
                var jformattedDate = jyear + "-" + jmonth + "-" + jday;


                $('#joinDate').val(jformattedDate);
                $('#birthday').val(formattedDate);
                $('#participatAge').val($(this).attr("ParticipatAge"));
                $('[name="HisPatientSource"][value="' + source + '"]').prop('checked', true);
                $('[name="Sex"][value="' + sex + '"]').prop('checked', true);
                if (hDepid != undefined) {
                    xmDeptsList2.setValue([hDepid]);
                }
                if (groupid != undefined) {
                    xmDeptsList4.setValue([groupid]);
                }
                form.render();
                windowsIndex = layer.open({
                    type: 1,
                    title: '修改科研患者信息',
                    area: ['75%', '90%'],
                    resize: true,
                    content: $('#addPatient')
                });
                url = '/ReportingManage/PatientManage/Edit';
            })
            checkOrientation();
        }

        var xmDeptsList = xmSelect.render({
            el: '#xmDeptsList',
            tips: '请选择科研组织',
            model: { label: { type: 'text' } },
            prop: {
                name: 'title',
                value: 'id',
            },
            minWidth: 200,
            radio: true,
            filterable: true,
            clickClose: true,
            //树
            tree: {
                show: true,
                //非严格模式
                strict: false,
                //默认展开节点
                expandedKeys: [-1],
            },
            data: [],
            // 添加 onChange 事件处理器
            on: function (val) {
                if (val.arr && val.arr.length > 0) {
                    GetGroupsTree(val.arr[0].id);
                }
            }
        });

        var xmDeptsList2 = xmSelect.render({
            el: '#xmDeptsList2',
            tips: '请选择科研组织',
            model: { label: { type: 'text' } },
            prop: {
                name: 'title',
                value: 'id',
            },
            minWidth: 200,
            radio: true,
            filterable: true,
            clickClose: true,
            //树
            tree: {
                show: true,
                //非严格模式
                strict: false,
                //默认展开节点
                expandedKeys: [-1],
            },
            data: [],
            on: function (val) {
                GetGroupsTree(val.arr[0].id);
            }
        });

        var xmDeptsList3 = xmSelect.render({
            el: '#xmDeptsList3',
            tips: '请选择专病',
            model: { label: { type: 'text' } },
            prop: {
                name: 'title',
                value: 'id',
            },
            minWidth: 200,
            radio: false,
            filterable: true,
            clickClose: true,
            //树
            tree: {
                show: true,
                //非严格模式
                strict: false,
                //默认展开节点
                expandedKeys: [-1],
            },
            data: []
        });

        var xmDeptsList4 = xmSelect.render({
            el: '#xmDeptsList4',
            tips: '请选择专病',
            model: { label: { type: 'text' } },
            prop: {
                name: 'title',
                value: 'id',
            },
            minWidth: 200,
            radio: true,
            filterable: true,
            clickClose: true,
            //树
            tree: {
                show: true,
                //非严格模式
                strict: false,
                //默认展开节点
                expandedKeys: [-1],
            },
            data: []
        });

        var xmCentersList = xmSelect.render({
            el: '#xmCentersList',
            tips: ' 请选择所属中心',
            model: { label: { type: 'text' } },
            prop: {
                name: 'title',
                value: 'id',
            },
            minWidth: 200,
            radio: true,
            filterable: true,
            clickClose: true,
            //树
            tree: {
                show: true,
                //非严格模式
                strict: false,
                //默认展开节点
                expandedKeys: [-1],
            },
            data: []
        });

        function GetDeptsTree() {
            $.ajax({
                url: '/CommAPI/GetOrgsTreeList',
                type: "post",
                datatype: 'json',
                success: function (result) {
                    xmDeptsList.update({
                        data: result
                    });
                    if (result[0].id) {
                        var arr = new Array();
                        arr.push(result[0].id);
                        GetGroupsTree(result[0].id);
                        xmDeptsList.setValue(arr);
                        setTimeout(function () {
                            ResearchPatList(page);
                        }, 1000);
                    }
                    //xmDeptsList.setValue([0]);

                    xmDeptsList2.update({
                        data: result
                    });

                }, error: function () {
                    layer.msg("获取失败！");
                }
            })
        };

        function GetGroupsTree(val) {
            $.ajax({
                url: '/CommAPI/GetGroupTreeList?hospitalDeptId=' + val,
                type: "post",
                datatype: 'json',
                success: function (result) {
                    xmDeptsList3.update({
                        data: result
                    });
                    xmDeptsList4.update({
                        data: result
                    });
                }, error: function () {
                    layer.msg("获取失败！");
                }
            })
        };

        function GetCentersTree(value) {
            $.ajax({
                url: '/CommAPI/GetCentersListByUser',
                type: "post",
                datatype: 'json',
                data: { 'Type': value },
                success: function (result) {
                    xmCentersList.update({
                        data: result
                    });
                    if (result[0].id) {
                        var arr = new Array();
                        arr.push(result[0].id);
                        setTimeout(function () {
                        }, 1000);

                    }

                }, error: function () {
                    layer.msg("获取失败！");
                }
            })
        };

        $('#xmDeptsList').on('change', function () {
            var value = $(this).val(); // 获取当前选中的值
            console.log('选中的值：', value);
            // 根据值进行其他操作
        });

        //新增患者弹窗
        $("#addBtn").on("click", function () {
            var hospitalDeptId = xmDeptsList.getValue('valueStr');
            var GroupId = xmDeptsList3.getValue('valueStr');

            if (hospitalDeptId != undefined) {
                xmDeptsList2.setValue([hospitalDeptId]);
                GetGroupsTree(hospitalDeptId);
            }
            if (GroupId != undefined) {
                xmDeptsList4.setValue([GroupId]);
            }
            $("#fm")[0].reset();
            $("#Id").val("0");
            windowsIndex = layer.open({
                type: 1,
                title: '新增科研患者',
                area: ['75%', '90%'],
                resize: true,
                content: $('#addPatient')
            });
            url = '/ReportingManage/PatientManage/SavePatient';
        });

        $("#Search2").on("click", function () {
            page = 1;
            ResearchPatList(page);
        });

        // 监听表单提交
        form.on('submit(submit)', function (data) {
            var hospitalDeptId = xmDeptsList2.getValue('valueStr');
            var groupId = xmDeptsList4.getValue('valueStr');
            var joindate = data.field.JoinDate;
            var IDCardNo = data.field.IDCardNo;
            var birthday = data.field.BirthDay;
            var age = data.field.ParticipatAge;
            var researchID = data.field.ResearchID;
            var patientName = data.field.PatientName;

            if (groupId == '') { layer.msg("专病组不能为空！"); return false; }
            if (IDCardNo == '') { layer.msg("身份证号不能为空！"); return false; }
            if (birthday == '') { layer.msg("出生日期不能为空！"); return false; }
            if (age == '') { layer.msg("受试年龄不能为空！"); return false; }
            if (researchID == '') { layer.msg("科研编号不能为空！"); return false; }
            if (patientName == '') { layer.msg("患者姓名不能为空！"); return false; }
            if (joindate == '') { layer.msg("入组日期不能为空！"); return false; }

            data.field.HospitalDeptId = hospitalDeptId;
            data.field.DiseaseSpecificGroupId = groupId;
            data.field.Sex = data.field.Sex == "1" ? "男" : "女";
            $.ajax({
                url: url,
                type: "post",
                data: { 'node': data.field },
                datatype: 'json',
                success: function (data) {
                    if (data.okMsg) {
                        layer.msg(data.okMsg);
                        layer.close(windowsIndex);//关闭弹出层
                        ResearchPatList(page);

                    }
                    else {
                        layer.msg(data.errorMsg);
                    }
                }, error: function (res) {
                    layer.msg("加载统计信息错误：" + res.responseText);
                }
            });
            return false; // 阻止默认提交行为
        });

        $("#previousPage").on("click", function () {
            if (page >= 2) {
                page--;
                // $("#nextPage").css("opacity", "1")
                $("#nextPage").css("display", "block")
                ResearchPatList(page);
                if (page == 1) {
                    //$("#previousPage").css("opacity", "0")
                    $("#previousPage").css("display", "none")
                }
            } else {
                // $("#nextPage").css("opacity", "1")
                $("#nextPage").css("display", "block")
            }
        });

        $("#nextPage").on("click", function () {
            if (page <= pageCount) {
                page++;
                ResearchPatList(page);
                // $("#previousPage").css("opacity", "1")
                $("#previousPage").css("display", "block")
                if (page == pageCount) {
                    //$("#nextPage").css("opacity", "0")
                    $("#nextPage").css("display", "none")
                }
            }
            else {
                //$("#nextPage").css("opacity", "0")
                $("#nextPage").css("display", "none")
            }
        });

    })

</script>



</body>
</html>