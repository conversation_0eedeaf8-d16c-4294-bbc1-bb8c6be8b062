#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\knowledgeBase\knowledgeBase.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "e26bac9b422f1a3301308b7c680995551c36303aa09b83109f2a4b898a73dd27"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_knowledgeBase_knowledgeBase), @"mvc.1.0.view", @"/Views/knowledgeBase/knowledgeBase.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI

#nullable disable
    ;
#nullable restore
#line 2 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"e26bac9b422f1a3301308b7c680995551c36303aa09b83109f2a4b898a73dd27", @"/Views/knowledgeBase/knowledgeBase.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"a5bcd83cf27c8ffb8baf597d24314352bf7b52b5a9bb5ee83e83e9d0089a628e", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_knowledgeBase_knowledgeBase : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/knowledgeBase_style.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "ChatGLM3-6B-32k", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "Llama3-8B", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", " Qwen1.5-7B-Chat", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("layui-form"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("layui-layout-body"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_13 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\knowledgeBase\knowledgeBase.cshtml"
  
    ViewBag.Title = "知识库管理";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e26bac9b422f1a3301308b7c680995551c36303aa09b83109f2a4b898a73dd279194", async() => {
                WriteLiteral("\r\n    <meta charset=\"utf-8\">\r\n    <title> ");
                Write(
#nullable restore
#line 9 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\knowledgeBase\knowledgeBase.cshtml"
             ViewBag.SiteTitle

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</title>
    <meta name=""renderer"" content=""webkit"">
    <meta http-equiv=""X-UA-Compatible"" content=""IE=edge,chrome=1"">
    <meta name=""viewport""
          content=""width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0"">
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "e26bac9b422f1a3301308b7c680995551c36303aa09b83109f2a4b898a73dd2710092", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "e26bac9b422f1a3301308b7c680995551c36303aa09b83109f2a4b898a73dd2711295", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "e26bac9b422f1a3301308b7c680995551c36303aa09b83109f2a4b898a73dd2712498", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        body .demo-class .layui-layer-btn0 {
            color: #fff;
            background-color: #FF5722;
            border-color: #FF5722;
        }

        body .myskin .layui-layer-content {
            overflow: visible;
        }
    </style>

");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e26bac9b422f1a3301308b7c680995551c36303aa09b83109f2a4b898a73dd2714682", async() => {
                WriteLiteral(@"
    <div class=""layui-layout layui-layout-admin"">

        <div class=""layui-card"" style=""margin-bottom:5px;"">
            <div class=""line_wrap lay_t_head"">
                <div class=""T_title"">
                    <i class=""layui-icon layui-icon-template-1""></i>
                    <span>我的知识库</span>
                </div>
                <button class=""layui-btn layui-btn-primary"" id=""addBtn"" style=""margin-left:10px""><i class=""layui-icon layui-icon-addition""></i>新建知识库</button>
            </div>
        </div>


        <div class=""base_wrap"">
        </div>





        <!--创建知识库-->
        <div id=""addBase"" style=""display:none"">
            <div class=""set_base"" style=""padding:15px;"">
                ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e26bac9b422f1a3301308b7c680995551c36303aa09b83109f2a4b898a73dd2715732", async() => {
                    WriteLiteral(@"
                    <div class=""layui-form-item"">
                        <label class=""layui-form-label"">取个名字</label>
                        <div class=""layui-input-block"">
                            <input type=""text"" name=""Name"" placeholder=""请输入"" autocomplete=""off"" class=""layui-input"" lay-verify=""required"">
                        </div>
                    </div>
                    <div class=""layui-form-item"">
                        <label class=""layui-form-label"">所属分类</label>
                        <div class=""layui-input-block"">
                            <div id=""parentId"" class=""xm-select-demo""></div>
                        </div>
                    </div>
                    <div class=""layui-form-item"">
                        <label class=""layui-form-label"">索引模型</label>
                        <div class=""layui-input-block"">
                            <select name=""ModelName"" lay-verify=""required"">
                                ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e26bac9b422f1a3301308b7c680995551c36303aa09b83109f2a4b898a73dd2717041", async() => {
                    }
                    );
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                    __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_4.Value;
                    __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_4);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                                ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e26bac9b422f1a3301308b7c680995551c36303aa09b83109f2a4b898a73dd2718336", async() => {
                        WriteLiteral("ChatGLM3-6B-32k");
                    }
                    );
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                    __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_5.Value;
                    __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_5);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                                ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e26bac9b422f1a3301308b7c680995551c36303aa09b83109f2a4b898a73dd2719689", async() => {
                        WriteLiteral("Llama3-8B");
                    }
                    );
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                    __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_6.Value;
                    __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_6);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                                ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e26bac9b422f1a3301308b7c680995551c36303aa09b83109f2a4b898a73dd2721036", async() => {
                        WriteLiteral(" Qwen1.5-7B-Chat");
                    }
                    );
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                    __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_7.Value;
                    __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_7);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral(@"

                            </select>
                        </div>
                    </div>
                    <div class=""layui-form-item"">
                        <label class=""layui-form-label"">备注</label>
                        <div class=""layui-input-block"">
                            <textarea placeholder=""请输入内容"" name=""Remark"" class=""layui-textarea""></textarea>
                        </div>
                    </div>
                    <button type=""submit"" id=""addSubmit"" class=""layui-btn""");
                    BeginWriteAttribute("lay-submit", " lay-submit=\"", 3514, "\"", 3527, 0);
                    EndWriteAttribute();
                    WriteLiteral(" lay-filter=\"demo1\" style=\"display:none\">立即提交</button>\r\n                    <button type=\"reset\" id=\"addReset\" class=\"layui-btn layui-btn-primary\" style=\"display:none\">重置</button>\r\n                ");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n            </div>\r\n        </div>\r\n\r\n\r\n\r\n\r\n        <!--重命名-->\r\n        <div id=\"changeName\" style=\"display:none\">\r\n            <div class=\"set_name\" style=\"padding:15px;\">\r\n                ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e26bac9b422f1a3301308b7c680995551c36303aa09b83109f2a4b898a73dd2724615", async() => {
                    WriteLiteral("\r\n                    <input type=\"text\" name=\"reName\" id=\"reName\" placeholder=\"请输入\" autocomplete=\"off\" class=\"layui-input\">\r\n                ");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
            </div>
        </div>




        <!--删除警告-->
        <div id=""warning"" style=""display:none"">
            <div class=""set_warning"" style=""padding:15px;"">
                <i class=""layui-icon layui-icon-tips"" style=""font-size:40px;color:#FF5722;""></i>
                <p style=""padding-top: 10px;""> 确认删除该知识库?删除后数据无法恢复，请确认! </p>
            </div>
        </div>











    </div>

    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e26bac9b422f1a3301308b7c680995551c36303aa09b83109f2a4b898a73dd2726615", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e26bac9b422f1a3301308b7c680995551c36303aa09b83109f2a4b898a73dd2727739", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e26bac9b422f1a3301308b7c680995551c36303aa09b83109f2a4b898a73dd2728864", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
        layui.use(['element', 'layer', 'table', 'laydate', 'form'], function () {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , laydate = layui.laydate
                , $ = layui.$
                , form = layui.form;

            $(window).resize(function () {
                baseHeight();
            });

           
            baseHeight();
            function baseHeight() {
                var winH = $(window).height();
                var serH = $("".lay_t_head"").outerHeight(true);
                var contentH = winH - serH - 20;
                $("".base_wrap"").css(""height"", contentH + ""px"");
            }

            $('.base_wrap').on('mouseenter', '.more_bj', function () {
                $(this).siblings('.tool_menu_list').css('display', 'block');
            }).on('mouseleave', '.more_bj', function () {
                $(this).siblings('.tool_menu_list').css('display");
                WriteLiteral(@"', 'none');
            });

            $('.base_wrap').on('mouseenter', '.tool_menu_list', function () {
                $(this).css('display', 'block');
            }).on('mouseleave', '.tool_menu_list', function () {
                $(this).css('display', 'none');
            });
            var windowIndex;
            //弹窗事件
            $(""#addBtn"").on(""click"", function () {
                $(""#addReset"").click();
               windowIndex=   layer.open({
                    skin: ""myskin"",
                    type: 1,
                    title: '创建一个知识库',
                    content: $('#addBase'),
                    area: ['60%', '500px'],
                    btn: ['确认创建', '关闭'],
                    yes: function () {
                        $(""#addSubmit"").click();
                    },
                    success: function (layero, index) {

                    }
                });

            })

            $('.base_wrap').on('click', '.setname_btn', function (eve");
                WriteLiteral(@"nt) {
                event.stopPropagation();
                var id = $(this).parent().data(""id"");
                var name = $(this).parent().data(""name"");
                setName(id,name);
            })

            $('.base_wrap').on('click', '.del_btn', function (event) {
                event.stopPropagation();
                var id = $(this).parent().data(""id"");
                windowIndex = layer.open({
                    skin: 'demo-class',
                    type: 1,
                    title: '删除警告',
                    content: $('#warning'),
                    area: ['60%', '200px'],
                    btn: ['确认', '关闭'],
                    yes: function () {
                        $.post('/knowledgeBase/Del', { Id: id}, function (res) {
                            if (res.code == 0) {
                                layer.msg(res.msg);
                                layer.close(windowIndex);//关闭弹出层
                                loadWiki();
                      ");
                WriteLiteral(@"      }
                        })
                    }

                });
            })

            function setName(id,name) {
                $(""#reName"").val(name);
                //更改姓名弹窗
                windowIndex = layer.open({
                    type: 1,
                    title: '重命名',
                    area: ['60%', '200px'],
                    content: $('#changeName'),
                    btn: ['确认', '关闭'],
                    yes: function () {
                        $.post('/knowledgeBase/ReName', { Id: id, Name:$(""#reName"").val()}, function (res) {
                            if (res.code == 0) {
                                layer.msg(res.msg);
                                layer.close(windowIndex);//关闭弹出层
                                loadWiki();
                            }
                        })
                    }
                });
            }

            $("".base_item"").on(""click"", function () {
                //window.location.");
                WriteLiteral(@"href = './DS';
            })

            function loadWiki() {
                $("".base_wrap"").html("""");
                $.get('/knowledgeBase/getWikiList', function (res) {
                    if (res.code == 0) {

                        var htm = """";
                        if (res.data.length == 0) {
                            htm = ` <div class=""none_base"" >
                                <div class=""none_base_icon""><i class=""layui-icon layui-icon-release""></i></div>
                                <p>还没有知识库，快去创建一个吧！</p>
                            </div>`;
                        }
                        else {
                            $.each(res.data, function (index, item) {
                           
                                htm += `<div class=""layui-col-xs4 layui-col-sm4 layui-col-md4 "">
                                                  <div class=""base_item"" data-id='`+ item.Id + `' data-name='` + item.Name + `' data-type='` + item.ClassificationName +`'>
       ");
                WriteLiteral(@"                                     <div class=""item_top line_wrap"">
                                                <div class=""base_title"">
                                                    <img src=""/images/base_icon.png"" alt=""Alternate Text"" />
                                                    <span>`+ item.Name + `</span>
                                                </div>
                                                <div class=""tool_menu"">
                                                    <div class=""more_bj"">
                                                        <i class=""layui-icon layui-icon-more""></i>
                                                    </div>

                                                    <div class=""tool_menu_list"" data-id='`+item.Id+`' data-name='`+item.Name+`'>
                                                        <div class=""tool_menu_item setname_btn"">
                                                            <i class=""layui-icon layui-icon-");
                WriteLiteral(@"edit""></i>
                                                            <span>重命名</span>
                                                        </div>
                                                        <div class=""tool_menu_item del_btn"">
                                                            <i class=""layui-icon layui-icon-delete""></i>
                                                            <span>删除</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                                    </div>`+ (item.Count == 0 ? '<div class=""item_middle""><p class=""briefs"">这个知识库还没有介绍~</p></div>' : '<div class=""item_middle""><p class=""briefs""></p></div>') + `

                                            <div class=""item_bottom line_wrap"">

                                                <div class=""base_type"">
                            ");
                WriteLiteral(@"                        <i class=""layui-icon layui-icon-template-1""></i>
                                                            <span>`+ item.ClassificationName + `</span>
                                                </div>

                                                <div class=""Model_name"">
                                                    <i class=""layui-icon layui-icon-component""></i>
                                                            <span>`+ item.ModelName + `</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>`;
                            })
                            htm = ` <div class=""base_list"">
                                <div class=""layui-row layui-col-space10"">`+ htm + `</div></div>`;
                        }

                        $("".base_wrap"").append(htm);




                        $(docum");
                WriteLiteral(@"ent).on('click', '.base_item', function() {
                            var thisId = $(this).attr(""data-id""),
                                thisName = $(this).attr(""data-name""),
                                thisType = $(this).attr(""data-type"");

                            var paramValue = ""id="" + thisId + ""&name="" + thisName + ""&type="" + thisType;
                   
                            

                            parent.layui.index.openTabsPage('knowledgeBase/DS/?' + paramValue, ""知识库详情"");

                            //// 构建带参数的URL
                            //var url = ""/knowledgeBase/DS/?"" + paramValue;

                            //// 使用jQuery或JavaScript进行页面跳转
                            //window.location.href = url;



                        });

                    }

                })
            }

            loadWiki();


                form.on('submit(demo1)', function (data) {

                data.field.ClassificationId = parentTree.getValue('va");
                WriteLiteral(@"lueStr');
                    $.post('/knowledgeBase/Save', data.field, function (res) {
                        if (res.code == 0) {
                            layer.msg(res.msg);
                        layer.close(windowIndex);//关闭弹出层
                        loadWiki();
                        }
                    })
                    return false;
                });

            var parentTree = xmSelect.render({
                el: '#parentId',
                autoRow: true,
                radio: true,
                tree: {
                    show: true,
                    showFolderIcon: true,
                    showLine: true,
                    indent: 20,
                    //  expandedKeys: true,
                    //是否严格遵守父子模式
                    strict: true,
                },
                filterable: true,
                max: 8,

                maxMethod(seles, item) {
                    layer.msg(`${item.name}不能选了, 最多8个指标项！`)
                },
");
                WriteLiteral(@"                height: 'auto',
            })

            function loadXMselect() {

                $.get('/knowledgeBase/getClassification', function (res) {
                    if (res.code == 0) {

                        parentTree.update({
                            data: res.data
                            , autoRow: true
                            , expandedKeys: true
                        });
                    }

                })
            }
            loadXMselect();
        })
    </script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
