#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Role\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "e126949cf1e1096fac27f07bffd836f85b6b509ad8854a4ae08b7afe62c8ea7f"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_Role_Index), @"mvc.1.0.view", @"/Views/Role/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI

#nullable disable
    ;
#nullable restore
#line 2 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"e126949cf1e1096fac27f07bffd836f85b6b509ad8854a4ae08b7afe62c8ea7f", @"/Views/Role/Index.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"a5bcd83cf27c8ffb8baf597d24314352bf7b52b5a9bb5ee83e83e9d0089a628e", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_Role_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/jquery/dist/jquery.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("layui-form"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("lay-filter", new global::Microsoft.AspNetCore.Html.HtmlString("fm"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("fm"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("action", new global::Microsoft.AspNetCore.Html.HtmlString(""), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Role\Index.cshtml"
  
    ViewBag.Title = "角色管理";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"en\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e126949cf1e1096fac27f07bffd836f85b6b509ad8854a4ae08b7afe62c8ea7f8054", async() => {
                WriteLiteral(@"
    <meta charset=""UTF-8"">
    <meta name=""viewport""
          content=""width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"">
    <meta http-equiv=""X-UA-Compatible"" content=""ie=edge"">
    <title>角色管理</title>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "e126949cf1e1096fac27f07bffd836f85b6b509ad8854a4ae08b7afe62c8ea7f8604", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "e126949cf1e1096fac27f07bffd836f85b6b509ad8854a4ae08b7afe62c8ea7f9806", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e126949cf1e1096fac27f07bffd836f85b6b509ad8854a4ae08b7afe62c8ea7f11008", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e126949cf1e1096fac27f07bffd836f85b6b509ad8854a4ae08b7afe62c8ea7f12132", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e126949cf1e1096fac27f07bffd836f85b6b509ad8854a4ae08b7afe62c8ea7f13256", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        /*弹窗*/
        .window_wrap {
            padding: 15px;
        }

        userList_window .layui-form-label {
            width: 100px;
        }

        #form_window .layui-form-label {
            width: 100px;
        }

        #form_window .layui-input, .layui-textarea {
            width: 85%;
        }

        userList_window .layui-form-val {
            padding: 9px 15px;
        }


        userList_window .layui-form-item {
            margin-bottom: 0;
        }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e126949cf1e1096fac27f07bffd836f85b6b509ad8854a4ae08b7afe62c8ea7f15634", async() => {
                WriteLiteral(@"
    <div class=""layui-col-md12"">
        <div class=""layui-card"">
            <div class=""layui-card-body"">
                <div class=""layui-inline"">
                    <label class=""layui-form-label"">关键字:</label>
                    <div class=""layui-input-inline"" style=""width:300px;"">
                        <input type=""text"" class=""layui-input"" name=""KeyWords"" id=""KeyWords"" />
                    </div>
                </div>
                <div class=""layui-inline"">
                    <div class=""layui-input-block"">
                        <button class=""layui-btn layui-btn-normal fr"" id=""Search"">查 &nbsp;&nbsp;询</button>
                        <button class=""layui-btn"" id=""addRole"" data-type=""add"">新增</button>
                        <button class=""layui-btn"" id=""copyRole"" data-type=""add"">复制</button>
                    </div>
                </div>
            </div>
            <div class=""layui-card-body"">
                <table id=""tablelist"" lay-filter=""tablelist""></table>
 ");
                WriteLiteral(@"               <script type=""text/html"" id=""tableBar1"">
                    <a class=""layui-btn layui-btn-normal layui-btn-xs"" lay-event=""edit""><i class=""layui-icon layui-icon-edit""></i>编辑</a>
                    <a class=""layui-btn layui-btn-danger layui-btn-xs"" lay-event=""del""><i class=""layui-icon layui-icon-delete""></i>删除</a>
                    <a class=""layui-btn layui-btn-danger layui-btn-xs"" lay-event=""addmenu""><i class=""layui-icon layui-icon-set-fill""></i>菜单管理</a>
                    <a class=""layui-btn layui-btn layui-btn-xs"" lay-event=""userdetail""><i class=""layui-icon layui-icon-group""></i>查看用户</a>
                </script>
            </div>
        </div>
        <script>
            layui.use(['element', 'layer', 'table', 'tree', 'form'], function () {
                var element = layui.element
                    , layer = layui.layer
                    , table = layui.table//表格
                    , tree = layui.tree//树
                    , $ = layui.$
                    , fo");
                WriteLiteral(@"rm = layui.form;
                ;

                var windowsIndex;
                var url = '';
                var selectTableRowData = [];
                var currentIndex; 


                //记录该行数据是否限制数据库状态
                var rowDataBaseRange = [];
                //记录该行数据是否限制脚本状态
                var rowScriptGroupRange = [];
                //tablelist
                table.render({
                    elem: '#tablelist'
                    , id: 'tablelist'
                    , url: '/Role/RoleList'
                    , page: true
                    , limit: 20
                    , height: 'full-110'
                    , cols: [[
                        { field: 'zizeng', title: '', type: 'numbers', fixed: 'left' }
                        , { field: 'Name', title: '角色名称', fixed: 'left' }
                        , { field: 'Description', title: '角色描述' }
                        , { field: 'CreateRoleTime', title: '创建时间' }
                        , { fixed: 'right', ali");
                WriteLiteral(@"gn: 'center', title: '操作', minWidth: 350, toolbar: '#tableBar1' }
                    ]]
                    , done: function (res, curr, count) {
                        $('.layui-table-view[lay-id=""tablelist""]').children('.layui-table-box')
                            .children('.layui-table-body').find('table tbody tr[data-index=0]').click();
                    }
                });

                $(document).ready(function () {
                    $(document).on('click', '#Search', function () {
                         rowDataBaseRange = []; 
                         rowScriptGroupRange = [];
                        table.reload('tablelist', {
                            //url: '/Script/ScriptRunLog/List',//数据接口
                            page: {
                                curr: 1
                            },
                            where: { 'Name': $.trim($(""#KeyWords"").val()) }
                        });
                    });

                    $(document).on('c");
                WriteLiteral(@"lick', '#addRole', function () {
                        $(""#fm"")[0].reset();
                        $('#Name').removeAttr(""readonly"");
                        $('#Id').val(0);
                        $(""#btn_reset"").show();
                        windowsIndex = layer.open({
                            type: 1,
                            title: '新增角色',
                            area: '600px',
                            resize: true,
                            content: $('#form_window')
                        });
                        url = '/Role/CreateRole';
                    });

                    $(document).on('click', '#copyRole', function () {
                        $('#Name').removeAttr(""readonly"");
                        form.val('fm', selectTableRowData);
                        windowsIndex = layer.open({
                            type: 1,
                            title: '复制角色',
                            area: '600px',
                            resize: ");
                WriteLiteral(@"true,
                            content: $('#form_window')
                        });
                        url = '/Role/CopyRole';
                    });

                    
                    //监听停用操作
                    form.on('switch(DataDemo)', function (obj) {
                        var switchObj = obj;
                        var thisName = this.name; 
                        var TipsStr = """";
                        //判断是否操作过该行限制数据库
                        var DataRange = GetDataRange(thisName);
                         
                        if (DataRange == """") {
                            //未操作过则使用开关值
                            DataRange = obj.value;
                        }


                        if (DataRange == ""true"") {
                            TipsStr = ""确定要取消限制角色数据库权限吗？"";
                        } else if (DataRange == ""false"") {

                            TipsStr = ""确定要限制角色数据库权限吗？"";
                        }

                      
       ");
                WriteLiteral(@"                  

                        layer.confirm(TipsStr, {
                            title: '',
                            btn: ['确定', '取消'], //按钮
                            cancel: function (index, layero) { 
                                RestoreSwitch(switchObj, DataRange);
                            },
                            resize: false
                        }, function (index) {
                                $.ajax(
                                    {
                                        url: '/Role/EditDataBaseRange',
                                        type: ""post"",
                                        data: { 'Id': thisName },
                                        datatype: 'json',
                                        success: function (data) {
                                            if (data.okMsg) {
                                                layer.msg(data.okMsg);

                                                GetrowDataBaseR");
                WriteLiteral(@"ange(thisName, DataRange);
                                            } else {

                                                layer.msg(data.errorMsg);
                                                RestoreSwitch(switchObj, DataRange);
                                            }

                                        }
                                    }
                                )
                        }, function (index) {
                                RestoreSwitch(switchObj, DataRange);
                        }
                            
                        );
                       

                    });


                    //监听停用操作
                    form.on('switch(ScriptDemo)', function (obj) {
                        var switchObj = obj;
                        var thisName = this.name;
                        var TipsStr = """";
                        //判断是否操作过该行限制脚本
                        var ScriptRange = GetScriptRange(thisName);

     ");
                WriteLiteral(@"                   if (ScriptRange == """") {
                            //未操作过则使用开关值
                            ScriptRange = obj.value;
                        }


                        if (ScriptRange == ""true"") {
                            TipsStr = ""确定要取消限制角色脚本权限吗？"";
                        } else if (ScriptRange == ""false"") {

                            TipsStr = ""确定要限制角色脚本权限吗？"";
                        }




                        layer.confirm(TipsStr, {
                            title: '',
                            btn: ['确定', '取消'], //按钮
                            cancel: function (index, layero) {
                                RestoreSwitch(switchObj, ScriptRange);
                            },
                            resize: false
                        }, function (index) {
                            $.ajax(
                                {
                                    url: '/Role/EditScriptGroupRange',
                                    type");
                WriteLiteral(@": ""post"",
                                    data: { 'Id': thisName },
                                    datatype: 'json',
                                    success: function (data) {
                                        if (data.okMsg) {
                                            layer.msg(data.okMsg);

                                            GetrowScriptGroupRange(thisName, ScriptRange);
                                        } else {

                                            layer.msg(data.errorMsg);
                                            RestoreSwitch(switchObj, ScriptRange);
                                        }

                                    }
                                }
                            )
                        }, function (index) {
                                RestoreSwitch(switchObj, ScriptRange);
                        }

                        );


                    });
                     
                });

  ");
                WriteLiteral(@"              //监听tablelist行单击事件(双击事件为：rowDouble)
                table.on('row(tablelist)', function (obj) {
                   
                    var data = obj.data;
                    var rowIndex = $(obj.tr).attr(""data-index"");
                    $(""#ScriptNameTips"").html(data.ScriptName);
                    obj.tr.addClass('layui-table-click').siblings().removeClass('layui-table-click');
                    if (currentIndex != rowIndex && selectTableRowData.Id != data.Id) {
                        
                        selectTableRowData = data;
                        currentIndex = rowIndex;

                    } 

                });



                //监听tablelist工具条
                table.on('tool(tablelist)', function (obj) {
                    
                    var data = obj.data;
                    if (obj.event === 'edit') {
                        $('#Name').attr(""readonly"", ""readonly"");
                        $(""#btn_reset"").hide(); 
                  ");
                WriteLiteral(@"      form.val('fm', data);
                        windowsIndex = layer.open({
                            type: 1,
                            title: '修改【' + data.Name + '】角色',
                            area: '600px',
                            resize: true,
                            content: $('#form_window')
                        });
                        url = '/Role/EditRole';
                    }
                    else if (obj.event === 'del') {
                        layer.confirm('确定要删除名为【' + data.Name + '】的角色么？将无法恢复。', {
                            title: '',
                            btn: ['确定', '取消'], //按钮
                            resize: false
                        }, function (index) {
                            $.post('/Role/DelRole', { id: data.Id }, function (result) {
                                if (result.okMsg) {
                                    layer.msg(result.okMsg);
                                    currentIndex = -1;
                  ");
                WriteLiteral(@"                  table.reload('tablelist'); //重载表格
                                } else {
                                    layer.msg(result.errorMsg);
                                }
                            }, 'json');
                            layer.close(index);
                        });
                    }
                    else if (obj.event === 'addmenu') {

                        //提交 Ajax 成功后，关闭当前弹层并重载表格
                        $.ajax({
                            url: '/Role/GetMenuToRoleList?roleid=' + data.Id,
                            type: ""post"",
                            datatype: 'json',
                            success: function (result) {
                                var treemenu = tree.render({
                                    elem: '#menuTree'
                                    , showLine: true
                                    , data: eval(result)
                                    , showCheckbox: true //是否显示复选框
                   ");
                WriteLiteral(@"                 , id: 'menuTree'
                                });
                            }, error: function (res) {
                                layer.msg(""加载信息错误："" + res.responseText);
                                layer.close(indes);
                            }
                        });
                        windowsIndex = layer.open({
                            type: 1,
                            title: '设置【' + data.Name + '】菜单权限',
                            area: ['400px', '400px'],
                            resize: true,
                            btn: ['保存', '取消'],
                            yes: function (index, layero) {
                                var checkedNodes = tree.getChecked('menuTree'); //获取选中节点的数据
                                var select_values = getCheckedId(checkedNodes);
                                //layer.alert(JSON.stringify(checkedNodes) + "";"" + select_values);
                                $.post('/Role/MenusToRole ', { menu_ids:");
                WriteLiteral(@" select_values, roleid: data.Id }, function (result) {
                                    if (result.okMsg) {
                                        layer.msg(result.okMsg);
                                        layer.close(windowsIndex);
                                    } else {
                                        layer.msg(result.errorMsg);
                                    }
                                }, 'json');
                            },
                            cancel: function (index, layro) { },
                            content: $('#menu_window')
                        });
                    }
                    else if (obj.event === 'userdetail') {
                        table.render({
                            elem: '#tablelistuser'
                            , id: 'tablelistuser'
                            , url: ""/Role/RoleUserListWithPager?roleid="" + data.Id
                            , page: true
                            , cols: [[
   ");
                WriteLiteral(@"                             { field: 'zizeng', title: '', type: 'numbers', fixed: 'left' }
                                , { field: 'UserName', title: '用户名称', fixed: 'left' }
                                , { field: 'TrueName', title: '真实姓名' }
                            ]]
                        });
                        windowsIndex = layer.open({
                            type: 1,
                            title: '查看【' + data.Name + '】用户列表',
                            area: ['55%', '70%'],
                            minWidth: 450,
                            maxmin: false,
                            content: $('#userList_window')
                        });
                    }
                });

                //还原开关
                function RestoreSwitch(obj, Range) {

                    if (Range == ""true"") { 
                        obj.elem.checked = true; 
                        
                    } else if (Range == ""false"") {
                        obj");
                WriteLiteral(@".elem.checked = false;
                    }
                    obj.value == Range;
                    obj.elem.value = Range;
                   
                    form.render();
                }

                function GetrowScriptGroupRange(Id, IsDing) {

                    //console.log(IsDing);
                    var vList = rowScriptGroupRange.filter(val => val.Id === Id);

                    if (vList.length == 0) {
                        var rowDing = false;
                        if (IsDing == ""false"") {
                            rowDing = true;
                        }
                        rowScriptGroupRange.push({ 'Id': Id, 'ScriptGroupRange': rowDing })
                    } else {
                        for (var i = 0; i < rowScriptGroupRange.length; i++) {
                            if (rowScriptGroupRange[i].Id == Id) {
                                var rowDing = true;
                                if (rowScriptGroupRange[i].ScriptGroupRange == tr");
                WriteLiteral(@"ue) {
                                    rowDing = false;
                                }
                                rowScriptGroupRange[i].ScriptGroupRange = rowDing;
                            }
                        }
                    }
                }


                // 获取选中节点的id
                function getCheckedId(jsonObj) {
                    var id = """";
                    $.each(jsonObj, function (index, item) {
                        if (id != """") {
                            id = id + "","" + item.id;
                        }
                        else {
                            id = item.id;
                        }
                        var i = getCheckedId(item.children);
                        if (i != """") {
                            id = id + "","" + i;
                        }
                    });
                    return id;
                }

                //监听提交
                form.on('submit(submit)', function (data) {
");
                WriteLiteral(@"                    var indes = layer.load(1);
                    //提交 Ajax 成功后，关闭当前弹层并重载表格
                    $.ajax({
                        url: url,
                        type: ""post"",
                        data: { 'roleModel': data.field },
                        datatype: 'json',
                        success: function (data) {
                            if (data.okMsg) {
                                layer.msg(data.okMsg);
                                layer.close(windowsIndex);//关闭弹出层
                                table.reload('tablelist'); //重载表格
                                rowDataBaseRange = [];
                                rowScriptGroupRange = [];
                            }
                            else {
                                layer.msg(data.errorMsg);
                            }
                            layer.close(indes);
                        }, error: function (res) {
                            layer.msg(""加载统计信息错误："" + res.resp");
                WriteLiteral("onseText);\r\n                            layer.close(indes);\r\n                        }\r\n                    });\r\n                    return false;\r\n                });\r\n\r\n\r\n\r\n            });\r\n        </script>\r\n    </div>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n<!-- 角色新增、修改、复制 -->\r\n<div class=\"window_wrap\" id=\"form_window\" style=\"display: none\">\r\n    ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e126949cf1e1096fac27f07bffd836f85b6b509ad8854a4ae08b7afe62c8ea7f37350", async() => {
                WriteLiteral(@"
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">角色名称</label>
            <div class=""layui-input-block"">
                <input type=""text"" name=""Id"" id=""Id"" style=""display:none;"" />
                <input type=""text"" name=""Name"" id=""Name"" required lay-verify=""required"" placeholder=""请输入角色名称"" autocomplete=""off"" class=""layui-input"">
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">角色描述</label>
            <div class=""layui-input-block"">
                <textarea name=""Description"" id=""Description"" class=""layui-textarea"" style=""resize: none""></textarea>
            </div>
        </div>
        <div class=""layui-form-item"">
            <div class=""layui-input-block"">
                <button type=""button"" class=""layui-btn"" lay-submit lay-filter=""submit"">提交</button>
                <button type=""reset"" class=""layui-btn layui-btn-primary"" id=""btn_reset"">重置</button>
            </div>
        </di");
                WriteLiteral("v>\r\n    ");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"
</div>

<!-- 菜单设置 -->
<div class=""window_wrap"" id=""menu_window"" style=""display: none"">
    <div id=""menuTree"" class=""demo-tree""></div>
</div>

<!-- 用户列表 -->
<div class=""window_wrap  layui-form"" id=""userList_window"" style=""display: none"">
    <div class=""layui-card"">
        <div class=""layui-card-body"">
            <table id=""tablelistuser"" lay-filter=""tablelistuser""></table>
        </div>
    </div>
</div>
</html>
");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
