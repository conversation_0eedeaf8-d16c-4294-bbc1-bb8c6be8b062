#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DepartmentCollection.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "9c565e5e8ef4a55219fbb5f4d392b256f3df9561f4240a8170d1301bb8eaf9ba"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_ReportingManage_Views_PatientManage_DepartmentCollection), @"mvc.1.0.view", @"/Areas/ReportingManage/Views/PatientManage/DepartmentCollection.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"9c565e5e8ef4a55219fbb5f4d392b256f3df9561f4240a8170d1301bb8eaf9ba", @"/Areas/ReportingManage/Views/PatientManage/DepartmentCollection.cshtml")]
    #nullable restore
    internal sealed class Areas_ReportingManage_Views_PatientManage_DepartmentCollection : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/lib/codemirror.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/theme/pastel-on-dark.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/dialog/style.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/images/img_icon.png"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("alt", new global::Microsoft.AspNetCore.Html.HtmlString("Alternate Text"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/images/pdf_icon.png"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/mp3/沁园春雪.mp3"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("type", new global::Microsoft.AspNetCore.Html.HtmlString("audio/mpeg"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/mp3/fjsl.mp3"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-1.10.2.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_13 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/dialog/handlebars.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.SingleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_14 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/dialog/list.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.SingleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_15 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/dialog/script.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_16 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\ReportingManage\Views\PatientManage\DepartmentCollection.cshtml"
  
    ViewBag.Title = "DepartmentCollection";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"zh\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "9c565e5e8ef4a55219fbb5f4d392b256f3df9561f4240a8170d1301bb8eaf9ba9769", async() => {
                WriteLiteral(@"
    <meta charset=""UTF-8"">
    <meta http-equiv=""X-UA-Compatible"" content=""IE=edge"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"">
    <meta name=""screen-orientation"" content=""landscape"">

    <title>数据采集</title>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "9c565e5e8ef4a55219fbb5f4d392b256f3df9561f4240a8170d1301bb8eaf9ba10353", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n\r\n    <!-- 代码高亮 -->\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "9c565e5e8ef4a55219fbb5f4d392b256f3df9561f4240a8170d1301bb8eaf9ba11585", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "9c565e5e8ef4a55219fbb5f4d392b256f3df9561f4240a8170d1301bb8eaf9ba12789", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "9c565e5e8ef4a55219fbb5f4d392b256f3df9561f4240a8170d1301bb8eaf9ba13992", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "9c565e5e8ef4a55219fbb5f4d392b256f3df9561f4240a8170d1301bb8eaf9ba15196", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        body {
            background-color: #fafafa;
            overflow: hidden;
        }

        .layui-card-header {
            height: 20px;
            line-height: 20px;
            font-weight: bold;
        }
        

        /* 移动端头部 */

        .headrow {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            background-color: #f6f6f6;
            height: 38px;
        }

            .headrow .layui-btn-primary {
                background-color: transparent;
                border: none;
                color: #333;
            }

        .patient_information {
            align-items: center;
        }
        .middle{
           text-align:center;
        }
        .left, .right{
            min-width:58px;
            height:38px;
        }
        /* 移动端头部 */
        .space-between{
            justify-content: space-between;
        }
        .user_info{
            disp");
                WriteLiteral(@"lay:flex;
            flex-direction:row;
            justify-content:space-between;
            align-items:center;
            background-color:#fff;
            padding:10px 15px;
        }

        .info_middle, .info_right {
            display: flex;
            flex-direction: row;
        }

    
        .user_info_item{
            margin-right:20px;
        }

        .content_wrap {
            display:flex;
            flex-direction:row;
            padding: 10px 5px;
        }

        .flex_one{
            flex:1;
        }

        .side_nav{
            width:40px;
        }

        .nav_btn{
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            width:100%;
            padding: 20px 0;
            margin-bottom: 2px;
            writing-mode: vertical-rl;
            text-orientation: mixed;
            background: #e6e6e6;
            border-radius:20px;       ");
                WriteLiteral(@"
        }
        .nav_btn_active{
            color:#fff;
            background: rgb(122, 77, 123);
        }
        .content_inner{
            position:relative;
            padding:10px;
            margin:5px;
            margin-top:0;
            background-color:#fff;
        }

        .btn_bottom{
            width:96%;
            padding:4px;
            padding-top:5px;
            display:flex;
            flex-direction:row;
            justify-content:space-between;
            position:absolute;
            left:0;
            bottom:15px;
            /* background-color:#f6f6f6; */
        }

        .nav_btn_group{
            display: flex;
            flex-direction: row;
        }

        .nav_btn_group .active {
                border: 1px solid #1e9fff;
                color: #1e9fff;
        }

        .nav_btn_item{
            text-align:center;
            width:60px;
            padding:0 10px;
            line-height:30px;
            ");
                WriteLiteral(@"background-color:#fff;
            color:#666;
            border-radius:2px;
            border:1px solid #e6e6e6;
            cursor:pointer;
        }

        .left_content{
            height:90%;

        }

        .left_inner{
            width:100%;
            height:100%;
        }

 
        .inner_user_info{
            justify-content:flex-start;
            background-color: transparent;
            padding:0;
        }

        .writ{
            position:relative;
            margin:10px 0;
            border:1px solid #e6e6e6;
            border-radius:2px;
        }
        .writ_bottom{
            width:96%;
            position:absolute;
            bottom:20px;
            text-align:right;
        }

        .empty{
            position:absolute;
            top:50%;
            left:50%;
            width:200px;
            height:100px;
            margin-left:-100px;
            margin-top:-50px;
            text-align:center;
        }
");
                WriteLiteral(@"

        .empty i{
            font-size:80px;
            color: #20222A;
        }

        .more_btn{
            display:flex;
            flex-direction:row;
            justify-content:flex-end;
            background-color:#fff;
        }

        .audio_item{
            padding-bottom:10px;
        }
        .audio_item audio{
            width:100%;
        }
        .audio_title{
            padding-left:5px;
            line-height:34px;
        }

        .history_wrap{
            display:flex;
            flex-direction:row;
            justify-content:space-between;
            border:1px solid #f6f6f6;
            border-radius:2px;
        }

        .history_wrap .layui-input{
            border-width:0;
        }

        .transfer_btn{
            width:38px;
            height:38px;
            text-align:center;
            cursor:pointer;
        }

        .transfer_btn i{
            font-size:20px;
            line-height: 38px;
       ");
                WriteLiteral(@" }

        .alert {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            text-align: center;
            padding-top: 20%;
            z-index: 1000;
        }

        .alert i{
            font-size:40px;
        
        }

        .alert p{
            margin-top: 20px;
        }
        
            .row_wrap{
               display:flex;
               flex-direction:row;
               align-content:center;
            }

        .layui-border-blue{
            border-color: #1E9FFF;
            color: #1E9FFF;

        }

        .translation_btn{
            background: rgb(122, 77, 123);
        }

        .btn_bg{
            width:38px;
            height:38px;
            margin-left:10px;
            background-color:#f6f6f6;
            border-radius:2px;
            ");
                WriteLiteral(@"cursor: pointer;
        }

        .right_btn_group{
            align-items:center;
        }

        .ws_btn{
            padding:8px;
            background-color:#f6f6f6;
            border-radius:2px;
            color: #1E9FFF;
            cursor:pointer;
        }
        </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "9c565e5e8ef4a55219fbb5f4d392b256f3df9561f4240a8170d1301bb8eaf9ba23687", async() => {
                WriteLiteral(@"

    <div class=""wrap"" id=""content"" >
        <div class=""headrow patient_information"">
            <div class=""left"">
                <button id=""previous"" class=""layui-btn layui-btn-primary layui-border-green"" style=""margin-right:15px;"">
                    <i class=""layui-icon layui-icon-close""></i>
                </button>
            </div>
            <div class=""middle"">
                <h5>数据采集</h5>
            </div>
            <div class=""right""></div>
        </div>

        <div class=""user_info"">
            <div class=""info_left"" style=""min-width:100px;"">

            </div>

            <div class=""info_middle"">
                <div class=""user_info_item"">患者:  <strong>琳琳</strong></div>
                <div class=""user_info_item"">患者卡号:  <strong>008007006</strong></div>
            </div>

            <div class=""info_right"">
                <div class=""user_info_item"">科室:  <strong>康無科</strong></div>
                <div class=""user_info_item"">当动用户:  <strong>008004</");
                WriteLiteral(@"strong></div>
            </div>
        </div>

        <div class=""content_wrap"">
            <div class=""side_nav"">
                <div class=""nav_btn"" id=""baselineBtn"">记录表</div>
                <div class=""nav_btn"" id=""recordsBtn"">病历文书</div>
            </div>
            <div class=""layui-row flex_one row_wrap"">
                
                <div class=""layui-col-xs3 layui-col-md3 baseline"" style=""display:none"">
                    <div class=""content_inner"">
                        <div class=""left_content"">
                            <div class=""left_inner structured_wrap"" style=""display:none"">
                                <iframe src=""http://8.131.88.44:9905/dmp/form/d127eb5778a04b85b1b782831dc96b2b.list"" style=""height:100%;width:100%;""></iframe>
                            </div>
                            <div class=""left_inner var_preview_wrap"" style=""display:block"">
                                <div class=""layui-card-header"">变量设置</div>
                               ");
                WriteLiteral(" <div");
                BeginWriteAttribute("class", " class=\"", 9277, "\"", 9285, 0);
                EndWriteAttribute();
                WriteLiteral(@">
                                    <table id=""demo"" lay-filter=""test""></table>
                                </div>
                            </div>
                        </div>
                        <div class=""btn_bottom"">
                            <div class=""nav_btn_group"">
                                <div class=""nav_btn_item"" id=""Structured"">结构化</div>
                                <div class=""nav_btn_item active"" id=""VarPreview"">变量预览</div>
                            </div>
                            <div class=""save"">
                                <button type=""button"" class=""layui-btn layui-btn-normal layui-btn-sm"">保存</button>
                            </div>
                        </div>
                    </div>
                </div>
              
                
                <div class=""layui-col-xs5 layui-col-md5 flex_one"">
                    <div class=""content_inner"">
                        <div class=""layui-card-header row_wrap space-between""");
                WriteLiteral(">\r\n                            <div");
                BeginWriteAttribute("class", " class=\"", 10345, "\"", 10353, 0);
                EndWriteAttribute();
                WriteLiteral(@">
                                结构化内容提取
                            </div>
                            <div class=""right_btn_group row_wrap"">
                                <div class=""ws_btn "">
                                    <i class=""layui-icon layui-icon-form""></i>  病例文书转写
                                </div>
                                <div class=""btn_bg"">");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("img", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "9c565e5e8ef4a55219fbb5f4d392b256f3df9561f4240a8170d1301bb8eaf9ba27992", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("</div>\r\n                                <div class=\"btn_bg\">");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("img", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "9c565e5e8ef4a55219fbb5f4d392b256f3df9561f4240a8170d1301bb8eaf9ba29250", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"</div>
                            </div>
                        </div>
                        <div class=""writ"">
                            <div class=""empty"">
                                <i class=""layui-icon layui-icon-mike""></i>
                                <p>请完成录音后生成病例文书</p>
                            </div>
                        </div>
                        <div class=""writ_bottom "">
                            <button type=""button"" class=""layui-btn layui-btn-normal layui-btn-sm"">病例文书引用</button>
                        </div>
                    </div>
                </div>
                <div class=""layui-col-xs4 layui-col-md4 "">
                    <div class=""content_inner"">
                        <div class=""layui-card-header row_wrap space-between"">
                            <div>医疗对话框</div>
                            <div class=""layui-btn layui-btn-primary layui-btn-xs layui-border-blue "">
                                <i class=""layui-icon layui-icon-time""");
                WriteLiteral(@"></i>  实时填写
                            </div>
                        </div>
                        <div class=""chat"">
                            <div class=""chat-history"">
                                <ul>
                                    <li class=""clearfix"">
                                        <div class=""message-data align-right"">
                                            <span class=""message-data-time"">2024/10/18 14:50:33</span> &nbsp; &nbsp;
                                            <span class=""message-data-name"">琳琳</span> <i class=""fa fa-circle me""></i>
                                        </div>
                                        <div class=""message other-message float-right"">
                                            我有点不太舒服.
                                        </div>
                                    </li>
                                    <li>
                                        <div class=""message-data"">
                                     ");
                WriteLiteral(@"       <span class=""message-data-name""><i class=""fa fa-circle online""></i> 科室医生</span>
                                            <span class=""message-data-time"">2024/10/18 14:47:48</span>
                                        </div>
                                        <div class=""message my-message"">
                                            你有什么症状?
                                        </div>
                                    </li>
                                    <li class=""clearfix"">
                                        <div class=""message-data align-right"">
                                            <span class=""message-data-time"">2024/10/18 15:10:12</span> &nbsp; &nbsp;
                                            <span class=""message-data-name"">琳琳</span> <i class=""fa fa-circle me""></i>

                                        </div>
                                        <div class=""message other-message float-right"">
                                            最近说话的时候");
                WriteLiteral(@"声音沙哑,还有点鼻塞.
                                        </div>
                                    </li>
                                </ul>
                            </div> <!-- end chat-history -->
                            <div class=""more_btn "">
                                <div class=""nav_btn_item""> 更多>>></div>
                            </div>
                        </div>
                        <div class=""right_bottom"">
                            <ul class=""audio_list"" id=""audioList"">
                                <li>
                                    <div class=""audio_item"">
                                        <div class=""audio_title"">2024/10/18 14:47:35</div>
                                        <audio id=""audio1"" controls>
                                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("source", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "9c565e5e8ef4a55219fbb5f4d392b256f3df9561f4240a8170d1301bb8eaf9ba34525", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"设置不支持音频文件
                                        </audio>
                                    </div>
                                </li>
                                <li>
                                    <div class=""audio_item"">
                                        <div class=""audio_title"">2024/10/17 09:34:13</div>
                                        <audio id=""audio2"" controls>
                                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("source", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "9c565e5e8ef4a55219fbb5f4d392b256f3df9561f4240a8170d1301bb8eaf9ba36180", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"设置不支持音频文件
                                        </audio>
                                    </div>
                                </li>
                            </ul>
                            <div class=""layui-row"">
                                <div class=""layui-col-xs8 layui-col-sm8 layui-col-md8"">
                                    <div class=""history_wrap"">
                                        <div class=""layui-input-inline"">
                                            <input type=""text"" name=""caseHistory"" required lay-verify=""required"" placeholder=""语音转文字区域"" autocomplete=""off"" class=""layui-input"">
                                        </div>
                                        <div class=""transfer_btn"">
                                            <i class=""layui-icon layui-icon-release""></i>
                                        </div>
                                    </div>
                                </div>

                                <div class=""layu");
                WriteLiteral(@"i-col-xs4 layui-col-sm4 layui-col-md4"" style=""text-align:right;margin-top:5px;"">
                                    <button type=""button"" class=""layui-btn layui-btn-sm translation_btn"">
                                        <i class=""layui-icon layui-icon-mike""></i>开始录音
                                    </button>
                                </div>
                            </div>

                        </div>

                    </div>
                </div>
            </div>

        </div>
       
    </div>


    <div class=""alert"" id=""orientationAlert"">
        <i class=""layui-icon layui-icon-refresh""></i>
        <p>请将您的设备旋转至横屏模式以继续。</p>
    </div>







    <!--对话框start-->
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "9c565e5e8ef4a55219fbb5f4d392b256f3df9561f4240a8170d1301bb8eaf9ba39218", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "9c565e5e8ef4a55219fbb5f4d392b256f3df9561f4240a8170d1301bb8eaf9ba40343", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "9c565e5e8ef4a55219fbb5f4d392b256f3df9561f4240a8170d1301bb8eaf9ba41468", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_14);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "9c565e5e8ef4a55219fbb5f4d392b256f3df9561f4240a8170d1301bb8eaf9ba42593", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_15);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    <!--对话框end-->\r\n\r\n\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "9c565e5e8ef4a55219fbb5f4d392b256f3df9561f4240a8170d1301bb8eaf9ba43747", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_16);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"

    <script >
            layui.use(['jquery', 'layer', 'form', 'element', 'code', 'laytpl', 'table'], function () {
                var layer = layui.layer,
                    $ = layui.$,
                    form = layui.form,
                    laytpl = layui.laytpl,
                    element = layui.element,
                    table = layui.table;

                if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
                    $(""#previous"").show();
                }
                else {
                    $(""#previous"").hide();
                }
                $(""#previous"").click(function () {
                    window.location.href = ""/ReportingManage/PatientManage/Index"";
                });


                $("".side_nav"").on(""click"", "".nav_btn"", function () {
                    $("".nav_btn"").removeClass(""nav_btn_active"");
     ");
                WriteLiteral(@"               $(this).addClass(""nav_btn_active"");

                    var navid = $(this).attr(""id"");
                    if (navid == ""baselineBtn"") {
                        //基线
                        $("".baseline"").css(""display"",""block"")

                    } else if (navid == ""recordsBtn"") {
                        //病历文书
                        $("".baseline"").css(""display"", ""none"")


                    }
                
                
                
                
                })

            function checkOrientation() {
                if (window.innerHeight > window.innerWidth) { // 如果是竖屏
                    document.getElementById('orientationAlert').style.display = 'block';
                    document.getElementById('content').style.display = 'none';
                } else { // 如果是横屏
                    document.getElementById('orientationAlert').style.display = 'none';
                    document.getElementById('content').style.display = 'block';
        ");
                WriteLiteral(@"        }
            }

            // 监听屏幕方向变化
            window.addEventListener('resize', checkOrientation);

            $(document).ready(function () {
                // 页面加载时检查一次
                checkOrientation();
                setContentH();
                setWrit();
                setChatH();


                    
                table.render({
                        elem: '#demo'
                        , height: 'full-240'
                        , data: [{
                            var: '变量1',
                            val: '值1',
                            gist: '依据1'
                        }, {
                            var: '变量2',
                            val: '值2',
                            gist: '依据2'
                        }, {
                            var: '变量3',
                            val: '值3',
                            gist: '依据3'
                        }, {
                            var: '变量4',
                          ");
                WriteLiteral(@"  val: '值4',
                            gist: '依据4'
                        }]
                        , cols: [[
                            { field: 'var', title: '变量', width: 60 }
                            , { field: 'val', title: '值', width: 60 }
                            , { field: 'gist', title: '依据', minWidth: 80 }

                        ]]
                    });

     

                // 使用原生JavaScript添加事件监听器
                var audios = document.querySelectorAll('#audioList audio');
                // 遍历所有音频元素并添加事件监听器
                audios.forEach(function (audio) {
                    audio.addEventListener('play', function (e) {
                        // 遍历所有音频元素
                        Array.from(audios).forEach(function (otherAudio) {
                            if (otherAudio !== e.target) {
                                console.log('暂停其他音频 (原生JS):', otherAudio.id);
                                otherAudio.pause();
                                otherAudio.");
                WriteLiteral(@"currentTime = 0; // 可选：将时间重置为0
                            }
                        });
                    });

               
          
                });



                });

                function setContentH() {
                    var winH = $(window).height(),
                        headerH = $("".headrow"").height(),
                        userInfoH = $("".user_info"").height();
                    var conentH = winH - (headerH + userInfoH)-60 ;
                    $("".content_inner"").css(""height"", conentH + ""px"");
                    return conentH;

                }

                function setWrit() {
                    var conentH = setContentH();
                    var thead = $("".inner_user_info"").height();
                    var writBottom = $("".writ_bottom"").height();
                    var writH = conentH - thead - writBottom-73;
                    $("".writ"").css(""height"", writH + ""px"");
                }

                function setChatH() {
   ");
                WriteLiteral(@"                 var conentH = setContentH();
                    var theadH = $("".inner_user_info"").height();
                    var bottomH = $("".right_bottom"").height();
                    var chatH = conentH - theadH - bottomH-98;
                    $("".chat-history"").css(""height"", chatH+""px"");
                }



                $("".nav_btn_group"").on(""click"", "".nav_btn_item"", function () {

                    $(this).siblings().removeClass(""active"");
                    $(this).addClass(""active"");

                    var navid = $(this).attr(""id"");
                    if (navid == ""Structured"") {
                  
                        $("".var_preview_wrap"").css(""display"",""none"");
                        $("".structured_wrap"").css(""display"",""block"");
                    } else if (navid == ""VarPreview"") {
                
                        $("".structured_wrap"").css(""display"",""none"");
                        $("".var_preview_wrap"").css(""display"",""block"");

           ");
                WriteLiteral("         }\r\n                \r\n                })\r\n\r\n               \r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n            });\r\n    </script>\r\n\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
