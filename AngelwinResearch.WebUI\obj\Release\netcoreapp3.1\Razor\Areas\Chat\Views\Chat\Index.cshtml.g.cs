#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Chat\Views\Chat\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "1fa75511ab55a4b90d256ecc6dbf4c53c6635ea333abe752bf82d3d6a2c037df"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_Chat_Views_Chat_Index), @"mvc.1.0.view", @"/Areas/Chat/Views/Chat/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"1fa75511ab55a4b90d256ecc6dbf4c53c6635ea333abe752bf82d3d6a2c037df", @"/Areas/Chat/Views/Chat/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_Chat_Views_Chat_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/index_style2.css?v=v22"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/perfect-scrollbar/perfect-scrollbar.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/jquery/dist/jquery.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js?ss=111"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/marked.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/perfect-scrollbar/perfect-scrollbar.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/images/chat_icon_Q.png"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("alt", new global::Microsoft.AspNetCore.Html.HtmlString(""), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_13 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/images/chat_icon_A.png"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Chat\Views\Chat\Index.cshtml"
  
    ViewBag.Title = "ChatGPT";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"zh\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "1fa75511ab55a4b90d256ecc6dbf4c53c6635ea333abe752bf82d3d6a2c037df8511", async() => {
                WriteLiteral("\r\n    <meta charset=\"UTF-8\">\r\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>chatGTP</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "1fa75511ab55a4b90d256ecc6dbf4c53c6635ea333abe752bf82d3d6a2c037df9006", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "1fa75511ab55a4b90d256ecc6dbf4c53c6635ea333abe752bf82d3d6a2c037df10208", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "1fa75511ab55a4b90d256ecc6dbf4c53c6635ea333abe752bf82d3d6a2c037df11411", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n    <!-- 代码高亮 -->\r\n    <!-- 自定义滚动条 -->\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "1fa75511ab55a4b90d256ecc6dbf4c53c6635ea333abe752bf82d3d6a2c037df12662", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "1fa75511ab55a4b90d256ecc6dbf4c53c6635ea333abe752bf82d3d6a2c037df13869", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "1fa75511ab55a4b90d256ecc6dbf4c53c6635ea333abe752bf82d3d6a2c037df14993", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "1fa75511ab55a4b90d256ecc6dbf4c53c6635ea333abe752bf82d3d6a2c037df16117", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        body {
            overflow: hidden;
            background-color: #fff;
        }

        .code_wrap {
            background-color: #3f3f3f;
            border-radius: 6px;
            overflow: hidden;
        }

        .code_top {
            padding: 4px 6px 0;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            color: #fff;
        }

        .code_content {
            padding: 10px;
            border: 10px solid #000;
            color: #c2be9e;
            text-align: left;
            resize: none;
        }

        .layui-btn-primary {
            border: none;
        }

        .layui-btn-xs {
            padding: 0 6px;
        }

        .code_btn_group button {
            border-color: transparent;
            color: #fff;
        }

            .code_btn_group button:hover {
                color: #eee;
                border-color: #eee;
            }

    ");
                WriteLiteral(@"    .code_top_left button {
            vertical-align: top;
            border-color: transparent;
            color: transparent;
        }

            .code_top_left button:hover {
                color: #eee;
                border-color: #eee;
            }

        .message_content {
            flex: 1;
        }

            .message_content p,
            .result_text {
                white-space: pre-line;
            }

        .result_text {
            padding: 10px;
            height: 80px;
            background-color: #3f3f3f;
            font-size: 14px;
            word-wrap: break-word;
            border: 10px solid #000;
            border-top: 2px solid #000;
            border-radius: 0;
        }

            .result_text:hover {
                border: 10px solid #000 !important;
                border-top: 2px solid #000 !important;
            }

            .result_text:focus {
                padding: 10px;
                border: 10px soli");
                WriteLiteral(@"d #000 !important;
                border-top: 2px solid #000 !important;
                color: #c2be9e;
                border-radius: 0;
            }

        .result_bj {
            display: flex;
            flex-direction: row;
            align-items: center;
            background-color: #009688;
            color: #fff;
        }

        .result_icon {
            width: 34px;
            height: 34px;
            border-radius: 10px;
            border: 2px solid #0bbe56;
            overflow: hidden;
            margin-right: 10px;
        }

            .result_icon img {
                vertical-align: top;
                width: 100%;
            }


        .right_title {
            padding: 0 2%;
            height: 40px;
            position: relative;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #eee;
        }


       ");
                WriteLiteral(@" .message_wrap {
            width: 70%;
            margin: 0 auto;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
        }

        .layui-btn-group .layui-btn-primary:first-child {
            border-color: transparent;
        }

        .layui-btn-group .layui-btn-primary {
            border-color: transparent;
        }

            .layui-btn-group .layui-btn-primary:hover {
                border-color: transparent;
            }

                .layui-btn-group .layui-btn-primary:hover i {
                    color: #16baaa;
                }

        .layui-btn-primary {
            background-color: transparent !important;
        }

        /* 折叠代码 */
        .code_top_left {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .code_name {
            width: 70px;
            padding-right: 10px;
 ");
                WriteLiteral(@"       }

        .code_name_wrap {
            flex: 1;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
        }

        .code_top_left button {
            color: #fff;
        }

        .code_shrink {
            width: 230px;
            height: 30px;
        }

        .rundtn {
            width: 60px;
        }

        .exhibition_wrap {
            padding-top: 10px;
        }

            .exhibition_wrap img {
                max-width: 100%;
            }




        .output {
            white-space: pre;
            word-wrap: break-word;
            overflow: auto;
        }




        /* 自定义滚动条 */
        .ps {
            position: relative;
        }

        .CodeMirror-scroll {
            height: 300px !important;
            margin-bottom: 0 !important;
            margin-right: 0 !important;
            padding-bottom: 0 !important;
            overflow: hidden !important;
     ");
                WriteLiteral(@"   }

        .CodeMirror {
            height: 300px !important;
        }

        .layui-tab-card {
            width: 100%;
        }

        .ltype {
            background-color: #3F3F3F;
            color: #fff;
            border: none;
            vertical-align: top;
        }

        .exhibition img {
            width: 100%;
        }

        .select_model {
            padding-left: 20px;
        }

        /*图片上传输入框*/
        .layui-upload-list {
            margin: 0;
            width: 100%;
            display: flex;
            flex-direction: row;
        }

        .Request_img_group {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
        }

        .Request_img_item {
            position: relative;
            width: 100px;
            height: 100px;
            border: 1px solid #eee;
            border-radius: 6px;
            margin: 10px 5px;
        }

            .Request_img_item img {
 ");
                WriteLiteral(@"               width: 100%;
            }

            .Request_img_item .layui-icon-close-fill {
                position: absolute;
                top: -10px;
                left: -10px;
                color: #FF5722;
                font-size: 24px;
                cursor: pointer;
            }

        .img_item {
            position: relative;
            width: 60px;
            height: 60px;
            border: 1px solid #eee;
            border-radius: 6px;
            margin: 0 5px;
        }

            .img_item img {
                width: 100%;
                height: 100%;
            }

            .img_item .layui-icon-close-fill {
                position: absolute;
                top: -10px;
                right: -10px;
                color: #FF5722;
                font-size: 24px;
                cursor: pointer;
            }

        .hide {
            display: none;
        }

        .pic_area {
            padding: 6px 20px;
        }");
                WriteLiteral(@"

        .input_wrap {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            border-top: 1px solid #eee;
        }

        .layui-icon-picture {
            color: #009688;
        }

        .pic_area .layui-input {
            border-width: 0;
        }

        /* 给表格添加边框 */
        .message_content table {
            border-collapse: collapse;
            /*width: 100%;*/ /* 宽度占满容器 */
        }

        .message_content th, td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
        }

        .row_wrap {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .tools_l {
            padding-left: 10px;
        }

        .tools_r {
            display: flex;
            flex-direction: row;
        }

        .layui-textarea {
            min-height: au");
                WriteLiteral(@"to;
        }

        .layui-elem-quote {
            border-left: 1px solid #8b8b8b;
        }

            .layui-elem-quote p {
                margin: 1em 0;
                font-size: 14px;
            }

        .message_content_txt {
            font: 16px Helvetica Neue, Helvetica, PingFang SC, Tahoma, Arial, sans-serif;
        }

            .message_content_txt h3, h4 {
                margin: 0px 20px 0px 20px;
                padding: 2px 2px 0px 0px;
            }

            .message_content_txt ul {
                margin: 18px 0px 18px 0px;
                padding: 0px 0px 0px 40px;
            }

            .message_content_txt p {
                margin: 16px 0px 16px 0px;
                padding: 0px 0px 0px 0px;
            }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "1fa75511ab55a4b90d256ecc6dbf4c53c6635ea333abe752bf82d3d6a2c037df27226", async() => {
                WriteLiteral(@"
    <div class=""chat layui-row"">
        <div class=""menu_wrap layui-col-xs2 layui-col-md2"">
            <div class=""menu_list"">
                <div class=""topic layui-form"">
                    <div class=""create_topic"">
                        <div class=""layui-btn-container"">
                            <button type=""button"" class=""layui-btn add_btn"" id=""addBtn"">
                                <i class=""layui-icon layui-icon-addition"" style=""margin-left: -10px;""></i>
                                新的话题
                            </button>
                        </div>
                    </div>
                    <div class=""topic_list"">
                        <ul class=""layui-form"" lay-filter=""topiclist"" id=""topiclist"">
                        </ul>
                    </div>
                </div>
            </div>
            <div class=""menu_right"" id=""close_menu""> </div>
        </div>
        <div class=""layui-col-xs10 layui-col-md10"">

            <div class=""layui-ro");
                WriteLiteral(@"w left_content"" id=""left_content"">
                <div class=""content_inner layui-col-md12 layui-col-lg12"">
                    <div class=""chat_main middle_main"">
                        <div class=""chat-history"" id=""chat-history"">
                            <ul id=""messgeList"">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>



            <div class=""input_wrap"">
                <div class=""tools_l pic_btn"">

                    <button type=""button"" class=""layui-btn layui-btn-primary layui-border-green"" id=""test2""><i class=""layui-icon layui-icon-picture-fine"" style=""font-size: 30px;""></i>图片上传</button>
                    <input type=""hidden"" id=""filePath"" />
                </div>
                <div class=""tools_r"">
                    <div class=""right_title layui-form"">
                        <form class=""layui-form"" lay-filter=""formModel"" id=""formModel""");
                BeginWriteAttribute("action", " action=\"", 11801, "\"", 11810, 0);
                EndWriteAttribute();
                WriteLiteral(@">
                            <div class=""layui-form right_title_select"">

                                <div class=""layui-inline select_model layui-form"" lay-filter=""selModelDIV"">
                                    <select name=""selModel"" id=""selModel"" lay-filter=""selModel"">
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>
                    <button type=""button"" class=""layui-btn layui-btn-normal sub_btn "" id=""submitBtn"" style=""width:60px;"">
                        <i class=""layui-icon layui-icon-release"" style=""font-size: 30px;""></i>
                    </button>
                </div>
            </div>
            <div class=""chat-message"">
                <textarea class=""layui-textarea"" name=""title"" lay-verify=""title"" autocomplete=""off""");
                BeginWriteAttribute("placeholder", " placeholder=\"", 12713, "\"", 12727, 0);
                EndWriteAttribute();
                WriteLiteral(@"
                          id=""message-to-send"" rows=""3""></textarea>
            </div>
            <div id=""picArea"" class=""pic_area"">
                <div class=""layui-upload-list"" id=""demo2""></div>
            </div>





        </div>
    </div>


    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "1fa75511ab55a4b90d256ecc6dbf4c53c6635ea333abe752bf82d3d6a2c037df31176", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "1fa75511ab55a4b90d256ecc6dbf4c53c6635ea333abe752bf82d3d6a2c037df32300", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n    <!-- 自定义滚动条 -->\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "1fa75511ab55a4b90d256ecc6dbf4c53c6635ea333abe752bf82d3d6a2c037df33451", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
        layui.use(['jquery', 'layer', 'form', 'element', 'code', 'laytpl', 'table', 'upload'], function () {
            var layer = layui.layer,
                $ = layui.$,
                form = layui.form,
                laytpl = layui.laytpl,
                element = layui.element,
                table = layui.table,
                upload = layui.upload;
            var themeid = 0;


            var filesList = []; // 存储选中的文件
            //多图片上传
            var uploadInst = upload.render({
                elem: '#test2',
                url: '/Chat/Chat/UploadImg',
                multiple: true,
                number:5,
                accept: 'images',
                exts:'jpeg|jpg|png|bmp|ico|WEBP',
                size:'5120',
                acceptMime: 'image/jpeg, image/png,image/bmp,image/webp,image/x-icon',
                data: {
                    modelType: function () {
                        return $('#selModel').val();
                    }");
                WriteLiteral(@"
                },
                // auto: false, // 关闭自动上传
                before: function (obj) {
                    //预读本地文件示例，不支持ie8
                },
                choose: function (obj) {

        
                }
                , done: function (res, index, upload) {
                    if (res.code == 0) {

                        $('#demo2').append(
                            '<div class=""img_item""><i class=""layui-icon layui-icon-close-fill hide""></i><img src=""' +
                            res.data.paths +
                            '"" alt=""' + res.data.name +
                            '"" class=""layui-upload-img""></div>');
                        filesList.push(res.data.paths);

                        arrangement0();
                    }
                    else{
                        layer.msg(res.msg);
                       
                    }
                }

            });

            // 监听下拉框选择
            form.on('select(selModel)', fun");
                WriteLiteral(@"ction (data) {

            });

            $("".layui-upload-list"").on(""mouseenter"", "".img_item"", function () {

                $(this).find(""i"").removeClass(""hide"")
                $(this).find(""i"").on(""click"", function () {
                    $(this).parent().remove();
                    if ($("".img_item"").length == 0) {
                        arrangement0();
                    }

                })
            })

            $("".layui-upload-list"").on(""mouseleave"", "".img_item"", function () {
                $(this).find(""i"").addClass(""hide"")
            })

            function modelList() {
                $(""#selModel"").html("""")
                $.get('/Chat/Chat/ModelList', function (res) {

                    $.each(res.data, function (index, item) {
                        if (index == 0)
                            $(""#selModel"").append(`<option selected value='${item.value}'>${item.txt}</option>`);
                        else
                            $(""#selMod");
                WriteLiteral(@"el"").append(`<option value='${item.value}'>${item.txt}</option>`);
                    })
                    form.render();
                })
            }
            modelList();




            //查询结果表格用的高度  height:""full-"" + table_top_H,
            var table_top_H;
            var leftContentScorll;
            layui.code();

            //用来存放CodeMirror 执行的时候通过索引获取到codemirror，再通过getValue获取值
            var CodeMirrorArray = [];

            // 默认布局
            arrangement0()
            function arrangement0() {
                var winH = $(window).height();
                var addbtnH = $("".create_topic"").outerHeight(true);
                var pic_areaH = $("".pic_area"").outerHeight(true);
                $("".left_content"").css({
                    ""height"": winH - pic_areaH - 120 + ""px"",
                    ""overflow"": ""hidden""
                });
                $("".topic"").css(""height"", winH + ""px"");
                $("".topic_list"").css(""height"", winH - addbtnH - 20 + """);
                WriteLiteral(@"px"");

                // 折叠层
                $("".menu_list"").css(""height"", winH + ""px"");
                $("".menu_right"").css(""height"", winH + ""px"");

            }
            //屏幕改变时
            $(window).resize(function () {
                arrangement0();
            });
            // 自定义布局end


            $(""#addBtn"").on(""click"", function () {

                filesList = [];

                $(""#messgeList"").children().remove();
                $(""#message-to-send"").focus();
                $("".topic_list"").find(""li"").removeClass(""active"");
                themeid = 0;
                var ele = document.getElementById(""left_content"");
                setTimeout(function () {
                    //设置滚动条到最底部
                    ele.scrollTop = 0;
                }, 500);

                $(""#selModel"").removeAttr(""disabled"");
                form.render('select', ""selModelDIV"");
                $(""#message-to-send"").val("""");
            })

            $('#message-to-se");
                WriteLiteral(@"nd').focus(function () {
                $(this).css(""border"", ""2px solid #009688"");
            });

            $('#message-to-send').blur(function () {
                $(this).css(""border"", ""2px solid #f0f0f0"");
            })

            $("".topic_list"").on(""click"", ""li"", function () {

                filesList = [];

                $("".topic_list"").find(""li"").removeClass(""active"");
                $(this).addClass(""active"");
                themeid = $(this).attr(""themeid"");
                modelName = $(this).attr(""model"");
                //  $(""#form_ShowResult"").html('');
                ChatHistroyList(themeid, modelName);
            })

            $(document).ready(function () {
                ChatThemeListList(-1, """");
                $(document).on('click', '#submitBtn', function () {
                    $(this).css(""display"", ""none"");
                    $(""#btnloading"").css(""display"", ""block"");
                    submitBtn();
                });

             ");
                WriteLiteral(@"   // 监听文本框中的按键事件
                $(""#message-to-send"").keypress(function (event) {
                    if (event.which == 13) {
                        submitBtn();
                    }
                });
                createScroll(""topic"", "".topic_list"")
                //createScroll(""l_content"", "".left_content"")
                var _name = document.querySelector("".left_content"");
                leftContentScorll = new PerfectScrollbar(_name, {
                    wheelspeed: 1,
                    wheelPropagation: false,
                    minScrollbarLength: 2,
                    scrollingThreshold: 1000
                });
            });

            // 增加滚动区封装
            function createScroll(name, scroll_box) {
                var _name = name
                _name = document.querySelector(scroll_box);
                var new_name = new PerfectScrollbar(_name, {
                    wheelspeed: 1,
                    wheelPropagation: false,
                    minScro");
                WriteLiteral(@"llbarLength: 2,
                    scrollingThreshold: 1000
                });

                if (scroll_box == "".topic_list"") {
                    _name.addEventListener(""ps-y-reach-end"", function () {
                        pageIndex++;
                        ChatThemeListList(-1, """");
                    })
                }
            }

            var pageIndex = 1;
            function ChatThemeListList(Id, modelName) {
                $.ajax({
                    url: '/Chat/Chat/ChatThemeList?page=' + pageIndex,
                    type: 'get',
                    cache: false,
                    dataType: 'json',//返回的数据类型
                    success: function (data) {
                        var getTpl = ChatThemeListModel.innerHTML;
                        laytpl(getTpl).render(data, function (html) {
                            $('#topiclist').html(html);
                        });
                        if (Id > 0) {
                            themeid = Id;
 ");
                WriteLiteral(@"                           $("".topic_list"").find(""li"").removeClass(""active"");
                            $("".topic_list li[themeid='"" + themeid + ""']"").addClass(""active"");

                            if (modelName != '') {
                                $(""#selModel"").val(modelName);
                                $(""#selModel"").attr(""disabled"", ""disabled"");
                                form.render('select', ""selModelDIV"");
                            }
                        }
                    },
                    error: function () {
                        alert(""error"");
                    }
                })
            }

            function ChatHistroyList(themeid, modelName) {

                if (modelName != '') {
                    $(""#selModel"").val(modelName);
                    $(""#selModel"").attr(""disabled"", ""disabled"");
                    form.render('select', ""selModelDIV"");
                }


                $.ajax({
                    url: '/Ch");
                WriteLiteral(@"at/Chat/ChatHistroyList?ThemeId=' + themeid,
                    type: 'get',
                    cache: false,
                    dataType: 'json',//返回的数据类型
                    success: function (data) {
                        var getTpl = ChatMsgListModel.innerHTML;
                        laytpl(getTpl).render(data, function (html) {
                            $('#messgeList').html(html);
                        });



                        var ul = document.querySelector('#messgeList'); //获取id为'my-ul'的ul元素
                        var liList = ul.getElementsByClassName('clearfix');
                        resetHistoryscrollTop(1);
                    },
                    error: function () {
                        alert(""error"");
                    }
                })
            }

            function submitBtn() {
                //   $('#form_ShowResult').html('');//再次提问清空结果展示区域
                var prompt = $.trim($(""#message-to-send"").val());
                if (prom");
                WriteLiteral(@"pt == """") {
                    layer.msg(""请输入问题"");
                    $(""#message-to-send"").focus();
                    return;
                }

                var getTplreq = RequestModel.innerHTML;


                resetHistoryscrollTop(0);
                if (filesList.length > 0) {
                    var newData = { ""content"": prompt, ""Img"": filesList };
                    laytpl(getTplreq).render(newData, function (html) {
                        $('#messgeList').append(html);
                    });
                    $('#demo2').html("""");
                    arrangement0();
                }
                else {
                    var newData = { ""content"": prompt, ""Img"": [] };
                    laytpl(getTplreq).render(newData, function (html) {
                        $('#messgeList').append(html);
                    });
                }

                $(""#message-to-send"").val('');

                var i = 0;
                var allData = """";
         ");
                WriteLiteral(@"       var modelName = $(""#selModel"").val();
                var newPath = filesList.join('$$$');
                var source = new EventSource('/Chat/Chat/GetChatStreamAnswer?ThemeId=' + themeid + ""&modelType="" + modelName + ""&prompt="" + encodeURIComponent(prompt) + ""&img="" + newPath);
                source.onmessage = function (event) {
                    var result = JSON.parse(event.data);
                    if (result.okMsg) {

                        if (i == 0) {

                            filesList = [];

                            var getTplreq = ResponseModelStream.innerHTML;
                            laytpl(getTplreq).render(prompt, function (html) {
                                $('#messgeList').append(html);
                            });
                        }
                        allData += result.data;
                        if (result.isThink == 1) {
                            //layui-elem-quote
                            $("".clearfix[responseCurrent='1']");
                WriteLiteral(@" .think_wrap"").show();
                            if (allData.indexOf(""</think>"") >= 0) {
                                if (result.data.indexOf(""</think>"") >= 0) {
                                    var thisallData = allData.replace(""<think>"", """").replace(""</think>"", """")
                                    console.log(thisallData);
                                    var htmlContent = marked.parse(thisallData);
                                    $("".clearfix[responseCurrent='1'] .thinktips"").html(""已深度思考"");
                                    $("".clearfix[responseCurrent='1'] .layui-elem-quote"").html(htmlContent);
                                }
                                else {
                                    var thisallData = allData.replace(/<think>[\s\S]*?<\/think>/g, '');
                                    var htmlContent = marked.parse(thisallData);
                                    console.log(""33"" + thisallData + ""&&&"" + htmlContent);
                                    ");
                WriteLiteral(@"$("".clearfix[responseCurrent='1'] .message_content_txt"").html(htmlContent);
                                }
                            }
                            else {
                                var thisallData = allData.replace(""<think>"", """");
                                var htmlContent = marked.parse(thisallData);
                                $("".clearfix[responseCurrent='1'] .layui-elem-quote"").html(htmlContent);
                            }

                        }
                        else {
                            $("".clearfix[responseCurrent='1'] .think_wrap"").hide();
                            var htmlContent = marked.parse(allData);
                            $("".clearfix[responseCurrent='1'] .message_content_txt"").html(htmlContent);
                        }
                        i = i + 1;
                    }
                    else {
                        layer.msg(result.errorMsg);
                        $("".clearfix[responseCurrent='1']"")");
                WriteLiteral(@".removeAttr(""responseCurrent"");
                        source.close();
                    }
                    resetHistoryscrollTop(0);
                };

                source.addEventListener('end', function (event) {
                    $(""#submitBtn"").css(""display"", ""block"");
                    $(""#btnloading"").css(""display"", ""none"");
                    var result = JSON.parse(event.data);
                    if (result.okMsg) {

                        ChatThemeListList(result.themeId, modelName);
                        // var getTplreq = ResponseModel.innerHTML;
                        // laytpl(getTplreq).render(result.content, function (html) {
                        //     $("".clearfix[responseCurrent='1']"").html(html);
                        // });
                        // layui.each(result.newContent, function (idx, item) {


                        // });
                        $("".clearfix[responseCurrent='1']"").removeAttr(""responseCurrent"");

                ");
                WriteLiteral(@"    }
                    else {
                        layer.msg(result.errorMsg);
                    }

                    // 结束事件源连接
                    source.close();
                }, false);

                source.onerror = function (event) {

                    filesList = [];

                    $("".clearfix[responseCurrent='1']"").removeAttr(""responseCurrent"");
                    source.close();
                };
            }

            //设置Y轴位置
            function resetHistoryscrollTop(update) {
                var ele = document.getElementById(""left_content"");
                if (update == 1) {
                    leftContentScorll.update();
                }
                if (ele.scrollHeight > ele.clientHeight) {
                    setTimeout(function () {
                        //设置滚动条到最底部
                        ele.scrollTop = ele.scrollHeight;
                    }, 500);
                }
            }

            // 显示话题
            $("".sho");
                WriteLiteral(@"w_menu_btn"").on(""click"", function () {
                $("".menu_wrap"").css(""display"", ""block"")
                $("".menu_list"").addClass(""show_menu_list"")
            })

            // 2023/3/29  话题列表增加编辑和删除功能
            var theme = {};
            $("".topic_list"").on(""click"", ""li"", function () {
                $("".topic_list"").find(""li"").css(""background-color"", ""#fff"");
                $("".topic_list"").find(""li"").removeClass(""active"");
                $("".topic_list"").find("".operating_btn"").css(""display"", ""none"");
                $("".topic_list"").find("".ok_close"").remove();
                $("".topic_list"").find(""input"").css(""border"", ""none"");
                $(this).addClass(""active"");
                $(this).find("".operating_btn"").css(""display"", ""block"");
                theme[""themeid""] = $(this).attr(""themeid"");
                theme[""themeval""] = $(this).find(""input"").val();
            });

            $("".topic_item"").hover(function () {
                var isact = $(this).hasClas");
                WriteLiteral(@"s(""active"")
                if (!isact) {
                    $(this).css(""background-color"", ""#eee"")
                }
            }, function () {
                var isact = $(this).hasClass(""active"")
                if (isact) {
                    $(this).css(""background-color"", ""#009688"")
                } else {
                    $(this).css(""background-color"", ""#fff"")
                }
            })

            var btn_type = 0;
            var title_text = """";

            function showOkClose(e, type) {
                let html = $(""#okClose"").html();
                let _that = $(e.target);
                $(_that).closest("".topic_item"").find("".operating_btn"").css(""display"", ""none"");
                $(_that).closest("".topic_item"").append(html);
                btn_type = type
            }

            $(document).on(""click"", "".layui-icon-edit"", function (enent) {
                event.stopPropagation();
                showOkClose(enent, 1);
                let _that");
                WriteLiteral(@" = enent.target;
                title_text = $(_that).closest("".topic_item"").find("".title input"").val();
                $(_that).closest("".topic_item"").find("".title input"").removeAttr(""readonly"");
                $(_that).closest("".topic_item"").find("".title input"").css(""border"", ""1px solid #ccc"");
            })

            $(document).on(""click"", "".layui-icon-delete"", function (enent) {
                event.stopPropagation();
                showOkClose(enent, 2);
            })
            $(document).on(""click"", "".layui-input-inline input"", function (e) {
                e.stopPropagation();
                var isinput = $(this).closest("".topic_item"").find("".title"").find(""input"").attr(""readonly"");
                if (isinput == undefined) {
                    $("".topic_list"").find(""input"").attr(""readonly"", ""true"");
                    $(this).closest("".topic_item"").find("".title input"").removeAttr(""readonly"");
                    $("".topic_list"").find("".operating_btn"").css(""display"", ""n");
                WriteLiteral(@"one"");
                    var html = $(""#okClose"").html();
                    $(this).closest("".topic_item"").append(html);
                    $(this).closest("".topic_item"").find("".title input"").css(""border"", ""1px solid #ccc"");
                    var inputval = $(this).val();
                }
            })


            $(document).on(""click"", '.ok_close button[id=""ok""]', function (enent) {
                event.stopPropagation()
                if (btn_type == 1) {
                    $("".topic_list"").find("".ok_close"").remove();
                    $("".active"").find("".operating_btn"").css(""display"", ""block"");
                    $("".topic_list"").find(""input"").attr(""readonly"", ""true"");
                    $("".topic_list"").find(""input"").css(""border"", ""none"");

                    $.ajax({
                        url: '/Chat/Chat/EditTheme',
                        type: ""post"",
                        data: { ""ThemeId"": theme.themeid, ""Theme"": theme.themeval },
                        ");
                WriteLiteral(@"datatype: 'json',
                        success: function (result) {
                            layer.msg(result.msg);
                        }, error: function () {
                            layer.msg(""获取失败！"");
                        }
                    });

                } else if (btn_type == 2) {
                    $(this).closest("".topic_item"").remove();
                    btn_type = 0;
                    layer.confirm('确定要删除吗？数据将无法恢复。', {
                        title: '',
                        btn: ['确定', '取消'], //按钮
                        resize: false
                    }, function (index) {
                        $.ajax({
                            url: '/Chat/Chat/DelTheme',
                            type: ""post"",
                            data: { ""ThemeId"": theme.themeid },
                            datatype: 'json',
                            success: function (result) {
                                if (result.status) {
                        ");
                WriteLiteral(@"            layer.msg(result.msg);
                                    ChatThemeListList(-1, """");
                                    $(""#addBtn"").trigger(""click"");
                                }
                                else {
                                    layer.msg(result.msg);
                                }
                            }, error: function () {
                                layer.msg(""获取失败！"");
                            }
                        });
                        layer.close(index);
                    });

                }
            })
            $(document).on(""click"", '.ok_close button[id=""close""]', function (enent) {
                event.stopPropagation()
                let _that = enent.target;
                $("".topic_list"").find("".ok_close"").remove();
                $("".active"").find("".operating_btn"").css(""display"", ""block"");
                if (btn_type == 1) {
                    $(_that).closest("".topic_item"").find("".titl");
                WriteLiteral(@"e"").find(""input"").attr(""value"", title_text)
                    $("".topic_item"").find(""input"").attr(""readonly"");
                    $("".topic_item"").find(""input"").css(""border"", ""none"");
                };
            })

            // 代码折叠
            var flage = 0;

            $(document).on(""click"", '.shrinkBtn', function () {
                if (flage % 2 == 0) {
                    $(this).html('<i class=""layui-icon layui-icon-up""></i> 隐藏代码');
                } else {
                    $(this).html('<i class=""layui-icon layui-icon-down""></i> 查看代码');
                };
                $(this).parent().parent().parent().toggleClass(""code_shrink"");
                flage++;
            })



            $('#picArea').on('click', 'img', function () {
                previewImage(this);
            });

            $('#messgeList').on('click', 'img', function () {
                previewImage(this);
            });
            // 图片预览函数
            function previewImage(imgObj)");
                WriteLiteral(@" {
                var src = $(imgObj).attr('src');
                layer.open({
                    type: 1,
                    title: '图片预览',
                    skin: 'layui-layer-rim', // 加上边框
                    area: ['80%', '80%'], // 宽高
                    content: '<img src=""' + src + '"" alt=""点击图片关闭"" style=""max-width: 100%; max-height: 80vh;"">'
                });
            }



            //深度思考折叠

            $(""#messgeList"").on(""click"", "".think_button"", function () {

                var isHide = $(this).siblings().hasClass(""hide"");
                if (!isHide) {
                    $(this).siblings().addClass(""hide"")
                    $(this).find("".collapse_icon i"").removeClass(""layui-icon-up"");
                    $(this).find("".collapse_icon i"").addClass(""layui-icon-down"");
                } else {
                    $(this).siblings().removeClass(""hide"")
                    $(this).find("".collapse_icon i"").addClass(""layui-icon-up"");
                    $(this).");
                WriteLiteral(@"find("".collapse_icon i"").removeClass(""layui-icon-down"");
                }




            })



        });
    </script>

    <!-- 提问模板 -->
    <script id=""RequestModel"" type=""text/html"">
        <li class=""clearfix"" requestDel=""1"">
            <div class=""message other-message"">
                <div class=""message-data"">");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("img", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "1fa75511ab55a4b90d256ecc6dbf4c53c6635ea333abe752bf82d3d6a2c037df60969", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"</div>
                <div>
                    <p>{{d.content}}</p>
                    {{# if(d.Img.length > 0) {}}
                    <div class=""Request_img_group"">
                        {{#   layui.each(d.Img, function(index, item){}}
                        <div class=""Request_img_item""> <img class=""layui-upload-img"" src=""{{ item }}"" /></div>
                        {{#  })}}
                    </div>
                    {{# } }}
                </div>
            </div>
        </li>
    </script>

    <!-- 回复模板(stream) -->
    <script id=""ResponseModelStream"" type=""text/html"">
        <li class=""clearfix"" responseCurrent=""1"">
            <div class=""message my-message"">
                <div class=""message-data"">");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("img", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "1fa75511ab55a4b90d256ecc6dbf4c53c6635ea333abe752bf82d3d6a2c037df62940", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"</div>
                <div class=""message_content"">
                    <div class=""think_wrap"">
                        <div class=""think_button"">
                            <div class=""think_time"">
                                <i class=""layui-icon layui-icon-component""></i>
                                <span class=""thinktips"">深度思考中</span>
                            </div>
                            <div class=""collapse_icon"">
                                <i class=""layui-icon layui-icon-up""></i>
                            </div>

                        </div>
                        <div class=""shink_content"">
                            <div class=""layui-elem-quote"">

                            </div>
                        </div>


                    </div>
                    <div class=""message_content_txt"">

                    </div>
                </div>
            </div>
        </li>
    </script>

    <!-- 回复模板 -->
    <script id=""ResponseModel"" typ");
                WriteLiteral("e=\"text/html\">\r\n        <div class=\"message my-message\">\r\n            <div class=\"message-data a_bg\">");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("img", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "1fa75511ab55a4b90d256ecc6dbf4c53c6635ea333abe752bf82d3d6a2c037df65332", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"</div>
            <div class=""message_content"">



                {{#  var htmlContent = marked.parse(d)}}
                {{htmlContent}}
                {{# }}
            </div>
        </div>
    </script>

    <!-- //主题栏模板 -->
    <script id=""ChatThemeListModel"" type=""text/html"">
        {{#  if(d.data.length > 0){ }}
        {{#  layui.each(d.data, function(index, item){ }}
        <li class=""topic_item"" themeid=""{{item.id}}"" model=""{{item.modelName}}"">
            <div class=""topic_item_text"">
                <i class=""layui-icon layui-icon-dialogue""></i>
                <div class=""title"">
                    <div class=""layui-input-inline"">
                        <input type=""text"" name=""title"" autocomplete=""off"" value=""{{ item.theme }}""
                               class=""layui-input topic_theme"" readonly>
                    </div>
                </div>
            </div>
            <!-- 编辑和删除 -->
            <div class=""operating_btn layui-btn-group"">
           ");
                WriteLiteral(@"     <button type=""button"" class=""layui-btn layui-btn-primary layui-btn-sm"">
                    <i class=""layui-icon layui-icon-edit""></i>
                </button>
                <button type=""button"" class=""layui-btn layui-btn-primary layui-btn-sm"" style=""margin-left:0"">
                    <i class=""layui-icon layui-icon-delete""></i>
                </button>
            </div>
        </li>
        {{#  }); }}
        {{#  } }}
    </script>

    <!-- //消息栏模板 -->
    <script id=""ChatMsgListModel"" type=""text/html"">
        {{#  if(d.data.length > 0){ }}
        {{#  layui.each(d.data, function(index, item){ }}
        <li class=""clearfix"">
            {{# if(item.chatType==""request""){  }}
            <div class=""message other-message"">
                <div class=""message-data"">
                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("img", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "1fa75511ab55a4b90d256ecc6dbf4c53c6635ea333abe752bf82d3d6a2c037df68482", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
                </div>
                <div>
                    <p>{{item.content}}</p>
                    <div class=""Request_img_group"">
                        {{# if(item.img){}}
                        {{# layui.each(item.img,function(Imgindex,ImgItem){}}
                        <div class=""Request_img_item""> <img class=""layui-upload-img"" src=""{{ ImgItem }}"" /></div>
                        {{# })}}}
                    </div>
                </div>
            </div>
            {{#  } else { }}
            <div class=""message my-message"">
                <div class=""message-data"">");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("img", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "1fa75511ab55a4b90d256ecc6dbf4c53c6635ea333abe752bf82d3d6a2c037df70303", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("</div>\r\n                <div class=\"message_content\">\r\n                    <div class=\"think_wrap\"");
                BeginWriteAttribute("{{item.isThink", " {{item.isThink=", 42797, "", 42813, 0);
                EndWriteAttribute();
                WriteLiteral(@"=1?"""":""style='display:none;'""}}>
                        <div class=""think_button"">
                            <div class=""think_time"">
                                <i class=""layui-icon layui-icon-component""></i>
                                <span>已深度思考</span>
                            </div>
                            <div class=""collapse_icon"">
                                <i class=""layui-icon layui-icon-up""></i>
                            </div>
                        </div>
                        <div class=""shink_content hide"">
                            <div class=""layui-elem-quote"">
                                {{#  var htmlContent = marked.parse(item.thinkContent)}}
                                {{htmlContent}}
                                {{# }}
                            </div>
                        </div>
                    </div>
                    <div class=""message_content_txt"">
                        {{#  var htmlContent = marked.parse(item.con");
                WriteLiteral(@"tent)}}
                        {{htmlContent}}
                        {{# }}
                    </div>
                </div>
            </div>
            {{#  } }}
        </li>
        {{#  }); }}
        {{#  } }}
    </script>

    <!-- 选择确定或取消 -->
    <script id=""okClose"" type=""text/x-handlebars-template"">
        <div class=""ok_close"">
            <!-- 确认和取消 -->
            <button type=""button"" class=""layui-btn layui-btn-primary layui-btn-sm"" id=""ok"">
                <i class=""layui-icon layui-icon-ok""></i>
            </button>
            <button type=""button"" class=""layui-btn layui-btn-primary layui-btn-sm"" id=""close"" style=""margin-left:0"">
                <i class=""layui-icon layui-icon-close""></i>
            </button>
        </div>
    </script>

");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>\r\n\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
