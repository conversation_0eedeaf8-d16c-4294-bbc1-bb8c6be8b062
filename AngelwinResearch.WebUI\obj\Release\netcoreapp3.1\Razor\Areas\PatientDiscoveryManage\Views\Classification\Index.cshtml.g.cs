#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\PatientDiscoveryManage\Views\Classification\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "389e5df5cec1135397e3d0705b96ecc328464b8b08497cfe619d4bfa410a8201"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_PatientDiscoveryManage_Views_Classification_Index), @"mvc.1.0.view", @"/Areas/PatientDiscoveryManage/Views/Classification/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"389e5df5cec1135397e3d0705b96ecc328464b8b08497cfe619d4bfa410a8201", @"/Areas/PatientDiscoveryManage/Views/Classification/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_PatientDiscoveryManage_Views_Classification_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-1.10.2.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\PatientDiscoveryManage\Views\Classification\Index.cshtml"
  
    ViewBag.Title = "分类管理";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"en\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "389e5df5cec1135397e3d0705b96ecc328464b8b08497cfe619d4bfa410a82016460", async() => {
                WriteLiteral(@"
    <meta charset=""UTF-8"">
    <meta name=""viewport""
          content=""width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"">
    <meta http-equiv=""X-UA-Compatible"" content=""ie=edge"">
    <title>分类管理</title>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "389e5df5cec1135397e3d0705b96ecc328464b8b08497cfe619d4bfa410a82017010", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "389e5df5cec1135397e3d0705b96ecc328464b8b08497cfe619d4bfa410a82018212", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "389e5df5cec1135397e3d0705b96ecc328464b8b08497cfe619d4bfa410a82019414", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "389e5df5cec1135397e3d0705b96ecc328464b8b08497cfe619d4bfa410a820110537", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        /*弹窗*/
        .window_wrap {
            padding: 15px;
        }

        #form_window .layui-form-label {
            width: 100px;
        }

        #form_window .layui-form-val {
            padding: 9px 15px;
        }

        #form_window .layui-form-item {
            margin-bottom: 0;
        }

        .layui-form-item {
            margin-bottom: 5px;
        }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "389e5df5cec1135397e3d0705b96ecc328464b8b08497cfe619d4bfa410a820112804", async() => {
                WriteLiteral(@"
    <div class=""layui-col-md12"">
        <div class=""layui-card"">
            <div class=""layui-card-header"">
                <div class=""layui-inline"">
                    <label class=""layui-form-label"">分类管理</label>
                </div>
            </div>
            <div class=""layui-card-body"">
                <table class=""layui-table layui-form"" id=""classificationTreeTb""></table>
                <script type=""text/html"" id=""tableBar1"">
                    <a class=""layui-btn  layui-btn-xs"" lay-event=""add""><i class=""layui-icon layui-icon-add-circle""></i>新增</a>
                    <a class=""layui-btn layui-btn-normal layui-btn-xs"" lay-event=""edit""><i class=""layui-icon layui-icon-edit""></i>编辑</a>
                    <a class=""layui-btn layui-btn-danger layui-btn-xs"" lay-event=""del""><i class=""layui-icon layui-icon-delete""></i>删除</a>
                </script>
            </div>
        </div>
        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "389e5df5cec1135397e3d0705b96ecc328464b8b08497cfe619d4bfa410a820114057", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "389e5df5cec1135397e3d0705b96ecc328464b8b08497cfe619d4bfa410a820115185", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"

        <script>
            layui.config({
                base: '/layuiadmin/layuiextend/'
            }).use(['element', 'layer', 'treeTable', 'table', 'form'], function () {
                var element = layui.element
                    , layer = layui.layer
                    , table = layui.table//表格
                    , treeTable = layui.treeTable //表格
                    , $ = layui.$
                    , form = layui.form;
                ;
                var windowsIndex;
                var selectTableRowData = [];
                var url = '';

                // 渲染表格
                var classificationTreeTb = treeTable.render({
                    elem: '#classificationTreeTb',
                    url: ""/PatientDiscoveryManage/Classification/ClassificationList"",
                    tree: {
                        idName: 'Id',
                        pidName: 'parentId',
                        haveChildName: 'haveChildName',
                        iconIndex: 1,  ");
                WriteLiteral(@"  // 折叠图标显示在第几列
                        isPidData: true,  // 是否是pid形式数据
                        arrowType: 'arrow2',
                        getIcon: 'ew-tree-icon-style2'
                    },
                    height: 'full-80',
                    cols: [[
                        { type: 'numbers', fixed: 'left' },
                        { field: 'Name', title: '名称', fixed: 'left', minWidth: 210 },
                        { field: 'Type', title: '类型', Width: 70 },
                        { field: 'Remark', title: '备注', Width: 100 },
                        { field: 'CreateUser', title: '创建用户', Width: 50 },
                        { field: 'CreateTime', title: '创建时间', Width: 130 },
                        { fixed: 'right', align: 'center', title: '操作', minWidth: 300, toolbar: '#tableBar1' }
                    ]],
                    done: function () {
                        classificationTreeTb.expand(1);
                    }
                });
                // 工具列点击事件
       ");
                WriteLiteral(@"         treeTable.on('tool(classificationTreeTb)', function (obj) {
                    var event = obj.event;
                    var data = obj.data;
                    if (data.haveChildName) {
                        $(""#parentId"").attr(""haveChildName"", ""1"");
                    }
                    else {
                        $(""#parentId"").attr(""haveChildName"", ""0"");
                    }
                    if (event === 'add') {
                        $(""#fm"")[0].reset();
                        $('#Id').val(0);
                        $('#parentId').val(data.Id);
                        $(""#btn_reset"").show();
                        $('#Type').html("""");
                        if (data.Type == ""根模块"") {
                            var option = $(""<option>"").val('知识库目录').text('知识库目录');
                            $(""#Type"").append(option);
                        }
                        else if (data.Type == ""知识库目录"") {
                            var option = $(""<option>"")");
                WriteLiteral(@".val('知识库').text('知识库');
                            $(""#Type"").append(option);
                        }
                        else if (data.Type == ""知识库"") {
                            layer.msg('不能在知识库下添加子级。');
                            return false;
                        }
                        $(""#Type"").get(0).selectedIndex = 0;
                        form.render('select');
                        $(""#parentId"").attr(""reload"", ""0"");
                        windowsIndex = layer.open({
                            type: 1,
                            title: '添加【' + data.Name + '】子模块',
                            area: '550px',
                            maxHeight: 650,
                            content: $('#form_window_classification')
                        });
                        url = '/PatientDiscoveryManage/Classification/CreateClassification';
                    }
                    else if (event === 'edit') {
                        $('#Type').html("""");
     ");
                WriteLiteral(@"                   var option = $(""<option>"").val(data.Type).text(data.Type);
                        $(""#Type"").append(option);
                        form.render('select');
                        $(""#btn_reset"").hide();
                        form.val('fm', data);
                        $(""#parentId"").attr(""reload"", ""1"");
                        windowsIndex = layer.open({
                            type: 1,
                            title: '修改【' + data.Name + '】',
                            area: '600px',
                            maxHeight: 650,
                            content: $('#form_window_classification')
                        });
                        url = '/PatientDiscoveryManage/Classification/EditClassification';
                    }
                    else if (event === 'del') {
                        layer.confirm('确定要删除【' + data.Name + '】模块吗？', {
                            title: '',
                            btn: ['确定', '取消'], //按钮
                 ");
                WriteLiteral(@"           resize: false
                        }, function (index) {
                            $.post('/PatientDiscoveryManage/Classification/DelClassification', { id: data.Id, pid: data.parentId }, function (result) {
                                if (result.code == 0) {
                                    layer.msg(result.msg);
                                    currentIndex = -1;
                                    if (data.parentId === '' || data.parentId == undefined || data.parentId === 0) {
                                        classificationTreeTb.reload({ url: ""/PatientDiscoveryManage/Classification/ClassificationList"", where: { 'pid': -1 } }); //重载表格
                                    }
                                    else {
                                        classificationTreeTb.refresh(data.parentId); //重载表格
                                    }
                                } else {
                                    layer.msg(result.errorMsg);
                ");
                WriteLiteral(@"                }
                            }, 'json');
                            layer.close(index);
                        });
                    }
                });

                $(document).ready(function () {


                });

                //自定义验证规则
                form.verify({
                    intVer: [
                        /^[1-9][0-9]{0,}$/
                        , '请输入正整数'
                    ]
                });

                //监听提交
                form.on('submit(ClassificationSubmit)', function (data) {
                    console.log(data);
                    var indes = layer.load(1);
                    //提交 Ajax 成功后，关闭当前弹层并重载表格
                    $.ajax({
                        url: url,
                        type: ""post"",
                        data: { 'node': data.field },
                        datatype: 'json',
                        success: function (result) {
                            if (result.code == 0) {
      ");
                WriteLiteral(@"                          debugger;
                                layer.msg(result.msg);
                                layer.close(windowsIndex);//关闭弹出层
                                if ($(""#parentId"").attr(""reload"") == ""1"") {
                                    classificationTreeTb.refresh(parseInt(data.field.parentId)); //重载表格
                                }
                                else {
                                    if ($(""#parentId"").attr(""haveChildName"") == ""1"") {
                                        classificationTreeTb.refresh(parseInt(data.field.parentId)); //重载表格
                                    }
                                    else {
                                        classificationTreeTb.reload({ url: ""/PatientDiscoveryManage/Classification/ClassificationList"", where: { 'pid': -1 } }); //重载表格
                                    }
                                }
                            }
                            else {
                 ");
                WriteLiteral(@"               layer.msg(result.errorMsg);
                            }
                            layer.close(indes);
                        }, error: function (res) {
                            layer.msg(""加载统计信息错误："" + res.responseText);
                            layer.close(indes);
                        }
                    });
                    return false;
                });
            });
        </script>
    </div>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"

<!-- 查看角色列表 -->
<div class=""window_wrap  layui-form"" id=""form_window"" style=""display: none"">
    <div class=""layui-card"">
        <div class=""layui-card-body"">
            <table id=""tablelist_Role"" lay-filter=""tablelist_Role""></table>
        </div>
    </div>
</div>

<!--新增/修改模块-->
<div class=""window_wrap"" id=""form_window_classification"" style=""display: none;"">
    <form class=""layui-form"" lay-filter=""fm"" id=""fm""");
            BeginWriteAttribute("action", " action=\"", 11207, "\"", 11216, 0);
            EndWriteAttribute();
            WriteLiteral(@">
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">分类名称:</label>
            <div class=""layui-input-block"">
                <input type=""text"" name=""Id"" id=""Id"" style=""display:none;"" />
                <input type=""text"" name=""parentId"" id=""parentId"" haveChildName=""0"" reload=""0"" style=""display:none;"" />
                <input type=""text"" name=""Name"" id=""Name"" required lay-verify=""required"" autocomplete=""off"" class=""layui-input"">
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">类型:</label>
            <div class=""layui-input-block"">
                <select id=""Type"" name=""Type"" lay-filter=""Type"">
                    <option value=""知识库目录"" selected=""selected"">知识库目录</option>
                </select>
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">备注:</label>
            <div class=""layui-input-block"">
                <textarea");
            WriteLiteral(@" name=""Remark"" id=""Remark"" class=""layui-textarea"" style=""resize: none""></textarea>
            </div>
        </div>
        <div class=""layui-form-item"">
            <div class=""layui-input-block"">
                <button type=""submit"" class=""layui-btn"" lay-submit lay-filter=""ClassificationSubmit"">提交</button>
                <button type=""reset"" class=""layui-btn layui-btn-primary"" id=""btn_reset"">重置</button>
            </div>
        </div>
    </form>
</div>
</html>
");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
