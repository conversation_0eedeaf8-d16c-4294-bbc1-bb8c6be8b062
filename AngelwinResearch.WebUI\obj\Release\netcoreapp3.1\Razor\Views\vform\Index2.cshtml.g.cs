#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\vform\Index2.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "dce6d6f9deefbd32384c6065a2656ef578264f3d51369da8b9d7995252e3e80e"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_vform_Index2), @"mvc.1.0.view", @"/Views/vform/Index2.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI

#nullable disable
    ;
#nullable restore
#line 2 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"dce6d6f9deefbd32384c6065a2656ef578264f3d51369da8b9d7995252e3e80e", @"/Views/vform/Index2.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"a5bcd83cf27c8ffb8baf597d24314352bf7b52b5a9bb5ee83e83e9d0089a628e", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_vform_Index2 : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css?v1.0"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/vform/index1.min.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/vform/VFormDesigner.css?t=20210730"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/vform/vue.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/vform/element-ui_2.15.7.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/vform/VFormDesigner.umd.min.js?t=20210730"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\vform\Index2.cshtml"
  
    ViewBag.Title = "科研AIDemo";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"en\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "dce6d6f9deefbd32384c6065a2656ef578264f3d51369da8b9d7995252e3e80e7541", async() => {
                WriteLiteral(@"
    <meta charset=""UTF-8"">
    <meta name=""viewport""
          content=""width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"">
    <meta http-equiv=""X-UA-Compatible"" content=""ie=edge"">
    <title>科研AIDemo</title>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "dce6d6f9deefbd32384c6065a2656ef578264f3d51369da8b9d7995252e3e80e8095", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "dce6d6f9deefbd32384c6065a2656ef578264f3d51369da8b9d7995252e3e80e9297", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "dce6d6f9deefbd32384c6065a2656ef578264f3d51369da8b9d7995252e3e80e10499", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "dce6d6f9deefbd32384c6065a2656ef578264f3d51369da8b9d7995252e3e80e11703", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(" <!-- 根据Web服务器或CDN路径修改 -->\r\n");
                WriteLiteral(@"    <style>
        /*弹窗*/
        .window_wrap {
            padding: 15px;
        }

        userList_window .layui-form-label {
            width: 100px;
        }

        userList_window .layui-form-val {
            padding: 9px 15px;
        }

        userList_window .layui-form-item {
            margin-bottom: 0;
        }

        userList_window .layui-form-item1 {
            width: 100px;
            height: 20px;
            border: 1px solid red;
            display: inline-block;
        }

        .min_width {
            width: 300px !important;
        }

        .min_top {
            margin-top: 8px;
        }

        .ue-container {
            width: 100%;
            margin: 0 auto;
            margin-top: 3%;
            background: #fff;
        }

        #timeDiv .layui-inline {
            margin-bottom: 5px;
            margin-right: 0px;
        }

        .layui-form-label.required:after {
            content: ' *';
            ");
                WriteLiteral("color: red;\r\n        }\r\n\r\n        .layui-table-body {\r\n            overflow-y: scroll;\r\n        }\r\n\r\n        .form_border {\r\n            border: 1px solid #D2D2D2;\r\n            border-radius: 2px;\r\n        }\r\n    </style>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "dce6d6f9deefbd32384c6065a2656ef578264f3d51369da8b9d7995252e3e80e14983", async() => {
                WriteLiteral("\r\n    <div class=\"layui-col-md12\">\r\n        <div class=\"layui-card\">\r\n");
#nullable restore
#line 83 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\vform\Index2.cshtml"
              
                string parameterValue = ViewBag.hzxm as string;
            

#line default
#line hidden
#nullable disable

                WriteLiteral(@"            <div class=""layui-form layui-card-header "" style=""padding:15px;"">
                <div class=""layui-form-item"">

                    <div class=""layui-inline"">
                        <div class=""layui-input-block"">
                            <button type=""button"" class=""layui-btn layui-btn-normal fr"" id=""add"">设计表单</button>
                            <button type=""button"" class=""layui-btn  fr"" id=""send"">转写病历文书</button>
                            <button type=""button"" class=""layui-btn  fr"" id=""reset"">重写</button>
");
#nullable restore
#line 94 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\vform\Index2.cshtml"
                             if (!string.IsNullOrEmpty(parameterValue))
                            {

                             


#line default
#line hidden
#nullable disable

                WriteLiteral("                                    <button type=\"submit\" class=\"layui-btn \" lay-submit lay-filter=\"formDemo\">保存</button>\r\n");
#nullable restore
#line 100 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\vform\Index2.cshtml"
                              

                            }

#line default
#line hidden
#nullable disable

                WriteLiteral("                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n          \r\n\r\n");
#nullable restore
#line 109 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\vform\Index2.cshtml"
             if (!string.IsNullOrEmpty(parameterValue))
            {


#line default
#line hidden
#nullable disable

                WriteLiteral(@"                <fieldset class=""layui-elem-field"" style=""margin-left:15px;"">
                    <legend>患者信息</legend>
                    <div class=""layui-field-box"">
                        <div class=""layui-form-item"">

                            <input type=""hidden"" id=""patid""");
                BeginWriteAttribute("value", " value=\"", 3524, "\"", 3546, 1);
                WriteAttributeValue("", 3532, 
#nullable restore
#line 117 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\vform\Index2.cshtml"
                                                                    ViewBag.patid

#line default
#line hidden
#nullable disable
                , 3532, 14, false);
                EndWriteAttribute();
                WriteLiteral(@">
                            <div class=""layui-inline"">
                                <label class=""layui-form-label"">患者姓名：</label>
                                <div class=""layui-input-inline"" style=""margin-top:9px"">
                                    ");
                Write(
#nullable restore
#line 121 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\vform\Index2.cshtml"
                                     ViewBag.hzxm

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"
                                </div>
                            </div>

                            <div class=""layui-inline"">
                                <label class=""layui-form-label"">年龄：</label>
                                <div class=""layui-input-inline"" style=""margin-top:10px"">
                                    ");
                Write(
#nullable restore
#line 128 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\vform\Index2.cshtml"
                                     ViewBag.nl

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"
                                </div>
                            </div>

                            <div class=""layui-inline"">
                                <label class=""layui-form-label"">病历号：</label>
                                <div class=""layui-input-inline"" style=""margin-top:11px"">
                                    ");
                Write(
#nullable restore
#line 135 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\vform\Index2.cshtml"
                                     ViewBag.blh

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </fieldset>\r\n");
#nullable restore
#line 141 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\vform\Index2.cshtml"

            }

#line default
#line hidden
#nullable disable

                WriteLiteral(@"            <div class=""layui-card-body"">
                <div class=""layui-row layui-col-space10"" style=""height: 82vh;"">
                    <div class=""layui-col-md6"" style=""background-color: #fff;height: 100%;"">
                        <div class=""form_border"" style=""height: 98%;overflow-y:auto"">



                            <div id=""app"" style=""margin:10px;"">
                                <v-form-render :form-json=""formJson"" :form-data=""formData"" :option-data=""optionData"" ref=""vFormRef"">
                                </v-form-render>
                            </div>
                        </div>
                    </div>
                    <div class=""layui-col-md6"" style=""background-color: #fff;height: 100%;"">
                        <textarea id=""txtWords"" class=""layui-textarea"" style=""height:98%;width:98%;resize:none;line-height:40px;font-size:20px"">");
                Write(
#nullable restore
#line 157 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\vform\Index2.cshtml"
                                                                                                                                                 ViewBag.val

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</textarea>
                    </div>
                </div>

            </div>
          
        </div>

        <script type=""text/javascript"">
            if (!!window.ActiveXObject || ""ActiveXObject"" in window) { //IE load polyfill.js for Promise
                var scriptEle = document.createElement(""script"");
                scriptEle.type = ""text/javascript"";
                scriptEle.src = ""/vform/6.23.0_polyfill.min.js""
                document.body.appendChild(scriptEle)
            }
        </script>
        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "dce6d6f9deefbd32384c6065a2656ef578264f3d51369da8b9d7995252e3e80e22288", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "dce6d6f9deefbd32384c6065a2656ef578264f3d51369da8b9d7995252e3e80e23416", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "dce6d6f9deefbd32384c6065a2656ef578264f3d51369da8b9d7995252e3e80e24544", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(" <!-- 根据Web服务器或CDN路径修改 -->\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "dce6d6f9deefbd32384c6065a2656ef578264f3d51369da8b9d7995252e3e80e25698", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"


        <script>
            layui.config({
                base: '/layuiadmin/layuiextend/'
            }).use(['layer', 'laydate', 'table', 'form'], function () {
                var layer = layui.layer
                    , table = layui.table//表格
                    , laydate = layui.laydate
                    , $ = layui.$
                    , form = layui.form
                var app = new Vue({
                    el: '#app',
                    data: {
                        formJson: {},
                        formData: {},
                        optionData: {}
                    },
                    created() {
                        this.fetchData();
                    },
                    methods: {
                        fetchData() {
                            $.get('/vform/CheckTemplate?type=tgjc', function (res) {

                                if (res.code == 0) {
                                    // console.log(res.data.obj);
                 ");
                WriteLiteral(@"                   app.$refs.vFormRef.setFormJson(res.data.obj);
                                    if (res.data.obj1)
                                        app.$refs.vFormRef.setFormData(JSON.parse(res.data.obj1));
                                    //  console.log(this);
                                }
                            })
                        }
                    }
                });

                $(""#add"").click(function () {

                    layer.open({
                        type: 2,
                        area: ['100%', '100%'],
                        //  content: $('#form_window_template'),
                        content: '/vform/template',
                        fixed: false, // 不固定
                        maxmin: true,
                        title: '创建【现病史】的模板表单',
                        shadeClose: true,
                        btn: ['保存', '关闭'],
                        btnAlign: 'c',
                        btn1: function (index1, layero) {");
                WriteLiteral(@"
                            var iframeWin = window[layero.find('iframe')[0]['name']];
                            // iframeWin.app.$refs.vFormRef.disableForm();
                            var formsetting = iframeWin.app.$refs.VFDesigner.getFormJson();

                            //alert(json);
                            $.post('/vform/SaveTemplate', { ""str"": JSON.stringify(formsetting), ""type"": ""tgjc"" }, function (res) {
                                if (res.code == 0) {
                                    app.$refs.vFormRef.setFormJson(JSON.stringify(formsetting));
                                    layer.close(index1);
                                }
                                else
                                    layer.msg(res.msg);
                            })

                        },
                        btn2: function (index, layero) {

                        },
                        success: function (layero, index) {
                            var ifram");
                WriteLiteral(@"eWin = window[layero.find('iframe')[0]['name']];
                            iframeWin.app.$refs.VFDesigner.clearDesigner();
                            if (app.formJson) {
                                iframeWin.app.$refs.VFDesigner.setFormJson(JSON.stringify(app.formJson))
                            }

                        },

                    })

                })
                var source;
                $(""#send"").click(function () {
                    $(""#txtWords"").val('');
                    if (source) {
                        source.close();
                    }
                    var _isDis = $(this).hasClass(""layui-btn-disabled"")
                    if (_isDis) {
                       
                        return false;

                    }
                    var url = ""/vform/getResult""
                    //var client = new XMLHttpRequest()
                    //client.open(""POST"", url)
                    //client.setRequestHeader('Content-Type'");
                WriteLiteral(@", 'application/json');
                    //client.onprogress = function (progressEvent) {
                    //   // div.innerText = progressEvent.target.responseText
                    //    console.log(progressEvent.target.responseText);

                    //    var data = progressEvent.target.responseText;
                    //    var result = JSON.parse(data);
                    //    if (result.okMsg) {
                    //       var msg = $(""#txtWords"").val()+result.data;
                    //        $(""#txtWords"").val(msg);
                    //    }
                    //}
                    //client.onloadend = function (progressEvent) {
                    //   // div.append(""END"")
                    //   console.log(""END"");
                    //}
                    $(""#send"").addClass(""layui-btn-disabled"")
                    
                    msg();

                })

                $(""#reset"").click(function () {
                    $(""#txtWords"").val(");
                WriteLiteral(@"'');
                    if (source) {
                        source.close();
                    }
                    var _isDis = $(""#send"").hasClass(""layui-btn-disabled"")
                    if (_isDis) {
                        $(""#send"").removeClass(""layui-btn-disabled"")
                    }
                    msg();
                })

                function msg()
                {
                    app.$refs.vFormRef.getFormData().then(function (formData) {
                        // Form Validation OK
                        var data = {};
                        data.type = ""tgjc"";
                        data.str = JSON.stringify(formData);

                        //client.send(JSON.stringify(data))
                        var i = 0;
                         source = new EventSource('/vform/getResult?type=tgjc&str=' + encodeURIComponent(JSON.stringify(data)));
                        source.onmessage = function (event) {
                            var result = JSON.p");
                WriteLiteral(@"arse(event.data);
                            if (result.okMsg) {
                                var btnloading = document.getElementById(""txtWords"");
                                if (i == 0) {
                                    var Info = result.data;
                                    btnloading.value = Info;
                                }
                                else {
                                    var Info = result.data;
                                    btnloading.value += Info;
                                }
                                i = i + 1;
                            }
                            else {
                                layer.msg(result.errorMsg);
                                source.close();
                            }
                            //resetHistoryscrollTop(0);
                        };

                        source.addEventListener('end', function (event) {

                            $(""#send"").removeClas");
                WriteLiteral(@"s(""layui-btn-disabled"")
                            var result = JSON.parse(event.data);
                            if (result.okMsg) {
                                layui.each(result.newContent, function (idx, item) {

                                });
                                //$(""#message-to-send"").val('');
                            }
                            else {
                                layer.msg(result.errorMsg);
                            }

                            // 结束事件源连接
                            source.close();
                        }, false);

                        source.onerror = function (event) {
                            $("".clearfix[responseCurrent='1']"").removeAttr(""responseCurrent"");
                            source.close();
                        };

                    }).catch(function (error) {
                        // Form Validation Failed
                        $(""#send"").removeClass(""layui-btn-disabled"")
       ");
                WriteLiteral(@"                 layer.msg(err);

                    })
                }
                //固定列表高度
                function tableheight() {
                    var winH = $(window).height();
                    var serH = $("".ser_div"").outerHeight(true);
                    $("".layui-table-main"").css(""height"", (winH - serH - 140) + ""px"");
                }
                //监听提交
                form.on('submit(formDemo)', function (data) {
                    app.$refs.vFormRef.getFormData().then(function (formData) {
                        // Form Validation OK
                        var data = {};
                        data.type = ""体格检查"";
                        data.str = JSON.stringify(formData);
                        data.patid = $(""#patid"").val();
                        data.words = $(""#txtWords"").val();
                        $.post('/vform/SaveFormData', data, function (res) {

                            if (res.code == 0)
                                layer.msg(""操作成功");
                WriteLiteral(@"！"");
                            else
                                layer.msg(res.msg);
                        })


                    }).catch(function (error) {
                        // Form Validation Failed
                        // $(""#send"").removeClass(""layui-btn-disabled"")
                        //  alert(error)
                        layer.msg(err);
                    })

                    return false;
                });

            });
        </script>
    </div>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
