#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\AfterStructured\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "01841012bc199ad925d195c9a95a85c45e0f5da321797c868a81f82834a5a5a4"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_CMIS_Views_AfterStructured_Index), @"mvc.1.0.view", @"/Areas/CMIS/Views/AfterStructured/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"01841012bc199ad925d195c9a95a85c45e0f5da321797c868a81f82834a5a5a4", @"/Areas/CMIS/Views/AfterStructured/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_CMIS_Views_AfterStructured_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<
#nullable restore
#line 5 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\AfterStructured\Index.cshtml"
       AngelwinResearch.WebUI.Areas.CMIS.Models.TempCMIS

#line default
#line hidden
#nullable disable
    >
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/jquery/dist/jquery.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/marked.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\AfterStructured\Index.cshtml"
  
    ViewBag.Title = "后结构化数据";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "01841012bc199ad925d195c9a95a85c45e0f5da321797c868a81f82834a5a5a46591", async() => {
                WriteLiteral("\r\n    <meta charset=\"utf-8\">\r\n    <title>后结构化数据</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "01841012bc199ad925d195c9a95a85c45e0f5da321797c868a81f82834a5a5a46938", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "01841012bc199ad925d195c9a95a85c45e0f5da321797c868a81f82834a5a5a48140", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "01841012bc199ad925d195c9a95a85c45e0f5da321797c868a81f82834a5a5a49342", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "01841012bc199ad925d195c9a95a85c45e0f5da321797c868a81f82834a5a5a410465", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "01841012bc199ad925d195c9a95a85c45e0f5da321797c868a81f82834a5a5a411589", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "01841012bc199ad925d195c9a95a85c45e0f5da321797c868a81f82834a5a5a412713", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }

        .container {
            height: 100%;
            padding: 15px;
            box-sizing: border-box;
        }

        .block {
            background: #f8f8f8;
            border-radius: 4px;
            padding: 15px;
            box-sizing: border-box;
            height: 92%;
        }
        /* 自定义表单间距 */
        .custom-form .layui-form-item {
            margin-bottom: 12px;
        }
        /* iframe样式 */
        .full-iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 4px;
        }
        /* 备注框样式 */
        .full-textarea {
            width: 100%;
            height: 100%;
            padding: 10px;
            font-size: 14px;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            resize: none; /* 禁止调整大小 */
        }

        .block_head");
                WriteLiteral(@"er {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 5px;
        }

        .infowrap .layui-input-inline {
            width: 70%;
            margin-bottom: 10px;
        }

            .infowrap .layui-input-inline .layui-input {
                line-height: 38px;
            }

        .from_wrap{
            height:88%;
            padding:15px 0;
            background-color:#fff;
        }

        .form_key{
            font-size: 16px;
            color: #1E9FFF;
            font-weight: bold;
        }

        .form_value{
            padding: 9px 15px;
            line-height: 20px;
            min-width:200px;
        }

    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "01841012bc199ad925d195c9a95a85c45e0f5da321797c868a81f82834a5a5a416434", async() => {
                WriteLiteral(@"
    <div class=""container layui-fluid"">
        <!-- 顶部：患者信息 -->
        <div class=""layui-row"" style=""height: 20%; margin-bottom: 15px;display:none"">
            <div class=""layui-col-md12"">
                <div class=""block"" style=""background: #F0F9EB;border: 1px solid #ccc"">
                    <form class=""layui-form custom-form"">
                        <div class=""layui-row infowrap"">
                            <!-- 每行显示3个表单项 -->

                            <div class=""layui-form-item layui-row"">
                                <div class=""layui-col-xs4"">
                                    <label class=""layui-form-label"">患者姓名</label>
                                    <div class=""layui-input-inline"">
                                        <div class=""layui-input"">");
                Write(
#nullable restore
#line 111 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\AfterStructured\Index.cshtml"
                                                                  Model?.PatientName

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</div>
                                    </div>
                                </div>

                                <div class=""layui-col-xs4"">
                                    <label class=""layui-form-label"">患者性别</label>
                                    <div class=""layui-input-inline"">
                                        <div class=""layui-input"">");
                Write(
#nullable restore
#line 118 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\AfterStructured\Index.cshtml"
                                                                  Model?.SEX

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</div>
                                    </div>
                                </div>
                                <div class=""layui-col-xs4"">
                                    <label class=""layui-form-label"">患者卡号</label>
                                    <div class=""layui-input-inline"">
                                        <div class=""layui-input"">");
                Write(
#nullable restore
#line 124 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\AfterStructured\Index.cshtml"
                                                                  Model?.BRKH

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</div>
                                    </div>
                                </div>
                                <div class=""layui-col-xs4"">
                                    <label class=""layui-form-label"">患者来源</label>
                                    <div class=""layui-input-inline"">
                                        <div class=""layui-input"">");
                Write(
#nullable restore
#line 130 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\AfterStructured\Index.cshtml"
                                                                  Model?.PatientSource

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</div>
                                    </div>
                                </div>
                                <div class=""layui-col-xs4"">
                                    <label class=""layui-form-label"">检查项目</label>
                                    <div class=""layui-input-inline"">
                                        <div class=""layui-input"">");
                Write(
#nullable restore
#line 136 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\AfterStructured\Index.cshtml"
                                                                  Model?.ProjectName

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</div>
                                    </div>
                                </div>
                                <div class=""layui-col-xs4"">
                                    <label class=""layui-form-label"">检查部位</label>
                                    <div class=""layui-input-inline"">
                                        <div class=""layui-input"">");
                Write(
#nullable restore
#line 142 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\AfterStructured\Index.cshtml"
                                                                  Model?.JCBW

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 下部布局 -->
        <div class=""layui-row"" style=""height: calc(99% - 1px);"">
            <!-- 左侧iframe -->
            <div class=""layui-col-md4"" style=""height: 100%; padding-right: 7px;"">
                <div class=""block_header"">
                    <p></p>
                    <button type=""button"" class=""layui-btn layui-btn-normal layui-btn-sm"" id=""btnCopy"">表单赋值</button>
                </div>
                <div class=""layui-form from_wrap"">
                    <div class=""layui-inline"">
                        <label class=""layui-form-label form_key"">报告单号:</label>
                        <div class=""layui-input-block form_value"">
                            <p>");
                Write(
#nullable restore
#line 165 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\AfterStructured\Index.cshtml"
                                Model?.ReportNo?.Trim()

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</p>
                        </div>
                    </div>
                    <div class=""layui-inline"">
                        <label class=""layui-form-label form_key"">报告时间:</label>
                        <div class=""layui-input-block form_value"">
                            <p>");
                Write(
#nullable restore
#line 171 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\AfterStructured\Index.cshtml"
                                Model?.ReportTime?.Trim()

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</p>
                        </div>
                    </div>
                    <div class=""layui-inline"">
                        <label class=""layui-form-label form_key"">报告医生:</label>
                        <div class=""layui-input-block form_value""><p>");
                Write(
#nullable restore
#line 176 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\AfterStructured\Index.cshtml"
                                                                      Model?.ReportUser?.Trim()

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</p></div>\r\n                    </div>\r\n                    <div class=\"layui-inline\">\r\n                        <label class=\"layui-form-label form_key\">检查描述:</label>\r\n                        <div class=\"layui-input-block form_value\"><p>");
                Write(
#nullable restore
#line 180 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\AfterStructured\Index.cshtml"
                                                                      Model?.ReportDiscription?.Trim()

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</p></div>
                    </div>
                    <div class=""layui-inline"">
                        <label class=""layui-form-label form_key"">检查结论:</label>
                        <div class=""layui-input-block form_value"">
                            <p>");
                Write(
#nullable restore
#line 185 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\AfterStructured\Index.cshtml"
                                Model?.ReportConclusion?.Trim()

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧备注 -->
            <div class=""layui-col-md8"" style=""height: 100%; padding-left: 7px;"">
                <div class=""layui-form layui-form-item"" style=""height:45px;"">
                    <label class=""layui-form-label"">选择模板：</label>
                    <div class=""layui-input-inline"">
                        <select id=""formSel"" lay-filter=""formSel"">
                        </select>
                    </div>
                </div>

                <div class=""block"" style=""padding: 10px;"">
                    <iframe class=""full-iframe"" frameborder=""0"" scrolling=""auto"" id=""reportFrame""></iframe>
                    <input type=""hidden"" id=""AIResult"" />
                </div>

            </div>
        </div>
    </div>

    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "01841012bc199ad925d195c9a95a85c45e0f5da321797c868a81f82834a5a5a426219", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
        layui.config({
            base: '/layuiadmin/layuiextend/'
        }).use(['element', 'layer', 'form'], function () {
            var element = layui.element
                , layer = layui.layer
                , $ = layui.$
                , form = layui.form ;


                var option='';
               var selectData = ");
                Write(
#nullable restore
#line 222 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\AfterStructured\Index.cshtml"
                                 Html.Raw(Json.Serialize(ViewBag.select))

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@";
             selectData.forEach(function(item,index) {
                 if(index==0)
                 {
                    var url =getReportUrl(item.id);
                     $(""#reportFrame"").attr(""src"",url);
                 }
             option+=`<option value=${item.id}>${item.name}</option>`;
           });
                $(""#formSel"").html(option);
                form.render();

                form.on('select(formSel)', function(data){
                    var val = data.value;
                     var url =getReportUrl(val);
                     $(""#reportFrame"").attr(""src"",url);

                });
                var source;
                  // 父窗口
            window.addEventListener('message', function (event) {
                if (event.data.action === 'save')
                {
                      var formData = event.data.data;
                    var obj = {};
                    obj.formData=formData;
                    obj.cmis = ");
                Write(
#nullable restore
#line 248 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\AfterStructured\Index.cshtml"
                                Html.Raw(Json.Serialize(Model))

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@";
                    obj.formId = $(""#formSel"").val();
                      obj.AIResult= $(""#AIResult"").val();
                     $.post('/CMIS/AfterStructured/SubmitData',{""request"":obj},function(res){

                              layer.msg(res.msg);
                        });
                }

            }, false);

            function getReportUrl(formId)
            {
                var randVersion = Math.random();
                  var url = """);
                Write(
#nullable restore
#line 262 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\AfterStructured\Index.cshtml"
                               $"{ViewBag.formUrl}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\" + formId + \"");
                Write(
#nullable restore
#line 262 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\AfterStructured\Index.cshtml"
                                                                    $".form?token={ViewBag.token}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("&version=\" + randVersion;\r\n                  return url;\r\n            }\r\n\r\n              $(\"#btnCopy\").click(function () {\r\n                  var load = layer.load(1);\r\n                 \r\n                     var obj = {};\r\n                    obj.cmis = ");
                Write(
#nullable restore
#line 270 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\CMIS\Views\AfterStructured\Index.cshtml"
                                Html.Raw(Json.Serialize(Model))

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@";
                    obj.formId = $(""#formSel"").val();
                   $.post('/CMIS/AfterStructured/FillForm',{""query"":obj},function(res){
                       var iframe = document.getElementById('reportFrame');
                       layer.close(load);
                    if (res.code == 0) {
                        //  var iframe = document.getElementById('reportingFrom');
                        // 发送消息到子页面
                        $(""#AIResult"").val(res.data);
                        iframe.contentWindow.postMessage({ action: ""show"", data: res.data }, ""*"");
                    }
                   })
                });

        });
    </script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<AngelwinResearch.WebUI.Areas.CMIS.Models.TempCMIS> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
