#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "2a4e0f66917386515f72978babc6640c55bb1d7941015e62fd01f541dc41fd46"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_HospitalCRF_Views_DataRecord_Index), @"mvc.1.0.view", @"/Areas/HospitalCRF/Views/DataRecord/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"2a4e0f66917386515f72978babc6640c55bb1d7941015e62fd01f541dc41fd46", @"/Areas/HospitalCRF/Views/DataRecord/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_HospitalCRF_Views_DataRecord_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<
#nullable restore
#line 5 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml"
       AngelwinResearch.Models.ResearchPatient

#line default
#line hidden
#nullable disable
    >
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/muihk/css/mui.min.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/font/eyes_icon/iconfont.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/jquery/dist/jquery.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/marked.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/muihk/js/mui.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml"
  
	ViewBag.Title = "病历文书转写";
	Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n\r\n<!DOCTYPE html>\r\n<html lang=\"en\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2a4e0f66917386515f72978babc6640c55bb1d7941015e62fd01f541dc41fd468440", async() => {
                WriteLiteral("\r\n\t<meta charset=\"UTF-8\">\r\n\t<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\">\r\n\r\n\t<meta http-equiv=\"X-UA-Compatible\" content=\"ie=edge\">\r\n\t<title>病历文书转写</title>\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "2a4e0f66917386515f72978babc6640c55bb1d7941015e62fd01f541dc41fd468965", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "2a4e0f66917386515f72978babc6640c55bb1d7941015e62fd01f541dc41fd4610165", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "2a4e0f66917386515f72978babc6640c55bb1d7941015e62fd01f541dc41fd4611366", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "2a4e0f66917386515f72978babc6640c55bb1d7941015e62fd01f541dc41fd4612567", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2a4e0f66917386515f72978babc6640c55bb1d7941015e62fd01f541dc41fd4613772", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2a4e0f66917386515f72978babc6640c55bb1d7941015e62fd01f541dc41fd4614894", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2a4e0f66917386515f72978babc6640c55bb1d7941015e62fd01f541dc41fd4616016", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"

	<style>

		/* 移动端头部 */
		.headrow {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			background-color: #f6f6f6;
			height: 38px;
			line-height: 38px;
		}

			.headrow .layui-btn-primary {
				background-color: transparent;
				border: none;
				color: #333;
			}
		/* 移动端头部 */

		.layui-btn-sm i {
			font-size: 22px !important;
			padding-right: 10px;
		}

		.flex_row {
			display: flex;
			flex-direction: row;
		}

		.layui-card {
			margin-bottom: 5px;
		}

		.content-inline {
			display: flex;
			flex-direction: row;
			line-height: 34px;
			padding-left: 20px;
		}

		.patient_information {
			align-items: center;
		}

		.user_information {
			flex: 1;
			flex-wrap: wrap;
			background-color: #fff;
			padding: 10px 0;
		}

		.user_name {
			padding-right: 20px;
			line-height: 38px;
			font-size: 18px;
		}

		.user_value {
			color: #000;
			padding-left: 10px;
		}

		.reporting_content {
			position: relativ");
                WriteLiteral(@"e;
		}

		.content_R {
			position: relative;
			flex: 1;
		}

		.sex {
			font-size: 14px;
			font-weight: normal;
		}

		.sex0 {
			color: #FF5722;
		}

		.sex1 {
			color: #1E9FFF;
		}

		.reporting {
			overflow: hidden;
		}

		.form_name {
			margin-bottom: 15px;
		}

		.nav_item {
			background-color: #fff;
			border: 1px solid #eee;
			padding: 10px 15px;
			border-radius: 4px;
			margin: 0 5px 10px;
		}

		.active {
			background: linear-gradient(70deg, #dcffff, #62ffff);
			border-color: transparent;
			color: #1E9FFF;
		}

			.active .layui-progress-text {
				color: #1E9FFF;
			}

		.layui-colla-content {
			padding: 0;
		}

			.layui-colla-content .layui-card-body {
				padding: 10px 0;
			}

		.search_hidden {
			text-align: center;
		}


			.search_hidden i {
				font-size: 22px;
				margin-right: 5px;
			}
		/* 折叠状态下左侧菜单的宽度 */
		.sider_btn {
			position: absolute;
			display: block;
			left: -5px;
			bottom: 10%;
			z-index: ");
                WriteLiteral(@"99;
			line-height: 0;
			/* padding: 28px 10px 28px 15px; */
			padding: 18px 0px 18px 6px;
			border: none;
			color: #fff;
			border-radius: 0 50% 50% 0;
			background: linear-gradient(to right, #56d1f9, #1eaddc);
		}

		.Menu_side {
			width: 230px;
			overflow-y: auto;
			overflow-x: hidden;
			transition: width 0.5s ease;
		}

			.Menu_side.folded {
				width: 0px;
			}

		.side_menu_title {
			font-size: 18px;
			color: #000;
			text-align: center;
			line-height: 30px;
			padding: 10px 0;
		}

		.content_L {
			overflow-y: auto;
		}



		.mui-btn .layui-icon {
			font-size: 24px;
		}

		.hide_report_list {
			padding: 15px;
		}

		.stretch {
			padding-right: 20px;
			flex-grow: 1;
		}

		.collapse_list {
			overflow-y: auto;
		}

		.null_wrap {
			text-align: center;
			margin-top: 10%;
		}

			.null_wrap img {
				width: 200px;
			}

		.mui-slider-handle {
			padding-left: 10px;
		}

        #txtWords {
            font: 16px Helvetic");
                WriteLiteral(@"a Neue, Helvetica, PingFang SC, Tahoma, Arial, sans-serif;
        }

            #txtWords h3, h4 {
                margin: 0px 20px 0px 20px;
                padding: 2px 2px 0px 0px;
            }

            #txtWords ul {
                margin: 18px 0px 18px 0px;
                padding: 0px 0px 0px 40px;
            }

            #txtWords p {
                margin: 16px 0px 16px 0px;
                padding: 0px 0px 0px 0px;
            }


	</style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2a4e0f66917386515f72978babc6640c55bb1d7941015e62fd01f541dc41fd4621507", async() => {
                WriteLiteral(@"
	<div class=""wrap"">
		<div class=""head_wrap patient_information"" id=""IpadHead"">
			<div class=""headrow"">
				<div class=""left"">
					<button id=""previous"" class=""layui-btn layui-btn-primary layui-border-green"" style=""margin-right:15px;display:none""><i class=""layui-icon layui-icon-return""></i></button>
				</div>
				<div class=""middle"">
					<h2 class=""user_name"">");
                Write(
#nullable restore
#line 253 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml"
                            Model?.PatientName

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("&nbsp;&nbsp;");
                Write(
#nullable restore
#line 253 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml"
                                                           ViewBag.DeptName

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("&nbsp;&nbsp;<span");
                BeginWriteAttribute("class", " class=\"", 4815, "\"", 4867, 1);
                WriteAttributeValue("", 4823, 
#nullable restore
#line 253 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml"
                                                                                                      Model?.Sex=="男" ? "sex sex1" : "sex sex0"

#line default
#line hidden
#nullable disable
                , 4823, 44, false);
                EndWriteAttribute();
                WriteLiteral(">");
                Write(
#nullable restore
#line 253 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml"
                                                                                                                                                   Model.Sex

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</span></h2>
                  
				</div>
				<div class=""right"">
					<div id=""popbtn"" style=""padding-right:10px;display:none"">
						<i class=""layui-icon layui-icon-survey"" style=""font-size:24px;""></i>
					</div>
                    <input type=""hidden"" id=""GroupName""");
                BeginWriteAttribute("value", " value=\"", 5158, "\"", 5184, 1);
                WriteAttributeValue("", 5166, 
#nullable restore
#line 260 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml"
                                                                ViewBag.GroupName

#line default
#line hidden
#nullable disable
                , 5166, 18, false);
                EndWriteAttribute();
                WriteLiteral(" />\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"user_information flex_row\">\r\n\t\t\t\t<div class=\"content-inline\">编号：<p class=\"user_value\">");
                Write(
#nullable restore
#line 264 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml"
                                                          Model?.ResearchID

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</p></div>
				<div class=""content-inline""> <button class=""layui-btn layui-btn-primary"" id=""btnAI""><i class=""layui-icon icon-zhinengwendangchouqu""></i>转写文书</button></div>
				<div style=""position:absolute;right:10px;"">
					<button type=""button"" class=""layui-btn layui-btn-primary"" id=""btnCopy""><i class=""layui-icon layui-icon-template-1""></i>一键复制</button>
				</div>
			</div>
		</div>

		<div class=""reporting_content flex_row"">
			<!-- 左侧折叠菜单 -->
			<div id=""sideMenu"" class=""Menu_side content_L layui-card mui-table-view"">
				<div class=""layui-collapse"" id=""OA_task_1"">
					<div class=""collapse_list""></div>
				</div>
			</div>

			<!-- 填报内容区域 -->
			<div id=""mainContent"" class=""reporting content_R"">
				<!--菜单折叠-->
				<button type=""button"" id=""toggleSidebar"" class=""layui-btn layui-btn-sm layui-btn-primary sider_btn""><i class=""layui-icon layui-icon-left"" style=""font-size:22px;""></i></button>
				<div class=""layui-col-xs12 layui-col-md12"">
					<div class=""layui-col-xs8 layui-col-md8"" id=""");
                WriteLiteral("crformListDiv\" style=\" height: 85vh;\">\r\n\t\t\t\t\t\t<!-- 这里是主体内容 -->\r\n");
                WriteLiteral("\t\t\t\t\t</div>\r\n\t\t\t\t\t<div class=\"layui-col-xs4 layui-col-md4\" style=\"height: 95vh;\">\r\n\t\t\t\t\t\t<div id=\"txtWords\" style=\"height:98%;width:100%; overflow:auto;\"></div>\r\n");
                WriteLiteral("\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n\r\n\r\n\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2a4e0f66917386515f72978babc6640c55bb1d7941015e62fd01f541dc41fd4626717", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2a4e0f66917386515f72978babc6640c55bb1d7941015e62fd01f541dc41fd4627839", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2a4e0f66917386515f72978babc6640c55bb1d7941015e62fd01f541dc41fd4628961", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t<!--滑动删除插件-->\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "2a4e0f66917386515f72978babc6640c55bb1d7941015e62fd01f541dc41fd4630103", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"

	<script>
        layui.use(['element', 'layer', 'table', 'laydate', 'form'], function () {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , laydate = layui.laydate
                , $ = layui.$
                , form = layui.form;

            var crformIds="""";

            if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
                $(""#previous"").show();
            }
            else {
                $(""#previous"").hide();
            }


            $(""#btnAI"").click(function () {
                // AIExtract();
                if (!crformIds)
                {
                    layer.msg(""请勾选CRF表单！"");
                    return;
                }
				var IdsArr = crformIds.split("","").filter(Boolean);
                $.each(IdsArr, function (index, ");
                WriteLiteral(@"id)
                {
                    if(id)
                    {
                        var iframe = document.getElementById(""reportingFrom_"" + id);
                        // 发送消息到子页面 触发表单保存的Click事件
                        iframe.contentWindow.postMessage({ action: ""save"" }, ""*"");
                    }
					
                })
				
            })

			$(""#btnCopy"").click(function ()
			{
				//var textBox = document.getElementById('txtMedical');
				//textBox.select(); // 选择文本
				//document.execCommand('copy'); // 执行复制命令
				//textBox.blur();
				//layer.msg('病历文书已复制');

				// 创建一个临时的textarea来复制内容
				var $temp = $(""<textarea>"");
				$(""body"").append($temp);
				$temp.val($(""#txtWords"").text()).select();
				try
				{
					var successful = document.execCommand('copy');
					var msg = successful ? '成功复制到剪贴板' : '复制失败';
					layer.msg(msg);
				} catch (err)
				{
					layer.msg('无法复制，浏览器不支持');
				}
				// 移除临时textarea
				$temp.remove();
			});

            $(""#po");
                WriteLiteral(@"pbtn"").on(""click"", function () {
                layer.open({
                    type: 1,
                    area: ['60%', '80%'],
                    resize: false,
                    shadeClose: true,
                    title: '帮助',
                    content: $(""#popwrap""),
                    success: function () {
                        // $(""#model_wrapL"").val(modelText);
                    }
                })
            })
            // 点击折叠按钮时触发的函数
            document.getElementById('toggleSidebar').addEventListener('click', function () {

                var sideMenu = document.getElementById('sideMenu');
                var spreadIcon = document.getElementById('toggleSidebar');

                // 切换folded类来实现折叠效果
                sideMenu.classList.toggle('folded');

                // 根据菜单的状态切换图标显示
                if (sideMenu.classList.contains('folded')) {
                    spreadIcon.children[0].classList.remove('layui-icon-left'); // 显示展开图标
                ");
                WriteLiteral(@"    spreadIcon.children[0].classList.add('layui-icon-right'); // 隐藏展开图标
                } else {
                    spreadIcon.children[0].classList.remove('layui-icon-right'); // 显示展开图标
                    spreadIcon.children[0].classList.add('layui-icon-left'); // 隐藏展开图标
                }

                // 如果需要，可以通过Layui重新初始化相关元素，以保证样式和事件正常工作
                element.init();

            });

            // 动态添加菜单项示例（如果需要）
            // $('#navMenu').append('');



            //患者列表的高度
            function setReportingContenttH() {
                var winH = $(window).height();
                var informationH = $("".patient_information"").height();
                var menuH = $("".Menu_side "").height();
                // var menuTitleH = $("".side_menu_title"").height();
                // if (menuTitleH == 0) {
                //     menuTitleH = 106;
                // }

                var contentH = winH - informationH - 15 + ""px"";
                $("".reporting_content"").css");
                WriteLiteral(@"(""height"", contentH);

                // var collapseListH = winH - informationH - menuTitleH - 45 + ""px"";
                // console.log(""collapse_list:"", collapseListH);
                // $("".collapse_list"").css(""height"", collapseListH);
            }
            setReportingContenttH();

            $(window).resize(function () {
                setReportingContenttH();
            });

            var isSwiping = false;

            //模拟器
            function detectScreenOrientation() {
                var userAgent = navigator.userAgent.toLowerCase();
                if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
                    isSwiping = true;

                    //ipad端                    var orientation = window.orientation;
                    if (orientation === 0 || orientation === 180) {
                        // 竖屏状态
              ");
                WriteLiteral(@"          console.log(""竖屏"");
                    } else if (orientation === 90 || orientation === -90) {
                        $(document).ready(function () {
                            $('#LAY_app').removeClass('layadmin-side-shrink');
                        });
                        // 横屏状态
                        console.log(""横屏"");
                    }
                } else {
                    //pc端
                    isSwiping = false;
                    // $("".patient_btn"").removeClass(""patient_btn_1"").addClass(""patient_btn_2"");
                    // $("".patient_info"").removeClass(""flex-row"").addClass(""flex-column"");
                }
            }

            // 初始化时检查一次屏幕方向

            detectScreenOrientation();
            var id = 0;
            var firstId = 0;
            var formId = """";

            function getFormLit() {
                var HospitalDeptId = '");
                Write(
#nullable restore
#line 470 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml"
                                       Model.HospitalDeptId

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"';
				var GroupName = $(""#GroupName"").val();
                $.get({
					url: '/HospitalCRF/DataRecord/GetCRFList?HospitalDeptId=' + HospitalDeptId + '&GroupName=' + GroupName, // 你的请求URL
                    async: false, // 设置为false以使请求同步执行
                    success: function (res) {
                        if (res.code == 0) {
                            var htm = """";
                            $("".layui-collapse"").html("""");
                            $.each(res.data, function (index, item) {

                                htm += `<div class=""layui-colla-item"">
                                                <h2 class=""layui-colla-title"">`+ item.title + `</h2>
                                                <div class=""layui-colla-content layui-show"">
                                                    <ul class=""navMenu layui-card-body"" lay-filter=""navMenu"">`;
                                var childHtm = """";
                                $.each(item.children, function (index1, c");
                WriteLiteral(@"hild) {
                                    // console.log(index1);
                                    if (index1 == 0 && index == 0) {
                                        firstId = child.id;
                                    }
                                    childHtm += `<li class=""nav_item mui-table-view-cell"" data-id=""` + child.id + `"" data-formId=""` + child.formId + `"" data-formName=""` + child.title + `"">
                                                         <div class=""flex_row"">
                                                                <input type=""checkbox"" class=""crfChk"" name="""" data-id=""` + child.id + `"" data-formId=""` + child.formId + `"" checked />
                                                                <div class=""mui-slider-handle"">
                                                                    <p class=""form_name"">`+ child.title + `</p>

                                                                </div>
                                             ");
                WriteLiteral("            </div>\r\n                                                </li>`;\r\n\t\t\t\t\t\t\t\t\tvar randVersion = Math.random();\r\n\t\t\t\t\t\t\t\t\tvar iframeUrl = \"");
                Write(
#nullable restore
#line 501 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml"
                            $"{ViewBag.formUrl}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\" + child.formId + \"");
                Write(
#nullable restore
#line 501 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml"
                                                                       $".form?token={ViewBag.token}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"&version="" + randVersion;
                                    // 创建一个新的iframe元素
									var frameid = child.id;
									var iframe = $('<iframe id=""reportingFrom_' + frameid +'""  frameborder=""0"" width=""100%""  height=""100%"" >');
									crformIds += "","" + frameid;
 // 将iframe追加到指定的div中
 $(""#crformListDiv"").append(iframe);
									$(""#reportingFrom_"" + frameid).attr(""src"", iframeUrl);
 var patientId = '");
                Write(
#nullable restore
#line 509 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml"
                   Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"';
 setTimeout(function () {
	 loadData(frameid, index1);
 }, 1500);
                                });

                                htm += childHtm + `</ul>
                                                </div>
                                            </div>`;

                            })

                            htm = ` <div class=""side_menu_title"">
                                       
                                     </div>` + htm;

                            $("".layui-collapse"").append(htm);
                            element.render('collapse');
                            // 渲染进度条组件
                            element.render('progress');

                            $('.crfChk').change(function () {
                                var id = $(this).attr(""data-id"");
                                formId = $(this).attr(""data-formId"");
                                var $navItem = $("".nav_item[data-id='"" + id + ""']"");
                                if (thi");
                WriteLiteral("s.checked) {\r\n                                    var randVersion = Math.random();\r\n\r\n                                    $navItem.removeClass(\"active\").addClass(\"active\");\r\n                                    var iframeUrl = \"");
                Write(
#nullable restore
#line 538 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml"
                                                       $"{ViewBag.formUrl}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\" + formId + \"");
                Write(
#nullable restore
#line 538 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml"
                                                                                            $".form?token={ViewBag.token}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"&version="" + randVersion;
                                    // 创建一个新的iframe元素
									var iframe = $('<iframe id=""reportingFrom_' + id +'""  frameborder=""0"" width=""100%""  height=""100%"">');
                                    crformIds += "",""+ id;
                                    // 将iframe追加到指定的div中
                                    $(""#crformListDiv"").append(iframe);
                                    $(""#reportingFrom_"" + id).attr(""src"", iframeUrl);
                                    var patientId = '");
                Write(
#nullable restore
#line 545 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml"
                                                      Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"';
                                    setTimeout(function () {
                                        loadData(id);
                                    }, 1500);

                                } else {
                                    $navItem.removeClass(""active"");
                                    crformIds = crformIds.replace("","" + id,"""");
                                    $(""#reportingFrom_"" + id).remove();
                                }
                            });
                        }
                    }
                });
            }

            getFormLit();


            //右滑影藏start
            mui.init();
            $('#OA_task_1').on('tap', '.mui-btn', function (event) {
                var elem = this;
                var li = elem.parentNode.parentNode;
                // 在这个例子中，我们只关心确认按钮的回调
                mui.confirm('是否隐藏当前填报表单？', '提示', btnArray, function (e) {
                    // 这里是点击确认按钮后的回调函数
                    console.log('用户点击了确");
                WriteLiteral("认按钮\');\r\n                    // 在这里执行你想要在点击确认后进行的操作\r\n                    if (e.index == 0) {\r\n                        var crformId = li.getAttribute(\"data-id\");\r\n                        var patid = \'");
                Write(
#nullable restore
#line 576 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml"
                                      Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"'
                        var obj = { CRFormId: crformId, ResearchPatientId: patid };
                        $.post('/ReportingManage/PatientManage/HideForm', obj, function (res) {
                            if (res.code == 0) {
                                getHideList();
                                li.parentNode.removeChild(li);
                            }
                            else {
                                layer.msg(res.msg);
                            }
                        })
                    }
                }, function () {
                    // 这里是点击取消按钮后的回调函数
                    console.log('用户点击了取消按钮');
                });
            });
            var btnArray = ['确认', '取消'];
            //右滑影藏end

            //第一次默认选中第一个;
            // 选择data-id为的li元素
            var targetLi = $('li[data-id=""' + firstId + '""]');
            // 模拟点击事件
            targetLi.trigger('click');

            var saveCount = 0;
            var loadIndex = 0");
                WriteLiteral(@";
            // 父窗口
            window.addEventListener('message', function (event)
            {
                console.log(event);
                if (event.data.action === 'save')
                {
                    saveCount++;
                    var IdsArr = crformIds.split("","").filter(Boolean);
                    //如果收到的提交次数==表单的数量 表示全部提交完 调用AI转写
                    loadIndex = layer.load(1);
                    var ResearchPatientId = '");
                Write(
#nullable restore
#line 614 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml"
                                              Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"'
                    var formData = event.data.data;
                    var obj = { ResearchPatientId: ResearchPatientId, CRFJsonValue: formData };
					$.post('/HospitalCRF/DataRecord/SaveForm', obj, function (res) {
                        if (res.code == 0)
                        {
                            if (IdsArr.length == saveCount)
                            {
                                saveCount = 0;
                                AIExtract();
                            }
                            // getFormLit();
                            // 选择data-id为的li元素
                            //var targetLi = $('li[data-id=""' + id + '""]');

                            //// 模拟点击事件
                            //targetLi.trigger('click');
                            // layer.msg(""操作成功"");
                        }
                        else
                        {
							layer.close(loadIndex);
                            layer.msg(""操作失败"");
                        }");
                WriteLiteral("\r\n\r\n                    })\r\n\r\n                }\r\n\r\n            }, false);\r\n\r\n            function loadData(hospitalCRFormId,formIndex) {\r\n\t\t\t\tvar url = \"/HospitalCRF/DataRecord/LoadFormData?ResearchID=");
                Write(
#nullable restore
#line 646 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml"
                                                                 Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"&HospitalCRFormId="" + hospitalCRFormId;
                $.get(url, function (res) {
					var iframe = document.getElementById(""reportingFrom_"" + hospitalCRFormId);

                    if (res.code == 0) {
                        //  var iframe = document.getElementById('reportingFrom');
                        // 发送消息到子页面
                        iframe.contentWindow.postMessage({ action: ""show"", data: res.data }, ""*"");
                        if (formIndex == 0)
                        {
							var htmlContent = marked.parse(res.aIExtractJson);
							$(""#txtWords"").html(htmlContent);

							var div = $('#txtWords');
							div.scrollTop(div[0].scrollHeight - div.innerHeight());
                        }
                    }
                    else {
                        // 发送消息到子页面
                        iframe.contentWindow.postMessage({ action: ""show"" }, ""*"");
                    }
                   // $(""#model_wrapR"").val(res.tips);

                })

            }
  ");
                WriteLiteral("          var timer;\r\n            function AIExtract() {\r\n                var i = 0;\r\n                var allData = \"\";\r\n\t\t\t\tsource = new EventSource(\'/HospitalCRF/DataRecord/GetBLTextResult?ResearchID=");
                Write(
#nullable restore
#line 676 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\HospitalCRF\Views\DataRecord\Index.cshtml"
                                                                                   Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"&crFormIds=' + crformIds);
                source.onmessage = function (event) {
                    var result = JSON.parse(event.data);
                    if (result.okMsg) {
                        //var btnloading = document.getElementById(""txtWords"");
                        //if (i == 0) {
                        //    var Info = result.data;
                        //    btnloading.value = Info;
                        //}
                        //else {
                        //    var Info = result.data;
                        //    btnloading.value += Info;
                        //}
						if (result.data && loadIndex != -99999999)
                        {
                            layer.close(loadIndex);
                            loadIndex = -99999999;
                        }
                        i = i + 1;
                        allData += result.data;
                        var htmlContent = marked.parse(allData);
                        $(""#txtWords"").html(h");
                WriteLiteral(@"tmlContent);

                        var div = $('#txtWords');
                        div.scrollTop(div[0].scrollHeight - div.innerHeight());
                    }
                    else {
                        layer.msg(result.errorMsg);
                        source.close();
                    }
                };

                source.addEventListener('end', function (event) {
                    var result = JSON.parse(event.data);
                    if (result.okMsg) {
						if (loadIndex != -99999999)
						{
							layer.close(loadIndex);
						}

                    }
                    else {
                        layer.msg(result.errorMsg);
                    }
                    source.close();
                }, false);

                source.onerror = function (event)
                {
					layer.close(loadIndex);
                    source.close();
                };
            }


            function load() {
                var n = 0;
    ");
                WriteLiteral(@"             timer = setInterval(function () {
                    n = n + Math.random() * 10 | 0;
                    if (n > 99) {
                        n = 99;
                        clearInterval(timer);

                    }
                    element.progress('demo-filter-progress', n + '%');
                }, 300 + Math.random() * 4000);
            }
        })
	</script>



");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"

<div class=""window_wrap"" id=""form_window_load"" style=""display: none;background-color:transparent"">
	<div class=""layui-progress layui-progress-big"" lay-showPercent=""true"" lay-filter=""demo-filter-progress"">
		<div class=""layui-progress-bar"" lay-percent=""0%"">
		</div>

	</div>
	<p style=""text-align:center""> AI数据提取中...</p>
</div>

<div id=""popwrap"" style=""display:none;height:100%;overflow:hidden;"">
	<div class=""layui-row layui-col-space30"" style=""height:100%"">
		<div class=""layui-col-md12"" style=""height:100%"">
			<div style=""background-color: #fff;height: 100%; padding:15px 15px;"">
				<textarea id=""model_wrapR"" class=""layui-textarea"" style="" height:100%;width:100%;line-height:40px;font-size:20px;resize:none"">

</textarea>
			</div>
		</div>
	</div>


</div>
</html>
");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<AngelwinResearch.Models.ResearchPatient> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
