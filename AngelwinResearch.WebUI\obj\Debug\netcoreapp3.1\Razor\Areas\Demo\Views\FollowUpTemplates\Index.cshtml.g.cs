#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\FollowUpTemplates\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "a852f261400804108ae3d5625ee2d10debe06ff87a35c6ddf2c0dbd0ab149805"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_Demo_Views_FollowUpTemplates_Index), @"mvc.1.0.view", @"/Areas/Demo/Views/FollowUpTemplates/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"a852f261400804108ae3d5625ee2d10debe06ff87a35c6ddf2c0dbd0ab149805", @"/Areas/Demo/Views/FollowUpTemplates/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_Demo_Views_FollowUpTemplates_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\FollowUpTemplates\Index.cshtml"
  
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "a852f261400804108ae3d5625ee2d10debe06ff87a35c6ddf2c0dbd0ab1498055961", async() => {
                WriteLiteral("\r\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n    <meta charset=\"utf-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>随访模板管理</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "a852f261400804108ae3d5625ee2d10debe06ff87a35c6ddf2c0dbd0ab1498056473", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        /*弹窗*/
        .window_wrap {
            padding: 15px;
        }

        .search_wrap {
            background-color: #f0f0f0;
        }

        .layui-tab-brief {
            background-color: #fff;
        }

        .line_wrap {
            padding: 10px 15px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .form_item {
            padding-top: 10px;
        }

        .btnwrap {
            padding-left: 10px;
        }

        .layui-show {
            display: flex !important;
            align-items: flex-end;
        }

        .form_wrap {
            flex: 1;
        }

        .table_wrap {
            overflow: hidden;
        }

        #detail_window .layui-form-label {
            width: 100px;
        }

        #detail_window .layui-form-val {
            padding: 9px 15px;
        }

        #detail_window .mindow_icon .title_icon {
        ");
                WriteLiteral(@"    display: inline-block;
        }

        #detail_window .mindow_icon {
            text-align: center;
            padding: 20px 0;
        }

        #detail_window .layui-form-item {
            margin-bottom: 0;
        }
        /* 定义表头样式 */
        .layui-table-header .layui-table-cell {
            font-weight: bold; /* 加粗 */
            font-size: 14px; /* 可选：调整字体大小以提升可读性 */
            color: #333; /* 可选：调整字体颜色 */
        }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "a852f261400804108ae3d5625ee2d10debe06ff87a35c6ddf2c0dbd0ab1498059912", async() => {
                WriteLiteral(@"
    <div class=""layui-col-md12"">
        <div class=""layui-card"">
            <!--搜索区-->
            <div class=""line_wrap search_wrap"">
                <div>
                    <div class=""layui-inline"">
                        <label class=""layui-form-label"">搜索条件</label>
                        <div class=""layui-input-inline"">
                            <input type=""text"" class=""layui-input"" name=""keyWord"" placeholder=""请输入关键字"" id=""keyWord""");
                BeginWriteAttribute("value", " value=\"", 2330, "\"", 2338, 0);
                EndWriteAttribute();
                WriteLiteral(@" />
                        </div>
                        <button id=""Search"" class=""layui-btn layui-btn-primary layui-border-green""><i class=""layui-icon layui-icon-search""></i> </button>
                    </div>
                </div>
                <div>
                    <button type=""button"" id=""addTemplate"" data-type=""add"" class=""layui-btn layui-bg-blue""><i class=""layui-icon layui-icon-add-1""></i></button>
                </div>
            </div>
            <!--编辑区-->
            <div class="" edit_area"">
                <div class=""line_wrap layui-card layui-colla-content layui-show"">
                    <div class=""layui-row form_wrap layui-form"">

                        <div class=""layui-col-xs4 layui-col-sm4 layui-col-md4"">
                            <div class=""form_item"">
                                <label class=""layui-form-label"">模板名称</label>
                                <div class=""layui-input-block "">
                                    <input type=""text"" name=""");
                WriteLiteral(@"Id"" id=""Id"" style=""display:none;"" />
                                    <input type=""text"" name=""TemplateName"" id=""TemplateName"" required lay-verify=""required"" placeholder=""请输入模板名称"" autocomplete=""off"" class=""layui-input"">
                                </div>
                            </div>
                        </div>
                        <div class=""layui-col-xs4 layui-col-sm4 layui-col-md4"">
                            <div class=""form_item"">
                                <label class=""layui-form-label"">编码</label>
                                <div class=""layui-input-block "">
                                    <input type=""text"" name=""DiseaseCode"" id=""DiseaseCode"" required lay-verify=""required"" placeholder=""请输入疾病/手术编码"" autocomplete=""off"" class=""layui-input"">
                                </div>
                            </div>
                        </div>

                        <div class=""layui-col-xs4 layui-col-sm4 layui-col-md4"">
                            <div cla");
                WriteLiteral(@"ss=""form_item"">
                                <label class=""layui-form-label"">随访次数</label>
                                <div class=""layui-input-block "">
                                    <input type=""text"" name=""Followup"" id=""Followup"" placeholder=""请输入随访次数"" autocomplete=""off"" class=""layui-input"">
                                </div>
                            </div>
                        </div>
                        <div class=""layui-col-xs6 layui-col-sm6 layui-col-md6"">
                            <div class=""form_item"">
                                <label class=""layui-form-label"">备注</label>
                                <div class=""layui-input-block "">
                                    <textarea name=""Remark"" id=""Remark"" class=""layui-textarea"" style=""resize: none""></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class=""btnwrap"">
                       ");
                WriteLiteral(@" <button class=""layui-btn"" lay-submit lay-filter=""submit"">保存</button>
                    </div>
                </div>
            </div>

            <div class=""layui-card-body table_wrap"">
                <table id=""tablelist"" lay-filter=""tablelist""></table>
                <script type=""text/html"" id=""tableBar"">
                    <a class=""layui-btn layui-btn-danger layui-btn-xs"" lay-event=""del"" style=""text-decoration:none""><i class=""layui-icon layui-icon-delete""></i>删除</a>
                </script>
            </div>
        </div>
    </div>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "a852f261400804108ae3d5625ee2d10debe06ff87a35c6ddf2c0dbd0ab14980514713", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "a852f261400804108ae3d5625ee2d10debe06ff87a35c6ddf2c0dbd0ab14980515837", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "a852f261400804108ae3d5625ee2d10debe06ff87a35c6ddf2c0dbd0ab14980516961", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "a852f261400804108ae3d5625ee2d10debe06ff87a35c6ddf2c0dbd0ab14980518085", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"

    <script>
        layui.use(['element', 'layer', 'table', 'laydate', 'form'], function () {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , laydate = layui.laydate
                , $ = layui.$
                , form = layui.form;
            ;
            var url = ''
            var value = '01';

            table.render({
                elem: '#tablelist'
                , id: 'tablelist'
                , page: true

                , limit: 10
                , height: 'full-325'
                , cols: [[
                    { field: 'No', title: '序号', type: 'numbers', fixed: 'left' }
                    , { field: 'TemplateName', title: '模板名称', width: 250 }
                    , { field: 'DiseaseCode', title: '疾病/手术编码' }
                    , { field: 'Followup', title: '随访次数' }
                    , { field: 'Remark', title: '备注' }
                    , { field: 'CreateUserName',");
                WriteLiteral(@" title: '创建人' }
                    , { field: 'CreatedTime', title: '创建时间' }
                    , { title: '操作', toolbar: '#tableBar', width: 160, minWidth: 160, fixed: 'right' }
                ]]
                , done: function (res, curr, count) {
                }
            });

            //监听tablelist工具条
            table.on('tool(tablelist)', function (obj) {
                var data = obj.data;
                if (obj.event === 'del') {
                    layer.confirm('确定要删除名为【' + data.TemplateName + '】的模板吗？将无法恢复。', {
                        title: '',
                        btn: ['确定', '取消'], //按钮
                        resize: false
                    }, function (index) {
                            $.post('/Demo/FollowUpTemplates/Del', { id: data.Id }, function (result) {
                            if (result.okMsg) {
                                layer.msg(result.okMsg);
                                currentIndex = -1;
                                table.rel");
                WriteLiteral(@"oad('tablelist'); //重载表格
                                EmptyData();
                            } else {
                                layer.msg(result.errorMsg);
                            }
                        }, 'json');
                        layer.close(index);
                    });
                }
            });

            //触发行单击事件
            table.on('row(tablelist)', function (obj) {
                var data = obj.data;
                $(""#TemplateName"").val(data.TemplateName);
                $(""#DiseaseCode"").val(data.DiseaseCode);
                $(""#Followup"").val(data.Followup);
                $(""#Remark"").val(data.Remark);
                $(""#Id"").val(data.Id);
            });

            function SearchData() {

                table.reload('tablelist', {
                    page: {
                        curr: 1
                    },
                    url: '/Demo/FollowUpTemplates/List'
                    , where: {
                       ");
                WriteLiteral(@" 'keyWord': $.trim($(""#keyWord"").val()),
                    }
                });
            };

            $(document).ready(function () {
                $('.layui-tab-title li').on('click', function () {
                    // 获取被点击列表项的 value
                    EmptyData();
                    SearchData();
                })
                $(document).on('click', '#Search', function () {
                    EmptyData();
                    SearchData();
                })

                $(document).on('click', '#addTemplate', function () {
                    EmptyData();
                });

            });
            function EmptyData() {
                $('#Id').val('');
                $(""#TemplateName"").val('');
                $(""#DiseaseCode"").val('');
                $(""#Followup"").val('');
                $(""#Remark"").val('');
            }
            SearchData();

            //监听提交
            form.on('submit(submit)', function (data) {
               ");
                WriteLiteral(@" var indes = layer.load(1);
                data.field.templateName = $(""#TemplateName"").val();
                data.field.diseaseCode = $(""#DiseaseCode"").val();
                data.field.followup = $(""#Followup"").val();
                data.field.remark = $(""#Remark"").val();
                data.field.id = $(""#Id"").val();
                //提交 Ajax 成功后，关闭当前弹层并重载表格
                $.ajax({
                    url: '/Demo/FollowUpTemplates/Save',
                    type: ""post"",
                    data: { 'node': data.field },
                    datatype: 'json',
                    success: function (data) {
                        if (data.okMsg) {
                            layer.msg(data.okMsg);
                            table.reload('tablelist'); //重载表格
                        }
                        else {
                            layer.msg(data.errorMsg);
                        }
                        layer.close(indes);
                    }, error: function (res) {
");
                WriteLiteral(@"                        layer.msg(""加载统计信息错误："" + res.responseText);
                        layer.close(indes);
                    }
                });
                return false;
            });

            function setTableH() {
                var winH = $(window).height();
                var navH = $("".layui-tab-brief"").height();
                var searchH = $("".search_wrap"").height();
                var editAreaH = $("".edit_area"").height();
                var tableH = winH - (navH + searchH + editAreaH) - 55 + ""px"";
                console.log(tableH)
                $("".table_wrap"").css(""height"", tableH);

            };
            setTableH();
            $(window).resize(function () {
                setTableH()
            });
        });
    </script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
