#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Chat\PdfInfo.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "e570e2c366efc2e3d243344b590a180ede6de5ee68696429eedf5eac60242c4b"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_Chat_PdfInfo), @"mvc.1.0.view", @"/Views/Chat/PdfInfo.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI

#nullable disable
    ;
#nullable restore
#line 2 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"e570e2c366efc2e3d243344b590a180ede6de5ee68696429eedf5eac60242c4b", @"/Views/Chat/PdfInfo.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"a5bcd83cf27c8ffb8baf597d24314352bf7b52b5a9bb5ee83e83e9d0089a628e", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_Chat_PdfInfo : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/chatGTPIndex.css?v=v21"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/lib/codemirror.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/CodeMirror/theme/pastel-on-dark.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "眼科", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "康复科", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("layui-form flex_row"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("lay-filter", new global::Microsoft.AspNetCore.Html.HtmlString("formModel"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("formModel"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("action", new global::Microsoft.AspNetCore.Html.HtmlString(""), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_13 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_14 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_15 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("layui-form"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_16 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("lay-filter", new global::Microsoft.AspNetCore.Html.HtmlString("fm"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_17 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("fm"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Chat\PdfInfo.cshtml"
  
    ViewBag.Title = "PDF信息提取";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"zh\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e570e2c366efc2e3d243344b590a180ede6de5ee68696429eedf5eac60242c4b10564", async() => {
                WriteLiteral("\r\n    <meta charset=\"UTF-8\">\r\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>chatGTP</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "e570e2c366efc2e3d243344b590a180ede6de5ee68696429eedf5eac60242c4b11060", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "e570e2c366efc2e3d243344b590a180ede6de5ee68696429eedf5eac60242c4b12263", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n    <!-- 代码高亮 -->\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "e570e2c366efc2e3d243344b590a180ede6de5ee68696429eedf5eac60242c4b13491", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "e570e2c366efc2e3d243344b590a180ede6de5ee68696429eedf5eac60242c4b14695", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e570e2c366efc2e3d243344b590a180ede6de5ee68696429eedf5eac60242c4b15898", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        body {
            background-color: #fafafa;
            overflow: hidden;
        }


        .code_wrap {
            background-color: #3f3f3f;
            border-radius: 6px;
            overflow: hidden;
        }

        .code_top {
            padding: 4px 6px 0;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            background-color: #000;
            color: #c2be9e;
        }

        .code_content {
            padding: 10px;
            border: 10px solid #000;
            color: #c2be9e;
            text-align: left;
            resize: none;
        }

        .layui-btn-xs {
            padding: 0 6px;
        }

        .layui-btn-primary {
            border-color: transparent;
            background-color: transparent;
            color: #fff;
        }

            .layui-btn-primary:hover {
                color: #eee;
                border-color: #eee;
          ");
                WriteLiteral(@"  }

        .message_content {
            flex: 1;
        }

            .message_content p,
            .result_text {
                white-space: pre-line;
            }

        .result_text {
            padding: 10px;
            height: 80px;
            background-color: #3f3f3f;
            font-size: 14px;
            word-wrap: break-word;
            overflow-x: hidden;
            overflow-y: auto;
            border: 10px solid #000;
            border-top: 2px solid #000;
            border-radius: 0;
        }

            .result_text:hover {
                border: 10px solid #000 !important;
                border-top: 2px solid #000 !important;
            }

            .result_text:focus {
                padding: 10px;
                border: 10px solid #000 !important;
                border-top: 2px solid #000 !important;
                color: #c2be9e;
                border-radius: 0;
            }

        .layui-table-view {
            mar");
                WriteLiteral(@"gin:10px;
        }

        .result_bj {
            display: flex;
            flex-direction: row;
            align-items: center;
            background-color: #009688;
            color: #fff;
        }

        .result_icon {
            width: 34px;
            height: 34px;
            border-radius: 10px;
            border: 2px solid #0bbe56;
            overflow: hidden;
            margin-right: 10px;
        }

            .result_icon img {
                vertical-align: top;
                width: 100%;
            }

        .flex_row {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .right_title {
            display: block;
        }

        .layui-btn {
            padding: 0 20px;
        }

        .right_content {
            margin: 10px;
            margin-left: 0;
            background-color: #fff;
            border: 1px solid #eee;
            border-radius: 10p");
                WriteLiteral(@"x;
            overflow: hidden;
        }

        .left_wrap{
            width:100%;
            margin: 10px;
            margin-left: 0;
            background-color: #fff;
            border: 1px solid #eee;
            border-radius: 10px;
        }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e570e2c366efc2e3d243344b590a180ede6de5ee68696429eedf5eac60242c4b21186", async() => {
                WriteLiteral("\r\n    <div");
                BeginWriteAttribute("class", " class=\"", 4038, "\"", 4046, 0);
                EndWriteAttribute();
                WriteLiteral(">\r\n\r\n\r\n\r\n\r\n\r\n        <div class=\"chat layui-row\">\r\n\r\n            <div class=\"layui-col-xs12 layui-col-md12\">\r\n\r\n                <div class=\"right_title\" style=\"margin-top:10px; \">\r\n");
                WriteLiteral("\r\n                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e570e2c366efc2e3d243344b590a180ede6de5ee68696429eedf5eac60242c4b21901", async() => {
                    WriteLiteral("\r\n                        <div class=\"layui-form right_title_select\">\r\n");
                    WriteLiteral("                            <div class=\"layui-inline\">\r\n\r\n                                <div class=\"layui-input-inline\">\r\n                                    <select id=\"dept\" name=\"dept\">\r\n                                        ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e570e2c366efc2e3d243344b590a180ede6de5ee68696429eedf5eac60242c4b22552", async() => {
                        WriteLiteral("全部");
                    }
                    );
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                    __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_6.Value;
                    __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_6);
                    BeginWriteTagHelperAttribute();
                    __tagHelperStringValueBuffer = EndWriteTagHelperAttribute();
                    __tagHelperExecutionContext.AddHtmlAttribute("selected", Html.Raw(__tagHelperStringValueBuffer), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.Minimized);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                                        ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e570e2c366efc2e3d243344b590a180ede6de5ee68696429eedf5eac60242c4b24235", async() => {
                        WriteLiteral("眼科");
                    }
                    );
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                    __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_7.Value;
                    __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_7);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                                        ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e570e2c366efc2e3d243344b590a180ede6de5ee68696429eedf5eac60242c4b25583", async() => {
                        WriteLiteral("康复科");
                    }
                    );
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                    __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                    __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_8.Value;
                    __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_8);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral(@"
                                    </select>
                                </div>
                            </div>
                            <div class=""layui-inline"">
                             
                                <div class=""layui-input-inline"" style=""width:300px;"">
                                    <input type=""text"" class=""layui-input"" placeholder=""请输入文件名称"" name=""KeyWords"" id=""KeyWords"" />
                                </div>

                            </div>
                            <div class=""layui-inline"">
                                <div class=""layui-input-inline"">
                                    <button type=""button"" class=""layui-btn layui-btn-normal"" id=""btnSearch"">查询</button>
                                </div>
                                <div class=""layui-input-inline"">
                                    <button type=""button"" class=""layui-btn layui-btn-warm"" id=""btnAnalysis"">
                                        AI分析
           ");
                    WriteLiteral(@"                         </button>
                                </div>
                                <div class=""layui-input-inline"">
                                    <button type=""button"" class=""layui-btn"" id=""btnUpload"">上传PDF</button>

                                </div>
                            </div>


                        </div>
                        <div style=""display:none"">
                            <button id=""popbtn"" type=""button"" class=""layui-btn layui-bg-blue"">
                                <i class=""layui-icon layui-icon-survey""></i>
                            </button>
                        </div>
                    ");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n");
                WriteLiteral("                </div>\r\n\r\n\r\n                <div class=\"layui-row \">\r\n                    <div class=\"layui-col-xs4 layui-col-md4 \" style=\"height:98vh\">\r\n                        <div class=\"left_wrap\">\r\n");
                WriteLiteral(@"                            <div id=""trees"" style=""height:50vh""></div>
                            <div style=""height:48vh"">
                                <textarea id=""AItips"" class=""layui-textarea"" style=""height:100%"">
                                    使用 <data></data> 标记中的内容作为你的知识:   <data>{text}</data> 回答要求：
- 如果你不清楚答案，你需要澄清。
- 避免提及你是从 <data></data> 获取的知识。
- 保持答案 与 <data></data>中描述的一致。
- 使用与问题相同的语言回答。
 -{""说明"":""从报告中提取以下特征变量，并记录对应的原文依据。"",""特征提取"":[]}
 -回答只输出以下格式的json串：
                  {\""variables\"":[{\""variable_name\"":\""\"",\""变量描述\"":\""\"",\""value\"":\""\"",\""source\"":\""\""}]}
                                </textarea>
                            </div>
                        </div>
                    </div>
                    <div class=""layui-col-xs8 layui-col-md8"">
                        <div class=""right_content"" style=""height:50vh"">
                            <textarea id=""resultPdf"" class=""layui-textarea"" style=""height:100%""></textarea>
                        </div>
           ");
                WriteLiteral(@"             <div class=""right_content"" style=""height:50vh"">
                            <table id=""tablelist2"" lay-filter=""tablelist2""></table>
                        </div>
                    </div>
                </div>
            </div>



        </div>
    </div>
    </div>



    <div id=""popwrap"" style=""display:none;height:100%;overflow:hidden;"">
        <div class=""layui-row layui-col-space30"" style=""height:100%"">
            <div class=""layui-col-md12"" style=""height:100%"">
                <div style=""background-color: #fff;height: 100%; padding:15px 15px;"">
                    <textarea id=""model_wrapR"" class=""layui-textarea"" style="" height:100%;width:100%;line-height:40px;font-size:20px;resize:none"">

</textarea>
                </div>
            </div>
        </div>


    </div>

    <div id=""resultwrap"" style=""display:none;height:100%;overflow:hidden;"">
        <div class=""layui-row layui-col-space30"" style=""height:100%"">
            <div class=""layui-col-md12");
                WriteLiteral(@""" style=""height:100%"">
                <div style=""background-color: #fff;height: 100%; padding:15px 15px;"">
                    <textarea id=""txtResult"" class=""layui-textarea"" style="" height:100%;width:100%;line-height:40px;font-size:20px;resize:none"">

</textarea>
                </div>
            </div>
        </div>


    </div>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e570e2c366efc2e3d243344b590a180ede6de5ee68696429eedf5eac60242c4b32927", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e570e2c366efc2e3d243344b590a180ede6de5ee68696429eedf5eac60242c4b34052", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_14);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
        layui.use(['jquery', 'layer', 'tree', 'form', 'element', 'upload', 'laytpl', 'table'], function () {
            var layer = layui.layer,
                $ = layui.$,
                form = layui.form,
                laytpl = layui.laytpl,
                element = layui.element,
                tree = layui.tree,
              upload = layui.upload,
                table = layui.table;

            //查询结果表格用的高度  height:""full-"" + table_top_H,
            var table_top_H;
            var windowsIndex;

            $(window).resize(function () {

                arrangement();
            })
            arrangement();
            function arrangement() {
                var winH = $(window).height();
             
               // $("".right_content"").css(""height"", (winH - 100) + ""px"");

            }

            //table.render({
            //    elem: '#tablelist'
            //    , id: 'tablelist'
            //    , url: '/Chat/PdfList'
            //");
                WriteLiteral(@"    , page: true
            //    , limit: 20
            //    , height: 'full-110'
            //    , cols: [[
            //        { type: 'checkbox' }
            //        , { field: 'zizeng', title: '', type: 'numbers', width: 40 }
            //        , { field: 'deptName', title: '科室名称', width: 100 }
            //        , { field: 'fileName', title: '文件名称', width: 100 }
            //        , { align: 'center', title: '操作', toolbar: '#tableBar1' }
            //    ]]
            //    , done: function (res, curr, count) {
            //        $('.layui-table-view[lay-id=""tablelist""]').children('.layui-table-box')
            //            .children('.layui-table-body').find('table tbody tr[data-index=0]').click();
            //    }
            //});

            $(document).ready(function () {

                $(document).on('click', '#btnUpload', function () {
                   
                    windowsIndex = layer.open({
                        type: 1,
        ");
                WriteLiteral(@"                title: '上传PDF文件',
                        area: '600px',
                        resize: true,
                        content: $('#form_window')
                    });
                     
                });
                $(document).on('click', '#btnSearch', function () {
                  
                    //loadTable();
                   loadTree();

                });
                

                $(document).on('click', '#btnAnalysis', function () {

                    var selectTrees = tree.getChecked('demo-id-1');
               // console.log(""长度"",selectTrees.length);
                // if (selectTrees.length == 0) {
                //     layer.msg(""请选择文档！"");
                //     return;
                // }
                    $(""#resultPdf"").val("""");
                    var i = 0;
                    var json = JSON.stringify(selectTrees);
                    var source = new EventSource('/Chat/AnalysePdf?json=' + encodeURIComponent(json) ");
                WriteLiteral(@"+ ""&prompt="" + encodeURIComponent($(""#AItips"").val()));
                    source.onmessage = function (event) {

                        var result = JSON.parse(event.data);
                        console.log(result);
                        if (result.okMsg) {
                          
                                var content = $(""#resultPdf"").val() + result.data;
                                $(""#resultPdf"").val(content);
                          

                            //$("".clearfix[responseCurrent='1'] p"").append(result.data);
                            i = i + 1;
                        }
                        else {
                            layer.msg(result.errorMsg);
                           // $("".clearfix[responseCurrent='1']"").removeAttr(""responseCurrent"");
                            source.close();
                        }
                        resetHistoryscrollTop();
                    };

                    source.addEventListener('end', funct");
                WriteLiteral(@"ion (event) {
                        var result = JSON.parse(event.data);
                        if (result.okMsg) {

                            console.log(event.data);
                           

                        }
                        else {
                            layer.msg(result.errorMsg);
                        }

                        // 结束事件源连接
                        source.close();
                    }, false);

                    source.onerror = function (event) {
                        //$("".clearfix[responseCurrent='1']"").removeAttr(""responseCurrent"");
                        source.close();
                    };

                });

                $(""#popbtn"").on(""click"", function () {
                    layer.open({
                        type: 1,
                        area: ['60%', '80%'],
                        resize: false,
                        shadeClose: true,
                        title: '帮助',
                        co");
                WriteLiteral(@"ntent: $(""#popwrap""),
                        success: function () {
                            // $(""#model_wrapL"").val(modelText);
                        }
                    })
                })

            });


            function loadTips(formId)
            {
                $.get('/Chat/GetTip?formId='+formId, function (res) {
                    if (res.code == 0) {

                        $(""#model_wrapR"").val(res.data);
                    }

                })
            }

           function loadTree(){
                getTree();
           }

           var treeNodeId = 0;
            var patientId = 0;
            function getTree() {
                var dept = $(""#dept"").val();
                var KeyWords = $(""#KeyWords"").val();
                $.get(""/Chat/PdfListTree?deptName="" + dept + ""&keyWords="" + KeyWords, function (res) {

                    // 渲染
                    tree.render({
                        elem: '#trees',
                    ");
                WriteLiteral(@"    data: res,
                        edit: [""preview""],
                        showCheckbox: true,
                        onlyIconControl: true,  // 是否仅允许节点左侧图标控制展开收缩
                        id: 'demo-id-1',
                        isJump: true, // 是否允许点击节点时弹出新窗口跳转
                        click: function (obj) {
                            var data = obj.data;  //获取当前点击的节点数据
                            //  layer.msg('状态：' + obj.state + '<br>节点数据：' + JSON.stringify(data));
                            console.log(obj);
                            //obj.elem.addClass(""active"");
                            // treeNodeId = data.id;
                            //   patientId = data.patientId;
                            //  $(""#trees"").find("".layui-tree-txt"").removeClass(""active"");
                            //  $(obj.elem).find("".layui-tree-txt"").eq(0).addClass(""active"");


                        }, operate: function (obj) {
                            var type = obj.type; //得到操作类型：add、edit");
                WriteLiteral(@"、del
                            var data = obj.data; //得到当前节点的数据
                            var elem = obj.elem; //得到当前节点元素

                            //Ajax 操作
                            var id = data.id; //得到节点索引
                            if (type === 'preview') { //增加节点
                                //返回 key 值
                                if (data.docmentType == ""文件"") {
                                    layer.open({
                                        type: 2,
                                        title: 'PDF预览',
                                        shadeClose: true, // 点击遮罩关闭层
                                        area: ['80%', '80%'], // 设置弹窗大小
                                        content: data.fileUrl
                                    });
                                }
                                else {
                                    layer.msg(""当前目录不支持预览！"");
                                }

                            }
                  ");
                WriteLiteral(@"      }
                    });
                })
            }
            getTree();
           

            //设置Y轴位置
            function resetHistoryscrollTop() {
                var ele = document.getElementById(""resultPdf"");
                if (ele.scrollHeight > ele.clientHeight) {
                    setTimeout(function () {
                        //设置滚动条到最底部
                        ele.scrollTop = ele.scrollHeight;
                    }, 500);
                }
            }

            function hidden() {
                $("".menu_wrap"").css(""display"", ""none"")
                $("".menu_list"").removeClass(""show_menu_list"")
            }

            function load(){
                var n = 0;
                var timer = setInterval(function () {
                    n = n + Math.random() * 10 | 0;
                    if (n > 100) {
                        n = 100;
                        clearInterval(timer);

                    }
                    element.progress");
                WriteLiteral(@"('demo-filter-progress', n + '%');
                }, 300 + Math.random() * 4000);
            }


            upload.render({
                elem: '#test10'
                , url: '/Chat/UploadFile' // 实际使用时改成您自己的上传接口即可。
                , accept: 'file' // 限制文件类型
                , acceptMime: 'application/pdf' // 仅接受 PDF 文件
                , done: function (res) {
                    layer.msg('上传成功');
                  
                   // console.log(res)
                   if(res.code==0){
                        $(""#fileName"").val(res.data.fileName);
                        $(""#guid"").val(res.data.guid);
                        $(""#newFileName"").val(res.data.newFileName);
                      
                   }
                   else
                      layer.msg(res.msg);
                }
            });

            //监听提交
            form.on('submit(submit)', function (data) {
                var indexs = layer.load(1);
                data.field.newFileName = $(");
                WriteLiteral(@"""#newFileName"").val();
                data.field.guid = $(""#guid"").val();
                $.ajax({
                    url: ""/Chat/SavePdf"",
                    type: ""post"",
                    data:data.field ,
                    datatype: 'json',
                    success: function (result) {
                        layer.close(indexs);
                        if (result.code == 0) {

                            layer.close(windowsIndex);
                            loadTree();
                        }
                        else
                            layer.msg(result.msg);
                       
                    }, error: function (res) {
                        layer.msg(""加载统计信息错误："" + res.responseText);
                        layer.close(indexs);
                    }
                });
                return false;
            });


            function loadTable()
            {
                table.reload('tablelist', {
                    //url: '/Script");
                WriteLiteral(@"/ScriptRunLog/List',//数据接口
                    page: {
                        curr: 1
                    },
                    where: { 'keyWord': $.trim($(""#KeyWords"").val()), ""deptName"": $(""#dept"").val() }
                });
            }
        });
    </script>



");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"

<div class=""window_wrap"" id=""form_window_load"" style=""display: none;background-color:transparent"">
    <div class=""layui-progress layui-progress-big"" lay-showPercent=""true"" lay-filter=""demo-filter-progress"">
        <div class=""layui-progress-bar"" lay-percent=""0%"">
        </div>

    </div>
    <p style=""text-align:center""> AI数据提取中...</p>
</div>

<div class=""window_wrap"" id=""form_window"" style=""display: none;"">
    ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e570e2c366efc2e3d243344b590a180ede6de5ee68696429eedf5eac60242c4b48385", async() => {
                WriteLiteral(@"
       
        <div class=""layui-form-item"" style=""margin-top:15px;"">
            <label class=""layui-form-label"">科室：</label>
            <div class=""layui-input-block"" style=""width:300px;"">
                <select id=""DeptName"" name=""DeptName"">
                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e570e2c366efc2e3d243344b590a180ede6de5ee68696429eedf5eac60242c4b48953", async() => {
                    WriteLiteral("眼科");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_7.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "e570e2c366efc2e3d243344b590a180ede6de5ee68696429eedf5eac60242c4b50217", async() => {
                    WriteLiteral("康复科");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_8.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
                  
                </select>
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">上传附件：</label>
            <div class=""layui-input-block line_wrap"">
                <div class=""layui-upload-drag"" id=""test10"">
                    <i class=""layui-icon""></i>
                    <p>点击上传，或将文件拖拽到此处</p>
                    <div class=""layui-hide"" id=""uploadDemoView"">
                        <hr>
                      
                        <input type=""hidden"" id=""newFileName"" />
                        <input type=""hidden"" id=""guid"" />
                    </div>
                </div>
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">文件名称：</label>
            <div class=""layui-input-block"" style=""width:300px;"">
                <input type=""text"" id=""fileName"" class=""layui-input"" name=""fileName"" />
              
            </div>
        </d");
                WriteLiteral(@"iv>
        <div class=""layui-form-item"">
            <div class=""layui-input-block"">
                <button type=""submit"" class=""layui-btn"" lay-submit lay-filter=""submit"">提交</button>
                <button type=""reset"" class=""layui-btn layui-btn-primary"" id=""btn_reset"">重置</button>
            </div>
        </div>
    ");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_15);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_16);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_17);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</div>\r\n</html>\r\n\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
