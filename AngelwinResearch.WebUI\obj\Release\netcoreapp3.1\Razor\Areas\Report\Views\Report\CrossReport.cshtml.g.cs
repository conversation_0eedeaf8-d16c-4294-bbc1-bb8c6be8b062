#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Report\Views\Report\CrossReport.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "bfe7ddcea8026f057a7dbcdcc8029e3d9e5f57ec4aa7020e28cc6407817b6e8f"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_Report_Views_Report_CrossReport), @"mvc.1.0.view", @"/Areas/Report/Views/Report/CrossReport.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"bfe7ddcea8026f057a7dbcdcc8029e3d9e5f57ec4aa7020e28cc6407817b6e8f", @"/Areas/Report/Views/Report/CrossReport.cshtml")]
    #nullable restore
    internal sealed class Areas_Report_Views_Report_CrossReport : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
            WriteLiteral("<!DOCTYPE html>\r\n<html lang=\"zh-CN\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bfe7ddcea8026f057a7dbcdcc8029e3d9e5f57ec4aa7020e28cc6407817b6e8f3062", async() => {
                WriteLiteral(@"
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <title>门诊住院—指标统计</title>
    <style>
        body {
            background: #f3f4f6;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .report-container {
            background: #fff;
            border-radius: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            padding: 2rem;
            margin: 2rem auto;
            min-width: 1200px;
            max-width: 95vw;
            position: relative;
        }

        h2 {
            text-align: center;
            margin-bottom: 2rem;
            color: #2563eb;
            margin-top: 0;
        }

        .top-bar {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 2rem;
            margin-bottom: 1.5rem;
            width: max-content;
            min-width: 0;
            margin-left");
                WriteLiteral(@": auto;
        }

        .export-btns {
            display: flex;
            gap: 1rem;
        }

        .icon-btn {
            background: #f1f5f9;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.2s;
            box-shadow: 0 1px 3px rgba(0,0,0,0.04);
            position: relative;
        }

            .icon-btn:hover {
                background: #dbeafe;
            }

            .icon-btn svg {
                width: 22px;
                height: 22px;
                color: #2563eb;
            }

            .icon-btn .tooltip {
                display: none;
                position: absolute;
                top: -32px;
                left: 50%;
                transform: translateX(-50%);
                background: #333;
       ");
                WriteLiteral(@"         color: #fff;
                padding: 4px 10px;
                border-radius: 4px;
                font-size: 12px;
                white-space: nowrap;
                pointer-events: none;
            }

            .icon-btn:hover .tooltip {
                display: block;
            }

        .search-panel {
            background: #fff;
            border-radius: 0;
            box-shadow: 0 2px 12px rgba(37,99,235,0.08);
            padding: 12px 12px 8px 12px;
            margin: 0 0 12px 0;
            width: 100%;
            box-sizing: border-box;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .search-form {
            display: flex;
            align-items: center;
            gap: 12px;
            width: 100%;
            max-width: 1000px;
        }

            .search-form label {
                color: #2563eb;
                font-size: 15px;
                font-weight: 500");
                WriteLiteral(@";
                margin-right: 4px;
            }

            .search-form select, .search-form input {
                height: 32px;
                min-width: 100px;
                max-width: 180px;
                padding: 0 10px;
                border-radius: 8px;
                border: 1.2px solid #cbd5e1;
                font-size: 14px;
                background: #fff;
                outline: none;
                transition: border 0.2s;
            }

                .search-form select:focus, .search-form input:focus {
                    border: 1.5px solid #2563eb;
                }

            .search-form button {
                background: #2563eb;
                color: #fff;
                border: none;
                border-radius: 8px;
                height: 32px;
                padding: 0 18px;
                font-size: 15px;
                font-weight: 500;
                cursor: pointer;
                transition: background 0.2s;
        ");
                WriteLiteral(@"        margin-left: 4px;
                box-shadow: 0 1px 4px rgba(37,99,235,0.08);
            }

                .search-form button:hover {
                    background: #1d4ed8;
                }

        .table-toolbar {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-bottom: 6px;
            margin-top: -8px;
        }

        ");
                WriteLiteral(@"@media (max-width: 900px) {
            .search-panel {
                padding: 6px 2px 4px 2px;
            }

            .search-form {
                gap: 6px;
                flex-wrap: wrap;
            }

                .search-form select, .search-form input, .search-form button {
                    height: 28px;
                    font-size: 13px;
                }

            .table-toolbar {
                margin-bottom: 2px;
            }
        }

        ");
                WriteLiteral(@"@media (max-width: 600px) {
            .search-form {
                flex-direction: column;
                align-items: stretch;
                gap: 4px;
            }

            .search-panel {
                padding: 2px 1px;
            }
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: #fff;
            font-size: 15px;
        }

        th, td {
            border: 1px solid #e5e7eb;
            padding: 0.5rem 0.7rem;
            text-align: center;
            min-width: 60px;
        }

        th {
            background: #f1f5f9;
            color: #1e293b;
            font-weight: 600;
        }

        .slant-header {
            position: relative;
            height: 60px;
            min-width: 80px;
            background: #ede9fe;
        }

            .slant-header span {
                position: absolute;
                left: 0;
                top: 0;
                wi");
                WriteLiteral(@"dth: 100%;
                height: 100%;
                display: flex;
                align-items: flex-start;
                justify-content: flex-start;
                font-size: 13px;
                color: #555;
            }

            .slant-header:before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                border-top: 2px solid #bdbdbd;
                border-right: 2px solid #bdbdbd;
                transform: skew(-30deg);
                z-index: 0;
            }

        .group {
            background: #e0f2fe;
            font-weight: bold;
        }

        .subtotal {
            background: #fef3c7;
            color: #b45309;
            font-weight: bold;
        }

        .highlight {
            color: #fff;
            background: #38bdf8;
            font-weight: bold;
        }

        .total-row {
    ");
                WriteLiteral(@"        background: #fde68a;
            color: #b45309;
            font-weight: bold;
        }

        tr:hover td {
            background: #f0f9ff;
        }
    </style>
    <!-- xlsx.js CDN -->
    <script src=""https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js""></script>
    <!-- jsPDF CDN -->
    <script src=""https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js""></script>
    <script src=""https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.7.0/jspdf.plugin.autotable.min.js""></script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "bfe7ddcea8026f057a7dbcdcc8029e3d9e5f57ec4aa7020e28cc6407817b6e8f11965", async() => {
                WriteLiteral(@"
    <div class=""report-container"">
        <h2>门诊、住院—指标统计</h2>
        <div class=""search-panel"">
            <form class=""search-form"" id=""searchForm"" onsubmit=""return false;"">
                <label for=""deptSelect"">科室：</label>
                <select id=""deptSelect"">
                    <option");
                BeginWriteAttribute("value", " value=\"", 7983, "\"", 7991, 0);
                EndWriteAttribute();
                WriteLiteral(@">全部</option>
                    <option value=""内科"">内科</option>
                    <option value=""外科"">外科</option>
                    <option value=""儿科"">儿科</option>
                </select>
                <label for=""doctorSelect"">医生：</label>
                <select id=""doctorSelect"">
                    <option");
                BeginWriteAttribute("value", " value=\"", 8315, "\"", 8323, 0);
                EndWriteAttribute();
                WriteLiteral(@">全部</option>
                    <option value=""张医生"" data-dept=""内科"">张医生</option>
                    <option value=""李医生"" data-dept=""内科"">李医生</option>
                    <option value=""王医生"" data-dept=""内科"">王医生</option>
                    <option value=""赵医生"" data-dept=""外科"">赵医生</option>
                    <option value=""钱医生"" data-dept=""外科"">钱医生</option>
                    <option value=""孙医生"" data-dept=""外科"">孙医生</option>
                    <option value=""周医生"" data-dept=""外科"">周医生</option>
                    <option value=""吴医生"" data-dept=""儿科"">吴医生</option>
                    <option value=""郑医生"" data-dept=""儿科"">郑医生</option>
                </select>
                <label for=""startDate"">开始日期：</label>
                <input type=""date"" id=""startDate"" name=""startDate"" />
                <label for=""endDate"">结束日期：</label>
                <input type=""date"" id=""endDate"" name=""endDate"" />
                <button type=""button"" id=""searchBtn"">检索</button>
                <button type=""button"" id=""resetBtn"">");
                WriteLiteral(@"重置</button>
            </form>
        </div>
        <div class=""table-toolbar"">
            <div class=""export-btns"">
                <button class=""icon-btn"" id=""excelExport"" title=""导出为Excel"">
                    <span class=""tooltip"">导出为Excel</span>
                    <svg viewBox=""0 0 24 24"" fill=""none"" stroke=""currentColor"" stroke-width=""2"" stroke-linecap=""round"" stroke-linejoin=""round""><path d=""M16 16v2a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v2"" /><polyline points=""15 10 20 10 20 15"" /><line x1=""10"" y1=""14"" x2=""20"" y2=""4"" /></svg>
                </button>
                <button class=""icon-btn"" id=""pdfExport"" title=""下载为PDF"">
                    <span class=""tooltip"">下载为PDF</span>
                    <svg viewBox=""0 0 24 24"" fill=""none"" stroke=""currentColor"" stroke-width=""2"" stroke-linecap=""round"" stroke-linejoin=""round""><path d=""M12 20h9"" /><path d=""M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19.5 2 20l.5-5L16.5 3.5z"" /></svg>
                </button>
                <span");
                WriteLiteral(@" class=""download-count"">下载次数：5次</span>
            </div>
        </div>
        <table id=""medicalTable"">
            <thead>
                <tr>
                    <th rowspan=""3"">科室</th>
                    <th rowspan=""3"">医生</th>
                    <th class=""slant-header"" colspan=""9"">
                        <span>月份 / 指标</span>
                    </th>
                    <th rowspan=""3"">小计</th>
                </tr>
                <tr>
                    <th colspan=""3"">202401</th>
                    <th colspan=""3"">202402</th>
                    <th colspan=""3"">202403</th>
                </tr>
                <tr>
                    <th>门诊人次</th>
                    <th>住院人次</th>
                    <th>手术例数</th>
                    <th>门诊人次</th>
                    <th>住院人次</th>
                    <th>手术例数</th>
                    <th>门诊人次</th>
                    <th>住院人次</th>
                    <th>手术例数</th>
                </tr>
            </thead>
      ");
                WriteLiteral(@"      <tbody>
                <tr class=""group""><td rowspan=""3"">内科</td><td>张医生</td><td>320</td><td>45</td><td>5</td><td>310</td><td>40</td><td>4</td><td>350</td><td class=""highlight"">60</td><td>6</td><td>1135</td></tr>
                <tr><td>李医生</td><td>280</td><td>38</td><td>3</td><td>295</td><td>35</td><td>2</td><td>310</td><td>40</td><td>4</td><td>1047</td></tr>
                <tr><td>王医生</td><td>260</td><td>30</td><td>2</td><td>270</td><td>28</td><td>1</td><td>290</td><td>35</td><td>3</td><td>949</td></tr>
                <tr class=""subtotal""><td colspan=""2"">小计</td><td>860</td><td>113</td><td>10</td><td>875</td><td>103</td><td>7</td><td>950</td><td>135</td><td>13</td><td>3161</td></tr>
                <tr class=""group""><td rowspan=""4"">外科</td><td>赵医生</td><td>190</td><td>50</td><td>10</td><td>200</td><td>48</td><td>9</td><td>210</td><td>55</td><td>11</td><td>783</td></tr>
                <tr><td>钱医生</td><td>210</td><td>60</td><td>15</td><td>220</td><td>55</td><td>12</td><td>230</td><td>70</td><td cl");
                WriteLiteral(@"ass=""highlight"">18</td><td>890</td></tr>
                <tr><td>孙医生</td><td>180</td><td>45</td><td>8</td><td>190</td><td>42</td><td>7</td><td>200</td><td>50</td><td>9</td><td>731</td></tr>
                <tr><td>周医生</td><td>170</td><td>40</td><td>6</td><td>180</td><td>38</td><td>5</td><td>190</td><td>45</td><td>7</td><td>719</td></tr>
                <tr class=""subtotal""><td colspan=""2"">小计</td><td>750</td><td>195</td><td>39</td><td>790</td><td>183</td><td>33</td><td>830</td><td>220</td><td>45</td><td>3085</td></tr>
                <tr class=""group""><td rowspan=""2"">儿科</td><td>吴医生</td><td>150</td><td>20</td><td>0</td><td>160</td><td>18</td><td>0</td><td>170</td><td>25</td><td>0</td><td>543</td></tr>
                <tr><td>郑医生</td><td>140</td><td>18</td><td>0</td><td>150</td><td>15</td><td>0</td><td>160</td><td>20</td><td>0</td><td>503</td></tr>
                <tr class=""subtotal""><td colspan=""2"">小计</td><td>290</td><td>38</td><td>0</td><td>310</td><td>33</td><td>0</td><td>330</td><td>45</td><td>0</td><");
                WriteLiteral(@"td>1046</td></tr>
                <tr class=""total-row""><td colspan=""2"">总计</td><td>1900</td><td>346</td><td>49</td><td>1975</td><td>319</td><td>40</td><td>2110</td><td>400</td><td>58</td><td>7197</td></tr>
            </tbody>
        </table>
    </div>
    <script>
        // Excel导出
        document.getElementById('excelExport').onclick = function() {
            var wb = XLSX.utils.table_to_book(document.getElementById('medicalTable'), {sheet: ""Sheet1""});
            XLSX.writeFile(wb, '医院临床医疗交叉报表.xlsx');
        };
        // PDF导出
        document.getElementById('pdfExport').onclick = function() {
            var { jsPDF } = window.jspdf;
            var doc = new jsPDF('l', 'pt', 'a4');
            doc.text('门诊、住院—指标统计', 40, 40);
            doc.autoTable({
                html: '#medicalTable',
                startY: 60,
                styles: { font: 'helvetica', fontSize: 8 },
                headStyles: { fillColor: [241,245,249], textColor: 30, fontStyle: 'bold' },
         ");
                WriteLiteral(@"       theme: 'grid',
                margin: { left: 40, right: 40 }
            });
            doc.save('医院临床医疗交叉报表.pdf');
        };
        // 检索功能
        const deptSelect = document.getElementById('deptSelect');
        const doctorSelect = document.getElementById('doctorSelect');
        const table = document.getElementById('medicalTable');
        const searchBtn = document.getElementById('searchBtn');
        const resetBtn = document.getElementById('resetBtn');
        // 联动医生下拉
        deptSelect.addEventListener('change', function() {
            const dept = this.value;
            for (let i = 0; i < doctorSelect.options.length; i++) {
                const opt = doctorSelect.options[i];
                if (!opt.value) { opt.style.display = ''; continue; }
                if (!dept || opt.getAttribute('data-dept') === dept) {
                    opt.style.display = '';
                } else {
                    opt.style.display = 'none';
                }
            }");
                WriteLiteral(@"
            doctorSelect.value = '';
        });
        // 检索按钮
        searchBtn.onclick = function() {
            const dept = deptSelect.value;
            const doctor = doctorSelect.value;
            for (const row of table.tBodies[0].rows) {
                if (row.classList.contains('subtotal') || row.classList.contains('total-row')) {
                    row.style.display = '';
                    continue;
                }
                const tds = row.getElementsByTagName('td');
                if (tds.length < 2) { row.style.display = ''; continue; }
                const rowDept = row.classList.contains('group') ? tds[0].textContent : row.previousElementSibling && row.previousElementSibling.classList.contains('group') ? row.previousElementSibling.cells[0].textContent : '';
                const rowDoctor = tds[1].textContent;
                let show = true;
                if (dept && tds[0].textContent !== dept) show = false;
                if (doctor && tds[1].textCont");
                WriteLiteral(@"ent !== doctor) show = false;
                row.style.display = show ? '' : 'none';
            }
        };
        // 重置按钮
        resetBtn.onclick = function() {
            deptSelect.value = '';
            doctorSelect.value = '';
            for (let i = 0; i < doctorSelect.options.length; i++) doctorSelect.options[i].style.display = '';
            for (const row of table.tBodies[0].rows) row.style.display = '';
        };
    </script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html> ");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
