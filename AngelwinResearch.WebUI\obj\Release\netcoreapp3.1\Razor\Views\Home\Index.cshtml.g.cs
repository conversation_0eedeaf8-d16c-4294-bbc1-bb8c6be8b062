#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Home\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "76c107587309bb3c0b0910c536ffbb34b50c8e2e99f812d49b07c27f3cd9144e"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_Home_Index), @"mvc.1.0.view", @"/Views/Home/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI

#nullable disable
    ;
#nullable restore
#line 2 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"76c107587309bb3c0b0910c536ffbb34b50c8e2e99f812d49b07c27f3cd9144e", @"/Views/Home/Index.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"a5bcd83cf27c8ffb8baf597d24314352bf7b52b5a9bb5ee83e83e9d0089a628e", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_Home_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<
#nullable restore
#line 5 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Home\Index.cshtml"
       List<MenuTreeDTO>

#line default
#line hidden
#nullable disable
    >
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("name", "MenuHtmlPartialChild", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("layui-layout-body"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_PartialTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Home\Index.cshtml"
  
    ViewBag.Title = "主页";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "76c107587309bb3c0b0910c536ffbb34b50c8e2e99f812d49b07c27f3cd9144e6656", async() => {
                WriteLiteral("\r\n    <meta charset=\"utf-8\">\r\n    <title> ");
                Write(
#nullable restore
#line 10 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Home\Index.cshtml"
             ViewBag.SiteTitle

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</title>
    <meta name=""renderer"" content=""webkit"">
    <meta http-equiv=""X-UA-Compatible"" content=""IE=edge,chrome=1"">
    <meta name=""viewport""
          content=""width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0"">
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "76c107587309bb3c0b0910c536ffbb34b50c8e2e99f812d49b07c27f3cd9144e7538", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "76c107587309bb3c0b0910c536ffbb34b50c8e2e99f812d49b07c27f3cd9144e8740", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "76c107587309bb3c0b0910c536ffbb34b50c8e2e99f812d49b07c27f3cd9144e9942", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "76c107587309bb3c0b0910c536ffbb34b50c8e2e99f812d49b07c27f3cd9144e11769", async() => {
                WriteLiteral(@"
    <div id=""LAY_app"">
        <div class=""layui-layout layui-layout-admin"">
            <div class=""layui-header"">
                <!-- 头部区域 -->
                <ul class=""layui-nav layui-layout-left"">
                    <li class=""layui-nav-item layadmin-flexible"" lay-unselect>
                        <a href=""javascript:;"" layadmin-event=""flexible"" title=""侧边伸缩"">
                            <i class=""layui-icon layui-icon-shrink-right"" id=""LAY_app_flexible""></i>
                        </a>
                    </li>
                    <li class=""layui-nav-item"" lay-unselect>
                        <a href=""javascript:;"" layadmin-event=""refresh"" title=""刷新"">
                            <i class=""layui-icon layui-icon-refresh-3""></i>
                        </a>
                    </li>
                </ul>
                <ul class=""layui-nav layui-layout-right"" lay-filter=""layadmin-layout-right"">
                    <li class=""layui-nav-item layui-hide-xs"" lay-unselect>
             ");
                WriteLiteral(@"           <a href=""javascript:;"" layadmin-event=""theme"">
                            <i class=""layui-icon layui-icon-theme""></i>
                        </a>
                    </li>
                    <li class=""layui-nav-item layui-hide-xs"" lay-unselect>
                        <a href=""javascript:;"" layadmin-event=""fullscreen"">
                            <i class=""layui-icon layui-icon-screen-full""></i>
                        </a>
                    </li>
                    <li class=""layui-nav-item"" lay-unselect>
                        <a href=""javascript:;"">
                            <cite id=""WorkId"">");
                Write(
#nullable restore
#line 49 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Home\Index.cshtml"
                                               ViewBag.trueName

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</cite>\r\n                        </a>\r\n");
#nullable restore
#line 51 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Home\Index.cshtml"
                         if (ViewBag.LoginType != "Single")
                        {

#line default
#line hidden
#nullable disable

                WriteLiteral(@"                            <dl class=""layui-nav-child"">
                                <dd><a lay-href=""/Account/ResetPwd"">修改密码</a></dd>
                                <dd><a lay-href=""/Home/changelog"">更新日志</a></dd>
                                <hr />
                                <dd style=""text-align: center;""><a href=""#"" id=""signOut"">退出</a></dd>
                            </dl>
");
#nullable restore
#line 59 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Home\Index.cshtml"
                        }

#line default
#line hidden
#nullable disable

                WriteLiteral(@"
                    </li>
                </ul>
            </div>

            <!-- 侧边菜单 -->
            <div class=""layui-side layui-side-menu"">
                <div class=""layui-side-scroll"">
                    <div class=""layui-logo"">
                        <img src=""/images/Hospitallogo.png"" style=""width:190px;"" />
");
                WriteLiteral("                    </div>\r\n                    <ul class=\"layui-nav layui-nav-tree\" lay-shrink=\"all\" id=\"LAY-system-side-menu\" lay-filter=\"layadmin-system-side-menu\">\r\n");
#nullable restore
#line 73 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Home\Index.cshtml"
                          
                            foreach (var treeDTO in Model)
                            {


#line default
#line hidden
#nullable disable

                WriteLiteral("                                <li data-name=\"");
                Write(
#nullable restore
#line 77 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Home\Index.cshtml"
                                                treeDTO.MenuID

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\" class=\"layui-nav-item\">\r\n                                    <a href=\"javascript:;\"");
                BeginWriteAttribute("lay-tips", " lay-tips=\"", 3718, "\"", 3746, 1);
                WriteAttributeValue("", 3729, 
#nullable restore
#line 78 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Home\Index.cshtml"
                                                                      treeDTO.MenuName

#line default
#line hidden
#nullable disable
                , 3729, 17, false);
                EndWriteAttribute();
                WriteLiteral(" lay-direction=\"2\">\r\n                                        <i");
                BeginWriteAttribute("class", " class=\"", 3810, "\"", 3846, 2);
                WriteAttributeValue("", 3818, "layui-icon", 3818, 10, true);
                WriteAttributeValue(" ", 3828, 
#nullable restore
#line 79 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Home\Index.cshtml"
                                                              treeDTO.MenuIcon

#line default
#line hidden
#nullable disable
                , 3829, 17, false);
                EndWriteAttribute();
                WriteLiteral("></i>\r\n                                        <cite>");
                Write(
#nullable restore
#line 80 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Home\Index.cshtml"
                                               treeDTO.MenuName

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</cite>\r\n                                    </a>\r\n                                    <dl class=\"layui-nav-child\">\r\n                                        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("partial", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "76c107587309bb3c0b0910c536ffbb34b50c8e2e99f812d49b07c27f3cd9144e18057", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_PartialTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_PartialTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_PartialTagHelper.Name = (string)__tagHelperAttribute_4.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_4);
                __Microsoft_AspNetCore_Mvc_TagHelpers_PartialTagHelper.Model = 
#nullable restore
#line 83 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Home\Index.cshtml"
                                                                                     (

#line default
#line hidden
#nullable disable
#nullable restore
#line 83 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Home\Index.cshtml"
                                                                                      new MenuPartialDTO
                                            {
                                                MenuTreeDTOList = treeDTO.Children,
                                                Ticket = ViewBag.Ticket,
                                                UserName = ViewBag.UserName
                                            }

#line default
#line hidden
#nullable disable
#nullable restore
#line 88 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Home\Index.cshtml"
                                             )

#line default
#line hidden
#nullable disable
                ;
                __tagHelperExecutionContext.AddTagHelperAttribute("model", __Microsoft_AspNetCore_Mvc_TagHelpers_PartialTagHelper.Model, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n                                    </dl>\r\n\r\n                                </li>\r\n");
#nullable restore
#line 92 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Home\Index.cshtml"
                            }
                        

#line default
#line hidden
#nullable disable

                WriteLiteral(@"
                    </ul>
                </div>
            </div>

            <!-- 页面标签 -->
            <div class=""layadmin-pagetabs"" id=""LAY_app_tabs"">
                <div class=""layui-icon layadmin-tabs-control layui-icon-prev"" layadmin-event=""leftPage""></div>
                <div class=""layui-icon layadmin-tabs-control layui-icon-next"" layadmin-event=""rightPage""></div>
                <div class=""layui-icon layadmin-tabs-control layui-icon-down"">
                    <ul class=""layui-nav layadmin-tabs-select"" lay-filter=""layadmin-pagetabs-nav"">
                        <li class=""layui-nav-item"" lay-unselect>
                            <a href=""javascript:;""></a>
                            <dl class=""layui-nav-child layui-anim-fadein"">
                                <dd layadmin-event=""closeThisTabs""><a href=""javascript:;"">关闭当前标签页</a></dd>
                                <dd layadmin-event=""closeOtherTabs""><a href=""javascript:;"">关闭其它标签页</a></dd>
                                <dd la");
                WriteLiteral(@"yadmin-event=""closeAllTabs""><a href=""javascript:;"">关闭全部标签页</a></dd>
                            </dl>
                        </li>
                    </ul>
                </div>
                <div class=""layui-tab"" lay-unauto lay-allowClose=""true"" lay-filter=""layadmin-layout-tabs"">
                    <ul class=""layui-tab-title"" id=""LAY_app_tabsheader"">
                        <li lay-id=""/home/<USER>"" lay-attr=""/home/<USER>"" class=""layui-this""><i class=""layui-icon layui-icon-home""></i></li>
                    </ul>
                </div>
            </div>


            <!-- 主体内容 -->
            <div class=""layui-body"" id=""LAY_app_body"">
                <div class=""layadmin-tabsbody-item layui-show"">
                    <iframe src=""/PatientDiscoveryManage/DataCollectionManagement"" frameborder=""0"" class=""layadmin-iframe""></iframe>
");
                WriteLiteral("                </div>\r\n            </div>\r\n\r\n            <!-- 辅助元素，一般用于移动设备下遮罩 -->\r\n            <div class=\"layadmin-body-shade\" layadmin-event=\"shade\"></div>\r\n        </div>\r\n\r\n    </div>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "76c107587309bb3c0b0910c536ffbb34b50c8e2e99f812d49b07c27f3cd9144e23235", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
        layui.config({
            base: '/layuiadmin/' //静态资源所在路径
            , verson: true
        }).extend({
            index: 'lib/index' //主入口模块
        }).use(['index', 'common', 'laytpl', 'element'], function () {
            var $ = layui.$;
            $(""#signOut"").click(function () {
                signOut();
            });

            $(document).ready(function () {
                //parent.layui.index.openTabsPage(""/Chat/Chat/Index"", ""AI智能问答平台"");
            });

            $("".layui-side-scroll li a"").mouseover(function () {
                $(this).addClass(""layui-this"");
            }).mouseout(function () {
                $(this).removeClass(""layui-this"");
            });
            function signOut() {
                window.location.href = ""/Account/LogOff"";
            }
        });

    </script>

");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<List<MenuTreeDTO>> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
