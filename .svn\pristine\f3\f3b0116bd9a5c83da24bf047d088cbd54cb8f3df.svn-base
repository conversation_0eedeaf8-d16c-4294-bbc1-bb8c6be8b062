﻿using AngelwinResearch.Library.AngelwinResearch.Models;
using AngelwinResearch.ModelExtends;
using AngelwinResearch.Models;
using AngelwinResearch.WebUI.Filters;
using AngelwinResearch.WebUI.Unity;
using Common.DataSourceSupport;
using Common.Tools;
using FluentFTP;
using FluentFTP.Exceptions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.FileProviders;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web;
using static AngelwinResearch.WebUI.Unity.Common;

namespace AngelwinResearch.WebUI.Areas.CMIS.Controllers
{
    [Area("CMIS")]
    [Authorizing]
    public class CMISDataDetailsController : Controller
    {
        private readonly IWebHostEnvironment env;
        private IConfiguration config { get; }
        private readonly AngelwinResearchDbContext db;
        public CMISDataDetailsController(IWebHostEnvironment _env, IConfiguration configuration, AngelwinResearchDbContext _db)
        {
            env = _env;
            config = configuration;
            db = _db;
        }

        public IActionResult Index()
        {
            return View();
        }

        public List<LayuiTreeDTO> GetCRFIdsList()
        {
            List<CMISCRFSetting> dd = db.CMISCRFSettings.ToList();
            var MenuTreeDTOList = new List<LayuiTreeDTO>();
            foreach (var r in dd)
            {
                var result = new LayuiTreeDTO();
                result.id = r.Id;
                result.spread = true;
                result.title = r.FormName;
                result.intro = "";
                MenuTreeDTOList.Add(result);
            };
            return MenuTreeDTOList;
        }

        public class LayuiTreeDTO
        {
            public int id { get; set; }
            public string title { get; set; }
            public string intro { get; set; }
            public bool spread { get; set; }
            public bool @checked { get; set; }
        }

        public static IConfigurationRoot GetXmlConfig(string configFileName = "GetPatientList",
    string basePath = "")
        {
            basePath = string.IsNullOrWhiteSpace(basePath) ? Directory.GetCurrentDirectory() : basePath;
            var builder = new ConfigurationBuilder().
               AddXmlFile(b =>
               {
                   b.Path = $"SQLMaps/{configFileName}.xml";
                   b.FileProvider = new PhysicalFileProvider(basePath);
               });
            return builder.Build();
        }

        public IActionResult detail(string json, string formId, int ResearchPatientId, int Fid, string DocDetailedNo)
        {
            var outfacMsg = "";
            var errMsg = "";
            var OutputDict = new Dictionary<string, string>();
            var query = db.CMISCRFSettings.Where(o => o.FormId == formId).FirstOrDefault();

            var DBType = GetXmlConfig("GetPDFUrl").GetSection(query.SystemId + ":DBType").Value;
            var DBConn = GetXmlConfig("GetPDFUrl").GetSection(query.SystemId + ":DBConn").Value;
            var ProcName = GetXmlConfig("GetPDFUrl").GetSection(query.SystemId + ":ProcName").Value;

            var dataset = DataSourceFactory.GetMyFactory(DBType, out outfacMsg)?.CreateContext()?
                .GetList<JianChaDTO>(CommandType.Text, DBConn, $"exec {ProcName} '{DocDetailedNo}'", null,
                out OutputDict, out errMsg);
            if (!string.IsNullOrEmpty(outfacMsg + errMsg))
            {
                LoggerHelper.WriteInfo("其他日志", $"执行获取报告时报错，ApplyInfo的DocDetailedNo={DocDetailedNo},报错信息：{outfacMsg + errMsg}");
            }
            ViewBag.pdfurl = dataset?[0].PDFPath; //"ftp://192.168.2.147/分类规则.pdf";// "ftp:\\zzbg:ZZbg@192.6.85.112\\2025\\05\\09\\6786187.pdf";
            ViewBag.json = json;
            ViewBag.formId = formId;
            ViewBag.ResearchPatientId = ResearchPatientId;
            ViewBag.Fid = Fid;
            ViewBag.formUrl = config["AppSettings:AnyReportUrl"];
            var token = TokenGenerator.getToken();
            ViewBag.token = WebUtility.UrlEncode(token);
            return View();
        }


        public IActionResult GetPdf(string pdfurl)
        {
            try
            {
                if (!string.IsNullOrEmpty(pdfurl))
                {
                    pdfurl = pdfurl.Replace('\\', '/');
                    // === 新增代码：修复密码中的 @ 未编码问题 ===
                    if (pdfurl.StartsWith("ftp://", StringComparison.OrdinalIgnoreCase))
                    {
                        int lastAtIndex = pdfurl.LastIndexOf('@');
                        if (lastAtIndex != -1)
                        {
                            string userInfoAndHost = pdfurl.Substring(6); // 去掉 "ftp://"
                            string[] parts = userInfoAndHost.Split( '@');
                            if (parts.Length == 3)
                            {
                                string userInfo = parts[0];
                                int colonIndex = userInfo.IndexOf(':');
                                if (colonIndex != -1)
                                {
                                    string username = userInfo.Substring(0, colonIndex);
                                    string password = userInfo.Substring(colonIndex + 1);
                                    string encodedPassword = Uri.EscapeDataString(password);
                                    string correctedUrl = $"ftp://{username}:{encodedPassword}${parts[1]}@{parts[2]}";
                                    pdfurl = correctedUrl;
                                }
                            }
                        }
                    }
                    // === 结束新增代码 ===
                    if (!Uri.TryCreate(pdfurl, UriKind.Absolute, out Uri uri) || uri.Scheme != "ftp")
                    {
                        return BadRequest("Invalid FTP URL format");
                    }

                    string user = null;
                    string pass = null;
                    if (!string.IsNullOrEmpty(uri.UserInfo))
                    {
                        var credentials = uri.UserInfo.Split(new[] { ':' }, 2);
                        user = HttpUtility.UrlDecode(credentials[0]);
                        pass = credentials.Length > 1 ? HttpUtility.UrlDecode(credentials[1]).Replace('$','@') : "";
                    }

                    string port = uri.Port != 21 ? ":" + uri.Port : "";
                    string host = uri.Host + port;

                    string path = Uri.UnescapeDataString(uri.AbsolutePath); // 解码路径

                    using (var conn = new FtpClient(host))
                    {
                        if (!string.IsNullOrEmpty(user) && !string.IsNullOrEmpty(pass))
                        {
                            conn.Credentials = new System.Net.NetworkCredential(user, pass);
                        }

                        conn.Connect(); using (var ms = new MemoryStream())
                        {
                            conn.DownloadStream(ms, path); ms.Position = 0;
                            return File(ms.ToArray(), "application/pdf");
                        }
                    }
                }
                else
                {
                    return StatusCode(500, $"pdf地址为空！");
                }
            }
            catch (System.Exception ex)
            { // 处理异常 
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }           
        }

        public IActionResult List(int page, int limit, int CRFId, string DataType, string keyWord, string date)
        {
            var totalCount = "0";
            dynamic resultCols = new List<dynamic>();
            dynamic col1 = new List<dynamic>(); //列头1
            dynamic col2 = new List<dynamic>(); //列头2
            var dataRowList = new List<dynamic>();

            var List = new List<CMISCRFData>();
            try
            {
                var cRForm = new
                {
                    CRForm = (from s in db.CMISCRFSettings
                              where s.Id == CRFId
                              select s).SingleOrDefault(),
                    FormFieldList = GetCRFormField(CRFId)
                };
                var query = db.CMISCRFDatas.Include(o => o.CMISCRFSetting).Where(o => o.CMISCRFSettingId == CRFId)
                    .AsQueryable();
                if (!string.IsNullOrWhiteSpace(DataType))
                {
                    query = query.Where(o => o.DataType == DataType).AsQueryable();
                }
                if (!string.IsNullOrWhiteSpace(date))
                {
                    var startdate = Convert.ToDateTime(date.Split('~')[0].Trim() + " 00:00:00");
                    var enddate = Convert.ToDateTime(date.Split('~')[1].Trim() + " 23:59:59");
                    query = query.Where(p => p.CreatedTime != null && p.CreatedTime >= startdate && p.CreatedTime <= enddate);
                }
                else
                {
                    var enddate = DateTime.Now;
                    var startdate = new DateTime(enddate.Year, enddate.Month, 1);
                    query = query.Where(p => p.CreatedTime != null && p.CreatedTime >= startdate && p.CreatedTime <= enddate);
                }
                var CommAPI = new CommAPIController(db, env, config);

                List = query
                     .Select(o => new CMISCRFData
                     {
                         Id = o.Id,
                         CMISCRFSettingId = o.CMISCRFSettingId,
                         ProjectName = o.ProjectName,
                         CreatedTime = o.CreatedTime,
                         DocDetailedNo = o.DocDetailedNo,
                         DataType = o.DataType,
                         AIExtractJsonValue = o.AIExtractJsonValue,
                         PatientId = o.PatientId,
                         TotalField = o.TotalField,
                         FillField = o.FillField,
                         CRFJsonValue = o.CRFJsonValue
                     })
                    .ToList();

                var dictKeyList = db.CRFormFieldSets.Include(o => o.CRForm)
                    .Where(o => o.CRForm.FormId == cRForm.CRForm.FormId)
                    .Select(o => new KeyValueDTO { Id = o.CRFormId, FormId = o.CRForm.FormId, Orderby = o.Orderby, Name = o.FieldName, Value = o.FieldComment })
                    .OrderBy(o => o.Orderby)
                    .Distinct().ToList();

                var patientList = (from s in db.ResearchPatients join q in query on s.HisPatientId equals q.PatientId select s).Distinct().ToList();
                if (!string.IsNullOrWhiteSpace(keyWord))
                {
                    keyWord = keyWord.Trim();
                    patientList = (from s in db.ResearchPatients join q in query on s.HisPatientId equals q.PatientId select s).Where(o => o.HisPatientId == keyWord || o.PatientName.Contains(keyWord)).Distinct().ToList();
                }



                #region 拼接表头
                #region 拼接患者相关列头 1-5
                dynamic col1item = new ExpandoObject();
                col1item.field = "zizeng";
                col1item.type = "numbers";
                col1item.title = "";
                col1item.align = "center";
                col1item.@fixed = "left";
                col1item.rowspan = 2;
                col1.Add(col1item);

                dynamic col1item2 = new ExpandoObject();
                col1item2.field = "PatientName";
                col1item2.title = $"<span class=\"table_header\">患者姓名</span>";
                col1item2.width = 120;
                col1item2.rowspan = 2;
                col1item2.align = "center";
                col1item2.@fixed = "left";
                col1.Add(col1item2);

                dynamic col1item3 = new ExpandoObject();
                col1item3.field = "HisPatientId";
                col1item3.title = $"<span class=\"table_header\">患者Id</span>";
                col1item3.rowspan = 2;
                col1item3.align = "center";
                col1item3.width = 150;
                col1item3.@fixed = "left";
                col1.Add(col1item3);

                dynamic col1itemSex = new ExpandoObject();
                col1itemSex.field = "Sex";
                col1itemSex.title = $"<span class=\"table_header\">性别</span>";
                col1itemSex.width = 80;
                col1itemSex.rowspan = 2;
                col1itemSex.align = "center";
                col1.Add(col1itemSex);

                dynamic col1itemAge = new ExpandoObject();
                col1itemAge.field = "ParticipatAge";
                col1itemAge.title = $"<span class=\"table_header\">年龄</span>";
                col1itemAge.width = 100;
                col1itemAge.rowspan = 2;
                col1itemAge.align = "center";
                col1.Add(col1itemAge);

                dynamic col1itemJCLX = new ExpandoObject();
                col1itemJCLX.field = "MedicalCenter";
                col1itemJCLX.title = $"<span class=\"table_header\">检查类型</span>";
                col1itemJCLX.width = 170;
                col1itemJCLX.rowspan = 2;
                col1itemJCLX.align = "center";
                col1.Add(col1itemJCLX);

                dynamic col1itemSJLX = new ExpandoObject();
                col1itemSJLX.field = "DataType";
                col1itemSJLX.title = $"<span class=\"table_header\">数据类型</span>";
                col1itemSJLX.width = 170;
                col1itemSJLX.rowspan = 2;
                col1itemSJLX.align = "center";
                col1.Add(col1itemSJLX);

                ///增加ProjectName、DocDetailedNo addbyzolf 20250509
                dynamic col1itemProjectName = new ExpandoObject();
                col1itemProjectName.field = "ProjectName";
                col1itemProjectName.title = $"<span class=\"table_header\">项目名称</span>";
                col1itemProjectName.width = 170;
                col1itemProjectName.rowspan = 2;
                col1itemProjectName.align = "center";
                col1.Add(col1itemProjectName);

                dynamic col1itemDocDetailedNo = new ExpandoObject();
                col1itemDocDetailedNo.field = "DocDetailedNo";
                col1itemDocDetailedNo.title = $"<span class=\"table_header\">申请单号</span>";
                col1itemDocDetailedNo.width = 170;
                col1itemDocDetailedNo.rowspan = 2;
                col1itemDocDetailedNo.align = "center";
                col1.Add(col1itemDocDetailedNo);

                dynamic col1itemCreatedTime = new ExpandoObject();
                col1itemCreatedTime.field = "CreatedTime";
                col1itemCreatedTime.title = $"<span class=\"table_header\">创建日期</span>";
                col1itemCreatedTime.width = 170;
                col1itemCreatedTime.rowspan = 2;
                col1itemCreatedTime.align = "center";
                col1.Add(col1itemCreatedTime);

                dynamic col2ItemYC = new ExpandoObject();
                col2ItemYC.field = "AIExtractJsonValue";
                col2ItemYC.title = $"<span class=\"table_header\">AIExtractJsonValue</span>";
                col2ItemYC.width = 200;
                col2ItemYC.align = "center";

                #endregion

                #region 拼接表单列头

                if (cRForm.FormFieldList.Any() && cRForm.FormFieldList.Count() > 0)
                {
                    dynamic col1ItemXM = new ExpandoObject();
                    col1ItemXM.title = $"<span class=\"table_header\">{cRForm.CRForm.FormName}</span>";
                    col1ItemXM.colspan = cRForm.FormFieldList.Count() + 1;
                    col1ItemXM.align = "center";
                    col1.Add(col1ItemXM);

                    dynamic col2ItemXMschedule = new ExpandoObject();
                    col2ItemXMschedule.title = $"<span class=\"table_header\">{"填报进度"}</span>";
                    col2ItemXMschedule.field = cRForm.CRForm.Id;
                    col2ItemXMschedule.width = 200;
                    col2ItemXMschedule.align = "center";
                    col2ItemXMschedule.id = cRForm.CRForm.Id;
                    col2.Add(col2ItemXMschedule);

                    var IncrementalField = cRForm.FormFieldList.ToList();   //从CRFDatas中获取的CRF字段集合
                    foreach (var item in dictKeyList)              //从CRFormFieldSets表中得到的字段集合
                    {
                        IncrementalField.Remove(item.Name);
                        dynamic col2ItemXM = new ExpandoObject();
                        col2ItemXM.field = $"{cRForm.CRForm.Id}-{item.Name}";
                        var key = item.Name;
                        if (item.Value != null)
                            key = item.Value;
                        col2ItemXM.title = $"<span class=\"table_header\">{key}</span>";
                        col2ItemXM.width = 200;
                        col2ItemXM.align = "center";
                        col2.Add(col2ItemXM);
                    }
                    foreach (var item in IncrementalField)      //将增量字段添加在排序后的字段后面
                    {
                        dynamic col2ItemXM = new ExpandoObject();
                        col2ItemXM.field = $"{cRForm.CRForm.Id}-{item}";
                        var key = item;
                        var dictKeyStr = dictKeyList.Where(o => o.Name.ToUpper() == key.ToUpper()).FirstOrDefault();
                        if (dictKeyStr != null)
                            key = dictKeyStr.Value;
                        col2ItemXM.title = $"<span class=\"table_header\">{key}</span>";
                        col2ItemXM.width = 200;
                        col2ItemXM.align = "center";
                        col2.Add(col2ItemXM);
                    }

                }
                else
                {
                    dynamic col1ItemXM = new ExpandoObject();
                    col1ItemXM.title = $"<span class=\"table_header\">{cRForm.CRForm.FormName}</span>";
                    col1ItemXM.width = 200;
                    col1ItemXM.rowspan = 2;
                    col1ItemXM.align = "center";
                    col1.Add(col1ItemXM);
                }

                #endregion

                #endregion

                foreach (var patient in patientList)
                {
                    var gender = patient.Sex == "男" ? 1 : 0;
                    dynamic patientData = new ExpandoObject();
                    patientData.PatientName = patient.PatientName;
                    patientData.HisPatientId = patient.HisPatientId;
                    patientData.IDCardNo = patient.IDCardNo;
                    patientData.Telephone = patient.Telephone;
                    patientData.Sex = $"<span class=\"sex{gender}\">{patient.Sex}</span>";
                    patientData.ParticipatAge = patient.ParticipatAge == -1 ? "" : patient.ParticipatAge.ToString();

                    var thisPatientCRFDataList = List.Where(o => o.PatientId == patient.HisPatientId).ToList();

                    patientData.MedicalCenter = cRForm.CRForm.CMISDomain;


                    var thisCRFData = thisPatientCRFDataList.Where(o => o.CMISCRFSettingId == cRForm.CRForm.Id).FirstOrDefault();
                    ///增加ProjectName、DocDetailedNo addbyzolf 20250509
                    patientData.DocDetailedNo = thisCRFData.DocDetailedNo;
                    patientData.CreatedTime = thisCRFData.CreatedTime.ToString("yyyy-MM-dd");
                    patientData.ProjectName = thisCRFData.ProjectName;
                    patientData.AIExtractJsonValue = thisCRFData.AIExtractJsonValue + "";
                    patientData.DataType = thisCRFData.DataType == "B" ? "前结构化" : thisCRFData.DataType == "A" ? "后结构化" : "";

                    if (!string.IsNullOrWhiteSpace(thisCRFData?.CRFJsonValue))
                    {
                        var Fid = cRForm.CRForm.Id;
                        var patid = patient.Id;
                        var docDetailedNo = thisCRFData.DocDetailedNo;
                        var xJson = TransformData(cRForm.CRForm.FormId, thisCRFData.CRFJsonValue);
                        var fromid = cRForm.CRForm.FormId;
                        var item = $"{cRForm.CRForm.FormName}";
                        var totalFields = thisCRFData.TotalField;
                        int NotEmptyFields = thisCRFData.FillField;
                        var fora = cRForm.CRForm.Id + "";
                        var va = NotEmptyFields + "/" + totalFields == null ? "" : NotEmptyFields + "/" + totalFields;
                        var val = $"<div style=\"margin-top: 5px;\"  ><div class=\"layui-progress layui-progress-big herfform\" lay-showPercent=\"true\" fid='{Fid}' patientid='{patid}' docDetailedNo='{docDetailedNo}' formId='{fromid}' jsonStr='{xJson}'><div class=\"layui-progress-bar\" lay-percent=\"{va}\">{va}</div></div></div> ";
                        ((IDictionary<string, object>)patientData).Add(new KeyValuePair<string, object>(fora, val));
                        var fora1 = cRForm.CRForm.Id + "status";
                        var val1 = "";
                        if (NotEmptyFields == totalFields)
                            val1 = $"<input type=\"checkbox\" id=\"switch\"  value=\"on\" checked lay-skin=\"switch\" lay-text=\"\" lay-filter=\"stop\" /> <div class=\"layui-unselect layui-form-switch layui-form-onswitch\" lay-skin=\"_switch\"><em></em><i></i></div>";
                        else
                            val1 = $"<input type=\"checkbox\" id=\"switch\"  value=\"on\"  lay-skin=\"switch\" lay-text=\"\" lay-filter=\"stop\" /> <div class=\"layui-unselect layui-form-switch layui-form-onswitch\" lay-skin=\"_switch\"><em></em><i></i></div>";
                        ((IDictionary<string, object>)patientData).Add(new KeyValuePair<string, object>(fora1, val1));





                        JObject jObject = JObject.Parse(thisCRFData.CRFJsonValue);
                        foreach (JProperty property in jObject.Properties())
                        {
                            var Filed = $"{thisCRFData.CMISCRFSettingId}-{property.Name.ToUpper()}";
                            if (!property.Name.ToUpper().StartsWith("DETAILARRAY"))
                            {
                                var value = property.Value;
                                if (value != null && value.ToString().Contains("&&&###"))
                                {
                                    value = value.ToString().Replace("&&&###", ",");
                                }
                            // 添加动态属性
                            ((IDictionary<string, object>)patientData).Add(new KeyValuePair<string, object>(Filed, value));
                            }
                            else
                            {
                                var thisValue = "";
                                var jsonArray = property.Value;
                                var i = 0;
                                // 遍历JArray中的每个JObject
                                foreach (JObject jsonObject in jsonArray)
                                {
                                    i++;
                                    thisValue += $"记录{i}:";
                                    // 动态地遍历JObject中的每个键值对
                                    foreach (JProperty arrayProperty in jsonObject.Properties())
                                    {
                                        string key = arrayProperty.Name;
                                        string value = arrayProperty.Value.ToString();
                                        var dictKeyStr = dictKeyList.Where(o => o.FormId == cRForm.CRForm.FormId && o.Name.ToUpper() == key.ToUpper()).FirstOrDefault();
                                        if (dictKeyStr != null)
                                            key = dictKeyStr.Value;
                                        thisValue += $"{key}:\"{value}\"；";
                                    }
                                    thisValue += "。<br/>";
                                }
                            // 添加动态属性
                            ((IDictionary<string, object>)patientData).Add(new KeyValuePair<string, object>(Filed, thisValue));
                            }
                        }
                    }


                    dataRowList.Add(patientData);
                }
                resultCols.Add(col1);
                resultCols.Add(col2);
                var setting = new JsonSerializerSettings();
                setting.ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver();//json字符串大小写原样输出
                return Json(new { code = "0", okMsg = "成功", count = totalCount, columns = resultCols, list = dataRowList }, setting);
            }
            catch (Exception ex)
            {
                return Json(new { code = "0", errorMsg = $"失败:{ex.Message}", count = 0 });
            }
        }

        public List<string> GetCRFormField(int crfId)
        {
            var fieldList = new List<string>();
            var cRFDataList = db.CMISCRFDatas.Where(o => o.CMISCRFSettingId == crfId)
                .OrderByDescending(o => o.CreatedTime).Take(5).ToList();
            foreach (var crfdata in cRFDataList)
            {
                JObject jObject = JObject.Parse(crfdata.CRFJsonValue);
                foreach (JProperty property in jObject.Properties())
                {
                    var fieldName = property.Name.ToUpper();
                    if (!fieldList.Exists(o => o == fieldName))
                    {
                        fieldList.Add(fieldName);
                    }
                }
            }
            return fieldList;
        }

        public IActionResult DownLoad(string keyWord, string DataType, int CRFId, string date)
        {
            keyWord = keyWord?.Trim();
            dynamic resultCols = new List<dynamic>();
            dynamic resultData = new List<dynamic>();
            var List = new List<CMISCRFData>();

            var commAPI = new CommAPIController(db, env, config);
            int? multiCenterId;
            bool? isCenter = commAPI.CheckCenterData(User.Identity.Name, out multiCenterId);
            var addLogs = commAPI.AddOperationLogs(OperationTypeName.数据下载, User.Identity.Name, null
                            , null, null, null, "下载CRF数据明细", HttpContext);


            var query = db.CMISCRFDatas.Include(o => o.CMISCRFSetting).Where(o => o.CMISCRFSettingId == CRFId)
                    .AsQueryable();
            if (!string.IsNullOrWhiteSpace(DataType))
            {
                query = query.Where(o => o.DataType == DataType).AsQueryable();
            }
            if (!string.IsNullOrWhiteSpace(date))
            {
                var startdate = Convert.ToDateTime(date.Split('~')[0].Trim() + " 00:00:00");
                var enddate = Convert.ToDateTime(date.Split('~')[1].Trim() + " 23:59:59");
                query = query.Where(p => p.CreatedTime != null && p.CreatedTime >= startdate && p.CreatedTime <= enddate);
            }
            else
            {
                var enddate = DateTime.Now;
                var startdate = new DateTime(enddate.Year, enddate.Month, 1);
                query = query.Where(p => p.CreatedTime != null && p.CreatedTime >= startdate && p.CreatedTime <= enddate);
            }
            List = query
                     .Select(o => new CMISCRFData
                     {
                         Id = o.Id,
                         CMISCRFSettingId = o.CMISCRFSettingId,
                         ProjectName = o.ProjectName,
                         CreatedTime = o.CreatedTime,
                         DocDetailedNo = o.DocDetailedNo,
                         DataType = o.DataType,
                         PatientId = o.PatientId,
                         CRFJsonValue = o.CRFJsonValue
                     })
                    .ToList();

            var cRForm = new
            {
                CRForm = (from s in db.CMISCRFSettings
                          where s.Id == CRFId
                          select s).SingleOrDefault(),
                FormFieldList = GetCRFormField(CRFId)
            };

            var dictKeyList = db.CRFormFieldSets.Include(o => o.CRForm)
                    .Where(o => o.CRForm.FormId == cRForm.CRForm.FormId)
                    .Select(o => new KeyValueDTO { Id = o.CRFormId, FormId = o.CRForm.FormId, Orderby = o.Orderby, Name = o.FieldName, Value = o.FieldComment })
                    .OrderBy(o => o.Orderby)
                    .Distinct().ToList();

            var patientList = (from s in db.ResearchPatients join q in query on s.HisPatientId equals q.PatientId select s).Distinct().ToList();
            if (!string.IsNullOrWhiteSpace(keyWord))
            {
                keyWord = keyWord.Trim();
                patientList = (from s in db.ResearchPatients join q in query on s.HisPatientId equals q.PatientId select s).Where(o => o.HisPatientId == keyWord || o.PatientName.Contains(keyWord)).Distinct().ToList();
            }
            var IncrementalField = cRForm.FormFieldList.ToList();   //从CRFDatas中获取的CRF字段集合

            var CommAPI = new CommAPIController(db, env, config);

            Stream stream = new MemoryStream();
            using (ExcelPackage package = new ExcelPackage(stream))
            {
                ExcelWorksheet worksheet = package.Workbook.Worksheets.Add("result");//创建worksheet
                #region 表头、样式
                worksheet.Cells[1, 1].Value = $"CMIS结构化报告详情";
                worksheet.Cells[2, 1].Value = "序号";
                worksheet.Cells[2, 2].Value = "患者姓名";
                worksheet.Cells[2, 3].Value = "患者Id";
                worksheet.Cells[2, 4].Value = "性别";
                worksheet.Cells[2, 5].Value = "年龄";
                worksheet.Cells[2, 6].Value = "检查类型";
                worksheet.Cells[2, 7].Value = "数据类型";
                worksheet.Cells[2, 8].Value = "项目名称";
                worksheet.Cells[2, 9].Value = "申请单号";
                worksheet.Cells[2, 10].Value = "创建日期";

                worksheet.Cells[2, 1, 3, 1].Merge = true;//合并单元格-行
                worksheet.Cells[2, 2, 3, 2].Merge = true;//合并单元格-行
                worksheet.Cells[2, 3, 3, 3].Merge = true;//合并单元格-行
                worksheet.Cells[2, 4, 3, 4].Merge = true;//合并单元格-行
                worksheet.Cells[2, 5, 3, 5].Merge = true;//合并单元格-行
                worksheet.Cells[2, 6, 3, 6].Merge = true;//合并单元格-行
                worksheet.Cells[2, 7, 3, 7].Merge = true;//合并单元格-行
                worksheet.Cells[2, 8, 3, 8].Merge = true;//合并单元格-行
                worksheet.Cells[2, 9, 3, 9].Merge = true;//合并单元格-行
                worksheet.Cells[2, 10, 3, 10].Merge = true;//合并单元格-行
                var col_Header = 11;

                if (cRForm.FormFieldList.Any() && cRForm.FormFieldList.Count() > 0)
                {
                    var SecondRowIndex = col_Header;
                    worksheet.Cells[2, SecondRowIndex].Value = $"{cRForm.CRForm.FormName}";
                    worksheet.Cells[2, SecondRowIndex, 2, SecondRowIndex + cRForm.FormFieldList.Count() - 1].Merge = true;//合并单元格-列
                    foreach (var item in dictKeyList)              //从CRFormFieldSets表中得到的字段集合
                    {
                        IncrementalField.Remove(item.Name);
                        var key = item.Name;
                        if (item.Value != null)
                            key = item.Value;
                        worksheet.Cells[3, col_Header].Value = key;
                        col_Header++;
                    }
                    foreach (var item in IncrementalField)      //将增量字段添加在排序后的字段后面
                    {
                        var key = item;
                        var dictKeyStr = dictKeyList.Where(o => o.Name.ToUpper() == key.ToUpper()).FirstOrDefault();
                        if (dictKeyStr != null)
                            key = dictKeyStr.Value;
                        worksheet.Cells[3, col_Header].Value = key;
                        col_Header++;
                    }
                }
                else
                {
                    worksheet.Cells[2, col_Header].Value = $"{cRForm.CRForm.FormName}";
                    worksheet.Cells[2, col_Header, 3, col_Header].Merge = true;//合并单元格-行
                    col_Header++;
                }

                worksheet.Cells[1, 1, 1, col_Header - 1].Merge = true;//合并单元格
                worksheet.Cells[1, 1, 3, col_Header - 1].Style.Font.Bold = true;//字体为粗体
                worksheet.Cells[1, 1, 3, col_Header - 1].Style.Font.Name = "微软雅黑";//字体
                worksheet.Cells[1, 1, 3, col_Header - 1].Style.Font.Size = 12;//字体大小
                #endregion

                #region 表身
                var row_Body = 4;
                var index = 0;
                foreach (var patient in patientList)
                {
                    index++;
                    worksheet.Cells[row_Body, 1].Value = index;                 // "序号";
                    worksheet.Cells[row_Body, 2].Value = patient?.PatientName;  // "患者姓名";
                    worksheet.Cells[row_Body, 3].Value = patient?.HisPatientId;   // "患者ID";
                    worksheet.Cells[row_Body, 4].Value = patient?.Sex;          //"性别";
                    worksheet.Cells[row_Body, 5].Value = patient.ParticipatAge == -1 ? "" : patient.ParticipatAge.ToString(); //"年龄";

                    var col_body = 11;

                    var thisPatientCRFDataList = List.Where(o => o.PatientId == patient.HisPatientId).ToList();

                    worksheet.Cells[row_Body, 6].Value = cRForm.CRForm.CMISDomain;

                    if (cRForm.FormFieldList.Any() && cRForm.FormFieldList.Count() > 0)
                    {
                        var thisCRFData = thisPatientCRFDataList.Where(o => o.CMISCRFSettingId == cRForm.CRForm.Id).FirstOrDefault();
                        worksheet.Cells[row_Body, 7].Value = thisCRFData.DataType == "B" ? "前结构化" : thisCRFData.DataType == "A" ? "后结构化" : "";
                        worksheet.Cells[row_Body, 8].Value = thisCRFData.ProjectName;
                        worksheet.Cells[row_Body, 9].Value = thisCRFData.DocDetailedNo;
                        worksheet.Cells[row_Body, 10].Value = thisCRFData.CreatedTime.ToString("yyyy-MM-dd"); ;
                        JObject jObject = null;
                        if (!string.IsNullOrWhiteSpace(thisCRFData?.CRFJsonValue))
                        {
                            jObject = JObject.Parse(thisCRFData.CRFJsonValue);
                        }
                        foreach (var fieldItem in dictKeyList)
                        {
                            if (jObject != null)
                            {
                                var isAdd = false;
                                foreach (JProperty property in jObject.Properties())
                                {
                                    if (property.Name.ToUpper() == fieldItem.Name.ToUpper())
                                    {
                                        isAdd = true;
                                        if (!property.Name.ToUpper().StartsWith("DETAILARRAY"))
                                        {
                                            worksheet.Cells[row_Body, col_body].Value
                                                        = (property.Value == null ? "" : property.Value.ToString().Replace("&&&###", ","));
                                        }
                                        else
                                        {
                                            var thisValue = "";
                                            var jsonArray = property.Value;
                                            var i = 0;
                                            // 遍历JArray中的每个JObject
                                            foreach (JObject jsonObject in jsonArray)
                                            {
                                                i++;
                                                thisValue += $"记录{i}:";
                                                // 动态地遍历JObject中的每个键值对
                                                foreach (JProperty arrayProperty in jsonObject.Properties())
                                                {
                                                    string key = arrayProperty.Name;
                                                    string value = arrayProperty.Value.ToString();
                                                    var dictKeyStr = dictKeyList.Where(o => o.Name.ToUpper() == key.ToUpper()).FirstOrDefault();
                                                    if (dictKeyStr != null)
                                                        key = dictKeyStr.Value;
                                                    thisValue += $"{key}:\"{value}\"；";
                                                }
                                                thisValue += "。";
                                            }
                                            worksheet.Cells[row_Body, col_body].Value = thisValue;
                                        }
                                        break;
                                    }
                                }
                                if (!isAdd)
                                {
                                    worksheet.Cells[row_Body, col_body].Value = "";
                                }
                            }
                            else
                            {
                                worksheet.Cells[row_Body, col_body].Value = "";
                            }
                            col_body++;
                        }
                        foreach (var fieldItem in IncrementalField)
                        {
                            if (jObject != null)
                            {
                                var isAdd = false;
                                foreach (JProperty property in jObject.Properties())
                                {
                                    if (property.Name.ToUpper() == fieldItem.ToUpper())
                                    {
                                        isAdd = true;
                                        if (!property.Name.ToUpper().StartsWith("DETAILARRAY"))
                                        {
                                            worksheet.Cells[row_Body, col_body].Value
                                                        = (property.Value == null ? "" : property.Value.ToString().Replace("&&&###", ","));
                                        }
                                        else
                                        {
                                            var thisValue = "";
                                            var jsonArray = property.Value;
                                            var i = 0;
                                            // 遍历JArray中的每个JObject
                                            foreach (JObject jsonObject in jsonArray)
                                            {
                                                i++;
                                                thisValue += $"记录{i}:";
                                                // 动态地遍历JObject中的每个键值对
                                                foreach (JProperty arrayProperty in jsonObject.Properties())
                                                {
                                                    string key = arrayProperty.Name;
                                                    string value = arrayProperty.Value.ToString();
                                                    var dictKeyStr = dictKeyList.Where(o => o.Name.ToUpper() == key.ToUpper()).FirstOrDefault();
                                                    if (dictKeyStr != null)
                                                        key = dictKeyStr.Value;
                                                    thisValue += $"{key}:\"{value}\"；";
                                                }
                                                thisValue += "。";
                                            }
                                            worksheet.Cells[row_Body, col_body].Value = thisValue;
                                        }
                                        break;
                                    }
                                }
                                if (!isAdd)
                                {
                                    worksheet.Cells[row_Body, col_body].Value = "";
                                }
                            }
                            else
                            {
                                worksheet.Cells[row_Body, col_body].Value = "";
                            }
                            col_body++;
                        }
                    }
                    else
                    {
                        worksheet.Cells[row_Body, col_body].Value = "";
                        col_body++;
                    }

                    row_Body++;
                }
                worksheet.Cells[row_Body, 1].Value = "注：“——”表示无需填写内容。";
                worksheet.Cells[row_Body, 1].Style.Font.Color.SetColor(Color.Red);
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
                worksheet.Cells[1, 1, row_Body - 1, col_Header - 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;//水平居左
                worksheet.Cells[1, 1, row_Body - 1, col_Header - 1].Style.VerticalAlignment = ExcelVerticalAlignment.Center;//垂直居中
                worksheet.Cells[2, 1, row_Body - 1, col_Header - 1].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                worksheet.Cells[2, 1, row_Body - 1, col_Header - 1].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                worksheet.Cells[2, 1, row_Body - 1, col_Header - 1].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                worksheet.Cells[2, 1, row_Body - 1, col_Header - 1].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                worksheet.View.FreezePanes(4, 1);
                #endregion
                package.Save();
            }
            return new DownLoadByStreamResult(stream, $"CMISDataDetails{System.DateTime.Now.ToString("yyyy-MM-dd")}.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        }


        public class Variable
        {
            public string variable_name { get; set; }
            public string value { get; set; }
            public string source { get; set; }
        }

        public class Root
        {
            public List<Variable> variables { get; set; }
        }

        public string TransformData(string formid, string jsonInput)
        {
            var inputObject = JObject.Parse(jsonInput);
            Root root = new Root { variables = new List<Variable>() };

            foreach (var property in inputObject.Properties())
            {
                JToken propertyValue = property.Value;

                string value;
                if (propertyValue.Type == JTokenType.Array)
                {
                    value = JsonConvert.SerializeObject(((JArray)propertyValue));
                }
                else
                {
                    value = (string)propertyValue;
                }

                Variable variable = new Variable
                {
                    variable_name = property.Name,
                    value = value,
                    source = ""
                };
                root.variables.Add(variable);
            }

            return JsonConvert.SerializeObject(root);
        }
        public List<int> GetHideCRFormList(int ResearchPatientId)
        {
            var list = db.PatientFormHides.Where(o => o.ResearchPatientId == ResearchPatientId).Select(o => o.CRFormId).ToList();
            return list;
        }

        public class KeyValueDTO
        {
            public int Id { get; set; }
            public string FormId { get; set; }
            public string Name { get; set; }
            public string Value { get; set; }
            public int Orderby { get; set; }
        }

    }
}
