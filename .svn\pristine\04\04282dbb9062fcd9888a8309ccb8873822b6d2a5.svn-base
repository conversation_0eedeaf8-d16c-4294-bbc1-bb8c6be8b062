﻿using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SignalRTest
{
    public partial class Form1 : Form
    {
        private HubConnection _connection;
        private string _connectionId = "";

        public Form1()
        {
            InitializeComponent();
        }

        private async void btnConnect_Click(object sender, EventArgs e)
        {
            try
            {
                string HubUrl = ConfigurationManager.AppSettings["SignalRHubUrl"];
                // 创建SignalR连接
                _connection = new HubConnectionBuilder()
                    .WithUrl(HubUrl)
                    .WithAutomaticReconnect()
                    .Build();

                //注册接收广播消息的事件
                _connection.On<string>("ReceiveMessage", (message) =>
                {
                    this.Invoke((Action)(() =>
                    {
                        txtLog.AppendText($"[广播] {DateTime.Now:T} - {message}\r\n");
                    }));
                });

                //注册接收私有消息的事件
                _connection.On<string>("ReceivePrivate", (message) =>
                {
                    this.Invoke((Action)(() =>
                    {
                        txtLog.AppendText($"[私有] {DateTime.Now:T} - {message}\r\n");
                    }));
                });

                //连接状态变化事件
                _connection.Closed += async (error) =>
                {
                    this.Invoke((Action)(() =>
                    {
                        txtLog.AppendText($"连接已断开: {error?.Message}\r\n");
                        UpdateConnectionStatus(false);
                    }));
                };

                _connection.Reconnected += (connectionId) =>
                {
                    this.Invoke((Action)(() =>
                    {
                        txtLog.AppendText($"连接已恢复. 新的连接ID: {connectionId}\r\n");
                        _connectionId = connectionId;
                        lblStatus.Text = $"已连接 (ID: {_connectionId})";
                    }));
                    return Task.CompletedTask;
                };

                // 启动连接
                await _connection.StartAsync();

                // 获取连接ID
                _connectionId = await _connection.InvokeAsync<string>("GetConnectionId");

                // 更新UI状态
                UpdateConnectionStatus(true);
                txtLog.AppendText($"已连接到服务端! 连接ID: {_connectionId}\r\n");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"连接失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnDisconnect_Click(object sender, EventArgs e)
        {
            if (_connection != null)
            {
                try
                {
                    await _connection.StopAsync();
                    await _connection.DisposeAsync();
                    _connection = null;

                    UpdateConnectionStatus(false);
                    txtLog.AppendText("已断开连接\r\n");
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"断开连接失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private async void btnSend_Click(object sender, EventArgs e)
        {
            var message = string.IsNullOrEmpty(txtMessage.Text) ? "全世界广播" : txtMessage.Text;

            if (_connection?.State == HubConnectionState.Connected && !string.IsNullOrEmpty(message))
            {
                try
                {
                    await _connection.InvokeAsync("SendToAll", message);
                    txtLog.AppendText($"已发送广播: {message}\r\n");
                    txtMessage.Text = "";
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"发送失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private async void btnSendPrivate_Click(object sender, EventArgs e)
        {
            if (_connection?.State == HubConnectionState.Connected &&
                !string.IsNullOrEmpty(txtTargetId.Text) &&
                !string.IsNullOrEmpty(txtMessage.Text))
            {
                try
                {
                    await _connection.InvokeAsync("SendToClient", txtTargetId.Text, txtMessage.Text);
                    txtLog.AppendText($"已发送私密消息给 {txtTargetId.Text}: {txtMessage.Text}\r\n");
                    txtMessage.Text = "";
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"发送失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void UpdateConnectionStatus(bool isConnected)
        {
            btnConnect.Enabled = !isConnected;
            btnDisconnect.Enabled = isConnected;
            btnSend.Enabled = isConnected;
            btnSendPrivate.Enabled = isConnected;

            lblStatus.Text = isConnected ? $"已连接 (ID: {_connectionId})" : "未连接";
        }

        private async void Form1_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_connection != null)
            {
                await _connection.StopAsync();
                await _connection.DisposeAsync();
            }
        }
    }

}
