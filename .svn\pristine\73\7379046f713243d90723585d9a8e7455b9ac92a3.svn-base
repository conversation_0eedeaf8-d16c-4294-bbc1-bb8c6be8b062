﻿using AngelwinResearch.Models;
using AngelwinResearch.WebUI.Areas.CMIS.Models;
using AngelwinResearch.WebUI.Areas.ReportingManage.Controllers;
using AngelwinResearch.WebUI.Filters;
using AngelwinResearch.WebUI.Unity;
using Common.Tools;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace AngelwinResearch.WebUI.Areas.CMIS.Controllers
{
    [Area("CMIS")]
    public class BeforeStructuredController : Controller
    {
        private readonly AngelwinResearchDbContext db;
        public IConfiguration config { get; }
        public IWebHostEnvironment webhostEnv { get; set; }
        public BeforeStructuredController(AngelwinResearchDbContext _db, IConfiguration _config, IWebHostEnvironment _webHostEnv)
        {
            db = _db;
            config = _config;
            webhostEnv = _webHostEnv;
        }
        public IActionResult Index([FromQuery] TempCMIS query)
        {
            var list = db.CMISCRFSettings.Where(a => a.SystemId == query.SystemId && a.IsUsed == true).AsQueryable();
            if (!string.IsNullOrEmpty(query.formId))
                list = list.Where(a => a.FormId == query.formId);
            var data = list.Select(a => new { id = a.FormId, name = a.FormName }).ToList();
            ViewBag.select = data;
            var token = TokenGenerator.getToken();
            ViewBag.token = WebUtility.UrlEncode(token);
            ViewBag.formUrl = config["AppSettings:AnyReportUrl"];
            return View(query);
        }

        [SSE]
        public async Task<ActionResult> GetResult(int Id)
        {
            // var CMISAIRequest = JsonConvert.DeserializeObject<CMISAIRequest>(words);
            var CMISData = db.CMISCRFDatas.Include(a => a.CMISCRFSetting).FirstOrDefault(a => a.Id == Id);
            var CMISCRFSetting = CMISData.CMISCRFSetting;
            var response = Response;
            response.Headers.Add("Cache-Control", "no-cache");
            response.Headers.Add("Connection", "keep-alive");




            try
            {

                var modelType = config["AppSettings:modelType"];
                //var promptInfo = db.PromptInfos.FirstOrDefault(a =>  (a.ModelName == "通用" || a.ModelName.ToLower() == modelType.ToLower())
                //&& a.PropmtIdentifier == "BeforeStructured" && a.IsUsed == true);
                var tips = CMISCRFSetting.Tips;
                if (string.IsNullOrEmpty(tips))
                {
                    await Response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(new { errorMsg = $"前结构化【{CMISCRFSetting.FormName}】提示词未配置" })}\n\n");
                    await Response.Body.FlushAsync();
                    return new EmptyResult();
                }
                //var ExtraRequire = CMISCRFSetting.ExtraRequire;
                //if(string.IsNullOrEmpty(ExtraRequire))
                //{
                //    ExtraRequire = GetTips(CMISCRFSetting.FormId);
                //}
                //if (string.IsNullOrEmpty(ExtraRequire)|| ExtraRequire=="[]")
                //{
                //    await Response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(new { errorMsg = $"前结构化【ExtraRequire】提取要求未配置" })}\n\n");
                //    await Response.Body.FlushAsync();
                //    return new EmptyResult();
                //}
                //var formField = GetTips(CMISCRFSetting.FormId);
                //var prompt = $"{tips.Replace("{{formContent}}", CMISData.CRFJsonValue).Replace("{{formField}}", formField)}";
                //20250528 把字段解释和表单内容拼在一起
                var CRFormFieldSets = db.CRFormFieldSets.Include(O => O.CRForm).Where(o => o.CRForm.FormId == CMISCRFSetting.FormId).ToList();

                var listJsonValue = JsonConvert.DeserializeObject<Dictionary<string, object>>(CMISData.CRFJsonValue);
                var newList = new JArray();
                foreach (var dic in listJsonValue)
                {
                    var key = dic.Key;
                    var value = dic.Value;
                    if (CMISCRFSetting.NotContainNull.HasValue && CMISCRFSetting.NotContainNull.Value && string.IsNullOrEmpty(value + ""))
                        continue;

                    var formField = CRFormFieldSets.FirstOrDefault(a => a.FieldName == key);
                    if (formField == null)
                    {
                        LoggerHelper.WriteInfo("其他日志", $"{CMISCRFSetting.FormName}【{CMISCRFSetting.FormId}】中的字段[{key}]变量描述未配置。");
                    }
                    var jobj = new JObject();
                    //jobj["变量名"] = key; //节约token数量 cancelbyzolf 20250606
                    //20250714  如果变量描述为空 就给原始名称
                    jobj["变量描述"] = formField == null ? key : formField?.FieldComment;
                    jobj["变量值"] = JToken.FromObject(value ?? new JObject()); ;
                    newList.Add(jobj);
                }
                var newJsonVlue = JsonConvert.SerializeObject(newList);
                var prompt = $"{tips.Replace("{{formContent}}", newJsonVlue).Replace("{{formField}}", "")}";
                string endpoint = "chat/completions";
                dynamic history = new List<dynamic>();
                var ResponseContent = "";
                var thinkResponseContent = "";
                var requestMsgTime = System.DateTime.Now;

                var contentStr = "";
                #region 读取配置文件
                string apiKey = config[$"GPTSetting:{modelType}:ApiKey"];
                string model = config[$"GPTSetting:{modelType}:Model"];
                string apiUrl = config[$"GPTSetting:{modelType}:apiUrl"];
                string maxTokenStr = config[$"GPTSetting:{modelType}:MaxTokens"];
                int maxTokens = 4000;
                if (!string.IsNullOrEmpty(maxTokenStr))
                {
                    int.TryParse(maxTokenStr, out maxTokens);
                }
                HttpClientHandler handler = null;
                string webProxy = config[$"GPTSetting:{modelType}:WebProxy"];
                if (!string.IsNullOrWhiteSpace(webProxy))
                {
                    handler = new HttpClientHandler()
                    {
                        Proxy = new WebProxy(webProxy)
                    };
                }
                else
                {
                    handler = new HttpClientHandler() { };
                }
                #endregion
                handler.ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator;
                var httpClient = new HttpClient(handler);
                httpClient.BaseAddress = new Uri(apiUrl);
                httpClient.Timeout = TimeSpan.FromMinutes(5);
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                history.Add(new { role = "user", content = $"{prompt}" });

                dynamic requstDTO = new ExpandoObject();
                requstDTO.model = model;
                requstDTO.max_tokens = maxTokens;
                requstDTO.messages = history;
                requstDTO.stream = true;
                var requestBody = JsonConvert.SerializeObject(requstDTO);
                LoggerHelper.WriteInfo("其他日志", $"请求大模型参数：【{requestBody}】");
                var request = new HttpRequestMessage(HttpMethod.Post, apiUrl + endpoint)
                {
                    Content = new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
                };

                using var APIResponse = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);
                {
                    if (APIResponse.IsSuccessStatusCode)
                    {
                        var stream = await APIResponse.Content.ReadAsStreamAsync();
                        var streamReader = new StreamReader(stream);
                        // var ddd = await streamReader.ReadLineAsync();
                        //var isThink = false;
                        var isThink = config.GetValue<bool>("AppSettings:IsThink");
                        while (!streamReader.EndOfStream)
                        {
                            var line = await streamReader.ReadLineAsync();
                            if (!string.IsNullOrWhiteSpace(line))
                            {
                                if (line != "data: [DONE]")
                                {
                                    if (line.StartsWith("event: error"))
                                    {
                                        await response.WriteAsync($"event: end\ndata:{JsonConvert.SerializeObject(new { errorMsg = $"{line}" })}\n\n");
                                        await response.Body.FlushAsync();
                                        return new EmptyResult();
                                    }
                                    else
                                    {
                                        if (line.StartsWith("data:"))
                                            line = line.Substring(5, line.Length - 5);
                                        var delta = JObject.Parse(line).SelectToken("choices").First().SelectToken("delta");
                                        var finish_reason = JObject.Parse(line).SelectToken("choices").First().SelectToken("finish_reason");
                                        if (delta != null && ((JContainer)delta).Count > 0)
                                        {
                                            if (delta["content"] != null)
                                            {
                                                contentStr = delta["content"].ToString();
                                                if (contentStr.Contains("<think>") && !isThink)
                                                {
                                                    isThink = true;
                                                }
                                                if (contentStr.Contains("</think>") && isThink)
                                                {
                                                    isThink = false;
                                                }
                                                if (isThink)
                                                {
                                                    thinkResponseContent += contentStr;
                                                    await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { okMsg = "成功", data = "" })}\n\n");
                                                    await response.Body.FlushAsync();
                                                }
                                                else
                                                {
                                                    ResponseContent += contentStr;
                                                    await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { okMsg = "成功", data = contentStr })}\n\n");
                                                    await response.Body.FlushAsync();
                                                }
                                            }
                                        }
                                        if (finish_reason != null)
                                        {
                                            var resultfinish = finish_reason as JProperty;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        LoggerHelper.WriteInfo("其他日志", $"连接大模型失败，错误码：【{APIResponse.StatusCode}】");
                        var errorMsg = $"Failed to connect to API.【{APIResponse.ReasonPhrase}】";
                        await response.WriteAsync($"event: end\ndata:{JsonConvert.SerializeObject(new { errorMsg = $"{errorMsg}" })}\n\n");
                        await response.Body.FlushAsync();
                        return new EmptyResult();
                    }
                }

                var resultAll = new
                {
                    okMsg = $"成功",
                    role = "assistant",
                    content = ResponseContent,
                };
                await Response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(resultAll)}\n\n");
                await Response.Body.FlushAsync();
                return new EmptyResult();
            }
            catch (Exception ex)
            {
                await Response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(new { errorMsg = $"报错:{ex.Message}-{ex.InnerException?.Message}" })}\n\n");
                await Response.Body.FlushAsync();
                return new EmptyResult();
            }
        }

        [HttpPost]
        public IActionResult SubmitData(CMISAIRequest request)
        {
            try
            {
                var cmis = request.cmis;
                var researchPatient = db.ResearchPatients.FirstOrDefault(a => a.HisPatientId == cmis.PatientID
                && a.HisPatientSource == cmis.PatientSource);
                if (researchPatient == null)
                {
                    researchPatient = new ResearchPatient();
                    researchPatient.ResearchID = $"CMIS_{cmis.PatientID}";
                    researchPatient.HisPatientId = cmis.PatientID;
                    researchPatient.HisPatientSource = cmis.PatientSource;
                    researchPatient.PatientName = cmis.PatientName;
                    researchPatient.IDCardNo = cmis.SFZH;
                    researchPatient.Sex = cmis.SEX;
                    researchPatient.ParticipatAge = -1;
                    researchPatient.CreatedTime = DateTime.Now;
                    researchPatient.JoinDate = DateTime.Now;
                    researchPatient.BRKH = cmis.BRKH;
                    researchPatient.BLH = cmis.BLH;
                    db.ResearchPatients.Add(researchPatient);
                }

                var CMISCRFSettings = db.CMISCRFSettings.FirstOrDefault(a => a.IsUsed == true &&
                a.FormId == request.formId && a.SystemId == cmis.SystemId);
                var CMISCRFData = db.CMISCRFDatas.FirstOrDefault(a => a.DocDetailedNo == cmis.DocDetailedNo &&
                a.CMISCRFSettingId == CMISCRFSettings.Id && a.PatientId == cmis.PatientID && a.Type == cmis.Type && a.DataType == "B");

                // 如果需要遍历所有属性，可以使用JObject
                JObject jObject = JObject.Parse(request.formData);
                //  var totalFields = jObject.Count;
                var totalFields = 0;
                int NotEmptyFields = 0;

                foreach (JProperty property in jObject.Properties())
                {
                    JToken propertyValue = property.Value;
                    var propertyName = property.Name;
                    totalFields++;
                    switch (propertyValue.Type)
                    {
                        case JTokenType.String:
                            if (!string.IsNullOrEmpty((string)propertyValue))
                            {
                                NotEmptyFields++;
                            }
                            break;
                        case JTokenType.Integer:
                        case JTokenType.Float:
                            if (((IConvertible)propertyValue).ToString() != "0")
                            {
                                NotEmptyFields++;
                            }
                            break;
                        case JTokenType.Array:
                            //if (((JArray)propertyValue).Count > 0)
                            //{
                            //    NotEmptyFields++; // 如果数组非空，则计数增加
                            //}
                            getDetailCount((JArray)propertyValue, ref totalFields, ref NotEmptyFields);
                            totalFields--;
                            break;
                        // 根据需要添加其他类型的处理
                        default:
                            // 处理其他类型的值或忽略
                            break;
                    }
                }

                if (CMISCRFData == null)
                {
                    CMISCRFData = new CMISCRFData();
                    CMISCRFData.PatientId = cmis.PatientID;
                    CMISCRFData.PatientSource = cmis.PatientSource;
                    CMISCRFData.DocDetailedNo = cmis.DocDetailedNo;
                    CMISCRFData.ProjectNo = cmis.ProjectNo;
                    CMISCRFData.ProjectName = cmis.ProjectName;
                    CMISCRFData.CMISCRFSettingId = CMISCRFSettings.Id;
                    CMISCRFData.CRFormId = request.formId;
                    CMISCRFData.CRFJsonValue = request.formData;
                    CMISCRFData.CRFTextData = JsonConvert.SerializeObject(cmis);
                    CMISCRFData.AIExtractJsonValue = request.AIResult;
                    CMISCRFData.TotalField = totalFields;
                    CMISCRFData.FillField = NotEmptyFields;
                    CMISCRFData.CreatedTime = DateTime.Now;
                    CMISCRFData.Type = cmis.Type;
                    CMISCRFData.DataType = "B";
                    db.CMISCRFDatas.Add(CMISCRFData);
                }
                else
                {
                    CMISCRFData.ProjectNo = cmis.ProjectNo;
                    CMISCRFData.ProjectName = cmis.ProjectName;
                    CMISCRFData.CMISCRFSettingId = CMISCRFSettings.Id;
                    CMISCRFData.CRFormId = request.formId;
                    CMISCRFData.CRFJsonValue = request.formData;
                    CMISCRFData.CRFTextData = JsonConvert.SerializeObject(cmis);
                    CMISCRFData.AIExtractJsonValue = request.AIResult;
                    CMISCRFData.TotalField = totalFields;
                    CMISCRFData.FillField = NotEmptyFields;
                    CMISCRFData.CreatedTime = DateTime.Now;
                    db.CMISCRFDatas.Update(CMISCRFData);
                }

                db.SaveChanges();
                return Json(new { code = 0, msg = "数据入库成功！", data = new { Id = CMISCRFData.Id } });
            }
            catch (Exception ex)
            {
                return Json(new { code = -100, msg = "前结构化入库失败！", data = ex });
            }

        }

        [HttpPost]
        public IActionResult Update(int id, string AIResult)
        {
            try
            {
                var cmisdata = db.CMISCRFDatas.Find(id);

                cmisdata.AIExtractJsonValue = AIResult;

                db.SaveChanges();
                return Json(new { code = 0, msg = "AI返回结果更新成功！", data = new { Id = cmisdata.Id } });
            }
            catch (Exception ex)
            {
                return Json(new { code = -100, msg = "AI返回结果入库失败！", data = ex });
            }

        }

        public IActionResult FillCRForm([FromBody] CMISAIRequest request)
        {
            try
            {
                var cmisData = db.CMISCRFDatas.FirstOrDefault(a => a.DocDetailedNo ==
                   request.cmis.DocDetailedNo && a.PatientId == request.cmis.PatientID && a.CRFormId
                   == request.formId && a.Type == request.cmis.Type && a.DataType == "B");
                if (cmisData == null)
                    return Json(new { code = -100, msg = "该表单暂未填报数据" });
                return Json(new { code = 0, data = TransformData(cmisData.CRFJsonValue) });
            }
            catch (Exception ex)
            {

                return Json(new { code = -100, msg = ex.Message, data = ex });
            }
        }

        public string TransformData(string jsonInput)
        {

            var inputObject = JObject.Parse(jsonInput);
            Root root = new Root { variables = new List<Variable>() };

            foreach (var property in inputObject.Properties())
            {
                JToken propertyValue = property.Value;

                string value;
                if (propertyValue.Type == JTokenType.Array)
                {
                    // 如果值是数组，将其转换为用逗号分隔的字符串
                    //   value = string.Join(",", ((JArray)propertyValue).Select(token => (string)token));
                    value = JsonConvert.SerializeObject(((JArray)propertyValue));
                }
                else
                {
                    value = (string)propertyValue;
                }

                Variable variable = new Variable
                {
                    variable_name = property.Name,
                    value = value,
                    source = ""
                };
                root.variables.Add(variable);
            }

            return JsonConvert.SerializeObject(root);
        }

        private void getDetailCount(JArray detailValue, ref int totalFields, ref int NotEmptyFields)
        {
            foreach (JObject detail in detailValue)
            {
                var detailPropertyList = detail.Properties();
                foreach (var property in detailPropertyList)
                {
                    totalFields++;
                    JToken propertyValue = property.Value;

                    switch (propertyValue.Type)
                    {
                        case JTokenType.String:
                            if (!string.IsNullOrEmpty((string)propertyValue))
                            {
                                NotEmptyFields++;
                            }
                            break;
                        case JTokenType.Integer:
                        case JTokenType.Float:
                            if (((IConvertible)propertyValue).ToString() != "0")
                            {
                                NotEmptyFields++;
                            }
                            break;
                        case JTokenType.Array:
                            if (((JArray)propertyValue).Count > 0)
                            {
                                NotEmptyFields++; // 如果数组非空，则计数增加
                            }

                            break;
                        // 根据需要添加其他类型的处理
                        default:
                            // 处理其他类型的值或忽略
                            break;
                    }
                }
            }
        }

        private string GetTips(string formId)
        {
            var resultResult = new List<dynamic>();
            var fromList = db.CRFormFieldSets.Include(O => O.CRForm).Where(o => o.CRForm.FormId == formId).ToList();

            foreach (var item in fromList)
            {
                dynamic result = new ExpandoObject();
                result.@变量名 = item.FieldName;
                result.@变量描述 = item.FieldComment;
                resultResult.Add(result);
            }

            var json = JsonConvert.SerializeObject(resultResult);
            return json;
        }
    }


}
