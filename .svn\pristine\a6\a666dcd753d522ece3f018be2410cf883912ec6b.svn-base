﻿using Microsoft.AspNetCore.SignalR;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AngelwinResearch.WebUI.Unity
{
    public class ConnService
    {
        private readonly ConcurrentDictionary<string, string> IpConnectionMap = new ConcurrentDictionary<string, string>();

        public void AddIP(string ip, string connectionId)
        {
            IpConnectionMap.TryAdd(ip, connectionId);
        }

        public void DelConnection(string connectionId)
        {

            var ip = IpConnectionMap.First(kvp => kvp.Value == connectionId).Key;
            if (!string.IsNullOrEmpty(ip))
                IpConnectionMap.TryRemove(ip, out var conn);
        }

        public string GetConnectionId(string ip)
        {
            return IpConnectionMap.TryGetValue(ip, out var connectionId) ? connectionId : null;
        }
    }

    public class ChatHub : Hub
    {
        private readonly ConnService connectionService;

        public ChatHub(ConnService _connectionService)
        {
            connectionService = _connectionService;
        }

        // 连接建立时获取IP
        public override async Task OnConnectedAsync()
        {
            var clientIp = Context.GetHttpContext().Request.HttpContext.Connection.RemoteIpAddress.ToString();
            // 处理代理转发（如Nginx/K8s）
            if (Context.GetHttpContext().Request.Headers.TryGetValue("X-Forwarded-For", out var xff))
            {
                clientIp = xff.ToString().Split(',')[0].Trim();
            }
            connectionService.AddIP(clientIp, Context.ConnectionId);
            await base.OnConnectedAsync();
        }

        // 断开连接时清理映射
        public override async Task OnDisconnectedAsync(Exception exception)
        {
            connectionService.DelConnection(Context.ConnectionId);
            await base.OnDisconnectedAsync(exception);
        }

        /// <summary>
        /// 向所有客户端广播消息
        /// </summary>
        /// <param name="message"></param>
        /// <returns></returns>
        public async Task SendToAll(string message)
        {
            await Clients.All.SendAsync("ReceiveMessage", message);
        }

        /// <summary>
        /// 向特定客户端发送消息
        /// </summary>
        /// <param name="connectionId"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public async Task SendToClient(string connectionId, string message)
        {
            await Clients.Client(connectionId).SendAsync("ReceivePrivate", message);
        }

        /// <summary>
        /// IP 推送消息
        /// </summary>
        /// <param name="connectionId"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public async Task SendToClientWithIP(string ip, string message)
        {
            var connectionId = connectionService.GetConnectionId(ip);
            if (connectionId != null)
            {
                await Clients.Client(connectionId).SendAsync("ReceivePrivate", $"私密消息: {message}");
            }
        }

        // 获取当前连接ID
        public string GetConnectionId() => Context.ConnectionId;
    }

}
