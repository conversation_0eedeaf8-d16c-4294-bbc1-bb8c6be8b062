﻿@{
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单字段设置管理</title>
    <link href="~/layuiadmin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/layuiadmin/style/admin.css" rel="stylesheet" />
    <style>
        /*弹窗*/
        .window_wrap {
            padding: 15px;
        }

        #detail_window .layui-form-label {
            width: 100px;
        }

        #detail_window .layui-form-val {
            padding: 9px 15px;
        }

        #detail_window .mindow_icon .title_icon {
            display: inline-block;
        }

        #detail_window .mindow_icon {
            text-align: center;
            padding: 20px 0;
        }

        #detail_window .layui-form-item {
            margin-bottom: 0;
        }
        /* 定义表头样式 */
        .layui-table-header .layui-table-cell {
            font-weight: bold; /* 加粗 */
            font-size: 14px; /* 可选：调整字体大小以提升可读性 */
            color: #333; /* 可选：调整字体颜色 */
        }
    </style>
</head>

<body style="padding:5px;">
    <div class="layui-col-md12">
        <div class="layui-card">
            <div class="layui-card-body layui-form">
                <div class="layui-inline">
                    <div id="xmDeptsList" class="xm-select-demo" style="width:250px"></div>
                </div>
                <div class="layui-inline">
                    <div id="xmGroupsList" class="xm-select-demo" style="width:250px"></div>
                </div>
                <div class="layui-inline">
                    <div id="xmSelectFormList" class="xm-select-demo" style="width:250px"></div>
                </div>
                <div class="layui-inline">
                    <div class="layui-input-inline" style="width:200px;">
                        <input type="text" class="layui-input" name="keyWord" placeholder="请输入变量名称" id="keyWord" />
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-normal fr" id="Search">查询</button>
                    <button class="layui-btn" id="addField" data-type="add">新增</button>
                    <button class="layui-btn layui-btn-danger" id="removeAll">删除</button>
                    <button class="layui-btn layui-btn-normal" id="importField">批量导入</button>
                </div>
            </div>
            <div class="layui-card-body">
                <table id="tablelist" lay-filter="tablelist"></table>
                <script type="text/html" id="tableBar">
                    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit" style="text-decoration:none"><i class="layui-icon layui-icon-edit"></i>编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del" style="text-decoration:none"><i class="layui-icon layui-icon-delete"></i>删除</a>
                </script>
            </div>
        </div>
    </div>
    <script src="~/js/jquery-3.5.1.min.js"></script>
    <script src="~/layuiadmin/layui/layui.js"></script>
    <script src="~/layuiadmin/layuiextend/xm-select.js"></script>
    <script src="~/js/common.js"></script>

    <script>
        layui.use(['element', 'layer', 'table', 'laydate', 'form'], function () {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , laydate = layui.laydate
                , $ = layui.$
                , form = layui.form
                , tableH = '';
            var url = ''

            table.render({
                elem: '#tablelist'
                , id: 'tablelist'
                , page: true
                , limit: 20
                , height: 'full-110'
                , cols: [[
                    { type: 'checkbox', fixed: 'left' },
                    { field: 'No', title: '序号', type: 'numbers', fixed: 'left' }
                    , { field: 'Id', title: 'Id',hide:true }
                    /* , { field: 'FormId', title: '表单Id'}*/
                    , { field: 'FormName', title: 'CRF表单名称', width: 250 }
                    , { field: 'DeptName', title: '科研机构', width: 200 }
                    , { field: 'FieldName', title: '变量名称' }
                    , { field: 'FieldComment', title: '变量描述' }
                    , { field: 'Orderby', title: '排序号' }
                    , { field: 'RangeValue', title: '取值范围' }
                    , { field: 'ExtractSet', title: '提取要求' }
                    , { field: 'GroupName', title: '分组名称' }
                    , { field: 'GroupDesc', title: '分组描述' }
                    , { title: '操作', toolbar: '#tableBar', width: 160, minWidth: 160, fixed: 'right' }
                ]]
                , done: function (res, curr, count) {
                }
            });

            //监听tablelist工具条
            table.on('tool(tablelist)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    form.val('fm', data);
                    xmDeptsList2.update({
                        disabled: true
                    });

                    xmSelectFormList2.update({
                        disabled: true
                    });
                    if (data.DeptId) {
                        var arr = new Array();
                        arr.push(data.DeptId);
                        xmDeptsList2.setValue(arr);
                        getFormList(2, data.DeptId, data.GroupId, data.CRFormId);
                    }

                    windowsIndex = layer.open({
                        type: 1,
                        title: '修改【' + data.FormName + '】变量信息',
                        area: '600px',
                        resize: true,
                        content: $('#form_window')
                    });
                    url = '/BasicConfig/CRFormFieldSetting/Edit';
                }
                else if (obj.event === 'del') {
                    layer.confirm('确定要删除名为【' + data.FieldName + '】的变量以及相关配置吗？将无法恢复。', {
                        title: '',
                        btn: ['确定', '取消'], //按钮
                        resize: false
                    }, function (index) {
                        $.post('/BasicConfig/CRFormFieldSetting/Del', { id: data.Id }, function (result) {
                            if (result.okMsg) {
                                layer.msg(result.okMsg);
                                currentIndex = -1;
                                table.reload('tablelist'); //重载表格
                            } else {
                                layer.msg(result.errorMsg);
                            }
                        }, 'json');
                        layer.close(index);
                    });
                }
            });

            $(document).ready(function () {

                GetDeptsTree();

                $(document).on('click', '#Search', function () {
                    getData();
                })

                $(document).on('click', '#addField', function () {
                    $("#fm")[0].reset();
                    $('#Id').val(0);
                    xmDeptsList2.update({
                        disabled: false
                    });
                    xmSelectFormList2.update({
                        disabled: false
                    });
                    windowsIndex = layer.open({
                        type: 1,
                        title: '新增变量',
                        area: '500px',
                        resize: true,
                        content: $('#form_window')
                    });
                    url = '/BasicConfig/CRFormFieldSetting/Create';
                });

                $(document).on('click', '#importField', function () {

                windowsIndex = layer.open({
                    title: '批量导入表单字段设置',
                    area: ['100%', '100%'],
                    type: 1,
                    resize: false,
                    content: $('#form_window_Import'),
                    success: function(layero, index){
                        var layerHeight = layero.height();
                        var pop1 = $(".pop1").height();
                        var pop2=$(".pop2").height();
                        tableH = layerHeight-(pop1+pop2)-150;
                        tableRender2([],tableH);
                    }
                });
            });



            $(document).on('click', '#downloadTemplate', function () {
                downloadTemplate();
            });

            $(document).on('click', '#addFileBtn', function () {
                uploadFile();
            });
            });

            var xmDeptsList = xmSelect.render({
                el: '#xmDeptsList',
                model: { label: { type: 'text' } },
                prop: {
                    name: 'title',
                    value: 'id',
                },
                minWidth: 200,
                radio: true,
                filterable: true,
                clickClose: true,
                //树
                tree: {
                    show: true,
                    //非严格模式
                    strict: false,
                    //默认展开节点
                    expandedKeys: [-1],
                },
                data: []
                , on: function (val) {
                    getGroupsList(1, val.arr[0].id, "");
                    getFormList(1, val.arr[0].id, "", "");
                }
            });

            var xmDeptsList2 = xmSelect.render({
                el: '#xmDeptsList2',
                model: { label: { type: 'text' } },
                prop: {
                    name: 'title',
                    value: 'id',
                },
                minWidth: 200,
                radio: true,
                filterable: true,
                clickClose: true,
                //树
                tree: {
                    show: true,
                    //非严格模式
                    strict: false,
                    //默认展开节点
                    expandedKeys: [-1],
                },
                data: []
                , on: function (val) {
                    getFormList(2, val.arr[0].id, "", "");
                }
            });

            var xmGroupsList = xmSelect.render({
                el: '#xmGroupsList',
                model: { label: { type: 'text' } },
                prop: {
                    name: 'title',
                    value: 'id',
                },
                minWidth: 200,
                radio: true,
                filterable: true,
                clickClose: true,
                //树
                tree: {
                    show: true,
                    //非严格模式
                    strict: false,
                    //默认展开节点
                    expandedKeys: [-1],
                },
                data: []
                , on: function (val) {
                    getFormList(1, "", val.arr[0].id, "");
                }
            });

            var xmSelectFormList = xmSelect.render({
                el: '#xmSelectFormList',
                autoRow: true,
                prop: {
                    name: 'Name',
                    value: 'Id',
                },
                filterable: true,
                tips: '请选择CRF表单',
                on: function (data) {
                },
                done: function (res) {
                }, model: {
                    label: {
                        type: 'total', //自定义与下面的对应
                        total: {
                            template(data, sels) {
                                return "已选中 " + sels.length + " 项, 共 " + data.length + " 项"
                            }
                        },
                    }
                }
            })

            var xmSelectFormList2 = xmSelect.render({
                el: '#xmSelectFormList2',
                autoRow: true,
                radio: true,
                prop: {
                    name: 'Name',
                    value: 'Id',
                },
                filterable: true,
                tips: '请选择CRF表单',
                on: function (data) {
                },
                done: function (res) {
                }
            })

            function GetDeptsTree() {
                $.ajax({
                    url: '/CommAPI/GetOrgsTreeList',
                    type: "post",
                    datatype: 'json',
                    success: function (result) {
                        xmDeptsList.update({
                            data: result
                        });
                        xmDeptsList2.update({
                            data: result
                        });
                        if (result[0].id) {
                            var arr = new Array();
                            arr.push(result[0].id);
                            xmDeptsList.setValue(arr);
                            getGroupsList(1, result[0].id, "");
                            getFormList(1, result[0].id, "", "");
                            setTimeout(function () {
                                getData();
                            }, 1000);
                        }
                    }, error: function () {
                        layer.msg("获取失败！");
                    }
                })
            };

            function getGroupsList(type, val, defaultValue) {
                $.ajax({
                    url: '/CommAPI/GetGroupTreeList?hospitalDeptId=' + val,
                    // contentType: "application/json",
                    type: "get",
                    datatype: 'json',
                    success: function (result) {
                        debugger;
                        if (type == 1) {
                            xmGroupsList.update({
                                data: result
                            });
                            if ($.trim(defaultValue) != '' && result[0].id) {
                                var arr = new Array();
                                arr.push(defaultValue);
                                xmGroupsList.setValue(arr);
                            }
                        }
                    }, error: function () {
                        layer.msg("获取失败！");
                    }
                });
            }

            function getFormList(type, deptId, groupId, defaultValue) {
                if (deptId == "") {
                    if (type == 1)
                        deptId = xmDeptsList.getValue('valueStr');
                    else
                        deptId = xmDeptsList2.getValue('valueStr');

                }
                if (groupId == "") {
                    if (type == 1)
                        groupId = xmGroupsList.getValue('valueStr');
                    else
                        groupId = xmGroupsList.getValue('valueStr');
                }
                $.ajax({
                    url: '/BasicConfig/CRFormFieldSetting/GetFormList?deptId=' + deptId + "&groupId=" + groupId,
                    // contentType: "application/json",
                    type: "get",
                    datatype: 'json',
                    success: function (result) {
                        if (type == 1) {
                            xmSelectFormList.update({
                                data: result.data
                            });
                            if ($.trim(defaultValue) != '') {
                                var arr = new Array();
                                arr.push(defaultValue);
                                xmSelectFormList.setValue(arr);
                            }
                        }
                        else {
                            xmSelectFormList2.update({
                                data: result.data
                            });
                            if ($.trim(defaultValue) != '') {
                                var arr = new Array();
                                arr.push(defaultValue);
                                xmSelectFormList2.setValue(arr);
                            }
                        }

                    }, error: function () {
                        layer.msg("获取失败！");
                    }
                });
            }

            function getData() {
                table.reload('tablelist', {
                    page: {
                        curr: 1
                    },
                    url: '/BasicConfig/CRFormFieldSetting/List',
                    where: {
                        'deptId': xmDeptsList.getValue('valueStr'),
                        'groupId': xmGroupsList.getValue('valueStr'),
                        'formIds': xmSelectFormList.getValue('valueStr'),
                        'keyWords': $.trim($("#keyWord").val()),
                    }
                });
            }

            //监听提交
            form.on('submit(submit)', function (data) {
                var indes = layer.load(1);
                var crformId = xmSelectFormList2.getValue('valueStr');
                var formName = xmSelectFormList2.getValue('nameStr');
                data.field.CRFormId = crformId;
                data.field.FormName = formName;
                //提交 Ajax 成功后，关闭当前弹层并重载表格
                $.ajax({
                    url: url,
                    type: "post",
                    data: { 'node': data.field },
                    datatype: 'json',
                    success: function (data) {
                        if (data.okMsg) {
                            layer.msg(data.okMsg);
                            layer.close(windowsIndex);//关闭弹出层
                            table.reload('tablelist'); //重载表格
                        }
                        else {
                            layer.msg(data.errorMsg);
                        }
                        layer.close(indes);
                    }, error: function (res) {
                        layer.msg("加载统计信息错误：" + res.responseText);
                        layer.close(indes);
                    }
                });
                return false;
            });

           
            form.verify({
                // 验证最小值
                RangValue: function (value, elem) {
                    if (!value) return; // 若值未填写，不进行后续规则验证
                    // 自定义规则
                    if (/^(\-|\+)?\d+(\.\d+)?$/.test(value) == false) {
                        return '必须填写数值';
                    }

                    var valueMin = parseFloat($.trim($("#MinRangeValue").val()));
                    var valueMax = parseFloat($.trim($("#MaxRangeValue").val()));
                    if (!isNaN(valueMin) && !isNaN(valueMax)) {
                        if (valueMax < valueMin) {
                            return '区间范围设置有误，上限小于下限';
                        }
                    }
                }
            });

            // 添加批量删除按钮的点击事件
            $('#removeAll').on('click', function () {
                var checkStatus = table.checkStatus('tablelist');
                var data = checkStatus.data;
                if (data.length === 0) {
                    layer.msg('请选择要删除的记录', { icon: 2 });
                    return;
                }

                layer.confirm(`确定要删除选中的 <b>${data.length}</b> 条记录吗？删除后将无法恢复。`, {
                    title: '批量删除',
                    btn: ['确定', '取消'], //按钮
                    resize: false
                }, function (index) {
                    var ids = [];
                    $.each(data, function (i, item) {
                        ids.push(item.Id);
                    });
                    $.ajax({
                        url: '/BasicConfig/CRFormFieldSetting/BatchDelete',
                        type: 'POST',
                        data: { ids: ids },
                        success: function (res) {
                            if (res.code == 0) {
                                layer.msg(res.msg);
                                table.reload('tablelist'); //重载表格
                            } else {
                                layer.msg(res.errorMsg);
                            }
                        }
                    });
                    layer.close(index);
                });
            });

             // 下载模板函数
        function downloadTemplate() {
            var deptIds = xmDeptsList.getValue('valueStr');
            var groupId = xmGroupsList.getValue('valueStr');
            var formIds = xmSelectFormList.getValue('valueStr');
            var keyWords = $.trim($("#keyWord").val());

            var url = '/BasicConfig/CRFormFieldSetting/DownloadTemplate?deptId=' + encodeURIComponent(deptIds) +
                     '&groupId=' + encodeURIComponent(groupId) +
                     '&formIds=' + encodeURIComponent(formIds) +
                     '&keyWords=' + encodeURIComponent(keyWords);

            window.open(url, '_blank');
        }

        // 上传文件函数
        function uploadFile() {
            var input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xlsx,.xls';
            input.onchange = function(e) {
                var file = e.target.files[0];
                if (!file) return;

                // 验证文件类型
                var fileName = file.name.toLowerCase();
                if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
                    layer.msg('请选择Excel文件（.xlsx或.xls格式）');
                    return;
                }

                var formData = new FormData();
                formData.append('file', file);

                $('#UploadSpan').show();

                $.ajax({
                    url: '/BasicConfig/CRFormFieldSetting/UploadFile',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    cache: false,
                    success: function(result) {
                        $('#UploadSpan').hide();
                        console.log('上传结果:', result);
                        if (result.status) {
                            layer.msg(result.msg);
                           
                            tableRender2(result.data || [],tableH);
                            // 刷新主表格
                            getData();
                        } else {
                            layer.msg(result.msg);
                        }
                    },
                    error: function(xhr, status, error) {
                        $('#UploadSpan').hide();
                        console.error('上传错误:', xhr, status, error);
                        if (xhr.status === 405) {
                            layer.msg('请求方法不被允许，请检查服务器配置');
                        } else {
                            layer.msg('文件上传失败: ' + error);
                        }
                    }
                });
            };
            input.click();
        }

         // 渲染导入结果表格
        function tableRender2(data,tableHeight) {
            table.render({
                elem: '#tablelist2',
                id: 'tablelist2',
                page: true,
                limit: 20,
                height: tableHeight,
                cols: [[
                    { field: 'formName', title: 'CRF表单名称', width: 200 },
                    { field: 'deptName', title: '科研机构', width: 150 },
                    { field: 'fieldName', title: '变量名称', width: 150 },
                    { field: 'fieldDescription', title: '变量描述', width: 200 },
                    { field: 'orderby', title: '排序号', width: 100 },
                    { field: 'fieldRange', title: '取值范围', width: 120 },
                    { field: 'extractRequirement', title: '提取要求', width: 200 },
                    { field: 'groupName', title: '分组名称', width: 100 },
                    { field: 'groupDesc', title: '分组描述', width: 150 },
                    { field: 'status', title: '处理状态', width: 150 }
                ]]
                , data: data
            });
        }
        });
    </script>
</body>

<!--新增/修改弹框-->
<div class="window_wrap" id="form_window" style="display: none">
    <form class="layui-form" lay-filter="fm" id="fm" action="">
        <div class="layui-form-item">
            <label class="layui-form-label">科研机构</label>
            <div class="layui-input-block">
                <div id="xmDeptsList2"></div>
            </div>
        </div>
        @*<div class="layui-form-item">
            <label class="layui-form-label">专病</label>
            <div class="layui-input-block">
                <div id="xmGroupsList2"></div>
            </div>
        </div>*@
        <div class="layui-form-item">
            <label class="layui-form-label">CRF表单</label>
            <div class="layui-input-block">
                <div id="xmSelectFormList2"></div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">变量名称</label>
            <div class="layui-input-block ">
                <input type="text" name="Id" id="Id" style="display:none;" />
                <input type="text" name="FieldName" id="FieldName" lay-verify="required" placeholder="请输入变量名" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">排序号</label>
            <div class="layui-input-block ">
                <input type="number" name="Orderby" id="Orderby" min="1" lay-verify="required" placeholder="请输入排序号" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">取值范围</label>
            <div class="layui-input-block ">
                <div class="layui-input-inline" style="width: 150px;">
                    <input type="text" id="MinRangeValue" name="MinRangeValue" lay-verify="RangValue" autocomplete="off" class="layui-input">
                </div>
                <div class="layui-form-mid">——</div>
                <div class="layui-input-inline" style="width: 150px;">
                    <input type="text" id="MaxRangeValue" name="MaxRangeValue" lay-verify="RangValue" autocomplete="off" class="layui-input">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">变量描述</label>
            <div class="layui-input-block ">
                <input type="text" name="FieldComment" id="FieldComment" placeholder="请输入描述" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">提取要求</label>
            <div class="layui-input-block">
                <textarea name="ExtractSet" id="ExtractSet" class="layui-textarea" style="resize: none"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">分组名称</label>
            <div class="layui-input-block ">
                <input type="text" name="GroupName" id="GroupName" placeholder="请输入分组名称" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">分组描述</label>
            <div class="layui-input-block ">
                <input type="text" name="GroupDesc" id="GroupDesc" placeholder="请输入分组描述" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="button" class="layui-btn" lay-submit lay-filter="submit">提交</button>
                <button type="reset" class="layui-btn layui-btn-primary" id="btn_reset">重置</button>
            </div>
        </div>
    </form>
</div>

<!-- 批量导入弹窗 -->
<div class="window_wrap" id="form_window_Import" style="display: none">
    <div class="layui-card">
        <div class="layui-card-header pop1">
            <div class="layui-inline">
                <div class="layui-input-inline">
                    <button class="layui-btn layui-btn-normal fr" id="downloadTemplate">下载Excel模板</button>
                    <button class="layui-btn layui-btn" id="addFileBtn">上传Excel文件</button>
                    <span style="color:red;display:none" id="UploadSpan">正在处理文件，请耐心等待，不要进行其他操作。</span>
                </div>
            </div>
        </div>
        <div class="layui-card-body ">
            <div class="pop2" style="margin-bottom: 10px;">
                <span style="color: #666;">说明：</span>
                <ul style="color: red; font-size: 12px; margin: 5px 0; padding-left: 20px;">
                    <li>下载模板时会根据当前筛选条件导出现有数据</li>
                    <li>只能修改：变量描述、取值范围、排序号、提取要求 这四列</li>
                </ul>
            </div>
            <table id="tablelist2" lay-filter="tablelist2"></table>
        </div>
    </div>
</div>
</html>
