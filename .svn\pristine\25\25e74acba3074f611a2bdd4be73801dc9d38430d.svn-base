﻿using AngelwinResearch.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using AngelwinResearch.WebUI.Filters;
using System.IO;
using OfficeOpenXml;
using Microsoft.AspNetCore.Http;
using static AngelwinResearch.WebUI.Unity.Common;

namespace AngelwinResearch.WebUI.Controllers
{

    [Authorizing]
    [Area("BasicConfig")]
    public class CRFormFieldSettingController : Controller
    {
        private readonly AngelwinResearchDbContext db;

        public CRFormFieldSettingController(AngelwinResearchDbContext _db)
        {
            db = _db;
        }
        public IActionResult Index()
        {
            return View();
        }


        public IActionResult GetFormList(int deptId, int? groupId)
        {
            try
            {
                var query = db.CRForms.Where(o => o.LevelType == 2).AsQueryable();
                if (deptId > 0)
                {
                    query = query.Where(o => o.HospitalDeptId == deptId).AsQueryable();
                }
                if (groupId != null)
                {
                    query = query.Where(o => o.DiseaseSpecificGroupId == groupId).AsQueryable();
                }
                var list = query.Select(o => new { Id = o.Id, Name = o.FormName })
                    .Distinct().ToList();
                var setting = new JsonSerializerSettings();
                setting.ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver();//json字符串大小写原样输出
                return Json(new { code = "0", msg = "成功", data = list }, setting);
            }
            catch (Exception ex)
            {
                return Json(new { code = "-100", msg = "数据查询失败！", data = ex });
            }
        }

        public IActionResult List(int page, int limit, int deptId, int? groupId, string formIds, string keyWords)
        {
            var totalCount = 0;
            var dept = db.HospitalDepts.Where(o => o.Id == deptId).FirstOrDefault();
            var resultResult = new List<dynamic>();
            var query = db.CRFormFieldSets.Include(o => o.CRForm).ThenInclude(o => o.DiseaseSpecificGroup)
                        .OrderBy(o => o.CRFormId).ThenBy(o => o.Orderby)
                        .Where(o => o.CRForm.HospitalDeptId == deptId).AsQueryable();
            if (groupId != null)
            {
                query = db.CRFormFieldSets.Include(o => o.CRForm)
                        .Where(o => o.CRForm.DiseaseSpecificGroupId == groupId).AsQueryable();
            }
            if (!string.IsNullOrEmpty(formIds))
            {
                var idsList = formIds.Split(",", StringSplitOptions.RemoveEmptyEntries).ToList();
                query = query.Where(o => idsList.Contains(o.CRForm.Id.ToString()));
            }
            if (!string.IsNullOrWhiteSpace(keyWords))
                query = query.Where(o => o.FieldName.Contains(keyWords));

            totalCount = query.Count();
            var list = query.Skip((page - 1) * limit).Take(limit).ToList();

            if (list != null && list.Any())
            {
                foreach (var item in list)
                {
                    dynamic result = new ExpandoObject();
                    result.Id = item.Id;
                    result.CRFormId = item.CRForm.Id;
                    result.FormId = item.CRForm.FormId;
                    result.FormName = item.CRForm.FormName;
                    result.DeptId = item.CRForm.HospitalDeptId;

                    result.DeptName = dept.DeptName;
                    result.GroupId = item.CRForm.DiseaseSpecificGroupId;
                    result.GroupName = item.CRForm.DiseaseSpecificGroup?.GroupName;
                    result.FieldName = item.FieldName;
                    result.FieldComment = item.FieldComment;
                    result.MinRangeValue = item.MinRangeValue;
                    result.MaxRangeValue = item.MaxRangeValue;
                    result.Orderby = item.Orderby;
                    result.RangeValue = $"{item.MinRangeValue}—{item.MaxRangeValue}";
                    result.ExtractSet = item.ExtractSet;
                    result.GroupName = item.GroupName;
                    result.GroupDesc = item.GroupDesc;
                    result.CreateUserName = item.CreateUserName;
                    result.CreatedTime = item.CreatedTime.ToString("yyyy-MM-dd HH:mm:ss");
                    resultResult.Add(result);
                }
            }
            var setting = new JsonSerializerSettings();
            setting.ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver();//json字符串大小写原样输出
            return Json(new { code = "0", msg = "成功", count = totalCount, data = resultResult }, setting);
        }


        [HttpPost]
        public IActionResult Create(CRFormFieldSet node)
        {
            if (ModelState.IsValid)
            {
                db.CRFormFieldSets.Add(node);
                try
                {
                    db.SaveChanges();
                    return Json(new { okMsg = "创建成功。" });
                }
                catch (Exception ex)
                {
                    return Json(new { errorMsg = "保存时数据出错。" });
                }
            }
            else
            {
                return Json(new { errorMsg = "数据验证错误。" });
            }

        }

        [HttpPost]
        public IActionResult Edit(CRFormFieldSet node)
        {
            if (ModelState.IsValid)
            {
                var entity = db.CRFormFieldSets.FirstOrDefault(o => o.Id == node.Id);
                entity.FieldName = node.FieldName;
                entity.FieldComment = node.FieldComment;
                entity.ExtractSet = node.ExtractSet;
                entity.MinRangeValue = node.MinRangeValue;
                entity.MaxRangeValue = node.MaxRangeValue;
                entity.Orderby = node.Orderby;
                entity.CreateUserName = User.Identity.Name;
                entity.CreatedTime = System.DateTime.Now;
                try
                {
                    db.SaveChanges();
                    return Json(new { okMsg = "修改成功。" });
                }
                catch (Exception ex)
                {
                    return Json(new { errorMsg = "保存数据时出错。" + ex.Message });
                }
            }
            else
            {
                return Json(new { errorMsg = "数据验证错误。" });
            }
        }

        [HttpPost]
        public IActionResult Del(int id)
        {
            try
            {
                var node = db.CRFormFieldSets.Where(a => a.Id == id).FirstOrDefault();
                db.Entry(node).State = EntityState.Deleted;
                db.SaveChanges();
                return Json(new { okMsg = "删除成功。" });
            }
            catch (Exception ex)
            {
                return Json(new { errorMsg = $"删除时数据出错:{ex.Message}。" });
            }
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult BatchDelete(List<int> ids)
        {
            try
            {
                if (ids == null || ids.Count == 0)
                {
                    return Json(new { code = -1, errorMsg = "未选择要删除的记录" });
                }
                var nodes = db.CRFormFieldSets.Where(a => ids.Contains(a.Id)).ToList();
                db.CRFormFieldSets.RemoveRange(nodes);
                db.SaveChanges();
                return Json(new { code = 0, msg = "批量删除成功。" });
            }
            catch (Exception ex)
            {
                return Json(new { code = -1, errorMsg = $"批量删除时出错:{ex.Message}。" });
            }
        }

        /// <summary>
        /// 下载批量导入模板
        /// </summary>
        /// <param name="deptIds">科室ID列表</param>
        /// <param name="formIds">表单ID列表</param>
        /// <param name="keyWords">关键词</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult DownloadTemplate(int deptId, int? groupId, string formIds = "", string keyWords = "")
        {
            try
            {
                Stream stream = new MemoryStream();

                using (ExcelPackage package = new ExcelPackage(stream))
                {
                    ExcelWorksheet worksheet = package.Workbook.Worksheets.Add("表单字段设置");

                    // 设置表头
                    worksheet.Cells["A1"].Value = "CRF表单名称";
                    worksheet.Cells["B1"].Value = "科研机构";
                    worksheet.Cells["C1"].Value = "变量名称";
                    worksheet.Cells["D1"].Value = "变量描述";
                    worksheet.Cells["E1"].Value = "排序号";
                    worksheet.Cells["F1"].Value = "取值范围";
                    worksheet.Cells["G1"].Value = "提取要求";
                    worksheet.Cells["H1"].Value = "分组名称";
                    worksheet.Cells["I1"].Value = "分组描述";
                    // 设置表头样式
                    using (var range = worksheet.Cells["A1:I1"])
                    {
                        range.Style.Font.Bold = true;
                        range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                        range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                        range.Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
                    }
                    var dept = db.HospitalDepts.Where(o => o.Id == deptId).FirstOrDefault();
                    var query = db.CRFormFieldSets.Include(o => o.CRForm).ThenInclude(o => o.DiseaseSpecificGroup)
                       .OrderBy(o => o.CRFormId).ThenBy(o => o.Orderby)
                       .Where(o => o.CRForm.HospitalDeptId == deptId).AsQueryable();
                    if (groupId != null)
                    {
                        query = db.CRFormFieldSets.Include(o => o.CRForm)
                                .Where(o => o.CRForm.DiseaseSpecificGroupId == groupId).AsQueryable();
                    }
                    if (!string.IsNullOrEmpty(formIds))
                    {
                        var idsList = formIds.Split(",", StringSplitOptions.RemoveEmptyEntries).ToList();
                        query = query.Where(o => idsList.Contains(o.CRForm.Id.ToString()));
                    }
                    if (!string.IsNullOrWhiteSpace(keyWords))
                        query = query.Where(o => o.FieldName.Contains(keyWords));

                    var data = query.ToList();

                    // 填充数据
                    int row = 2;
                    foreach (var item in data)
                    {
                        worksheet.Cells[row, 1].Value = item.CRForm.FormName ?? "";
                        worksheet.Cells[row, 2].Value = dept.DeptName ?? "";
                        worksheet.Cells[row, 3].Value = item.FieldName ?? "";
                        worksheet.Cells[row, 4].Value = item.FieldComment ?? "";
                        worksheet.Cells[row, 5].Value = item.Orderby;
                        worksheet.Cells[row, 6].Value = $"{item.MinRangeValue ?? ""}—{item.MaxRangeValue ?? ""}";
                        worksheet.Cells[row, 7].Value = item.ExtractSet ?? "";
                        worksheet.Cells[row, 8].Value = item.GroupName;
                        worksheet.Cells[row, 9].Value = item.GroupDesc;

                        // 设置边框
                        using (var range = worksheet.Cells[row, 1, row, 9])
                        {
                            range.Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
                        }

                        row++;
                    }

                    // 自动调整列宽
                    worksheet.Cells.AutoFitColumns();

                    package.Save();
                }

                return new DownLoadByStreamResult(stream, "表单字段设置模板.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            }
            catch (Exception ex)
            {
                return Json(new { code = "-100", msg = "下载模板失败：" + ex.Message });
            }
        }

        /// <summary>
        /// 批量导入表单字段设置
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> UploadFile(IFormFile file)
        {
            try
            {
                #region 解析文件
                var formFile = file;
                if (formFile.Length == 0)
                {
                    return Json(new { status = false, msg = "上传文件有误，请检查！" });
                }

                //formFile转StreamReader
                StreamReader streamReader = new StreamReader(formFile.OpenReadStream());
                //StreamReader转byte
                byte[] data;

                using (StreamReader sr = new StreamReader(formFile.OpenReadStream()))
                {
                    using (MemoryStream ms = new MemoryStream())
                    {
                        sr.BaseStream.CopyTo(ms);
                        data = ms.ToArray();
                    }
                }
                //byte转Stream
                Stream stream = new MemoryStream(data);

                #endregion
                using (ExcelPackage package = new ExcelPackage(stream))
                {
                    ExcelWorksheet worksheet = package.Workbook.Worksheets[0];
                    if (worksheet.Name != "表单字段设置")
                    {
                        return Json(new { status = false, msg = "上传文件非指定模板格式，请检查!" });
                    }

                    int rowCount = worksheet.Dimension.Rows;
                    var resultResult = new List<dynamic>();
                    var updateCount = 0;
                    var errorList = new List<string>();

                    for (int row = 1; row <= rowCount; row++)
                    {
                        string formName = worksheet.Cells[row, 1].Value?.ToString()?.Trim();
                        string deptName = worksheet.Cells[row, 2].Value?.ToString()?.Trim();
                        string fieldName = worksheet.Cells[row, 3].Value?.ToString()?.Trim();
                        string fieldDescription = worksheet.Cells[row, 4].Value?.ToString()?.Trim();
                        string orderbyStr = worksheet.Cells[row, 5].Value?.ToString()?.Trim();
                        string fieldRange = worksheet.Cells[row, 6].Value?.ToString()?.Trim();
                        string extractRequirement = worksheet.Cells[row, 7].Value?.ToString()?.Trim();
                        string groupName = worksheet.Cells[row, 8].Value?.ToString()?.Trim();
                        string groupDesc = worksheet.Cells[row, 9].Value?.ToString()?.Trim();

                        if (row == 1)
                        {
                            // 验证表头
                            if (formName != "CRF表单名称" ||
                                deptName != "科研机构" ||
                                fieldName != "变量名称" ||
                                fieldRange != "取值范围" ||
                                fieldDescription != "变量描述" ||
                                orderbyStr != "排序号" ||
                                extractRequirement != "提取要求" ||
                                 groupName != "分组名称" ||
                                  groupDesc != "分组描述")
                            {
                                return Json(new { status = false, msg = "上传文件非指定模板格式，请检查表头!" });
                            }
                            continue;
                        }

                        // 跳过空行
                        if (string.IsNullOrEmpty(formName) && string.IsNullOrEmpty(fieldName))
                            continue;

                        try
                        {
                            // 根据表单名称和变量名称查找现有记录
                            var existingField = (from a in db.CRFormFieldSets
                                                 join b in db.CRForms.Include(x => x.HospitalDept) on a.CRFormId equals b.Id
                                                 where b.FormName == formName && a.FieldName == fieldName
                                                 select a).FirstOrDefault();

                            if (existingField != null)
                            {
                                // 只更新允许修改的字段
                                existingField.FieldComment = fieldDescription ?? existingField.FieldComment;
                                var parts = fieldRange?.Split('—');
                                existingField.MinRangeValue = parts?.Length == 2 ? parts[0] : null;
                                existingField.MaxRangeValue = parts?.Length == 2 ? parts[1] : null;
                                if (int.TryParse(orderbyStr, out int orderby))
                                {
                                    existingField.Orderby = orderby;
                                }

                                existingField.ExtractSet = extractRequirement ?? existingField.ExtractSet;
                                existingField.GroupName = groupName ?? existingField.GroupName;
                                existingField.GroupDesc = groupDesc ?? existingField.GroupDesc;
                                updateCount++;

                                resultResult.Add(new
                                {
                                    FormName = formName,
                                    DeptName = deptName,
                                    FieldName = fieldName,
                                    FieldRange = fieldRange,
                                    FieldDescription = fieldDescription,
                                    Orderby = orderbyStr,
                                    ExtractRequirement = extractRequirement,
                                    GroupName = groupName,
                                    GroupDesc = groupDesc,
                                    Status = "更新成功"
                                });
                            }
                            else
                            {
                                errorList.Add($"第{row}行：未找到表单'{formName}'中的变量'{fieldName}'");
                                resultResult.Add(new
                                {
                                    FormName = formName,
                                    DeptName = deptName,
                                    FieldName = fieldName,
                                    FieldRange = fieldRange,
                                    FieldDescription = fieldDescription,
                                    Orderby = orderbyStr,
                                    ExtractRequirement = extractRequirement,
                                    GroupName = groupName,
                                    GroupDesc = groupDesc,
                                    Status = "未找到对应记录"
                                });
                            }
                        }
                        catch (Exception ex)
                        {
                            errorList.Add($"第{row}行处理失败：{ex.Message}");
                            resultResult.Add(new
                            {
                                FormName = formName,
                                DeptName = deptName,
                                FieldName = fieldName,
                                FieldRange = fieldRange,
                                FieldDescription = fieldDescription,
                                Orderby = orderbyStr,
                                ExtractRequirement = extractRequirement,
                                GroupName = groupName,
                                GroupDesc = groupDesc,
                                Status = "处理失败：" + ex.Message
                            });
                        }
                    }

                    // 保存更改
                    if (updateCount > 0)
                    {
                        await db.SaveChangesAsync();
                    }

                    var message = $"处理完成！成功更新 {updateCount} 条记录";
                    if (errorList.Any())
                    {
                        message += $"，{errorList.Count} 条记录处理失败";
                    }

                    return Json(new
                    {
                        status = true,
                        msg = message,
                        data = resultResult,
                        updateCount = updateCount,
                        errorCount = errorList.Count,
                        errors = errorList
                    });
                }
            }
            catch (Exception ex)
            {
                return Json(new { status = false, msg = "文件处理失败：" + ex.Message });
            }
        }

    }
}
