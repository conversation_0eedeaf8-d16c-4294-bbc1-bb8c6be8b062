﻿using AngelwinResearch.ModelExtends;
using AngelwinResearch.Models;
using AngelwinResearch.WebUI.Filters;
using AngelwinResearch.WebUI.Unity;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using static AngelwinResearch.WebUI.Unity.Common;

namespace AngelwinResearch.WebUI.Areas.HospitalCRF.Controllers
{
    [Area("HospitalCRF")]
    [Authorizing]
    public class HospitalCRFDetailsController : Controller
    {
        private readonly IWebHostEnvironment env;
        private IConfiguration config { get; }
        private readonly AngelwinResearchDbContext db;
        public HospitalCRFDetailsController(IWebHostEnvironment _env, IConfiguration configuration, AngelwinResearchDbContext _db)
        {
            env = _env;
            config = configuration;
            db = _db;
        }

        public IActionResult Index()
        {
            return View();
        }

        public List<LayuiTreeDTO> GetOrgsTreeList(bool IsUseicon = false, string Type = "")
        {
            //string currentUserName = User.Identity.Name;
            //var query = db.UserZBZSettings.Include(o => o.UserInfo).Include(o => o.HospitalDept)
            //    .Where(o => o.UserInfo.UserName == currentUserName).AsQueryable();
            var query = db.HospitalDepts.AsQueryable();
            if (!string.IsNullOrWhiteSpace(Type))
                query = query.Where(o => o.DeptType == Type).AsQueryable();
            //var userZBZSettingList = query.ToList();
            List<HospitalDept> orgsList = query.ToList();
            var childList = orgsList.OrderBy(o => o.DeptType).ThenBy(c => c.Id).ToList();
            var OrgsTreeDTOList = new List<LayuiTreeDTO>();
            foreach (var r in childList)
            {
                var result = new LayuiTreeDTO();
                result.id = r.Id+"";
                result.spread = true;
                result.title = r.DeptName;
                result.intro = r.DeptIntro;
                OrgsTreeDTOList.Add(result);
            };
            return OrgsTreeDTOList;
        }

        public List<LayuiTreeDTO> GetGroupList(int hospitalDeptId)
        {
            var HospitalGroup = db.HospitalCRForms.Where(i => i.HospitalDeptId == hospitalDeptId)
                                        .Select(i => new {i.GroupName})
                                        .Distinct()
                                        .ToList();
            var MenuTreeDTOList = new List<LayuiTreeDTO>();
            foreach (var r in HospitalGroup)
            {
                var result = new LayuiTreeDTO();
                result.id = r.GroupName;
                result.spread = true;
                result.title = r.GroupName;
                result.intro = "";
                MenuTreeDTOList.Add(result);
            };
            return MenuTreeDTOList;
        }

        public class LayuiTreeDTO
        {
            public string id { get; set; }
            public string title { get; set; }
            public string intro { get; set; }
            public bool spread { get; set; }
            public bool @checked { get; set; }
        }

        public IActionResult List(int? deptId, string groupName, string keyWord)
        {
            var totalCount = "0";
            dynamic resultCols = new List<dynamic>();
            dynamic col1 = new List<dynamic>(); //列头1
            dynamic col2 = new List<dynamic>(); //列头2
            var dataRowList = new List<dynamic>();
            keyWord = keyWord?.Trim();

            var List = new List<HospitalDatas>();
            try
            {
                var query = db.HospitalCRFDatas.Include(o => o.HospitalCRForm)
                    .Include(o => o.HospitalCRForm.HospitalDept)
                    .Where(o => o.HospitalCRForm.HospitalDeptId == deptId  && o.HospitalCRForm.GroupName == groupName)
                    .AsQueryable();

                if (!string.IsNullOrWhiteSpace(keyWord))
                    query = query.Where(o =>o.PatientId.Contains(keyWord) || o.BLH.Contains(keyWord) || o.PatientName.Contains(keyWord)).AsQueryable();

                List = query
                     .Select(o => new HospitalDatas
                     {
                         Id = o.Id,
                         FormId = o.FormId,
                         StopUsing = o.HospitalCRForm.StopUsing,
                         HospitalCRFormId = o.HospitalCRFormId,
                         AIExtractJsonValue = o.AIExtractJsonValue,
                         OrderBy = o.HospitalCRForm.OrderBy,
                         PatientSource = o.PatientSource,
                         FormName = o.HospitalCRForm.FormName,
                         DeptName = o.HospitalCRForm.HospitalDept.DeptName,
                         PatientId = o.PatientId,
                         PatientName = o.PatientName,
                         Sex = o.Sex,
                         BLH = o.BLH,
                         CRFJsonValue = o.CRFJsonValue
                     }).OrderBy(o =>o.PatientId).ThenBy(o => o.OrderBy)
                    .ToList();

                var cRFormList = List
                .GroupBy(o => o.HospitalCRFormId)
                .Select(g => new {
                    HospitalCRFormId = g.Key,
                    CRForm = g.First(),
                    FormFieldList = GetCRFormField(g.Key)
                }).ToList();

                var dictKeyList = db.CRFormFieldSets.Include(o => o.CRForm)
                    .Where(o => o.CRForm.HospitalDeptId == deptId)
                    .Select(o => new KeyValueDTO { Id = o.CRFormId, FormId = o.CRForm.FormId, Name = o.FieldName, OrderBy = o.Orderby, Value = o.FieldComment })
                    .OrderBy(o => o.OrderBy)
                    .Distinct().ToList();

                #region 拼接表头
                #region 拼接患者相关列头 1-5
                dynamic col1item = new ExpandoObject();
                col1item.field = "zizeng";
                col1item.type = "numbers";
                col1item.title = "";
                col1item.align = "center";
                col1item.@fixed = "left";
                col1item.rowspan = 2;
                col1.Add(col1item);

                dynamic col1item2 = new ExpandoObject();
                col1item2.field = "PatientName";
                col1item2.title = $"<span class=\"table_header\">患者姓名</span>";
                col1item2.width = 120;
                col1item2.rowspan = 2;
                col1item2.align = "center";
                col1item2.@fixed = "left";
                col1.Add(col1item2);

                dynamic col1item3 = new ExpandoObject();
                col1item3.field = "PatientID";
                col1item3.title = $"<span class=\"table_header\">患者ID</span>";
                col1item3.rowspan = 2;
                col1item3.align = "center";
                col1item3.width = 120;
                col1item3.@fixed = "left";
                col1.Add(col1item3);

                dynamic col1itemSex = new ExpandoObject();
                col1itemSex.field = "Sex";
                col1itemSex.title = $"<span class=\"table_header\">性别</span>";
                col1itemSex.width = 80;
                col1itemSex.rowspan = 2;
                col1itemSex.align = "center";
                col1.Add(col1itemSex);

                dynamic col1itemPatSource = new ExpandoObject();
                col1itemPatSource.field = "PatientSource";
                col1itemPatSource.title = $"<span class=\"table_header\">患者来源</span>";
                col1itemPatSource.width = 100;
                col1itemPatSource.rowspan = 2;
                col1itemPatSource.align = "center";
                col1.Add(col1itemPatSource);

                dynamic col1itemYXZX = new ExpandoObject();
                col1itemYXZX.field = "HospitalDeptName";
                col1itemYXZX.title = $"<span class=\"table_header\">科室名称</span>";
                col1itemYXZX.width = 200;
                col1itemYXZX.rowspan = 2;
                col1itemYXZX.align = "center";
                col1.Add(col1itemYXZX);

                dynamic col2ItemAI = new ExpandoObject();
                col2ItemAI.field = "AIExtractJsonValue";
                col2ItemAI.title = $"<span class=\"table_header\">AIExtractJsonValue</span>";
                col2ItemAI.width = 200;
                col2ItemAI.align = "center";

                dynamic col2ItemYC = new ExpandoObject();
                col2ItemYC.field = "StopUsing";
                col2ItemYC.title = $"<span class=\"table_header\">StopUsing</span>";
                col2ItemYC.width = 200;
                col2ItemYC.align = "center";

                #endregion

                #region 拼接表单列头
                foreach (var CRF in cRFormList)
                {
                    if (CRF.FormFieldList.Any() && CRF.FormFieldList.Count() > 0)
                    {
                        dynamic col1ItemXM = new ExpandoObject();
                        col1ItemXM.title = $"<span class=\"table_header\">{CRF.CRForm.FormName}</span>";
                        col1ItemXM.colspan = CRF.FormFieldList.Count();
                        col1ItemXM.align = "center";
                        col1.Add(col1ItemXM);

                        foreach (var item in CRF.FormFieldList)
                        {
                            dynamic col2ItemXM = new ExpandoObject();
                            col2ItemXM.field = $"{CRF.CRForm.FormId}-{item}";
                            var key = item;
                            var dictKeyStr = dictKeyList.Where(o => o.FormId == CRF.CRForm.FormId && o.Name.ToUpper() == key.ToUpper()).FirstOrDefault();
                            if (dictKeyStr != null)
                                key = dictKeyStr.Value;
                            col2ItemXM.title = $"<span class=\"table_header\">{key}</span>";
                            col2ItemXM.width = 120;
                            col2ItemXM.align = "center";
                            col2.Add(col2ItemXM);
                        }
                    }
                    else
                    {
                        dynamic col1ItemXM = new ExpandoObject();
                        col1ItemXM.title = $"<span class=\"table_header\">{CRF.CRForm.FormName}</span>";
                        col1ItemXM.width = 200;
                        col1ItemXM.rowspan = 2;
                        col1ItemXM.align = "center";
                        col1.Add(col1ItemXM);
                    }
                }
                #endregion

                #endregion
                dynamic patientData ="";
                for (int i = 0; i < List.Count(); i++)
                {
                    if (i == 0 || List[i].PatientId != List[i - 1].PatientId)
                    {
                        patientData = new ExpandoObject();

                        var gender = List[i].Sex == "男" ? 1 : 0;
                        patientData.PatientName = List[i].PatientName;
                        patientData.PatientID = List[i].PatientId;
                        patientData.PatientSource = List[i].PatientSource == "O" ? "门诊" : "住院";
                        patientData.Sex = $"<span class=\"sex{gender}\">{List[i].Sex}</span>";
                        patientData.AIExtractJsonValue = List[i].AIExtractJsonValue + "";
                        patientData.HospitalDeptName = List[i].DeptName;
                        patientData.StopUsing = List[i].StopUsing;

                        if (!string.IsNullOrWhiteSpace(List[i].CRFJsonValue))
                        {
                            JObject jObject = JObject.Parse(List[i].CRFJsonValue);
                            if (List[i].StopUsing == true)
                            {
                                foreach (JProperty property in jObject.Properties())
                                {
                                    var Filed = $"{List[i].FormId}-{property.Name.ToUpper()}";
                                    if (!property.Name.ToUpper().StartsWith("DETAILARRAY"))
                                    {
                                        var value = property.Value;
                                        if (value != null && value.ToString().Contains("&&&###"))
                                        {
                                            value = value.ToString().Replace("&&&###", ",");
                                        }
                                        // 添加动态属性
                                        ((IDictionary<string, object>)patientData).Add(new KeyValuePair<string, object>(Filed, value));
                                    }
                                    else
                                    {
                                        var thisValue = "";
                                        var jsonArray = property.Value;
                                        var j = 0;
                                        // 遍历JArray中的每个JObject
                                        foreach (JObject jsonObject in jsonArray)
                                        {
                                            j++;
                                            thisValue += $"记录{j}:";
                                            // 动态地遍历JObject中的每个键值对
                                            foreach (JProperty arrayProperty in jsonObject.Properties())
                                            {
                                                string key = arrayProperty.Name;
                                                string value = arrayProperty.Value.ToString();
                                                var dictKeyStr = dictKeyList.Where(o => o.FormId == List[i].FormId && o.Name.ToUpper() == key.ToUpper()).FirstOrDefault();
                                                if (dictKeyStr != null)
                                                    key = dictKeyStr.Value;
                                                thisValue += $"{key}:\"{value}\"；";
                                            }
                                            thisValue += "。<br/>";
                                        }
                                    // 添加动态属性
                                    ((IDictionary<string, object>)patientData).Add(new KeyValuePair<string, object>(Filed, thisValue));
                                    }
                                }
                                if (i != List.Count - 1 && List[i].PatientId != List[i + 1].PatientId)
                                {
                                    dataRowList.Add(patientData);
                                }
                                if (i == List.Count - 1)
                                {
                                    dataRowList.Add(patientData);
                                }
                            }
                            else
                            {
                                foreach (JProperty property in jObject.Properties())
                                {
                                    var Filed = $"{List[i].FormId}-{property.Name.ToUpper()}";
                                    var value = "<span class='NoNeedInputSpan'>——<span>";
                                    ((IDictionary<string, object>)patientData).Add(new KeyValuePair<string, object>(Filed, value));
                                    if (i == List.Count - 1)
                                    {
                                        dataRowList.Add(patientData);
                                    }
                                }
                            }
                            
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrWhiteSpace(List[i].CRFJsonValue))
                        {
                            JObject jObject = JObject.Parse(List[i].CRFJsonValue);
                            if (List[i].StopUsing == true)
                            {
                                foreach (JProperty property in jObject.Properties())
                                {
                                    var Filed = $"{List[i].FormId}-{property.Name.ToUpper()}";
                                    if (!property.Name.ToUpper().StartsWith("DETAILARRAY"))
                                    {
                                        var value = property.Value;
                                        if (value != null && value.ToString().Contains("&&&###"))
                                        {
                                            value = value.ToString().Replace("&&&###", ",");
                                        }
                                    // 添加动态属性
                                    ((IDictionary<string, object>)patientData).Add(new KeyValuePair<string, object>(Filed, value));
                                    }
                                    else
                                    {
                                        var thisValue = "";
                                        var jsonArray = property.Value;
                                        var j = 0;
                                        // 遍历JArray中的每个JObject
                                        foreach (JObject jsonObject in jsonArray)
                                        {
                                            j++;
                                            thisValue += $"记录{j}:";
                                            // 动态地遍历JObject中的每个键值对
                                            foreach (JProperty arrayProperty in jsonObject.Properties())
                                            {
                                                string key = arrayProperty.Name;
                                                string value = arrayProperty.Value.ToString();
                                                var dictKeyStr = dictKeyList.Where(o => o.FormId == List[i].FormId && o.Name.ToUpper() == key.ToUpper()).FirstOrDefault();
                                                if (dictKeyStr != null)
                                                    key = dictKeyStr.Value;
                                                thisValue += $"{key}:\"{value}\"；";
                                            }
                                            thisValue += "。<br/>";
                                        }
                                    // 添加动态属性
                                    ((IDictionary<string, object>)patientData).Add(new KeyValuePair<string, object>(Filed, thisValue));
                                    }
                                }
                            }
                            else
                            {
                                foreach (JProperty property in jObject.Properties())
                                {
                                    var Filed = $"{List[i].FormId}-{property.Name.ToUpper()}";
                                    var value = "<span class='NoNeedInputSpan'>——<span>";
                                    ((IDictionary<string, object>)patientData).Add(new KeyValuePair<string, object>(Filed, value));
                                }
                            }

                        }
                        if (i == List.Count - 1)
                        {
                            dataRowList.Add(patientData);
                        }
                        else if (i != List.Count - 1 && List[i].PatientId != List[i + 1].PatientId)
                        {
                            dataRowList.Add(patientData);
                        }
                    }

                }
                resultCols.Add(col1);
                resultCols.Add(col2);
                var setting = new JsonSerializerSettings();
                setting.ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver();//json字符串大小写原样输出
                return Json(new { code = "0", okMsg = "成功", count = totalCount, columns = resultCols, list = dataRowList }, setting);
            }
            catch (Exception ex)
            {
                return Json(new { code = "0", errorMsg = $"失败:{ex.Message}", count = 0 });
            }
        }

        public List<string> GetCRFormField(int? crfId)
        {
            var fieldList = new List<string>();
            var cRFDataList = db.HospitalCRFDatas.Where(o => o.HospitalCRFormId == crfId)
                .OrderByDescending(o => o.CreatedTime).Take(5).ToList();
            foreach (var crfdata in cRFDataList)
            {
                JObject jObject = JObject.Parse(crfdata.CRFJsonValue);
                foreach (JProperty property in jObject.Properties())
                {
                    var fieldName = property.Name.ToUpper();
                    if (!fieldList.Exists(o => o == fieldName))
                    {
                        fieldList.Add(fieldName);
                    }
                }
            }
            return fieldList;
        }

        public IActionResult DownLoad(int? deptId, string groupName, string keyWord)
        {
            keyWord = keyWord?.Trim();
            dynamic resultCols = new List<dynamic>();
            dynamic resultData = new List<dynamic>();
            HospitalDept dept = null;
            if (deptId != null)
            {
                dept = db.HospitalDepts.Where(o => o.Id == deptId).FirstOrDefault();
            }

            var List = new List<HospitalDatas>();

            var query = db.HospitalCRFDatas.Include(o => o.HospitalCRForm)
                    .Include(o => o.HospitalCRForm.HospitalDept)
                    .Where(o => o.HospitalCRForm.HospitalDeptId == deptId && o.HospitalCRForm.GroupName == groupName)
                    .AsQueryable();

            if (!string.IsNullOrWhiteSpace(keyWord))
                query = query.Where(o => o.PatientId.Contains(keyWord) || o.BLH.Contains(keyWord) || o.PatientName.Contains(keyWord)).AsQueryable();

            List = query
                 .Select(o => new HospitalDatas
                 {
                     Id = o.Id,
                     FormId = o.FormId,
                     StopUsing = o.HospitalCRForm.StopUsing,
                     HospitalCRFormId = o.HospitalCRFormId,
                     OrderBy = o.HospitalCRForm.OrderBy,
                     PatientSource = o.PatientSource,
                     FormName = o.HospitalCRForm.FormName,
                     DeptName = o.HospitalCRForm.HospitalDept.DeptName,
                     PatientId = o.PatientId,
                     PatientName = o.PatientName,
                     Sex = o.Sex,
                     BLH = o.BLH,
                     CRFJsonValue = o.CRFJsonValue
                 }).OrderBy(o => o.PatientId).ThenBy(o => o.OrderBy)
                .ToList();

            var cRFormList = List
            .GroupBy(o => o.HospitalCRFormId)
            .Select(g => new {
                HospitalCRFormId = g.Key,
                CRForm = g.First(),
                FormFieldList = GetCRFormField(g.Key)
            }).ToList();

            var dictKeyList = db.CRFormFieldSets.Include(o => o.CRForm)
                .Where(o => o.CRForm.HospitalDeptId == deptId)
                .Select(o => new KeyValueDTO { Id = o.CRFormId, FormId = o.CRForm.FormId, Name = o.FieldName, OrderBy = o.Orderby, Value = o.FieldComment })
                .OrderBy(o => o.OrderBy)
                .Distinct().ToList();

            Stream stream = new MemoryStream();
            using (ExcelPackage package = new ExcelPackage(stream))
            {
                ExcelWorksheet worksheet = package.Workbook.Worksheets.Add("result");//创建worksheet
                #region 表头、样式
                worksheet.Cells[1, 1].Value = $"全院级CRF详细数据({dept.DeptName})";
                worksheet.Cells[2, 1].Value = "序号";
                worksheet.Cells[2, 2].Value = "患者姓名";
                worksheet.Cells[2, 3].Value = "患者ID";
                worksheet.Cells[2, 4].Value = "性别";
                worksheet.Cells[2, 5].Value = "患者来源";
                worksheet.Cells[2, 6].Value = "科室名称";
                worksheet.Cells[2, 1, 3, 1].Merge = true;//合并单元格-行
                worksheet.Cells[2, 2, 3, 2].Merge = true;//合并单元格-行
                worksheet.Cells[2, 3, 3, 3].Merge = true;//合并单元格-行
                worksheet.Cells[2, 4, 3, 4].Merge = true;//合并单元格-行
                worksheet.Cells[2, 5, 3, 5].Merge = true;//合并单元格-行
                worksheet.Cells[2, 6, 3, 6].Merge = true;//合并单元格-行
                var col_Header = 7;

                foreach (var CRF in cRFormList)
                {
                    if (CRF.FormFieldList.Any() && CRF.FormFieldList.Count() > 0)
                    {
                        var SecondRowIndex = col_Header;
                        worksheet.Cells[2, SecondRowIndex].Value = $"{CRF.CRForm.FormName}";
                        worksheet.Cells[2, SecondRowIndex, 2, SecondRowIndex + CRF.FormFieldList.Count() - 1].Merge = true;//合并单元格-列

                        foreach (var item in CRF.FormFieldList)
                        {
                            var key = item;
                            var dictKeyStr = dictKeyList.Where(o => o.FormId == CRF.CRForm.FormId && o.Name.ToUpper() == key.ToUpper()).FirstOrDefault();
                            if (dictKeyStr != null)
                                key = dictKeyStr.Value;
                            worksheet.Cells[3, col_Header].Value = key;
                            col_Header++;
                        }
                    }
                    else
                    {
                        worksheet.Cells[2, col_Header].Value = $"{CRF.CRForm.FormName}";
                        worksheet.Cells[2, col_Header, 3, col_Header].Merge = true;//合并单元格-行
                        col_Header++;
                    }
                }
                worksheet.Cells[1, 1, 1, col_Header - 1].Merge = true;//合并单元格
                worksheet.Cells[1, 1, 3, col_Header - 1].Style.Font.Bold = true;//字体为粗体
                worksheet.Cells[1, 1, 3, col_Header - 1].Style.Font.Name = "微软雅黑";//字体
                worksheet.Cells[1, 1, 3, col_Header - 1].Style.Font.Size = 12;//字体大小
                #endregion

                #region 表身
                var row_Body = 4;
                var index = 0;
                var col_body = 0;
                for (int i = 0; i < List.Count(); i++)
                {                   
                    if (i == 0 || List[i].PatientId != List[i - 1].PatientId)    
                    {
                        index++;
                        worksheet.Cells[row_Body, 1].Value = index;                 // "序号";
                        worksheet.Cells[row_Body, 2].Value = List[i]?.PatientName;  // "患者姓名";
                        worksheet.Cells[row_Body, 3].Value = List[i]?.PatientId;   // "患者编号";
                        worksheet.Cells[row_Body, 4].Value = List[i]?.Sex;          //"性别";
                        worksheet.Cells[row_Body, 5].Value = List[i]?.PatientSource == "O" ? "门诊" : "住院";  //"年龄";
                        worksheet.Cells[row_Body, 6].Value = List[i]?.DeptName; //"医学中心";
                        col_body = 7;

                        foreach (var CRF in cRFormList)
                        {                                                                               
                            if (CRF.FormFieldList.Any() && CRF.FormFieldList.Count() > 0 && CRF.HospitalCRFormId == List[i].HospitalCRFormId)
                            {
                                JObject jObject = null;
                                if (!string.IsNullOrWhiteSpace(List[i]?.CRFJsonValue))
                                {
                                    jObject = JObject.Parse(List[i]?.CRFJsonValue);
                                }
                                foreach (var fieldItem in CRF.FormFieldList)
                                {                                   
                                    if (jObject != null)
                                    {
                                        var isAdd = false;
                                        if (List[i].StopUsing == true)
                                        {
                                            foreach (JProperty property in jObject.Properties())
                                            {
                                                if (property.Name.ToUpper() == fieldItem.ToUpper())
                                                {
                                                    isAdd = true;
                                                    if (!property.Name.ToUpper().StartsWith("DETAILARRAY"))
                                                    {
                                                        worksheet.Cells[row_Body, col_body].Value
                                                                    = (property.Value == null ? "" : property.Value.ToString().Replace("&&&###", ","));
                                                    }
                                                    else
                                                    {
                                                        var thisValue = "";
                                                        var jsonArray = property.Value;
                                                        var j = 0;
                                                        // 遍历JArray中的每个JObject
                                                        foreach (JObject jsonObject in jsonArray)
                                                        {
                                                            j++;
                                                            thisValue += $"记录{j}:";
                                                            // 动态地遍历JObject中的每个键值对
                                                            foreach (JProperty arrayProperty in jsonObject.Properties())
                                                            {
                                                                string key = arrayProperty.Name;
                                                                string value = arrayProperty.Value.ToString();
                                                                var dictKeyStr = dictKeyList.Where(o => o.FormId == CRF.CRForm.FormId && o.Name.ToUpper() == key.ToUpper()).FirstOrDefault();
                                                                if (dictKeyStr != null)
                                                                    key = dictKeyStr.Value;
                                                                thisValue += $"{key}:\"{value}\"；";
                                                            }
                                                            thisValue += "。";
                                                        }
                                                        worksheet.Cells[row_Body, col_body].Value = thisValue;
                                                    }
                                                    break;
                                                }
                                            }
                                        }
                                        else
                                        {
                                            worksheet.Cells[row_Body, col_body].Value = "——";
                                        }
                                        if (!isAdd)
                                        {
                                            worksheet.Cells[row_Body, col_body].Value = "";
                                        }
                                    }
                                    else
                                    {
                                        worksheet.Cells[row_Body, col_body].Value = "";
                                    }
                                    
                                    col_body++;
                                }
                                if (i != List.Count - 1 && List[i].PatientId != List[i + 1].PatientId)
                                {
                                    row_Body++;
                                }
                                if (i == List.Count - 1)
                                {
                                    row_Body++;
                                }
                            }
                            else
                            {
                                continue;
                            }
                        }
                    }
                    else
                    {
                        foreach (var CRF in cRFormList)
                        {
                            if (CRF.FormFieldList.Any() && CRF.FormFieldList.Count() > 0 && CRF.HospitalCRFormId == List[i].HospitalCRFormId)
                            {
                                JObject jObject = null;
                                if (!string.IsNullOrWhiteSpace(List[i]?.CRFJsonValue))
                                {
                                    jObject = JObject.Parse(List[i]?.CRFJsonValue);
                                }
                                foreach (var fieldItem in CRF.FormFieldList)
                                {
                                    if (jObject != null)
                                    {
                                        var isAdd = false;
                                        if (List[i].StopUsing == true)
                                        {
                                            foreach (JProperty property in jObject.Properties())
                                            {
                                                if (property.Name.ToUpper() == fieldItem.ToUpper())
                                                {
                                                    isAdd = true;
                                                    if (!property.Name.ToUpper().StartsWith("DETAILARRAY"))
                                                    {
                                                        worksheet.Cells[row_Body, col_body].Value
                                                                    = (property.Value == null ? "" : property.Value.ToString().Replace("&&&###", ","));
                                                    }
                                                    else
                                                    {
                                                        var thisValue = "";
                                                        var jsonArray = property.Value;
                                                        var j = 0;
                                                        // 遍历JArray中的每个JObject
                                                        foreach (JObject jsonObject in jsonArray)
                                                        {
                                                            j++;
                                                            thisValue += $"记录{j}:";
                                                            // 动态地遍历JObject中的每个键值对
                                                            foreach (JProperty arrayProperty in jsonObject.Properties())
                                                            {
                                                                string key = arrayProperty.Name;
                                                                string value = arrayProperty.Value.ToString();
                                                                var dictKeyStr = dictKeyList.Where(o => o.FormId == CRF.CRForm.FormId && o.Name.ToUpper() == key.ToUpper()).FirstOrDefault();
                                                                if (dictKeyStr != null)
                                                                    key = dictKeyStr.Value;
                                                                thisValue += $"{key}:\"{value}\"；";
                                                            }
                                                            thisValue += "。";
                                                        }
                                                        worksheet.Cells[row_Body, col_body].Value = thisValue;
                                                    }
                                                    break;
                                                }
                                            }
                                        }
                                        else
                                        {
                                            worksheet.Cells[row_Body, col_body].Value = "——";
                                        }
                                        if (!isAdd)
                                        {
                                            worksheet.Cells[row_Body, col_body].Value = "";
                                        }
                                    }
                                    else
                                    {
                                        worksheet.Cells[row_Body, col_body].Value = "";
                                    }

                                    col_body++;
                                }
                            }
                            else
                            {
                                continue;
                            }
                        }
                        if (i == List.Count - 1)
                        {
                            row_Body++;
                        }
                        else if (i != List.Count - 1 && List[i].PatientId != List[i + 1].PatientId)
                        {
                            row_Body++;
                        }
                    }
                }
                worksheet.Cells[row_Body, 1].Value = "注：“——”表示无需填写内容。";
                worksheet.Cells[row_Body, 1].Style.Font.Color.SetColor(Color.Red);
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
                worksheet.Cells[1, 1, row_Body - 1, col_Header - 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;//水平居左
                worksheet.Cells[1, 1, row_Body - 1, col_Header - 1].Style.VerticalAlignment = ExcelVerticalAlignment.Center;//垂直居中
                worksheet.Cells[2, 1, row_Body - 1, col_Header - 1].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                worksheet.Cells[2, 1, row_Body - 1, col_Header - 1].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                worksheet.Cells[2, 1, row_Body - 1, col_Header - 1].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                worksheet.Cells[2, 1, row_Body - 1, col_Header - 1].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                worksheet.View.FreezePanes(4, 1);
                #endregion
                package.Save();
            }
            return new DownLoadByStreamResult(stream, $"ResearchSubjectsData{System.DateTime.Now.ToString("yyyy-MM-dd")}.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        }

        public class KeyValueDTO
        {
            public int Id { get; set; }
            public string FormId { get; set; }
            public string Name { get; set; }
            public string Value { get; set; }
            public int OrderBy { get; set; }
        }

        public class HospitalDatas
        {
            public int Id { get; set; }
            public int? HospitalCRFormId { get; set; }
            public string AIExtractJsonValue { get; set; }
            public int OrderBy { get; set; }     
            public string FormId { get; set; }
            public bool StopUsing { get; set; }
            public string PatientId { get; set; }
            public string DeptName { get; set; }
            public string PatientSource { get; set; }
            public string PatientName { get; set; }
            public string FormName { get; set; }
            public string Sex { get; set; }
            public string BLH { get; set; }
            public string CRFJsonValue { get; set; }
        }

        
    }
}
