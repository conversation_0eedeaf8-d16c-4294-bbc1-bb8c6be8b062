#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\FollowUp\Views\FollowUpVisit\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "152f3760269875a5ffa6b8a43a0ae822e09e974349fcd3452d6b413e3b9630c7"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_FollowUp_Views_FollowUpVisit_Index), @"mvc.1.0.view", @"/Areas/FollowUp/Views/FollowUpVisit/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"152f3760269875a5ffa6b8a43a0ae822e09e974349fcd3452d6b413e3b9630c7", @"/Areas/FollowUp/Views/FollowUpVisit/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_FollowUp_Views_FollowUpVisit_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("layui-layout-body"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\FollowUp\Views\FollowUpVisit\Index.cshtml"
  
    ViewBag.Title = "随访管理";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n\r\n<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "152f3760269875a5ffa6b8a43a0ae822e09e974349fcd3452d6b413e3b9630c75639", async() => {
                WriteLiteral("\r\n    <meta charset=\"utf-8\">\r\n    <title> ");
                Write(
#nullable restore
#line 11 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\FollowUp\Views\FollowUpVisit\Index.cshtml"
             ViewBag.SiteTitle

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</title>
    <meta name=""renderer"" content=""webkit"">
    <meta http-equiv=""X-UA-Compatible"" content=""IE=edge,chrome=1"">
    <meta name=""viewport""
          content=""width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0"">
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "152f3760269875a5ffa6b8a43a0ae822e09e974349fcd3452d6b413e3b9630c76545", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "152f3760269875a5ffa6b8a43a0ae822e09e974349fcd3452d6b413e3b9630c77747", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "152f3760269875a5ffa6b8a43a0ae822e09e974349fcd3452d6b413e3b9630c78949", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "152f3760269875a5ffa6b8a43a0ae822e09e974349fcd3452d6b413e3b9630c710776", async() => {
                WriteLiteral(@"

    <div class=""layui-col-md12"">
        <div class=""layui-card"">
            <div class=""layui-card-body layui-form linewrap"">
                <div class=""layui-inline"">
                    <div class=""layui-input-inline"" style=""width:300px;"">
                        <input type=""text"" class=""layui-input"" name=""keyWord"" placeholder=""请输入病历号/患者姓名"" id=""keyWord"" />
                    </div>
                </div>
                <div class=""layui-inline"">
                    <button class=""layui-btn"" id=""Search2"">查询</button>
                </div>
            </div>
        </div>
        <div class=""layui-card-body"">
            <table id=""tablelist"" lay-filter=""tablelist""></table>
            <script type=""text/html"" id=""tableBar1"">
                {{#  if(d.isLY){ }}
                <a class=""layui-btn layui-btn-normal layui-btn-xs"" lay-event=""record"" style=""text-decoration: none ""><i class=""layui-icon""></i>查看录音</a>
                {{#  } }}
            </script>
        </div>
    <");
                WriteLiteral("/div>\r\n\r\n\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "152f3760269875a5ffa6b8a43a0ae822e09e974349fcd3452d6b413e3b9630c712182", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "152f3760269875a5ffa6b8a43a0ae822e09e974349fcd3452d6b413e3b9630c713306", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
        layui.use(['element', 'layer', 'table', 'laydate', 'form'], function () {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , laydate = layui.laydate
                , $ = layui.$
                , form = layui.form;



            var ListData = [{
                ""Id"": 1,
                ""isSF"": true,
                ""isLY"": true,
                        ""BLH"": ""**********"",
                        ""SFZH"": null,
                        ""PATIENTID"": ""**********"",
                        ""VISITID"": null,
                        ""HZXM"": ""林兰"",
                        ""BRZT"": null,
                        ""KSDM"": ""1218"",
                        ""KSMC"": ""23区神经内科"",
                        ""BQDM"": ""0001"",
                        ""BQMC"": ""1区神内科"",
                        ""YLZDM"": ""0102"",
                        ""YLZMC"": ""于*国组二"",
                        ""CYZDDM"": null,
              ");
                WriteLiteral(@"          ""CYZDMC"": null,
                        ""SSCODE"": null,
                        ""SSMC"": null,
                        ""YSDM"": ""d0436"",
                        ""YSMC"": ""于*国"",
                        ""IDM"": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                        ""LJSL"": null,
                        ""ZXRQ"": null,
                        ""XH"": null,
                        ""DXMDM"": null,
                        ""DXMMC"": null,
                        ""RYZDDM"": null,
                        ""RYZDMC"": null,
                        ""YYDM"": ""01"",
                        ""SEX"": ""女"",
                        ""NL"": 28,
                        ""YF"": null,
                        ""TS"": null,
                        ""KDYSMC"": null,
                        ""ZXKSDM"": null,
                        ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
       ");
                WriteLiteral(@"                 ""YBSHBZ"": null,
                        ""DBZBZ"": 0,
                        ""JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
                        ""YBDM"": null,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
                        ""CBDDM"": null,
                        ""CBDMC"": null,
                        ""ISYBXM"": null,
                        ""RZBZ"": 0,
                        ""RYRQ"": null,
                        ""RQRQ"": null,
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": """",
                        ""Status"": 1,
                        ""XBS"": ""患者在2023年9月20日出现心悸等症状，病程约10小时。初始症状包括心悸，与该症状相关的其他症状包括无黑朦、无晕厥、无胸闷和无胸痛。患者曾于房山区良乡医院就诊，初步诊断为心房颤动，接受胺碘酮治疗，治疗效果良好，已转为窦律。此外，患者进行了冠脉造影，发现冠状动脉粥样硬化，建议择期进行射频消融治疗。患者在出院后继续服用利伐沙班和胺碘酮，最近的心电图检查显示窦性心律。目前，患者计划接受房颤射频消融手术治疗。"",
          ");
                WriteLiteral(@"              ""XBSJson"": ""{\""symptom_onset_date\"":\""2023-09-20\"",\""initial_symptoms\"":[\""心悸\""],\""associated_symptoms\"":[\""无黑朦\"",\""无晕厥\"",\""无胸闷\"",\""无胸痛\""],\""symptom_duration\"":\""约10小时\"",\""first_hospital_visit_date\"":null,\""first_hospital_visit_location\"":\""房山区良乡医院\"",\""initial_diagnosis\"":\""心房颤动\"",\""initial_treatment\"":\""胺碘酮治疗\"",\""treatment_result\"":\""转为窦律\"",\""additional_tests_type\"":\""冠脉造影\"",\""additional_tests_findings\"":\""冠状动脉粥样硬化\"",\""suggested_treatment\"":\""择期行射频消融\"",\""post_discharge_medication\"":[\""利伐沙班\"",\""胺碘酮\""],\""recent_test_type\"":\""心电图\"",\""recent_test_findings\"":\""窦性心律\"",\""planned_treatment\"":\""房颤射频消融手术\""}"",
                        ""TGJC"": ""该患者的体格检查结果如下：\r\n\r\n体温：36摄氏度，正常范围。\r\n脉搏：79次/分，处于正常范围。\r\n呼吸频率：16次/分，正常范围。\r\n未测量血压，无法评估。\r\n意识状态：清晰，正常。\r\n面色：无病容，正常。\r\n体位：自动体位，正常。\r\n眼睑：无浮肿，正常。\r\n结膜：无水肿，正常。\r\n巩膜：无黄染，正常。\r\n瞳孔：等大、等圆，正常。\r\n唇部：无紫绀，正常。\r\n甲状腺：无肿大，正常。\r\n颈静脉：无怒张，正常。\r\n颈部血管杂音：无，正常。\r\n肺部听诊：无异常呼吸音。\r\n啰音：无，正常。\r\n肝脾肋下触诊：无，正常。\r\n心音：正常，心音强弱一致，A2>P2。\r\n心率：79次/分，正常范围。\r\n心音性质：心脏听诊无杂音。\r\n血压：未测量，无法评");
                WriteLiteral(@"估。\r\n腹部检查：无异常，正常。\r\n肝脏触诊：无，正常。\r\n下肢水肿：无，正常。\r\n肝颈静脉反流征：无，正常。\r\n\r\n根据以上体格检查结果，患者整体情况良好，无明显异常。"",
                        ""TGJCJson"": ""{\""Direct_rectal_cancer_first_diagnosis_time\"":\""2023-06-06\"",\""History_Intestinal_perforation\"":\""是\"",\""History_intestinal_obstruction\"":\""是\"",\""rectal_finger_examination_tumor_anus_distance(cm)\"":\""1.4\"",\""Lithotomy_Position_OClock_Direction\"":\""9\"",\""Occupying_Intestinal_Lumen\"":\""≥1/4圈，<1/2圈\"",\""Colonoscopy_Tumor_Lower_Edge_to_Anus_Distance(cm)\"":\""1.4\"",\""Colonoscopy_Passability\"":\""是\"",\""MRI_Tumor_Lower_Edge_to_Anus_Distance(cm)\"":\""1.35\"",\""Tumor_Longitudinal_Diameter(cm)\"":\""1.5\"",\""Tumor_Transverse_Diameter(cm)\"":\""0.9\"",\""Pathological_Type\"":\""印戒细胞癌\"",\""Pathological_Grade\"":\""中分化\"",\""Clinical_Staging_Evaluation_Method\"":\""磁共振\"",\""Primary_Tumor\"":\""T1\"",\""the_entire_rectal_mesentery_Involvement\"":\""MRF\"",\""regional_lymph_nodes\"":\""N0\"",\""distant_metastasis\"":\""M0\"",\""Tumor_Staging\"":\""IIb\"",\""neoadjuvant_therapy\"":\""是\"",\""doctor_signature\"":\""张医生\""}""
             ");
                WriteLiteral(@"       }, {
                ""Id"": 2,
                ""isSF"": true,
                ""isLY"": true,
                        ""BLH"": ""80404455"",
                        ""SFZH"": null,
                        ""PATIENTID"": ""1522266"",
                        ""VISITID"": null,
                        ""HZXM"": ""刘*明"",
                        ""BRZT"": ""1"",
                        ""KSDM"": ""9327"",
                        ""KSMC"": ""感染性疾病科"",
                        ""BQDM"": ""0056"",
                        ""BQMC"": ""56区感染性疾病科"",
                        ""YLZDM"": ""5602"",
                        ""YLZMC"": ""许*銮组2"",
                        ""CYZDDM"": """",
                        ""CYZDMC"": """",
                        ""SSCODE"": """",
                        ""SSMC"": """",
                        ""YSDM"": ""d0634"",
                        ""YSMC"": ""许*銮"",
                        ""IDM"": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                     ");
                WriteLiteral(@"   ""LJSL"": null,
                        ""ZXRQ"": null,
                        ""XH"": null,
                        ""DXMDM"": null,
                        ""DXMMC"": null,
                        ""RYZDDM"": ""M54.503,G06.100x002,M99.500,M51.202,A41.900,J18.900,J96.900x002,A41.901,A41.900x003,R94.500,I10.x00x002,E77.801"",
                        ""RYZDMC"": ""腰痛待查,椎管内脓肿？,腰椎管椎间盘狭窄,腰椎间盘突出,血流感染,双侧肺炎,I型呼吸衰竭,脓毒症,脓毒性休克,肝功能异常,高血压病,低蛋白血症"",
                        ""YYDM"": ""01"",
                        ""SEX"": ""男"",
                        ""NL"": 56,
                        ""YF"": null,
                        ""TS"": null,
                        ""KDYSMC"": null,
                        ""ZXKSDM"": null,
                        ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
                        ""YBSHBZ"": null,
                        ""DBZBZ"": 0,
                        ""JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
  ");
                WriteLiteral(@"                      ""YBDM"": null,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
                        ""CBDDM"": null,
                        ""CBDMC"": null,
                        ""ISYBXM"": null,
                        ""RZBZ"": 0,
                        ""RYRQ"": ""2023-12-19T17:00:00"",
                        ""RQRQ"": ""2023-12-19T17:00:00"",
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": ""肾移植,脓毒症"",
                        ""Status"": 1,
                        ""XBS"": ""患者在2023年9月20日出现心悸等症状，病程约10小时。初始症状包括心悸，与该症状相关的其他症状包括无黑朦、无晕厥、无胸闷和无胸痛。患者曾于房山区良乡医院就诊，初步诊断为心房颤动，接受胺碘酮治疗，治疗效果良好，已转为窦律。此外，患者进行了冠脉造影，发现冠状动脉粥样硬化，建议择期进行射频消融治疗。患者在出院后继续服用利伐沙班和胺碘酮，最近的心电图检查显示窦性心律。目前，患者计划接受房颤射频消融手术治疗。"",
                        ""XBSJson"": ""{\""symptom_onset_date\"":\""2023-09-20\"",\""initial_symptoms\"":[\""心悸\""],\""associated_symptoms\"":[\""无黑朦\"",\""无晕厥\"",\""无胸闷\"",\""无胸痛");
                WriteLiteral(@"\""],\""symptom_duration\"":\""约10小时\"",\""first_hospital_visit_date\"":null,\""first_hospital_visit_location\"":\""房山区良乡医院\"",\""initial_diagnosis\"":\""心房颤动\"",\""initial_treatment\"":\""胺碘酮治疗\"",\""treatment_result\"":\""转为窦律\"",\""additional_tests_type\"":\""冠脉造影\"",\""additional_tests_findings\"":\""冠状动脉粥样硬化\"",\""suggested_treatment\"":\""择期行射频消融\"",\""post_discharge_medication\"":[\""利伐沙班\"",\""胺碘酮\""],\""recent_test_type\"":\""心电图\"",\""recent_test_findings\"":\""窦性心律\"",\""planned_treatment\"":\""房颤射频消融手术\""}"",
                        ""TGJC"": ""该患者的体格检查结果如下：\r\n\r\n体温：36摄氏度，正常范围。\r\n脉搏：79次/分，处于正常范围。\r\n呼吸频率：16次/分，正常范围。\r\n未测量血压，无法评估。\r\n意识状态：清晰，正常。\r\n面色：无病容，正常。\r\n体位：自动体位，正常。\r\n眼睑：无浮肿，正常。\r\n结膜：无水肿，正常。\r\n巩膜：无黄染，正常。\r\n瞳孔：等大、等圆，正常。\r\n唇部：无紫绀，正常。\r\n甲状腺：无肿大，正常。\r\n颈静脉：无怒张，正常。\r\n颈部血管杂音：无，正常。\r\n肺部听诊：无异常呼吸音。\r\n啰音：无，正常。\r\n肝脾肋下触诊：无，正常。\r\n心音：正常，心音强弱一致，A2>P2。\r\n心率：79次/分，正常范围。\r\n心音性质：心脏听诊无杂音。\r\n血压：未测量，无法评估。\r\n腹部检查：无异常，正常。\r\n肝脏触诊：无，正常。\r\n下肢水肿：无，正常。\r\n肝颈静脉反流征：无，正常。\r\n\r\n根据以上体格检查结果，患者整体情况良好，无明显异常。"",
                        ""TGJCJson"": ""{\""Direct_r");
                WriteLiteral(@"ectal_cancer_first_diagnosis_time\"":\""2023-06-06\"",\""History_Intestinal_perforation\"":\""是\"",\""History_intestinal_obstruction\"":\""是\"",\""rectal_finger_examination_tumor_anus_distance(cm)\"":\""1.4\"",\""Lithotomy_Position_OClock_Direction\"":\""9\"",\""Occupying_Intestinal_Lumen\"":\""≥1/4圈，<1/2圈\"",\""Colonoscopy_Tumor_Lower_Edge_to_Anus_Distance(cm)\"":\""1.4\"",\""Colonoscopy_Passability\"":\""是\"",\""MRI_Tumor_Lower_Edge_to_Anus_Distance(cm)\"":\""1.35\"",\""Tumor_Longitudinal_Diameter(cm)\"":\""1.5\"",\""Tumor_Transverse_Diameter(cm)\"":\""0.9\"",\""Pathological_Type\"":\""印戒细胞癌\"",\""Pathological_Grade\"":\""中分化\"",\""Clinical_Staging_Evaluation_Method\"":\""磁共振\"",\""Primary_Tumor\"":\""T1\"",\""the_entire_rectal_mesentery_Involvement\"":\""MRF\"",\""regional_lymph_nodes\"":\""N0\"",\""distant_metastasis\"":\""M0\"",\""Tumor_Staging\"":\""IIb\"",\""neoadjuvant_therapy\"":\""是\"",\""doctor_signature\"":\""张医生\""}""
                    }, {
                ""Id"": 3,
                ""isSF"": false,
                ""isLY"": false,
                        ""BLH"": ""80407031"",
  ");
                WriteLiteral(@"                      ""SFZH"": null,
                        ""PATIENTID"": ""1525240"",
                        ""VISITID"": null,
                        ""HZXM"": ""郑*金"",
                        ""BRZT"": ""1"",
                        ""KSDM"": ""1105"",
                        ""KSMC"": ""内分泌科"",
                        ""BQDM"": ""0052"",
                        ""BQMC"": ""52区内分泌科"",
                        ""YLZDM"": ""5208"",
                        ""YLZMC"": ""侯*明组(李连涛)"",
                        ""CYZDDM"": """",
                        ""CYZDMC"": """",
                        ""SSCODE"": """",
                        ""SSMC"": """",
                        ""YSDM"": ""d0290"",
                        ""YSMC"": ""侯*明"",
                        ""IDM"": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                        ""LJSL"": null,
                        ""ZXRQ"": null,
                        ""XH"": null,
                        ""DXMDM"": null,
             ");
                WriteLiteral(@"           ""DXMMC"": null,
                        ""RYZDDM"": """",
                        ""RYZDMC"": """",
                        ""YYDM"": ""01"",
                        ""SEX"": ""女"",
                        ""NL"": 70,
                        ""YF"": null,
                        ""TS"": null,
                        ""KDYSMC"": null,
                        ""ZXKSDM"": null,
                        ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
                        ""YBSHBZ"": null,
                        ""DBZBZ"": 0,
                        ""JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
                        ""YBDM"": null,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
                        ""CBDDM"": null,
                        ""CBDMC"": null,
                        ""ISYBXM"": null,
           ");
                WriteLiteral(@"             ""RZBZ"": 0,
                        ""RYRQ"": ""2023-12-27T15:00:50"",
                        ""RQRQ"": ""2023-12-27T15:00:50"",
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": ""肾移植,脓毒症,泌尿系结石"",
                        ""Status"": 1,
                        ""XBS"": ""患者在2023年9月20日出现心悸等症状，病程约10小时。初始症状包括心悸，与该症状相关的其他症状包括无黑朦、无晕厥、无胸闷和无胸痛。患者曾于房山区良乡医院就诊，初步诊断为心房颤动，接受胺碘酮治疗，治疗效果良好，已转为窦律。此外，患者进行了冠脉造影，发现冠状动脉粥样硬化，建议择期进行射频消融治疗。患者在出院后继续服用利伐沙班和胺碘酮，最近的心电图检查显示窦性心律。目前，患者计划接受房颤射频消融手术治疗。"",
                        ""XBSJson"": ""{\""symptom_onset_date\"":\""2023-09-20\"",\""initial_symptoms\"":[\""心悸\""],\""associated_symptoms\"":[\""无黑朦\"",\""无晕厥\"",\""无胸闷\"",\""无胸痛\""],\""symptom_duration\"":\""约10小时\"",\""first_hospital_visit_date\"":null,\""first_hospital_visit_location\"":\""房山区良乡医院\"",\""initial_diagnosis\"":\""心房颤动\"",\""initial_treatment\"":\""胺碘酮治疗\"",\""treatment_result\"":\""转为窦律\"",\""additional_tests_type\"":\""冠脉造影\"",\""additional_tests_findings\"":\""冠状动脉粥样硬化\"",\""suggested_treatment\"":\""择期行射频消融\"",");
                WriteLiteral(@"\""post_discharge_medication\"":[\""利伐沙班\"",\""胺碘酮\""],\""recent_test_type\"":\""心电图\"",\""recent_test_findings\"":\""窦性心律\"",\""planned_treatment\"":\""房颤射频消融手术\""}"",
                        ""TGJC"": ""该患者的体格检查结果如下：\r\n\r\n体温：36摄氏度，正常范围。\r\n脉搏：79次/分，处于正常范围。\r\n呼吸频率：16次/分，正常范围。\r\n未测量血压，无法评估。\r\n意识状态：清晰，正常。\r\n面色：无病容，正常。\r\n体位：自动体位，正常。\r\n眼睑：无浮肿，正常。\r\n结膜：无水肿，正常。\r\n巩膜：无黄染，正常。\r\n瞳孔：等大、等圆，正常。\r\n唇部：无紫绀，正常。\r\n甲状腺：无肿大，正常。\r\n颈静脉：无怒张，正常。\r\n颈部血管杂音：无，正常。\r\n肺部听诊：无异常呼吸音。\r\n啰音：无，正常。\r\n肝脾肋下触诊：无，正常。\r\n心音：正常，心音强弱一致，A2>P2。\r\n心率：79次/分，正常范围。\r\n心音性质：心脏听诊无杂音。\r\n血压：未测量，无法评估。\r\n腹部检查：无异常，正常。\r\n肝脏触诊：无，正常。\r\n下肢水肿：无，正常。\r\n肝颈静脉反流征：无，正常。\r\n\r\n根据以上体格检查结果，患者整体情况良好，无明显异常。"",
                        ""TGJCJson"": ""{\""Direct_rectal_cancer_first_diagnosis_time\"":\""2023-06-06\"",\""History_Intestinal_perforation\"":\""是\"",\""History_intestinal_obstruction\"":\""是\"",\""rectal_finger_examination_tumor_anus_distance(cm)\"":\""1.4\"",\""Lithotomy_Position_OClock_Direction\"":\""9\"",\""Occupying_Intestinal_Lumen\"":\""≥1/4圈，<1/2圈\"",\""Colonoscopy_Tumor_Lower_Edge_to_A");
                WriteLiteral(@"nus_Distance(cm)\"":\""1.4\"",\""Colonoscopy_Passability\"":\""是\"",\""MRI_Tumor_Lower_Edge_to_Anus_Distance(cm)\"":\""1.35\"",\""Tumor_Longitudinal_Diameter(cm)\"":\""1.5\"",\""Tumor_Transverse_Diameter(cm)\"":\""0.9\"",\""Pathological_Type\"":\""印戒细胞癌\"",\""Pathological_Grade\"":\""中分化\"",\""Clinical_Staging_Evaluation_Method\"":\""磁共振\"",\""Primary_Tumor\"":\""T1\"",\""the_entire_rectal_mesentery_Involvement\"":\""MRF\"",\""regional_lymph_nodes\"":\""N0\"",\""distant_metastasis\"":\""M0\"",\""Tumor_Staging\"":\""IIb\"",\""neoadjuvant_therapy\"":\""是\"",\""doctor_signature\"":\""张医生\""}""
                    }, {
                ""Id"": 4,
                ""isSF"": true,
                ""isLY"": true,
                        ""BLH"": ""80397373"",
                        ""SFZH"": null,
                        ""PATIENTID"": ""1514014"",
                        ""VISITID"": null,
                        ""HZXM"": ""陈*绩"",
                        ""BRZT"": ""1"",
                        ""KSDM"": ""1806"",
                        ""KSMC"": ""急诊外科"",
                        ""BQDM"": ""0032");
                WriteLiteral(@""",
                        ""BQMC"": ""32区(急诊外科)"",
                        ""YLZDM"": ""3207"",
                        ""YLZMC"": ""张*鸣组七"",
                        ""CYZDDM"": """",
                        ""CYZDMC"": """",
                        ""SSCODE"": ""86.2200x011 ,86.2800x012 ,86.0401 ,86.2200x011 ,86.0401 ,86.2200x011 ,86.0401 ,86.9100x002 ,86.6906 ,86.0401 "",
                        ""SSMC"": ""左下肢皮肤和皮下坏死组织切除清创术,左下肢清创术,创面封闭式负压引流术（VSD),左下肢皮下坏死组织切除清创术,创面封闭式负压引流术（VSD),左下肢坏死组织切除清创术,创面封闭式负压引流术（VSD),左大腿皮片取皮术,左小腿植皮术,创面封闭式负压引流术（VSD)"",
                        ""YSDM"": ""d0236"",
                        ""YSMC"": ""张*鸣"",
                        ""IDM"": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                        ""LJSL"": null,
                        ""ZXRQ"": null,
                        ""XH"": null,
                        ""DXMDM"": null,
                        ""DXMMC"": null,
                        ""RYZDDM"": """",
                    ");
                WriteLiteral(@"    ""RYZDMC"": """",
                        ""YYDM"": ""01"",
                        ""SEX"": ""男"",
                        ""NL"": 74,
                        ""YF"": null,
                        ""TS"": null,
                        ""KDYSMC"": null,
                        ""ZXKSDM"": null,
                        ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
                        ""YBSHBZ"": null,
                        ""DBZBZ"": 0,
                        ""JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
                        ""YBDM"": null,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
                        ""CBDDM"": null,
                        ""CBDMC"": null,
                        ""ISYBXM"": null,
                        ""RZBZ"": 0,
                        ""RYRQ"": ""2023-11-29T15:06:15"",
     ");
                WriteLiteral(@"                   ""RQRQ"": ""2023-11-29T15:06:15"",
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": """",
                        ""Status"": -1,
                        ""XBS"": null,
                        ""XBSJson"": null,
                        ""TGJC"": null,
                        ""TGJCJson"": null
                    }, {
                ""Id"": 5,
                ""isSF"": false,
                ""isLY"": false,
                        ""BLH"": ""80401264"",
                        ""SFZH"": null,
                        ""PATIENTID"": ""1518543"",
                        ""VISITID"": null,
                        ""HZXM"": ""庄*江"",
                        ""BRZT"": ""1"",
                        ""KSDM"": ""1206"",
                        ""KSMC"": ""整形烧伤科"",
                        ""BQDM"": ""0005"",
                        ""BQMC"": ""5区整形烧伤科"",
                        ""YLZDM"": ""0503"",
                        ""YLZMC"": ""郑*武组"",
                        ""CYZDDM"": """",
");
                WriteLiteral(@"
                        ""CYZDMC"": """",
                        ""SSCODE"": ""48.6302 ,46.8101 ,54.2100 ,54.3x00x011 ,86.7100x009 "",
                        ""SSMC"": ""腹腔镜下直肠根治术(Dixon手术)+预防性回肠造口术,小肠扭转复位术,腹腔镜探查,腹壁伤口清创术,皮瓣预制术"",
                        ""YSDM"": ""d0727"",
                        ""YSMC"": ""郑*武"",
                        ""IDM"": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                        ""LJSL"": null,
                        ""ZXRQ"": null,
                        ""XH"": null,
                        ""DXMDM"": null,
                        ""DXMMC"": null,
                        ""RYZDDM"": """",
                        ""RYZDMC"": """",
                        ""YYDM"": ""01"",
                        ""SEX"": ""男"",
                        ""NL"": 35,
                        ""YF"": null,
                        ""TS"": null,
                        ""KDYSMC"": null,
                        ""ZXKSDM"": null,
                     ");
                WriteLiteral(@"   ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
                        ""YBSHBZ"": null,
                        ""DBZBZ"": 0,
                        ""JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
                        ""YBDM"": null,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
                        ""CBDDM"": null,
                        ""CBDMC"": null,
                        ""ISYBXM"": null,
                        ""RZBZ"": 0,
                        ""RYRQ"": ""2023-12-11T10:59:36"",
                        ""RQRQ"": ""2023-12-11T10:59:36"",
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": ""甲状腺"",
                        ""Status"": 1,
                        ""XBS"": ""患者在2023年9月20日出现心悸等症状，病程约10小时。初始症状包括心悸，与该症状相关的其他症状包括无黑朦、无晕厥、无胸闷和无胸痛。患");
                WriteLiteral(@"者曾于房山区良乡医院就诊，初步诊断为心房颤动，接受胺碘酮治疗，治疗效果良好，已转为窦律。此外，患者进行了冠脉造影，发现冠状动脉粥样硬化，建议择期进行射频消融治疗。患者在出院后继续服用利伐沙班和胺碘酮，最近的心电图检查显示窦性心律。目前，患者计划接受房颤射频消融手术治疗。"",
                        ""XBSJson"": ""{\""symptom_onset_date\"":\""2023-09-20\"",\""initial_symptoms\"":[\""心悸\""],\""associated_symptoms\"":[\""无黑朦\"",\""无晕厥\"",\""无胸闷\"",\""无胸痛\""],\""symptom_duration\"":\""约10小时\"",\""first_hospital_visit_date\"":null,\""first_hospital_visit_location\"":\""房山区良乡医院\"",\""initial_diagnosis\"":\""心房颤动\"",\""initial_treatment\"":\""胺碘酮治疗\"",\""treatment_result\"":\""转为窦律\"",\""additional_tests_type\"":\""冠脉造影\"",\""additional_tests_findings\"":\""冠状动脉粥样硬化\"",\""suggested_treatment\"":\""择期行射频消融\"",\""post_discharge_medication\"":[\""利伐沙班\"",\""胺碘酮\""],\""recent_test_type\"":\""心电图\"",\""recent_test_findings\"":\""窦性心律\"",\""planned_treatment\"":\""房颤射频消融手术\""}"",
                        ""TGJC"": ""该患者的体格检查结果如下：\r\n\r\n体温：36摄氏度，正常范围。\r\n脉搏：79次/分，处于正常范围。\r\n呼吸频率：16次/分，正常范围。\r\n未测量血压，无法评估。\r\n意识状态：清晰，正常。\r\n面色：无病容，正常。\r\n体位：自动体位，正常。\r\n眼睑：无浮肿，正常。\r\n结膜：无水肿，正常。\r\n巩膜：无黄染，正常。\r\n瞳孔：等大、等圆，正常。\r\n唇部：无紫绀，正常。\r\n甲状腺：无肿大，正");
                WriteLiteral(@"常。\r\n颈静脉：无怒张，正常。\r\n颈部血管杂音：无，正常。\r\n肺部听诊：无异常呼吸音。\r\n啰音：无，正常。\r\n肝脾肋下触诊：无，正常。\r\n心音：正常，心音强弱一致，A2>P2。\r\n心率：79次/分，正常范围。\r\n心音性质：心脏听诊无杂音。\r\n血压：未测量，无法评估。\r\n腹部检查：无异常，正常。\r\n肝脏触诊：无，正常。\r\n下肢水肿：无，正常。\r\n肝颈静脉反流征：无，正常。\r\n\r\n根据以上体格检查结果，患者整体情况良好，无明显异常。"",
                        ""TGJCJson"": ""{\""Direct_rectal_cancer_first_diagnosis_time\"":\""2023-06-06\"",\""History_Intestinal_perforation\"":\""是\"",\""History_intestinal_obstruction\"":\""是\"",\""rectal_finger_examination_tumor_anus_distance(cm)\"":\""1.4\"",\""Lithotomy_Position_OClock_Direction\"":\""9\"",\""Occupying_Intestinal_Lumen\"":\""≥1/4圈，<1/2圈\"",\""Colonoscopy_Tumor_Lower_Edge_to_Anus_Distance(cm)\"":\""1.4\"",\""Colonoscopy_Passability\"":\""是\"",\""MRI_Tumor_Lower_Edge_to_Anus_Distance(cm)\"":\""1.35\"",\""Tumor_Longitudinal_Diameter(cm)\"":\""1.5\"",\""Tumor_Transverse_Diameter(cm)\"":\""0.9\"",\""Pathological_Type\"":\""印戒细胞癌\"",\""Pathological_Grade\"":\""中分化\"",\""Clinical_Staging_Evaluation_Method\"":\""磁共振\"",\""Primary_Tumor\"":\""T1\"",\""the_entire_rectal_mesentery_Involvement\"":\""MRF\"",\""regional_lym");
                WriteLiteral(@"ph_nodes\"":\""N0\"",\""distant_metastasis\"":\""M0\"",\""Tumor_Staging\"":\""IIb\"",\""neoadjuvant_therapy\"":\""是\"",\""doctor_signature\"":\""张医生\""}""
                    }, {
                ""Id"": 6,
                ""isSF"": false,
                ""isLY"": false,
                        ""BLH"": ""80408043"",
                        ""SFZH"": null,
                        ""PATIENTID"": ""1526421"",
                        ""VISITID"": null,
                        ""HZXM"": ""杨*民"",
                        ""BRZT"": ""1"",
                        ""KSDM"": ""1112"",
                        ""KSMC"": ""血液科"",
                        ""BQDM"": ""0030"",
                        ""BQMC"": ""30区血液/康复科"",
                        ""YLZDM"": ""2902"",
                        ""YLZMC"": ""陈*云组"",
                        ""CYZDDM"": """",
                        ""CYZDMC"": """",
                        ""SSCODE"": ""38.7x04 ,88.5102 "",
                        ""SSMC"": ""下腔静脉滤器置入术,下腔静脉造影"",
                        ""YSDM"": ""d0317"",
                        ""YSMC"": ""陈*云"",
");
                WriteLiteral(@"
                        ""IDM"": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                        ""LJSL"": null,
                        ""ZXRQ"": null,
                        ""XH"": null,
                        ""DXMDM"": null,
                        ""DXMMC"": null,
                        ""RYZDDM"": """",
                        ""RYZDMC"": """",
                        ""YYDM"": ""01"",
                        ""SEX"": ""女"",
                        ""NL"": 58,
                        ""YF"": null,
                        ""TS"": null,
                        ""KDYSMC"": null,
                        ""ZXKSDM"": null,
                        ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
                        ""YBSHBZ"": null,
                        ""DBZBZ"": 0,
                        ""JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
    ");
                WriteLiteral(@"                    ""YBDM"": null,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
                        ""CBDDM"": null,
                        ""CBDMC"": null,
                        ""ISYBXM"": null,
                        ""RZBZ"": 0,
                        ""RYRQ"": ""2023-12-31T16:21:14"",
                        ""RQRQ"": ""2023-12-31T16:21:14"",
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": null,
                        ""Status"": 0,
                        ""XBS"": null,
                        ""XBSJson"": null,
                        ""TGJC"": null,
                        ""TGJCJson"": null
                    }, {
                ""Id"": 7,
                ""isSF"": true,
                ""isLY"": false,
                        ""BLH"": ""80409943"",
                        ""SFZH"": null,
                        ""PATIENTID"": ""1528590""");
                WriteLiteral(@",
                        ""VISITID"": null,
                        ""HZXM"": ""林*才"",
                        ""BRZT"": ""1"",
                        ""KSDM"": ""1850"",
                        ""KSMC"": ""神经内科"",
                        ""BQDM"": ""0024"",
                        ""BQMC"": ""24区神经内科"",
                        ""YLZDM"": ""2304"",
                        ""YLZMC"": ""林*华组"",
                        ""CYZDDM"": """",
                        ""CYZDMC"": """",
                        ""SSCODE"": """",
                        ""SSMC"": """",
                        ""YSDM"": ""d0558"",
                        ""YSMC"": ""林*华"",
                        ""IDM"": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                        ""LJSL"": null,
                        ""ZXRQ"": null,
                        ""XH"": null,
                        ""DXMDM"": null,
                        ""DXMMC"": null,
                        ""RYZDDM"": """",
                      ");
                WriteLiteral(@"  ""RYZDMC"": """",
                        ""YYDM"": ""01"",
                        ""SEX"": ""男"",
                        ""NL"": 88,
                        ""YF"": null,
                        ""TS"": null,
                        ""KDYSMC"": null,
                        ""ZXKSDM"": null,
                        ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
                        ""YBSHBZ"": null,
                        ""DBZBZ"": 0,
                        ""JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
                        ""YBDM"": null,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
                        ""CBDDM"": null,
                        ""CBDMC"": null,
                        ""ISYBXM"": null,
                        ""RZBZ"": 0,
                        ""RYRQ"": ""2024-01-05T12:00:00"",
       ");
                WriteLiteral(@"                 ""RQRQ"": ""2024-01-05T12:00:00"",
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": null,
                        ""Status"": 0,
                        ""XBS"": null,
                        ""XBSJson"": null,
                        ""TGJC"": null,
                        ""TGJCJson"": null
                    }, {
                ""Id"": 8,
                ""isSF"": false,
                ""isLY"": false,
                        ""BLH"": ""n2400486"",
                        ""SFZH"": null,
                        ""PATIENTID"": ""1529395"",
                        ""VISITID"": null,
                        ""HZXM"": ""吴*瑛"",
                        ""BRZT"": ""1"",
                        ""KSDM"": ""n305"",
                        ""KSMC"": ""外科(分院)"",
                        ""BQDM"": ""n004"",
                        ""BQMC"": ""4区（南院）"",
                        ""YLZDM"": ""9073"",
                        ""YLZMC"": ""林*鹰组"",
                        ""CYZDDM"": """",
");
                WriteLiteral(@"                        ""CYZDMC"": """",
                        ""SSCODE"": """",
                        ""SSMC"": """",
                        ""YSDM"": ""t1168"",
                        ""YSMC"": ""林*鹰"",
                        ""IDM"": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                        ""LJSL"": null,
                        ""ZXRQ"": null,
                        ""XH"": null,
                        ""DXMDM"": null,
                        ""DXMMC"": null,
                        ""RYZDDM"": """",
                        ""RYZDMC"": """",
                        ""YYDM"": ""02"",
                        ""SEX"": ""女"",
                        ""NL"": 60,
                        ""YF"": null,
                        ""TS"": null,
                        ""KDYSMC"": null,
                        ""ZXKSDM"": null,
                        ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
   ");
                WriteLiteral(@"                     ""YBSHBZ"": null,
                        ""DBZBZ"": 0,
                        ""JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
                        ""YBDM"": null,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
                        ""CBDDM"": null,
                        ""CBDMC"": null,
                        ""ISYBXM"": null,
                        ""RZBZ"": 0,
                        ""RYRQ"": ""2024-01-08T10:42:09"",
                        ""RQRQ"": ""2024-01-08T10:42:09"",
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": null,
                        ""Status"": 0,
                        ""XBS"": null,
                        ""XBSJson"": null,
                        ""TGJC"": null,
                        ""TGJCJson"": null
                    }, {
                ""Id"": ");
                WriteLiteral(@"9,
                ""isSF"": false,
                ""isLY"": false,
                        ""BLH"": ""80409604"",
                        ""SFZH"": null,
                        ""PATIENTID"": ""1528187"",
                        ""VISITID"": null,
                        ""HZXM"": ""郭*云"",
                        ""BRZT"": ""1"",
                        ""KSDM"": ""7219"",
                        ""KSMC"": ""心血管外一科"",
                        ""BQDM"": ""0037"",
                        ""BQMC"": ""37区心外一科"",
                        ""YLZDM"": ""3716"",
                        ""YLZMC"": ""王*组一（周围血管组）"",
                        ""CYZDDM"": """",
                        ""CYZDMC"": """",
                        ""SSCODE"": """",
                        ""SSMC"": """",
                        ""YSDM"": ""d0903"",
                        ""YSMC"": ""王*"",
                        ""IDM"": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                        ""LJSL"": null,
            ");
                WriteLiteral(@"            ""ZXRQ"": null,
                        ""XH"": null,
                        ""DXMDM"": null,
                        ""DXMMC"": null,
                        ""RYZDDM"": """",
                        ""RYZDMC"": """",
                        ""YYDM"": ""01"",
                        ""SEX"": ""女"",
                        ""NL"": 57,
                        ""YF"": null,
                        ""TS"": null,
                        ""KDYSMC"": null,
                        ""ZXKSDM"": null,
                        ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
                        ""YBSHBZ"": null,
                        ""DBZBZ"": 0,
                        ""JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
                        ""YBDM"": null,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
               ");
                WriteLiteral(@"         ""CBDDM"": null,
                        ""CBDMC"": null,
                        ""ISYBXM"": null,
                        ""RZBZ"": 0,
                        ""RYRQ"": ""2024-01-04T15:18:35"",
                        ""RQRQ"": ""2024-01-04T15:18:35"",
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": null,
                        ""Status"": 0,
                        ""XBS"": null,
                        ""XBSJson"": null,
                        ""TGJC"": null,
                        ""TGJCJson"": null
                    }, {
                ""Id"": 10,
                ""isSF"": false,
                ""isLY"": false,
                        ""BLH"": ""80403028"",
                        ""SFZH"": null,
                        ""PATIENTID"": ""1520576"",
                        ""VISITID"": null,
                        ""HZXM"": ""王*琛"",
                        ""BRZT"": ""1"",
                        ""KSDM"": ""1217"",
                        ""KSMC"": ""胃肠外科"",
   ");
                WriteLiteral(@"                     ""BQDM"": ""003b"",
                        ""BQMC"": ""3B区胃肠外科"",
                        ""YLZDM"": ""2601"",
                        ""YLZMC"": ""黄*祥组一"",
                        ""CYZDDM"": """",
                        ""CYZDMC"": """",
                        ""SSCODE"": ""43.9905 ,40.5900x013 ,43.9901 ,54.2300x005 "",
                        ""SSMC"": ""腹腔镜下根治性全胃切除术,胃周围淋巴结清扫术,食管空肠Roux-en-Y吻合术,腹腔镜下横结肠系膜结节切除术"",
                        ""YSDM"": ""d0447"",
                        ""YSMC"": ""黄*祥"",
                        ""IDM"": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                        ""LJSL"": null,
                        ""ZXRQ"": null,
                        ""XH"": null,
                        ""DXMDM"": null,
                        ""DXMMC"": null,
                        ""RYZDDM"": ""K25.900x001,K21.001,D64.901,M81.900,M19.900x091,K29.400"",
                        ""RYZDMC"": ""胃溃疡浸润性病变性质待查,反流性食管炎（A级）,轻度贫血,骨质疏松症,骨关节炎,慢性");
                WriteLiteral(@"萎缩性胃炎"",
                        ""YYDM"": ""01"",
                        ""SEX"": ""女"",
                        ""NL"": 82,
                        ""YF"": null,
                        ""TS"": null,
                        ""KDYSMC"": null,
                        ""ZXKSDM"": null,
                        ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
                        ""YBSHBZ"": null,
                        ""DBZBZ"": 0,
                        ""JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
                        ""YBDM"": null,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
                        ""CBDDM"": null,
                        ""CBDMC"": null,
                        ""ISYBXM"": null,
                        ""RZBZ"": 0,
                        ""RYRQ"": ""2023-12-15T09:50:49"",
               ");
                WriteLiteral(@"         ""RQRQ"": ""2023-12-15T09:50:49"",
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": null,
                        ""Status"": 0,
                        ""XBS"": null,
                        ""XBSJson"": null,
                        ""TGJC"": null,
                        ""TGJCJson"": null
                    }, {
                ""Id"": 11,
                ""isSF"": false,
                ""isLY"": false,
                        ""BLH"": ""80391741"",
                        ""SFZH"": null,
                        ""PATIENTID"": ""1507448"",
                        ""VISITID"": null,
                        ""HZXM"": ""陈*英"",
                        ""BRZT"": ""1"",
                        ""KSDM"": ""1216"",
                        ""KSMC"": ""骨二科"",
                        ""BQDM"": ""0020"",
                        ""BQMC"": ""20区骨二科"",
                        ""YLZDM"": ""2004"",
                        ""YLZMC"": ""张*茂组"",
                        ""CYZDDM"": """",
          ");
                WriteLiteral(@"              ""CYZDMC"": """",
                        ""SSCODE"": """",
                        ""SSMC"": """",
                        ""YSDM"": ""d0522"",
                        ""YSMC"": ""张*茂"",
                        ""IDM"": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                        ""LJSL"": null,
                        ""ZXRQ"": null,
                        ""XH"": null,
                        ""DXMDM"": null,
                        ""DXMMC"": null,
                        ""RYZDDM"": """",
                        ""RYZDMC"": """",
                        ""YYDM"": ""01"",
                        ""SEX"": ""女"",
                        ""NL"": 88,
                        ""YF"": null,
                        ""TS"": null,
                        ""KDYSMC"": null,
                        ""ZXKSDM"": null,
                        ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
             ");
                WriteLiteral(@"           ""YBSHBZ"": null,
                        ""DBZBZ"": 0,
                        ""JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
                        ""YBDM"": null,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
                        ""CBDDM"": null,
                        ""CBDMC"": null,
                        ""ISYBXM"": null,
                        ""RZBZ"": 0,
                        ""RYRQ"": ""2023-11-14T10:04:05"",
                        ""RQRQ"": ""2023-11-14T10:04:05"",
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": null,
                        ""Status"": 0,
                        ""XBS"": null,
                        ""XBSJson"": null,
                        ""TGJC"": null,
                        ""TGJCJson"": null
                    }, {
                ""Id"": 12,
     ");
                WriteLiteral(@"           ""isSF"": false,
                ""isLY"": false,
                        ""BLH"": ""80408520"",
                        ""SFZH"": null,
                        ""PATIENTID"": ""1526973"",
                        ""VISITID"": null,
                        ""HZXM"": ""康*"",
                        ""BRZT"": ""1"",
                        ""KSDM"": ""1214"",
                        ""KSMC"": ""基本外科"",
                        ""BQDM"": ""0018"",
                        ""BQMC"": ""18肿瘤外基外口腔"",
                        ""YLZDM"": ""1607"",
                        ""YLZMC"": ""陈*组"",
                        ""CYZDDM"": """",
                        ""CYZDMC"": """",
                        ""SSCODE"": """",
                        ""SSMC"": """",
                        ""YSDM"": ""d0540"",
                        ""YSMC"": ""陈*"",
                        ""IDM"": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                        ""LJSL"": null,
                        ""ZXRQ""");
                WriteLiteral(@": null,
                        ""XH"": null,
                        ""DXMDM"": null,
                        ""DXMMC"": null,
                        ""RYZDDM"": """",
                        ""RYZDMC"": """",
                        ""YYDM"": ""01"",
                        ""SEX"": ""女"",
                        ""NL"": 68,
                        ""YF"": null,
                        ""TS"": null,
                        ""KDYSMC"": null,
                        ""ZXKSDM"": null,
                        ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
                        ""YBSHBZ"": null,
                        ""DBZBZ"": 0,
                        ""JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
                        ""YBDM"": null,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
                        ""CBDDM"": ");
                WriteLiteral(@"null,
                        ""CBDMC"": null,
                        ""ISYBXM"": null,
                        ""RZBZ"": 0,
                        ""RYRQ"": ""2024-01-02T10:55:28"",
                        ""RQRQ"": ""2024-01-02T10:55:28"",
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": null,
                        ""Status"": 0,
                        ""XBS"": null,
                        ""XBSJson"": null,
                        ""TGJC"": null,
                        ""TGJCJson"": null
                    }, {
                ""Id"": 13,
                ""isSF"": false,
                ""isLY"": false,
                        ""BLH"": ""80403908"",
                        ""SFZH"": null,
                        ""PATIENTID"": ""1521625"",
                        ""VISITID"": null,
                        ""HZXM"": ""胡*芳"",
                        ""BRZT"": ""1"",
                        ""KSDM"": ""1109"",
                        ""KSMC"": ""消化内科"",
                     ");
                WriteLiteral(@"   ""BQDM"": ""0017"",
                        ""BQMC"": ""17区消化内科"",
                        ""YLZDM"": ""1808"",
                        ""YLZMC"": ""陈*组"",
                        ""CYZDDM"": """",
                        ""CYZDMC"": """",
                        ""SSCODE"": """",
                        ""SSMC"": """",
                        ""YSDM"": ""d0426"",
                        ""YSMC"": ""陈*"",
                        ""IDM"": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                        ""LJSL"": null,
                        ""ZXRQ"": null,
                        ""XH"": null,
                        ""DXMDM"": null,
                        ""DXMMC"": null,
                        ""RYZDDM"": """",
                        ""RYZDMC"": """",
                        ""YYDM"": ""01"",
                        ""SEX"": ""女"",
                        ""NL"": 45,
                        ""YF"": null,
                        ""TS"": null,
                        ""K");
                WriteLiteral(@"DYSMC"": null,
                        ""ZXKSDM"": null,
                        ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
                        ""YBSHBZ"": null,
                        ""DBZBZ"": 0,
                        ""JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
                        ""YBDM"": null,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
                        ""CBDDM"": null,
                        ""CBDMC"": null,
                        ""ISYBXM"": null,
                        ""RZBZ"": 0,
                        ""RYRQ"": ""2023-12-18T14:34:16"",
                        ""RQRQ"": ""2023-12-18T14:34:16"",
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": null,
                        ""Status"": 0,
                        ");
                WriteLiteral(@"""XBS"": null,
                        ""XBSJson"": null,
                        ""TGJC"": null,
                        ""TGJCJson"": null
                    }, {
                ""Id"": 14,
                ""isSF"": false,
                ""isLY"": false,
                        ""BLH"": ""80409687"",
                        ""SFZH"": null,
                        ""PATIENTID"": ""1528278"",
                        ""VISITID"": null,
                        ""HZXM"": ""林*珍"",
                        ""BRZT"": ""1"",
                        ""KSDM"": ""1942"",
                        ""KSMC"": ""耳鼻咽喉科"",
                        ""BQDM"": ""0012"",
                        ""BQMC"": ""12区(耳鼻咽喉科)"",
                        ""YLZDM"": ""1201"",
                        ""YLZMC"": ""叶*组"",
                        ""CYZDDM"": """",
                        ""CYZDMC"": """",
                        ""SSCODE"": """",
                        ""SSMC"": """",
                        ""YSDM"": ""d0732"",
                        ""YSMC"": ""叶*"",
                        ""IDM""");
                WriteLiteral(@": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                        ""LJSL"": null,
                        ""ZXRQ"": null,
                        ""XH"": null,
                        ""DXMDM"": null,
                        ""DXMMC"": null,
                        ""RYZDDM"": """",
                        ""RYZDMC"": """",
                        ""YYDM"": ""01"",
                        ""SEX"": ""女"",
                        ""NL"": 78,
                        ""YF"": null,
                        ""TS"": null,
                        ""KDYSMC"": null,
                        ""ZXKSDM"": null,
                        ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
                        ""YBSHBZ"": null,
                        ""DBZBZ"": 0,
                        ""JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
                        ""YBDM"": nu");
                WriteLiteral(@"ll,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
                        ""CBDDM"": null,
                        ""CBDMC"": null,
                        ""ISYBXM"": null,
                        ""RZBZ"": 0,
                        ""RYRQ"": ""2024-01-04T20:21:50"",
                        ""RQRQ"": ""2024-01-04T20:21:50"",
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": null,
                        ""Status"": 0,
                        ""XBS"": null,
                        ""XBSJson"": null,
                        ""TGJC"": null,
                        ""TGJCJson"": null
                    }, {
                ""Id"": 15,
                ""isSF"": false,
                ""isLY"": false,
                        ""BLH"": ""80407670"",
                        ""SFZH"": null,
                        ""PATIENTID"": ""1525977"",
                        """);
                WriteLiteral(@"VISITID"": null,
                        ""HZXM"": ""徐*诗"",
                        ""BRZT"": ""1"",
                        ""KSDM"": ""1207"",
                        ""KSMC"": ""儿外科"",
                        ""BQDM"": ""0042"",
                        ""BQMC"": ""42区小儿外科"",
                        ""YLZDM"": ""4203"",
                        ""YLZMC"": ""庄*组"",
                        ""CYZDDM"": """",
                        ""CYZDMC"": """",
                        ""SSCODE"": """",
                        ""SSMC"": """",
                        ""YSDM"": ""d0594"",
                        ""YSMC"": ""庄*"",
                        ""IDM"": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                        ""LJSL"": null,
                        ""ZXRQ"": null,
                        ""XH"": null,
                        ""DXMDM"": null,
                        ""DXMMC"": null,
                        ""RYZDDM"": """",
                        ""RYZDMC"": """",
              ");
                WriteLiteral(@"          ""YYDM"": ""01"",
                        ""SEX"": ""女"",
                        ""NL"": 5,
                        ""YF"": null,
                        ""TS"": null,
                        ""KDYSMC"": null,
                        ""ZXKSDM"": null,
                        ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
                        ""YBSHBZ"": null,
                        ""DBZBZ"": 0,
                        ""JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
                        ""YBDM"": null,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
                        ""CBDDM"": null,
                        ""CBDMC"": null,
                        ""ISYBXM"": null,
                        ""RZBZ"": 0,
                        ""RYRQ"": ""2024-01-08T09:18:42"",
                        ""RQRQ"": ""2024-0");
                WriteLiteral(@"1-08T09:18:42"",
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": null,
                        ""Status"": 0,
                        ""XBS"": null,
                        ""XBSJson"": null,
                        ""TGJC"": null,
                        ""TGJCJson"": null
                    }, {
                ""Id"": 16,
                ""isSF"": false,
                ""isLY"": false,
                        ""BLH"": ""80409050"",
                        ""SFZH"": null,
                        ""PATIENTID"": ""1527564"",
                        ""VISITID"": null,
                        ""HZXM"": ""林*庆"",
                        ""BRZT"": ""1"",
                        ""KSDM"": ""1850"",
                        ""KSMC"": ""神经内科"",
                        ""BQDM"": ""0023"",
                        ""BQMC"": ""23区神经内科"",
                        ""YLZDM"": ""2302"",
                        ""YLZMC"": ""张*组"",
                        ""CYZDDM"": """",
                        ""CYZDMC"":");
                WriteLiteral(@" """",
                        ""SSCODE"": """",
                        ""SSMC"": """",
                        ""YSDM"": ""d0542"",
                        ""YSMC"": ""张*"",
                        ""IDM"": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                        ""LJSL"": null,
                        ""ZXRQ"": null,
                        ""XH"": null,
                        ""DXMDM"": null,
                        ""DXMMC"": null,
                        ""RYZDDM"": """",
                        ""RYZDMC"": """",
                        ""YYDM"": ""01"",
                        ""SEX"": ""男"",
                        ""NL"": 69,
                        ""YF"": null,
                        ""TS"": null,
                        ""KDYSMC"": null,
                        ""ZXKSDM"": null,
                        ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
                        ""YBSHBZ"": nul");
                WriteLiteral(@"l,
                        ""DBZBZ"": 0,
                        ""JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
                        ""YBDM"": null,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
                        ""CBDDM"": null,
                        ""CBDMC"": null,
                        ""ISYBXM"": null,
                        ""RZBZ"": 0,
                        ""RYRQ"": ""2024-01-03T14:15:00"",
                        ""RQRQ"": ""2024-01-03T14:15:00"",
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": null,
                        ""Status"": 0,
                        ""XBS"": null,
                        ""XBSJson"": null,
                        ""TGJC"": null,
                        ""TGJCJson"": null
                    }, {
                ""Id"": 17,
                ""isSF"": false");
                WriteLiteral(@",
                ""isLY"": false,
                        ""BLH"": ""80409119"",
                        ""SFZH"": null,
                        ""PATIENTID"": ""1527641"",
                        ""VISITID"": null,
                        ""HZXM"": ""郑*俤"",
                        ""BRZT"": ""1"",
                        ""KSDM"": ""1850"",
                        ""KSMC"": ""神经内科"",
                        ""BQDM"": ""0023"",
                        ""BQMC"": ""23区神经内科"",
                        ""YLZDM"": ""2305"",
                        ""YLZMC"": ""李*坤组"",
                        ""CYZDDM"": """",
                        ""CYZDMC"": """",
                        ""SSCODE"": """",
                        ""SSMC"": """",
                        ""YSDM"": ""y1625"",
                        ""YSMC"": ""李*坤"",
                        ""IDM"": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                        ""LJSL"": null,
                        ""ZXRQ"": null,
              ");
                WriteLiteral(@"          ""XH"": null,
                        ""DXMDM"": null,
                        ""DXMMC"": null,
                        ""RYZDDM"": """",
                        ""RYZDMC"": """",
                        ""YYDM"": ""01"",
                        ""SEX"": ""男"",
                        ""NL"": 65,
                        ""YF"": null,
                        ""TS"": null,
                        ""KDYSMC"": null,
                        ""ZXKSDM"": null,
                        ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
                        ""YBSHBZ"": null,
                        ""DBZBZ"": 0,
                        ""JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
                        ""YBDM"": null,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
                        ""CBDDM"": null,
                ");
                WriteLiteral(@"        ""CBDMC"": null,
                        ""ISYBXM"": null,
                        ""RZBZ"": 0,
                        ""RYRQ"": ""2024-01-03T16:00:33"",
                        ""RQRQ"": ""2024-01-03T16:00:33"",
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": null,
                        ""Status"": 0,
                        ""XBS"": null,
                        ""XBSJson"": null,
                        ""TGJC"": null,
                        ""TGJCJson"": null
                    }, {
                ""Id"": 18,
                ""isSF"": false,
                ""isLY"": false,
                        ""BLH"": ""80407960"",
                        ""SFZH"": null,
                        ""PATIENTID"": ""1526325"",
                        ""VISITID"": null,
                        ""HZXM"": ""葛*英"",
                        ""BRZT"": ""1"",
                        ""KSDM"": ""1141"",
                        ""KSMC"": ""心血管内一科"",
                        ""BQDM"": ""0039"",
 ");
                WriteLiteral(@"                       ""BQMC"": ""39区心内一科"",
                        ""YLZDM"": ""3917"",
                        ""YLZMC"": ""陈*峰组二"",
                        ""CYZDDM"": """",
                        ""CYZDMC"": """",
                        ""SSCODE"": """",
                        ""SSMC"": """",
                        ""YSDM"": ""d0698"",
                        ""YSMC"": ""陈*峰"",
                        ""IDM"": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                        ""LJSL"": null,
                        ""ZXRQ"": null,
                        ""XH"": null,
                        ""DXMDM"": null,
                        ""DXMMC"": null,
                        ""RYZDDM"": """",
                        ""RYZDMC"": """",
                        ""YYDM"": ""01"",
                        ""SEX"": ""女"",
                        ""NL"": 92,
                        ""YF"": null,
                        ""TS"": null,
                        ""KDYSMC"": null,
   ");
                WriteLiteral(@"                     ""ZXKSDM"": null,
                        ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
                        ""YBSHBZ"": null,
                        ""DBZBZ"": 0,
                        ""JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
                        ""YBDM"": null,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
                        ""CBDDM"": null,
                        ""CBDMC"": null,
                        ""ISYBXM"": null,
                        ""RZBZ"": 0,
                        ""RYRQ"": ""2023-12-31T00:01:07"",
                        ""RQRQ"": ""2023-12-31T00:01:07"",
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": null,
                        ""Status"": 0,
                        ""XBS"": null,
    ");
                WriteLiteral(@"                    ""XBSJson"": null,
                        ""TGJC"": null,
                        ""TGJCJson"": null
                    }, {
                ""Id"": 19,
                ""isSF"": false,
                ""isLY"": false,
                        ""BLH"": ""80391107"",
                        ""SFZH"": null,
                        ""PATIENTID"": ""1506694"",
                        ""VISITID"": null,
                        ""HZXM"": ""高*"",
                        ""BRZT"": ""1"",
                        ""KSDM"": ""1854"",
                        ""KSMC"": ""泌尿外科"",
                        ""BQDM"": ""0028"",
                        ""BQMC"": ""28区泌尿外科"",
                        ""YLZDM"": ""2703"",
                        ""YLZMC"": ""叶*夫组"",
                        ""CYZDDM"": """",
                        ""CYZDMC"": """",
                        ""SSCODE"": ""55.6901 ,55.5300x001 ,54.3x00x011 ,86.0401 "",
                        ""SSMC"": ""肾移植术（同种异体）,移植肾切除术,左下腹壁伤口清创术,创面封闭式负压引流术（VSD)"",
                        ""YSDM"": ""d0737"",
    ");
                WriteLiteral(@"                    ""YSMC"": ""叶*夫"",
                        ""IDM"": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                        ""LJSL"": null,
                        ""ZXRQ"": null,
                        ""XH"": null,
                        ""DXMDM"": null,
                        ""DXMMC"": null,
                        ""RYZDDM"": ""N18.900,N02.801,I10.x00x002,G47.900"",
                        ""RYZDMC"": ""慢性肾功能衰竭尿毒症期,IgA肾病,高血压,睡眠障碍"",
                        ""YYDM"": ""01"",
                        ""SEX"": ""男"",
                        ""NL"": 37,
                        ""YF"": null,
                        ""TS"": null,
                        ""KDYSMC"": null,
                        ""ZXKSDM"": null,
                        ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
                        ""YBSHBZ"": null,
                        ""DBZBZ"": 0,
                        """);
                WriteLiteral(@"JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
                        ""YBDM"": null,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
                        ""CBDDM"": null,
                        ""CBDMC"": null,
                        ""ISYBXM"": null,
                        ""RZBZ"": 0,
                        ""RYRQ"": ""2023-11-13T09:29:27"",
                        ""RQRQ"": ""2023-11-13T09:29:27"",
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": null,
                        ""Status"": 0,
                        ""XBS"": null,
                        ""XBSJson"": null,
                        ""TGJC"": null,
                        ""TGJCJson"": null
                    }, {
                ""Id"": 20,
                ""isSF"": false,
                ""isLY"": false,
                        ""BLH"": ");
                WriteLiteral(@"""80407910"",
                        ""SFZH"": null,
                        ""PATIENTID"": ""1526263"",
                        ""VISITID"": null,
                        ""HZXM"": ""郑*勋"",
                        ""BRZT"": ""1"",
                        ""KSDM"": ""1850"",
                        ""KSMC"": ""神经内科"",
                        ""BQDM"": ""0024"",
                        ""BQMC"": ""24区神经内科"",
                        ""YLZDM"": ""2303"",
                        ""YLZMC"": ""汪*洲组"",
                        ""CYZDDM"": """",
                        ""CYZDMC"": """",
                        ""SSCODE"": """",
                        ""SSMC"": """",
                        ""YSDM"": ""d0818"",
                        ""YSMC"": ""汪*洲"",
                        ""IDM"": null,
                        ""IDMNAME"": null,
                        ""XMBM"": null,
                        ""XMMC"": null,
                        ""LJSL"": null,
                        ""ZXRQ"": null,
                        ""XH"": null,
                        ""DXMDM"": null,
   ");
                WriteLiteral(@"                     ""DXMMC"": null,
                        ""RYZDDM"": ""I63.900,I10.x00x026,E11.900"",
                        ""RYZDMC"": ""脑梗死,高血压病2级（中危）,2型糖尿病"",
                        ""YYDM"": ""01"",
                        ""SEX"": ""男"",
                        ""NL"": 59,
                        ""YF"": null,
                        ""TS"": null,
                        ""KDYSMC"": null,
                        ""ZXKSDM"": null,
                        ""ZXKSMC"": null,
                        ""CZYMC"": null,
                        ""ZY_YBMLWFY"": null,
                        ""YBSHBZ"": null,
                        ""DBZBZ"": 0,
                        ""JSXH"": null,
                        ""ZHBZ"": null,
                        ""JSRQ"": null,
                        ""YBDM"": null,
                        ""YBMC"": null,
                        ""YBDL"": null,
                        ""GJYBDM"": null,
                        ""GJYBMC"": null,
                        ""CBDDM"": null,
                        ""CBDMC"": nu");
                WriteLiteral(@"ll,
                        ""ISYBXM"": null,
                        ""RZBZ"": 0,
                        ""RYRQ"": ""2023-12-30T12:29:00"",
                        ""RQRQ"": ""2023-12-30T12:29:00"",
                        ""CYRQ"": null,
                        ""CQRQ"": null,
                        ""KYRZ"": null,
                        ""Status"": 0,
                        ""XBS"": null,
                        ""XBSJson"": null,
                        ""TGJC"": null,
                        ""TGJCJson"": null
                    }]




            table.render({
                elem: '#tablelist'
                , id: 'tablelist'
                , page: true
                , limit: 20
                , height: 'full-130'
                , data: ListData
                , cols: [[
                    { field: 'No', title: '序号', width: 60, type: 'numbers' }
                    , { field: 'BLH', title: '病历号', width: 100 }
                    , { field: 'HZXM', title: '患者姓名', width: 120 }
            ");
                WriteLiteral(@"        , { field: 'NL', title: '年龄', width: 80 }
                    , {
                        field: 'isSF', title: '是否随访', width: 120,
                        templet: function (d) {
                            console.log(d)
                            if (d.isSF == true)
                                return ""<span  style='background-color: #00FF00'>已随访</span>"";
                            else if (d.isSF == false)
                                return ""<span  style='background-color: #FF9933'>未随访</span>"";
                            else
                                return ' ';
                        }
                    }
                    , {
                        field: 'isLY', title: '是否录音', width: 120,
                        templet: function (d) {
                            if (d.isLY == true)
                                return ""<span  style='background-color: #00FF00'>已录音</span>"";
                            else if (d.isLY == false)
                         ");
                WriteLiteral(@"       return ""<span  style='background-color: #FF9933'>未录音</span>"";
                            else
                                return ' ';
                        }
                    }
                    , { field: 'CWDM', title: '床位代码', width: 100 }
                    , { field: 'KSDM', title: '科室代码', width: 100 }
                    , { field: 'KSMC', title: '科室名称', width: 100 }
                    , { field: 'BQDM', title: '病区代码', width: 100 }
                    , { field: 'BQMC', title: '病区名称', width: 100 }
                    , { field: 'ZXKSDM', title: '执行科室', maxWidth: 100 }
                    , { field: 'YLZDM', title: '医疗组代码', width: 100 }
                    , { field: 'YLZMC', title: '医疗组名称', width: 100 }
                    , {
                        title: '操作',
                        width: 160,
                        fixed: 'right',
                        toolbar: '#tableBar1'
                    }
                ]],
                done: function (res, cur");
                WriteLiteral(@"r, count) {

                }

            });

            //监听tablelist工具条
            table.on('tool(tablelist)', function (obj) {
                var data = obj.data;
                if (obj.event === 'record') {
                    openRecord()
                }
            })


            function openRecord() {

                var windowsIndex = layer.open({
                    skin: 'alert-skin',
                    type: 2,
                    title: '随访录音分析',
                    area: ['100%', '100%'],

                    resize: true,
                    content: '/FollowUp/SoundAnalysis/Index',
                    success: function () {

                    }

                });

            }




        })
    </script>




");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
