{"runtimeTarget": {"name": ".NETCoreApp,Version=v3.1", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v3.1": {"SignalRTest/1.0.0": {"dependencies": {"Microsoft.AspNetCore.SignalR.Client": "3.1.32"}, "runtime": {"SignalRTest.dll": {}}}, "Microsoft.AspNetCore.Connections.Abstractions/3.1.32": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "3.1.32", "System.IO.Pipelines": "4.7.5"}, "runtime": {"lib/netcoreapp3.1/Microsoft.AspNetCore.Connections.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}}}, "Microsoft.AspNetCore.Http.Connections.Client/3.1.32": {"dependencies": {"Microsoft.AspNetCore.Http.Connections.Common": "3.1.32", "Microsoft.Extensions.Logging.Abstractions": "3.1.32", "Microsoft.Extensions.Options": "3.1.32"}, "runtime": {"lib/netstandard2.1/Microsoft.AspNetCore.Http.Connections.Client.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}}}, "Microsoft.AspNetCore.Http.Connections.Common/3.1.32": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "3.1.32"}, "runtime": {"lib/netcoreapp3.1/Microsoft.AspNetCore.Http.Connections.Common.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}}}, "Microsoft.AspNetCore.Http.Features/3.1.32": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.32", "System.IO.Pipelines": "4.7.5"}, "runtime": {"lib/netcoreapp3.1/Microsoft.AspNetCore.Http.Features.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}}}, "Microsoft.AspNetCore.SignalR.Client/3.1.32": {"dependencies": {"Microsoft.AspNetCore.Http.Connections.Client": "3.1.32", "Microsoft.AspNetCore.SignalR.Client.Core": "3.1.32"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Client.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}}}, "Microsoft.AspNetCore.SignalR.Client.Core/3.1.32": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "3.1.32", "Microsoft.AspNetCore.SignalR.Protocols.Json": "3.1.32", "Microsoft.Extensions.DependencyInjection": "3.1.32", "Microsoft.Extensions.Logging": "3.1.32", "System.Threading.Channels": "4.7.1"}, "runtime": {"lib/netstandard2.1/Microsoft.AspNetCore.SignalR.Client.Core.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}}}, "Microsoft.AspNetCore.SignalR.Common/3.1.32": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "3.1.32", "Microsoft.Extensions.Options": "3.1.32"}, "runtime": {"lib/netcoreapp3.1/Microsoft.AspNetCore.SignalR.Common.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}}}, "Microsoft.AspNetCore.SignalR.Protocols.Json/3.1.32": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "3.1.32"}, "runtime": {"lib/netcoreapp3.1/Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56610"}}}, "Microsoft.Extensions.Configuration/3.1.32": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.32"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}}}, "Microsoft.Extensions.Configuration.Abstractions/3.1.32": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.32"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}}}, "Microsoft.Extensions.Configuration.Binder/3.1.32": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.32"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}}}, "Microsoft.Extensions.DependencyInjection/3.1.32": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.32"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.32": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}}}, "Microsoft.Extensions.Logging/3.1.32": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "3.1.32", "Microsoft.Extensions.DependencyInjection": "3.1.32", "Microsoft.Extensions.Logging.Abstractions": "3.1.32", "Microsoft.Extensions.Options": "3.1.32"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}}}, "Microsoft.Extensions.Logging.Abstractions/3.1.32": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}}}, "Microsoft.Extensions.Options/3.1.32": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.32", "Microsoft.Extensions.Primitives": "3.1.32"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Options.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}}}, "Microsoft.Extensions.Primitives/3.1.32": {"runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "********", "fileVersion": "3.100.3222.56602"}}}, "System.IO.Pipelines/4.7.5": {"runtime": {"lib/netcoreapp3.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.22.12208"}}}, "System.Threading.Channels/4.7.1": {"runtime": {"lib/netcoreapp3.0/System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}}}, "libraries": {"SignalRTest/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Connections.Abstractions/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-zxJLqD7B0cFA+EBFwchMJNH6zhqG6WPls24AwZelZyYkWJZTvgFi7cLjS9Xsiq/TWZnAaadMeCbG+sNtEssthA==", "path": "microsoft.aspnetcore.connections.abstractions/3.1.32", "hashPath": "microsoft.aspnetcore.connections.abstractions.3.1.32.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Client/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-ckYAH9XHpajAMJ42TE3bK4icXySy6NhjuRsaSZffSzp75LFkyDUfEQirfbIrA9yYW+OY0rA0GJm8I/Kme8Jn5Q==", "path": "microsoft.aspnetcore.http.connections.client/3.1.32", "hashPath": "microsoft.aspnetcore.http.connections.client.3.1.32.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Common/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-PMPA49C3yP0o4Ybca0mb/moqRaCK0iTZTja6PPIMdSAVWMsGd6OSZtBKFTr4AHyd8oyI/aAQPHFPwWAvb71yYg==", "path": "microsoft.aspnetcore.http.connections.common/3.1.32", "hashPath": "microsoft.aspnetcore.http.connections.common.3.1.32.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-uf8Cae8ThkSWPqzRD1HyBzGjd3HbJzw4HDPtMRv2sMmJCDBkGxMIc3gGFP09VLPzt5bHRv82TNPfOYFsMRhr8Q==", "path": "microsoft.aspnetcore.http.features/3.1.32", "hashPath": "microsoft.aspnetcore.http.features.3.1.32.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Client/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-X+/AoVhbHAYpCeCWyn9D7d1JJjQJQKROCTcoWqz+Ony+l+JEMyeCnM8joNSIIZh0VPe+xfL2XJWuspN2KMgsgQ==", "path": "microsoft.aspnetcore.signalr.client/3.1.32", "hashPath": "microsoft.aspnetcore.signalr.client.3.1.32.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Client.Core/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-fHFf3P7TpUNwi2hOm7dFhSjYwT9RxLixboDP93gfAnXq9MPPf9eNXmt6mRKYxMXXwcK3fSjOVje4aWZY0j2xBQ==", "path": "microsoft.aspnetcore.signalr.client.core/3.1.32", "hashPath": "microsoft.aspnetcore.signalr.client.core.3.1.32.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Common/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-5gTwbkKC3jHAwtzGisT6u8VS5j8YjaRfN5ebXCLDhMc4HEh8BiZE2gA49d8WuOdfwMGRjd5bKmhoayUiORAEuQ==", "path": "microsoft.aspnetcore.signalr.common/3.1.32", "hashPath": "microsoft.aspnetcore.signalr.common.3.1.32.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Protocols.Json/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-WkKqvlIZhhI8bxzXzNH7b5nVOD636xbB295u7hpyZkWBTP/1ILzXhsPnM0j/JH7V5x3AgmWqFyQIfBx2GfcyNw==", "path": "microsoft.aspnetcore.signalr.protocols.json/3.1.32", "hashPath": "microsoft.aspnetcore.signalr.protocols.json.3.1.32.nupkg.sha512"}, "Microsoft.Extensions.Configuration/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-WuOHTU9FB1yHaIU+/Ar1s5swHshH+7YjU7eA9Lmv0kO+rta7xOrR5Xu68srdxNpE9HjqjzxGZhPJFLxpP3J1Og==", "path": "microsoft.extensions.configuration/3.1.32", "hashPath": "microsoft.extensions.configuration.3.1.32.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-w8WEwVFYbTkoDQ/eJgGUPiL4SqZOiIVBkGxbkmnJAWnFxRigFk4WZla/3MDkN9fGSis6JwJfc57YgnleTw48AA==", "path": "microsoft.extensions.configuration.abstractions/3.1.32", "hashPath": "microsoft.extensions.configuration.abstractions.3.1.32.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-e89Od7dtbQpSPL3wNLlZ5LYv6KtMczxr2KSTULt4mhhUnMJtSfDIe1JJJJaL/Ed2+cwyAXtkMpqf4aR/Sd4mTQ==", "path": "microsoft.extensions.configuration.binder/3.1.32", "hashPath": "microsoft.extensions.configuration.binder.3.1.32.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-VUbvtpsoHZf4XtBhsfio0+2cpqC9bJs6ZiApT3G81CwBfcDV5OSeU9SaI6it6wxaGf0K2uADzzIS+4yTADaTRg==", "path": "microsoft.extensions.dependencyinjection/3.1.32", "hashPath": "microsoft.extensions.dependencyinjection.3.1.32.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-dARl3iAcHZshMvnXtKpLGES4QZq8ExxIQnF2s8pXfP3MJOOXRSBsk+UmA3TMVN4hPr4O2QFS8AMJp9GDD/4lDw==", "path": "microsoft.extensions.dependencyinjection.abstractions/3.1.32", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.3.1.32.nupkg.sha512"}, "Microsoft.Extensions.Logging/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-5nx7SuLJnqINfv9zslFGig1Czt2AWAyKLiSEZj8eHxYJucuHcoenvD8p75FafYANsMMOach6ZiNZ1MdlPFS/MQ==", "path": "microsoft.extensions.logging/3.1.32", "hashPath": "microsoft.extensions.logging.3.1.32.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-BShtU4APsEGa72vaj42rHc8CW40xpFp1dousoNeziHwOUFJLNOJP2wxKX2jyfer0nFaLSkIMtgKn+qeJtS3Pcw==", "path": "microsoft.extensions.logging.abstractions/3.1.32", "hashPath": "microsoft.extensions.logging.abstractions.3.1.32.nupkg.sha512"}, "Microsoft.Extensions.Options/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-JRX2+OxOC/jqWrkyjJZstpe0NdY/HfOJNPOzVkMxqCxTirecIYfaKXAms4HDsEKl3i6CD8jDkwHyv3fyWZO6hQ==", "path": "microsoft.extensions.options/3.1.32", "hashPath": "microsoft.extensions.options.3.1.32.nupkg.sha512"}, "Microsoft.Extensions.Primitives/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-N8lTVwdjR+df9Sx3VdHfrecV6zl8KQoAx7kQXJ3rYwQBEw2vuZM0LKVAqjnaA/TBC8ZKnt99ptwH5iaEOxBuYQ==", "path": "microsoft.extensions.primitives/3.1.32", "hashPath": "microsoft.extensions.primitives.3.1.32.nupkg.sha512"}, "System.IO.Pipelines/4.7.5": {"type": "package", "serviceable": true, "sha512": "sha512-iqGBxw9zqxHK4bzwiYMYGYnaKK1OBQkLGwqqN/+kkj1lQVo+jEaEntOCL4XztOHjpp+ANYQ4UPKNUeeQmFK3mQ==", "path": "system.io.pipelines/4.7.5", "hashPath": "system.io.pipelines.4.7.5.nupkg.sha512"}, "System.Threading.Channels/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-6akRtHK/wab3246t4p5v3HQrtQk8LboOt5T4dtpNgsp3zvDeM4/Gx8V12t0h+c/W9/enUrilk8n6EQqdQorZAA==", "path": "system.threading.channels/4.7.1", "hashPath": "system.threading.channels.4.7.1.nupkg.sha512"}}}