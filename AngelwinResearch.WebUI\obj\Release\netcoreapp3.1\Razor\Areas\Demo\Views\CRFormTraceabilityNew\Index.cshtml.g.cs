#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\CRFormTraceabilityNew\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "5cefed09d205937f95436b4e83df0ab59bc82094f14d3c6c2d8c9c50bb0f983b"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_Demo_Views_CRFormTraceabilityNew_Index), @"mvc.1.0.view", @"/Areas/Demo/Views/CRFormTraceabilityNew/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"5cefed09d205937f95436b4e83df0ab59bc82094f14d3c6c2d8c9c50bb0f983b", @"/Areas/Demo/Views/CRFormTraceabilityNew/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_Demo_Views_CRFormTraceabilityNew_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<
#nullable restore
#line 4 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\CRFormTraceabilityNew\Index.cshtml"
       AngelwinResearch.Models.ResearchPatient

#line default
#line hidden
#nullable disable
    >
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/touchslider/touchslider.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/font/eyes_icon/iconfont.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/columnDrag/column_drag.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("type", new global::Microsoft.AspNetCore.Html.HtmlString("text/javascript"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/columnDrag/column_drag.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\CRFormTraceabilityNew\Index.cshtml"
  
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "5cefed09d205937f95436b4e83df0ab59bc82094f14d3c6c2d8c9c50bb0f983b7342", async() => {
                WriteLiteral("\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>CRF溯源</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "5cefed09d205937f95436b4e83df0ab59bc82094f14d3c6c2d8c9c50bb0f983b7770", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "5cefed09d205937f95436b4e83df0ab59bc82094f14d3c6c2d8c9c50bb0f983b8972", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "5cefed09d205937f95436b4e83df0ab59bc82094f14d3c6c2d8c9c50bb0f983b10174", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "5cefed09d205937f95436b4e83df0ab59bc82094f14d3c6c2d8c9c50bb0f983b11377", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"

    <style>
        .layui-tab-title {
            border-bottom-width: 0;
        }

        .layui-tab-brief > .layui-tab-more li.layui-this:after, .layui-tab-brief > .layui-tab-title .layui-this:after {
            border-bottom: 2px solid #50314F;
        }

        .panel-header {
            border-bottom: 2px solid #50314F;
        }

        .panel-content {
            overflow-y: auto;
            overflow-x: hidden;
        }

        .top_right {
            display: flex;
            flex-direction: row;
            align-items: baseline;
        }

        .project-info {
            font-size: 18px;
            padding-left: 10px;
            font-weight: bold;
            color: #1E9FFF;
        }

        .user-name {
            padding-right: 10px;
        }

        /* 折叠状态下左侧菜单的宽度 */
        .sider_btn {
            position: absolute;
            display: block;
            left: -5px;
            bottom: 10%;
            z-index: 99;
       ");
                WriteLiteral(@"     line-height: 0;
            padding: 28px 10px 28px 15px;
            border: none;
            color: #fff;
            border-radius: 0 50% 50% 0;
            background: linear-gradient(to right, #56d1f9, #1eaddc);
        }

        .sider_btn_R {
            position: absolute;
            display: block;
            right: -5px;
            bottom: 10%;
            z-index: 99;
            line-height: 0;
            padding: 28px 10px 28px 15px;
            border: none;
            color: #fff;
            border-radius: 50% 0 0 50%;
            background: linear-gradient(to right, #56d1f9, #1eaddc);
        }

        .Menu_side {
            width: 260px;
            overflow-y: auto;
            overflow-x: hidden;
            transition: right 0.5s ease;
        }

            .Menu_side.folded {
                width: 0px;
            }


        .Menu_sideR {
            width: 0px;
            overflow-y: auto;
            overflow-x: hidden;
        ");
                WriteLiteral(@"    transition: width 0.5s ease;
        }

            .Menu_sideR.folded {
                width: 316px;
            }

            .Menu_sideR .layui-textarea {
                min-height: 100% !important;
                resize: none;
                font-size: 14px;
            }

        .side_menu_title {
            font-size: 18px;
            color: #000;
            text-align: center;
            line-height: 30px;
            padding: 10px 0;
        }

        .content_L {
            overflow-y: auto;
        }

        .mui-btn .layui-icon {
            font-size: 24px;
        }

        .hide_report_list {
            padding: 15px;
        }

        .stretch {
            padding-right: 20px;
            flex-grow: 1;
        }

        .collapse_list {
            overflow-y: auto;
        }

        .null_wrap {
            text-align: center;
            margin-top: 10%;
        }

            .null_wrap img {
                width: 200px;");
                WriteLiteral(@"
            }



        /*基线*/
        .jx_btn {
            padding: 5px 15px;
            cursor: pointer;
            border: 1px solid #1E9FFF;
            border-radius: 20px;
        }

        .Menu_side {
            height: 92vh;
            position: fixed;
            right: -260px;
            top: 60px;
        }

        .nav_item {
            background-color: #fff;
            border: 1px solid #eee;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 10px;
        }

        .active {
            background: linear-gradient(70deg, #dcffff, #62ffff);
            border-color: transparent;
            color: #1E9FFF;
        }

        .form_name {
            padding-bottom: 15px;
        }

        .unselectable {
            -webkit-user-select: none; /* Safari */
            -moz-user-select: none; /* Firefox */
            -ms-user-select: none; /* Internet Explorer/Edge */
            user-select: none; /*");
                WriteLiteral(" 标准语法 */\r\n        }\r\n\r\n    </style>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "5cefed09d205937f95436b4e83df0ab59bc82094f14d3c6c2d8c9c50bb0f983b17565", async() => {
                WriteLiteral(@"
    <div class=""container"">
        <div class=""top-nav"">
            <div class=""layui-tab layui-tab-brief"" lay-filter=""docDemoTabBrief"">
                <ul class=""layui-tab-title"">
                    <li class=""layui-this"">CRF资料核查</li>
                    <li>修改历史</li>
                </ul>
            </div>
            <div class=""top_right"">
                <div class=""user-info"">
                    <div class=""user-avatar"">");
                Write(
#nullable restore
#line 202 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\CRFormTraceabilityNew\Index.cshtml"
                                              Model.DiseaseSpecificGroup.GroupName

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</div>\r\n                    <div class=\"user-avatar\">");
                Write(
#nullable restore
#line 203 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\CRFormTraceabilityNew\Index.cshtml"
                                              Model.PatientName

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("</div>\r\n                    <div class=\"user-name\">");
                Write(
#nullable restore
#line 204 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\CRFormTraceabilityNew\Index.cshtml"
                                             Model.ParticipatAge

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("岁</div>\r\n                    <div>");
                Write(
#nullable restore
#line 205 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\CRFormTraceabilityNew\Index.cshtml"
                          Model.Sex

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</div>
                </div>
                <div class=""project-info jx_btn"">
                    <span id=""JxBtn"">基线</span>
                </div>
            </div>
        </div>
        <div class=""main-content"">
            <div class=""left-panel"">
                <div class=""layui-tab-item layui-show"">
                    <div class=""panel-header"">CRF资料核查</div>
                    <div class=""panel-content"">

                    </div>
                </div>

            </div>
            <div class=""resizer"">
                <div class=""resize_btn unselectable"">⋮</div>
            </div>
            <div class=""right-panel"">
                <div class=""panel-header"">CRF 项目</div>
                <div class=""crf_btn_group"">
                    <div class=""status-bar"">
                        <div class=""status-item active"">存在质疑(X)</div>
                        <div class=""status-item"">待审核(X)</div>
                        <button class=""layui-btn"">全部审核</button>
              ");
                WriteLiteral("          <button class=\"layui-btn\">已审核</button>\r\n                        <button class=\"layui-btn layui-btn-danger\">质疑</button>\r\n");
                WriteLiteral("                    </div>\r\n                   \r\n                </div>\r\n\r\n                <div class=\"iframe_content\">\r\n                    <iframe id=\"reportingFrom\"");
                BeginWriteAttribute("src", " src=\"", 6769, "\"", 6775, 0);
                EndWriteAttribute();
                WriteLiteral(@" width=""99%"" height=""98%"">
                    </iframe>
                </div>
            </div>
        </div>
        <div id=""sideMenu"" class=""Menu_side content_L layui-card mui-table-view"">
            <div class=""layui-collapse"" id=""OA_task_1"">
                <div class=""collapse_list""></div>
            </div>
        </div>
    </div>

    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "5cefed09d205937f95436b4e83df0ab59bc82094f14d3c6c2d8c9c50bb0f983b21770", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "5cefed09d205937f95436b4e83df0ab59bc82094f14d3c6c2d8c9c50bb0f983b22981", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "5cefed09d205937f95436b4e83df0ab59bc82094f14d3c6c2d8c9c50bb0f983b24192", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script type=""text/javascript"">
        layui.use(['jquery', 'layer', 'form', 'element', 'code', 'laytpl', 'table'], function () {
            var layer = layui.layer,
                $ = layui.$,
                form = layui.form,
                laytpl = layui.laytpl,
                element = layui.element,
                table = layui.table;

            var JsonObj = {
                ""variables"": [
                    {
                        ""variable_name"": ""ALCOHOLHISTORY"",
                        ""变量描述"": ""饮酒史"",
                        ""value"": ""无"",
                        ""source"": ""无饮酒史。""
                    },
                    {
                        ""variable_name"": ""SURGERYHISTORYDESC"",
                        ""变量描述"": ""手术史具体描述"",
                        ""value"": """",
                        ""source"": ""否认手术、外伤史，否认输血史，否认食物过敏史，预防接种史不详。""
                    },
                    {
                        ""variable_name"": ""SURGERYHISTORY"",
                        ");
                WriteLiteral(@"""变量描述"": ""手术史"",
                        ""value"": ""无"",
                        ""source"": ""否认手术、外伤史，否认输血史，否认食物过敏史，预防接种史不详。""
                    },
                    {
                        ""variable_name"": ""SMOKINGYEARS"",
                        ""变量描述"": ""吸烟时长/年"",
                        ""value"": ""40"",
                        ""source"": ""吸烟40余年，平均1包／日，未戒烟。""
                    },
                    {
                        ""variable_name"": ""SMOKINGHISTORY"",
                        ""变量描述"": ""吸烟史"",
                        ""value"": ""持续吸烟"",
                        ""source"": ""吸烟40余年，平均1包／日，未戒烟。""
                    },
                    {
                        ""variable_name"": ""OTHER_PHYSICAL_EXAMINATION"",
                        ""变量描述"": ""其他体格检查描述"",
                        ""value"": ""神志清楚，查体合作。全身皮肤粘膜黄染，无紫绀，无皮疹、皮下出血。毛发分布正常，可见肝掌，皮下无水肿，无蜘蛛痣。全身浅表淋巴结未触及肿大。眼结膜黄染。双肺呼吸音清晰，未闻及干湿性啰音，无胸膜摩擦音。心率66次/分，律齐，A2>P2，各瓣膜听诊区未闻及杂音，无心包摩擦音。腹平坦，未见胃形、肠形，无腹壁静脉曲张，腹壁柔软，全腹无压痛、反跳痛，未触及包块，肝脾脏肋下未触及，墨菲氏征阴性。肠鸣音4次/分。"",
          ");
                WriteLiteral(@"              ""source"": ""神志清楚，查体合作。全身皮肤粘膜黄染，无紫绀，无皮疹、皮下出血。毛发分布正常，可见肝掌，皮下无水肿，无蜘蛛痣。全身浅表淋巴结未触及肿大。眼结膜黄染。双肺呼吸音清晰，未闻及干湿性啰音，无胸膜摩擦音。心率66次/分，律齐，A2&gt;P2，各瓣膜听诊区未闻及杂音，无心包摩擦音。腹平坦，未见胃形、肠形，无腹壁静脉曲张，腹壁柔软，全腹无压痛、反跳痛，未触及包块，肝脾脏肋下未触及，墨菲氏征阴性。肠鸣音4次/分。""
                    },
                    {
                        ""variable_name"": ""HISTORYDESC"",
                        ""变量描述"": ""既往病史具体描述"",
                        ""value"": ""“乙肝小三阳”病史20余年，未诊治。"",
                        ""source"": ""“乙肝小三阳”病史20余年，未诊治。""
                    },
                    {
                        ""variable_name"": ""HISTORY"",
                        ""变量描述"": ""既往病史"",
                        ""value"": ""有"",
                        ""source"": ""“乙肝小三阳”病史20余年，未诊治。""
                    },
                    {
                        ""variable_name"": ""OTHERHISTORY"",
                        ""变量描述"": ""过敏史"",
                        ""value"": ""无"",
                        ""source"": ""否认食物过敏史""
                    },
                    {
                     ");
                WriteLiteral(@"   ""variable_name"": ""NEUROPSYCHDISDESC"",
                        ""变量描述"": ""神经精神疾病描述"",
                        ""value"": """",
                        ""source"": ""否认糖尿病、脑血管疾病、精神疾病史，否认手术、外伤史，否认输血史，否认食物过敏史，预防接种史不详。""
                    },
                    {
                        ""variable_name"": ""NEUROPSYCHDIS"",
                        ""变量描述"": ""神经精神疾病"",
                        ""value"": ""无"",
                        ""source"": ""否认糖尿病、脑血管疾病、精神疾病史，否认手术、外伤史，否认输血史，否认食物过敏史，预防接种史不详。""
                    },
                    {
                        ""variable_name"": ""MARITALSTATUS"",
                        ""变量描述"": ""婚姻状况"",
                        ""value"": ""maritalStatusM"",
                        ""source"": ""适龄结婚,育有2女,配偶及女儿体健。""
                    },
                    {
                        ""variable_name"": ""INITIAL_DIAGNOSIS"",
                        ""变量描述"": ""初步诊断"",
                        ""value"": ""1.肝功能异常待查;2.慢性HBV携带；"",
                        ""source"": ""1.肝功能异常待查;2.慢性HBV携带；""
               ");
                WriteLiteral(@"     },
                    {
                        ""variable_name"": ""SYSTOLIC_BLOOD_PRESSURE"",
                        ""变量描述"": ""收缩压"",
                        ""value"": ""103"",
                        ""source"": ""BP:103/67mmHg。""
                    },
                    {
                        ""variable_name"": ""IMMUNEDISDESC"",
                        ""变量描述"": ""免疫系统疾病具体描述"",
                        ""value"": """",
                        ""source"": ""“乙肝小三阳”病史20余年，未诊治。""
                    },
                    {
                        ""variable_name"": ""HEART_RATE"",
                        ""变量描述"": ""心率"",
                        ""value"": ""66"",
                        ""source"": ""P:66次／分;""
                    },
                    {
                        ""variable_name"": ""DIASTOLIC_BLOOD_PRESSURE"",
                        ""变量描述"": ""舒张压"",
                        ""value"": ""67"",
                        ""source"": ""BP:103/67mmHg。""
                    },
                    {
                   ");
                WriteLiteral(@"     ""variable_name"": ""DESCRIPTION_CONDITION"",
                        ""变量描述"": ""病情描述"",
                        ""value"": ""缘于入院前15天无明显诱因出现持续性中上腹闷胀，进食后加重，活动后可稍缓解，伴皮肤瘙痒、尿黄、嗳气，无恶心、呕吐，无腹痛、腹泻，无呕血、排黑便，无腰痛、尿频、尿急、尿痛，无皮疹、双下肢水肿等不适。"",
                        ""source"": ""缘于入院前15天无明显诱因出现持续性中上腹闷胀，进食后加重，活动后可稍缓解，伴皮肤瘙痒、尿黄、嗳气，无恶心、呕吐，无腹痛、腹泻，无呕血、排黑便，无腰痛、尿频、尿急、尿痛，无皮疹、双下肢水肿等不适。""
                    },
                    {
                        ""variable_name"": ""CIGARETTESPERDAY"",
                        ""变量描述"": ""吸烟包数/日"",
                        ""value"": ""1"",
                        ""source"": ""吸烟40余年，平均1包／日，未戒烟。""
                    },
                    {
                        ""variable_name"": ""CHECK_RESULTS2"",
                        ""变量描述"": ""检查结果2"",
                        ""value"": ""全腹部CT平扫+增强:1、肝门区不规则浸润性病变，考虑为恶性肿瘤，胆管癌可能性大，伴肝内胆管多发扩张，肝门区多发肿大淋巴结，建议结合MRI检查及复查。2、余肝内多发小肿，肝右叶小钙化灶;肝Ⅱ段血管瘤;肝左叶动脉期强化灶，考虑为异常灌注。3、慢性胆.炎可能。4、右肾小结石。5、前列腺增生。6、所摄入双肺肺气肿，双侧少量胸腔积液，建议结合相关检查。"",
                        ""source"": ""全腹部CT平扫+增强:1、肝门区不规则浸润");
                WriteLiteral(@"性病变，考虑为恶性肿瘤，胆管癌可能性大，伴肝内胆管多发扩张，肝门区多发肿大淋巴结，建议结合MRI检查及复查。2、余肝内多发小肿，肝右叶小钙化灶;肝Ⅱ段血管瘤;肝左叶动脉期强化灶，考虑为异常灌注。3、慢性胆.炎可能。4、右肾小结石。5、前列腺增生。6、所摄入双肺肺气肿，双侧少量胸腔积液，建议结合相关检查。""
                    },
                    {
                        ""variable_name"": ""CHECK_RESULTS1"",
                        ""变量描述"": ""检查结果1"",
                        ""value"": ""肝脏MRI平扫+增强+MRCP:1、肝门区浸润性病变，考虑胆管细胞癌可能，并多发肝内胆管梗阻性扩张;肝门区多发肿大淋巴结，部分为MT待除。2、肝Ⅱ段包膜下富血供病变，考虑为血管瘤;余肝内多发小囊肿可能。3、肝硬化伴再生结节。4、腹腔少量积液。5、扫及下腰椎椎体上缘异常强化灶，首先考虑终板骨软骨可能，MT待除。"",
                        ""source"": ""肝脏MRI平扫+增强+MRCP:1、肝门区浸润性病变，考虑胆管细胞癌可能，并多发肝内胆管梗阻性扩张;肝门区多发肿大淋巴结，部分为MT待除。2、肝Ⅱ段包膜下富血供病变，考虑为血管瘤;余肝内多发小囊肿可能。3、肝硬化伴再生结节。4、腹腔少量积液。5、扫及下腰椎椎体上缘异常强化灶，首先考虑终板骨软骨可能，MT待除。""
                    },
                    {
                        ""variable_name"": ""CHECK_NAME2"",
                        ""变量描述"": ""检查名称2"",
                        ""value"": ""全腹部CT平扫+增强"",
                        ""source"": ""全腹部CT平扫+增强:1、肝门区不规则浸润性病变，考虑为恶性肿瘤，胆管癌可能性大，伴肝内胆管多发扩张，肝门区多发肿大淋巴结，建议结合MRI检查及复查。""
                    },
   ");
                WriteLiteral(@"                 {
                        ""variable_name"": ""CHECK_NAME1"",
                        ""变量描述"": ""检查名称1"",
                        ""value"": ""肝脏MRI平扫+增强+MRCP"",
                        ""source"": ""肝脏MRI平扫+增强+MRCP:1、肝门区浸润性病变，考虑胆管细胞癌可能，并多发肝内胆管梗阻性扩张;肝门区多发肿大淋巴结，部分为MT待除。""
                    },
                    {
                        ""variable_name"": ""CARDIODISDESC"",
                        ""变量描述"": ""心血管系统疾病具体描述"",
                        ""value"": """",
                        ""source"": ""否认高血压、心脏病史，否认糖尿病、脑血管疾病、精神疾病史，否认手术、外伤史，否认输血史，否认食物过敏史，预防接种史不详。""
                    },
                    {
                        ""variable_name"": ""CARDIODIS"",
                        ""变量描述"": ""心血管系统疾病"",
                        ""value"": ""无"",
                        ""source"": ""否认高血压、心脏病史，否认糖尿病、脑血管疾病、精神疾病史，否认手术、外伤史，否认输血史，否认食物过敏史，预防接种史不详。""
                    },
                    {
                        ""variable_name"": ""BODY_TEMPERATURE"",
                        ""变量描述"": ""体温"",
                        ");
                WriteLiteral(@"""value"": ""36.5℃"",
                        ""source"": ""T:36.5℃;P:66次／分;R:19次／分;BP:103/67mmHg。""
                    },
                    {
                        ""variable_name"": ""IMMUNEDIS"",
                        ""变量描述"": ""免疫系统疾病"",
                        ""value"": ""无"",
                        ""source"": ""否认高血压、心脏病史，否认糖尿病、脑血管疾病、精神疾病史，否认手术、外伤史，否认输血史，否认食物过敏史，预防接种史不详。""
                    },
                    {
                        ""variable_name"": ""TREATMENT_OPTIONS"",
                        ""变量描述"": ""治疗方案"",
                        ""value"": ""肝门部胆管根治术"",
                        ""source"": ""科内讨论或MDT会诊，经讨论后制定如下治疗方案:肝门部胆管根治术。""
                    }
                ]
            };
            var isSwiping = true;
            //患者列表的高度
            function setPanelH() {
                var winH = $(window).height();
                var topNavH = $("".top-nav"").height();
                var titleH = $("".panel-header"").height();
                var panel_H = winH - (topNavH + titleH) - 70 + ""p");
                WriteLiteral(@"x"";
                $("".panel-content"").css(""height"", panel_H);

                var CRFBtn = $("".crf_btn_group"").height();
                var iframe_H = winH - (topNavH + titleH + CRFBtn) - 70 + ""px"";
                $("".iframe_content"").css(""height"", iframe_H);
            };
            setPanelH();

            $(document).ready(function () {
                getFormLit();
                if (firstId != undefined) {
                    var targetLi = $('li[data-id=""' + firstId + '""]');
                    // 模拟点击事件
                    targetLi.trigger('click');
                    setTimeout(function () {
                        document.getElementById('JxBtn').click();
                    }, 2000);
                }
            });

            function getFormLit() {
                var patientId = '");
                Write(
#nullable restore
#line 464 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\CRFormTraceabilityNew\Index.cshtml"
                                  Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"';
                $.get({
                    url: '/Demo/CRFormTraceabilityNew/GetCRFList?ResearchPatientId=' + patientId, // 你的请求URL
                    async: false, // 设置为false以使请求同步执行
                    success: function (res) {
                        if (res.code == 0) {
                            var htm = """";
                            $("".layui-collapse"").html("""");
                            $.each(res.data, function (index, item) {

                                htm += `<div class=""layui-colla-item"">
                                                        <h2 class=""layui-colla-title"">`+ item.title + `</h2>

                                                        <div class=""layui-colla-content layui-show"">

                                                            <ul class=""navMenu layui-card-body"" lay-filter=""navMenu"">`;
                                var childHtm = """";

                                $.each(item.children, function (index1, child) {
                ");
                WriteLiteral(@"                    // console.log(index1);
                                    if (child.formId ==""13e1fa87369b49dfa61528cd24be0c54"") {
                                        firstId = child.id;
                                    }
                                    childHtm += `<li class=""nav_item mui-table-view-cell"" data-businessDomain=""` + child.businessDomain + `"" data-id=""` + child.id + `"" data-formId=""` + child.formId + `"" data-formName=""` + child.title + `"">
                                                                    <div class=""mui-slider-handle"">
                                                                        <p class=""form_name"">`+ child.title + `</p>
                                                                        <div class=""layui-progress"" lay-showPercent=""true"">
                                                                            <div class=""layui-progress-bar layui-bg-blue"" lay-percent=""`+ child.per + `""></div>
                                        ");
                WriteLiteral(@"                                </div>
                                                                    </div>
                                                                </li>`;
                                });

                                htm += childHtm + `</ul>
                                                        </div>
                                                    </div>`;

                            })

                            htm = ` <div class=""side_menu_title"">
                                                <div>基线</div>
                                                </div>` + htm;

                            $("".layui-collapse"").append(htm);
                            element.render('collapse');
                            // 渲染进度条组件
                            element.render('progress');

                            //报表切换事件
                            $("".navMenu"").on(""click"", "".nav_item"", function () {
                                $('.panel");
                WriteLiteral(@"-content').html("""");
                                $("".nav_item"").removeClass(""active"");
                                $(this).addClass(""active"");
                                var randVersion = Math.random();

                                formId = $(this).attr(""data-formId"");
                                id = $(this).attr(""data-id"");
                                //和之前的表单的业务域不一样才重新调接口
                                //if (businessDomain != $(this).attr(""data-businessDomain""))
                                //{
                                //    businessDomain = $(this).attr(""data-businessDomain"")
                                //    getTree(businessDomain);
                                //}

                                var url = """);
                Write(
#nullable restore
#line 528 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\CRFormTraceabilityNew\Index.cshtml"
                                             $"{ViewBag.formUrl}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\" + formId + \"");
                Write(
#nullable restore
#line 528 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\CRFormTraceabilityNew\Index.cshtml"
                                                                                  $".form?token={ViewBag.token}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"&version="" + randVersion;

                                $(""#reportingFrom"").attr(""src"", url);

                                if (isSwiping) {
                                    document.getElementById('JxBtn').click();
                                }
                                var patientId = '");
                Write(
#nullable restore
#line 535 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\CRFormTraceabilityNew\Index.cshtml"
                                                  Model.Id

#line default
#line hidden
#nullable disable
                );
                WriteLiteral("\';\r\n\r\n                                var DelayTime = parseInt(\'");
                Write(
#nullable restore
#line 537 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\CRFormTraceabilityNew\Index.cshtml"
                                                           ViewBag.CRFDelayMinTime

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"', 10);
                                if (formId == ""13e1fa87369b49dfa61528cd24be0c54""
                                    || formId == ""13e1fa87369b49dfa61528cd24be0c54""
                                    || formId == ""9180f2c1a6af406091fa7623583a2805""
                                    || formId ==""baba446e1bf2453c98d89c4c41202581"") {
                                    DelayTime = parseInt('");
                Write(
#nullable restore
#line 542 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Demo\Views\CRFormTraceabilityNew\Index.cshtml"
                                                           ViewBag.CRFDelayMaxTime

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"', 10);
                                }
                                setTimeout(function () {
                                    loadData(patientId, id);
                                }, DelayTime);
                                var formName = $(this).attr(""data-formName"");
                            })
                        }
                    }
                });
            }

            function loadData(ResearchPatientId, formId) {
                var url = ""/Demo/CRFormTraceabilityNew/LoadFormData?ResearchPatientId="" + ResearchPatientId + ""&CRFormId="" + formId;
                $.get(url, function (res) {
                    var iframe = document.getElementById('reportingFrom');
                    if (res.code == 0) {
                        JsonObj = eval('(' + res.aIExtractJson + ')');
                        console.log(JsonObj);
                        //  var iframe = document.getElementById('reportingFrom');
                        // 发送消息到子页面
                ");
                WriteLiteral(@"        iframe.contentWindow.postMessage({ action: ""show"", data: res.data }, ""*"");

                        if (res.dataSourceZBXXList) {
                            LoadSourceTable(1,res.dataSourceZBXXList);
                        }
                        if (res.dataSourceJCList) {
                            LoadSourceTable(2,res.dataSourceJCList);
                        }
                        if (res.dataSourceJYList) {
                           LoadSourceTable(1,res.dataSourceJYList);
                        }
                        if (res.dataSourceBLList) {
                            LoadSourceTable(2,res.dataSourceBLList);
                        }
                    }
                    else {
                        // 发送消息到子页面
                        iframe.contentWindow.postMessage({ action: ""show"" }, ""*"");
                    }
                    $(""#model_wrapR"").val(res.tips);

                })

            }

            function LoadSourceTable(type,dat");
                WriteLiteral(@"a) {
                if (type == 1) {
                    var getTpl = StructuredModel.innerHTML;
                    laytpl(getTpl).render(data, function (html) {
                        $('.panel-content').append(html);
                    });
                        for (var i = 0; i < data.length; i++) {
                            debugger;
                            var No = i + 1;
                            var clos = data[i].columns;
                            var columnsAll = new Array();
                            var columnLength = 100;
                            if (clos.length <= 5) columnLength = 200;
                            for (var j = 0; j < clos.length; j++) {
                                var col = {};
                                col['title'] = clos[j];
                                col['field'] = clos[j];
                                col['width'] = columnLength;
                                //把这个对象添加到列集合中
                                columnsAll.");
                WriteLiteral(@"push(col);
                            };
                            var tabledata = eval('(' + data[i].json + ')');
                            console.log(tabledata);
                            console.log(columnsAll);
                            console.log('#sourcetable_' + data[i].type + ""_"" + No);

                            table.render({
                                elem: '#sourcetable_' + data[i].type + ""_""+ No
                                , id: 'sourcetable_' + data[i].type + ""_"" + No
                                , page: false
                                , limit: Number.MAX_VALUE // 数据表格默认全部显示
                                , cols: [columnsAll]
                                , data: tabledata
                            });
                        }
                }
                else {
                    var getTpl2 = NoStructurModel.innerHTML;
                    laytpl(getTpl2).render(data, function (html) {
                        $('.panel-content').app");
                WriteLiteral(@"end(html);
                    });
                }
            }

            $(window).resize(function () {
                setPanelH();
            });


            // 定义一个函数，接受variable_name作为参数
            function getSourceByVariableName(variableName) {
                var node = JsonObj.variables.find(function (item) {
                    return item.variable_name === variableName;
                });
                return node ? node.source + ""$$$"" + node.value : ""NotFound"";
            }

             // 父窗口
            window.addEventListener('message', function (event) {
                if (event.data.action === 'show') {
                    var fieldname = event.data.fieldname;
                    var searchStringAll = getSourceByVariableName(fieldname);
                    var searchStringArray = $(searchStringAll.split('$$$'));

                    var sourceList = $("".record-section"");
                    sourceList.each(function (index, element) {
                 ");
                WriteLiteral(@"       var textContent = $(this).html();
                        if (textContent.includes('<span class=""currentMouse"" style=""color:red; font-weight:bold;"">')) {
                            textContent = textContent.replace('<span class=""currentMouse"" style=""color:red; font-weight:bold;"">', """")
                                        .replace(""</span>"", """");
                        }
                        // 检查textContent是否包含特定字符串
                        if (searchStringAll != ""NotFound"") {
                            if (textContent.includes(searchStringArray[0])) {
                                textContent = textContent.replace(searchStringArray[0], '<span class=""currentMouse"" style=""color:red; font-weight:bold;"">' + searchStringArray[0] + '</span>');
                            }
                            else if (textContent.includes(searchStringArray[1])){
                                textContent = textContent.replace(searchStringArray[1], '<span class=""currentMouse"" style=""color:red; ");
                WriteLiteral(@"font-weight:bold;"">' + searchStringArray[1] + '</span>');
                            }

                            //// 假设目标元素的class是'my-class'
                            //var $element = $('.currentMouse');

                            //// 滚动到该元素的位置，如果它在屏幕外，它会滚动到视口内
                            //$('html, body').animate({
                            //    scrollTop: $element.offset().top
                            //}, 'slow');
                        }
                        else {

                        }
                        $(this).html(textContent);


                    });


                    // addbyzolf 自动滚动到定位位置 2024-10-30
                    var targetElement = document.querySelector('.currentMouse');
                    // 滚动到该元素的位置
                    if (targetElement) {
                        targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }

            }, false);


            $('#JxBtn')");
                WriteLiteral(@".click(function () {
                if ($('.Menu_side').css('right') === '5px') {
                    $('.Menu_side').animate({ right: '-260px' }, 'slow');
                } else {
                    $('.Menu_side').animate({ right: '5px' }, 'fast');
                }
            });

        });
    </script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"

<script id=""StructuredModel"" type=""text/html"">
    {{#  if(d.length > 0){ }}
    {{#  for (var i=0;i<d.length;i++) {}}
    <div class=""record-section"">
        <p>
            <strong>{{ d[i].type}}—{{ d[i].title}}：</strong>
            <table id=""sourcetable_{{d[i].type}}_{{i+1}}"" lay-filter=""sourcetable_{{d[i].type}}_{{i+1}}""></table>
        </p>
    </div>
    {{# } }}
    {{# } }}
</script>

<script id=""NoStructurModel11"" type=""text/html"">
    {{#  if(d.length > 0){ }}
    {{#  for (var i=0;i<d.length;i++) {}}
    <div class=""record-section"">
        <p>
            <strong>{{ d[i].title }}：</strong>{{d[i].json}}
        </p>
    </div>
    {{# } }}
    {{# } }}
</script>

<script id=""NoStructurModel"" type=""text/html"">
    {{#  if(d.length > 0){ }}
    {{#  for (var i=0;i<d.length;i++) {}}
    <div class=""record-section"">
        <p><strong>{{ d[i].title }}：</strong></p>
        {{#  for (var j=0;j<d[i].json.length;j++) {}}
    <div class=""subsection-header"">{{d[i].json");
            WriteLiteral("[j].Name}}：</div>\r\n    <pre style=\"color: #666; font-size: 14px; line-height: 25px; font-family: \'Microsoft YaHei\', sans-serif; \">\r\n        {{ d[i].json[j].Value}}\r\n    </pre>\r\n        {{# } }}\r\n    </div>\r\n    {{# } }}\r\n    {{# } }}\r\n</script>\r\n\r\n</html>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<AngelwinResearch.Models.ResearchPatient> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
