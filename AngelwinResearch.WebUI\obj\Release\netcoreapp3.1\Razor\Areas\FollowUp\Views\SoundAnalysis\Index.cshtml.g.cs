#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\FollowUp\Views\SoundAnalysis\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "643c9f112ae6217cf839e5a5bd3522f9a0c3d0f2a0d00b4d03eeebc95cbb56a3"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_FollowUp_Views_SoundAnalysis_Index), @"mvc.1.0.view", @"/Areas/FollowUp/Views/SoundAnalysis/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"643c9f112ae6217cf839e5a5bd3522f9a0c3d0f2a0d00b4d03eeebc95cbb56a3", @"/Areas/FollowUp/Views/SoundAnalysis/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_FollowUp_Views_SoundAnalysis_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/jquery/dist/jquery.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("audio"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/mp3/fjsl.mp3"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/vform/vue.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/vform/element-ui_2.15.7.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/vform/VFormDesigner.umd.min.js?t=20210730"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\FollowUp\Views\SoundAnalysis\Index.cshtml"
  
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"en\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "643c9f112ae6217cf839e5a5bd3522f9a0c3d0f2a0d00b4d03eeebc95cbb56a37418", async() => {
                WriteLiteral(@"
    <meta http-equiv=""Content-Type"" content=""text/html; charset=utf-8"" />
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <meta http-equiv=""X-UA-Compatible"" content=""ie=edge"">
    <title>随访转写</title>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "643c9f112ae6217cf839e5a5bd3522f9a0c3d0f2a0d00b4d03eeebc95cbb56a37980", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "643c9f112ae6217cf839e5a5bd3522f9a0c3d0f2a0d00b4d03eeebc95cbb56a39182", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "643c9f112ae6217cf839e5a5bd3522f9a0c3d0f2a0d00b4d03eeebc95cbb56a310384", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        .audio-player {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

            .audio-player .audio-box {
                background-color: #d0f3c7;
                padding: 5px;
                border-radius: 4px;
                display: inline-flex;
                align-items: center;
                width: 500px;
            }

            .audio-player .audio-btn {
                width: 20px;
                height: 20px;
                background-color: #fff;
                border: 1px solid #ccc;
                border-radius: 50%;
                margin-left: 10px;
                position: relative;
                cursor: pointer;
            }

                .audio-player .audio-btn::after {
                    content: """";
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
          ");
                WriteLiteral(@"          width: 6px;
                    height: 10px;
                    background-color: #000;
                    border-radius: 2px;
                }

                .audio-player .audio-btn.playing::after {
                    width: 10px;
                    height: 10px;
                    background-color: #fff;
                    border-radius: 50%;
                }

            .audio-player .duration {
                font-size: 14px;
                color: #999;
                margin-left: 10px;
            }

        .line_wrap {
            display: flex;
            flex-direction: row;
            align-items: center;
        }

        .line_between {
            justify-content: space-between
        }
    </style>

");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "643c9f112ae6217cf839e5a5bd3522f9a0c3d0f2a0d00b4d03eeebc95cbb56a314058", async() => {
                WriteLiteral(@"
    <div class=""layui-col-md12"">
        <div class=""layui-card"">
            <div class=""audio-player"">
                <div class=""line_wrap line_between"" style=""width:98%;"">
                    <div class=""line_wrap"">
                        <label class=""el-form-item__label"" style=""width: 150px; line-height: 60px; font-size: 20px;"">随访录音：</label>
                        <div class=""audio-box"" style=""justify-content: space-between;"">
                            <div class=""duration"">00:00</div>
                            <div class=""audio-btn""></div>
                            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("audio", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "643c9f112ae6217cf839e5a5bd3522f9a0c3d0f2a0d00b4d03eeebc95cbb56a314961", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
                            <div class=""total-duration"">00:00</div>
                        </div>
                        <div class=""layui-input-block"">
                            <button class=""layui-btn"" id=""submitBtn"" style=""display:none;"">信息提取</button>
                            <button class=""layui-btn"" id=""reset"">重写</button>
                        </div>
                    </div>

                    <button id=""popbtn"" type=""button"" class=""layui-btn layui-bg-blue"">
                        <i class=""layui-icon layui-icon-survey""></i>
                    </button>
                </div>
            </div>


            <div class=""layui-card-body"">
                <div class=""layui-row"" style=""height: 85vh;"">                    
                    <div class=""layui-col-md6"" style=""background-color: #fff;height: 95%;padding:1px 10px;"">
                        <div style=""font-size: 25px;"">
                            <pre style=""white-space: pre; text-align: left;"">随访回填表单：</pre");
                WriteLiteral(@">
                        </div>
                        <pre id=""btnloading"" style=""overflow-y: auto; border: 1px solid #ccc; height: 100%; width: 100%; line-height: 40px; font-size: 20px; resize: none ""></pre>
                    </div>
                    <div class=""layui-col-md6"" style=""background-color: #fff;height: 95%; padding:1px 10px;"">
                        <div style=""font-size: 25px;"">
                            <pre style=""white-space: pre; text-align: left;"">CRF表单报告：</pre>
                        </div>
                        <iframe id=""rptframe"" width=""100%"" height=""100%""></iframe>
                    </div>
                </div>
            </div>
        </div>


        <div id=""popwrap"" style=""display:none;height:100%;overflow:hidden;"">
            <div class=""layui-row layui-col-space30"" style=""height:100%"">
                <div class=""layui-col-md6"" style=""height:100%"">
                    <div style=""background-color: #fff;height: 100%; padding:15px 15px;"">
    ");
                WriteLiteral(@"                    <textarea id=""model_wrapR"" style=""overflow-y: auto; border: 1px solid #ccc; height: 100%; width: 100%; line-height: 40px; font-size: 20px; resize: none"">
                        </textarea>
                    </div>
                </div>
                <div class=""layui-col-md6"" style=""height:100%"">
                    <div style=""background-color: #fff;height: 100%; padding:15px 15px;"">
                        <textarea id=""model_wrapL"" class=""layui-textarea"" style="" height:100%;width:100%;line-height:40px;font-size:20px;resize:none""></textarea>
                    </div>

                </div>
            </div>


        </div>
        <script type=""text/javascript"">
            if (!!window.ActiveXObject || ""ActiveXObject"" in window) { //IE load polyfill.js for Promise
                var scriptEle = document.createElement(""script"");
                scriptEle.type = ""text/javascript"";
                scriptEle.src = ""/vform/6.23.0_polyfill.min.js""
               ");
                WriteLiteral(" document.body.appendChild(scriptEle)\r\n            }\r\n        </script>\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "643c9f112ae6217cf839e5a5bd3522f9a0c3d0f2a0d00b4d03eeebc95cbb56a319524", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "643c9f112ae6217cf839e5a5bd3522f9a0c3d0f2a0d00b4d03eeebc95cbb56a320652", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "643c9f112ae6217cf839e5a5bd3522f9a0c3d0f2a0d00b4d03eeebc95cbb56a321780", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(" <!-- 根据Web服务器或CDN路径修改 -->\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "643c9f112ae6217cf839e5a5bd3522f9a0c3d0f2a0d00b4d03eeebc95cbb56a322934", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"


        <script>
            layui.config({
                base: '/layuiadmin/layuiextend/'
            }).use(['layer', 'laydate', 'table', 'form'], function () {
                var layer = layui.layer
                    , table = layui.table//表格
                    , laydate = layui.laydate
                    , $ = layui.$
                    , form = layui.form;


                // 假设这是您的文本变量
                var textArea = ""你好，我是福建省立医院信息管理中心黄基啊，我要今天打了个电话呢，是要回访一下你啊，请问你是林兰吗？我是。我这里有几个问题需要这边。随访一下你医院，完医院后，你最近的身体状况怎么样？有没有出现新的症状或不适啊？我出院完之后，我身体整体感觉还可以，但是有时候会感觉到有一点点头痛。明白了。那你是否有没有有按时服用我们开的药呢？有没有出现任何吃完这些药有没有出现不良反应或者副作用啊。我都有按时吃药，目前没有发现什么不良反应。嗯，那我最后再问一个你那个服完药了以后，最后你的您的睡眠和饮食习惯有没有改变，睡眠充足吗？我睡眠情况还可以，但是因为最近工作压力有点大，所以有时候我心里会有点焦虑。有时候可能会有这。一点点影响到我睡眠。好感谢你今天的配合啊，后面我们还有一些需要随访的信息网，可能还要随时打扰到你。好的好的。"";
                $(""#model_wrapR"").val(textArea);

                var source;
                var modelText = """";
                getModelText();
                $(""#popbtn"").on(""click"", function () {
    ");
                WriteLiteral(@"                layer.open({
                        type: 1,
                        area: ['60%', '80%'],
                        resize: false,
                        shadeClose: true,
                        title: '帮助',
                        content: $(""#popwrap""),
                        success: function () {
                            $(""#model_wrapL"").val(modelText);
                        }
                    })
                })



                // 播放按钮点击事件处理函数
                function toggleAudio() {
                    if (audio.paused) {
                        audio.play();
                        audioBtn.classList.add('playing');
                    } else {
                        audio.pause();
                        audioBtn.classList.remove('playing');
                    }
                }

                // 获取音频元素
                var audio = document.getElementById('audio');
                var durationDisplay = document.querySelector('.duration')");
                WriteLiteral(@";
                var totalDurationDisplay = document.querySelector('.total-duration');

                // 播放按钮点击事件处理函数
                var k = 0;
                function toggleAudio() {
                    if (audio.paused) {
                        audio.play();
                        audioBtn.classList.add('playing');

                        if (k == 0) {
                            if (source) {
                                source.close();
                            }
                            submitBtn(modelText);
                            var _isDis = $(this).hasClass(""layui-btn-disabled"")
                            if (_isDis) {
                                return false;
                            } else {
                                $(this).addClass(""layui-btn-disabled"")
                            }
                        }
                        k = k + 1;

                    } else {
                        audio.pause();
                        aud");
                WriteLiteral(@"ioBtn.classList.remove('playing');
                    }
                }

                // 格式化时间显示
                function formatTime(time) {
                    var minutes = Math.floor(time / 60);
                    var seconds = Math.floor(time % 60);
                    return ('0' + minutes).slice(-2) + ':' + ('0' + seconds).slice(-2);
                }

                // 音频元素加载完成事件处理函数
                function handleLoadedMetadata() {
                    var duration = audio.duration;
                    //durationDisplay.textContent = formatTime(duration);
                    totalDurationDisplay.textContent = formatTime(duration);
                }

                // 音频元素时间更新事件处理函数
                function handleTimeUpdate() {
                    var currentTime = audio.currentTime;
                    durationDisplay.textContent = formatTime(currentTime);
                }

                // 获取播放按钮元素
                var audioBtn = document.querySelector('.audio-btn')");
                WriteLiteral(@";

                // 绑定播放按钮点击事件
                audioBtn.addEventListener('click', toggleAudio);

                // 监听音频元素的加载完成事件
                audio.addEventListener('loadedmetadata', handleLoadedMetadata);

                // 监听音频元素的时间更新事件
                audio.addEventListener('timeupdate', handleTimeUpdate);

                // 监听音频元素的加载完成事件
                audio.addEventListener('loadedmetadata', handleLoadedMetadata);

                // 页面加载时获取音频时长并更新显示
                handleLoadedMetadata();

                //获取文本信息
                function getModelText() {
                    var fileURL = '/model/FollowUpBackfillInfo.txt';
                    // 创建一个XMLHttpRequest对象
                    var xhr = new XMLHttpRequest();
                    // 发送GET请求以获取文件内容
                    xhr.open('GET', fileURL, true);
                    // 设置响应类型为文本
                    xhr.responseType = 'text';
                    // 当请求完成时执行的回调函数
                    xhr.onload = function () {
");
                WriteLiteral(@"                        if (xhr.status === 200) {
                            modelText = xhr.responseText;
                            console.log('文件内容已存储到modelText变量中：', modelText);
                        } else {
                            console.error('无法获取文件内容：', xhr.statusText);
                        }
                    };
                    // 发送请求
                    xhr.send();
                }

                $(document).ready(function () {
                    $(document).on('click', '#submitBtn', function () {
                        if (source) {
                            source.close();
                        }
                        submitBtn(modelText);

                        var _isDis = $(this).hasClass(""layui-btn-disabled"")
                        if (_isDis) {
                            return false;
                        } else {
                            $(this).addClass(""layui-btn-disabled"")
                        }
                    });
");
                WriteLiteral("\r\n                    $(document).on(\'click\', \'#reset\', function () {\r\n                        var url = \"");
                Write(
#nullable restore
#line 311 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\FollowUp\Views\SoundAnalysis\Index.cshtml"
                                     $"{ViewBag.formUrl}ebc75c9b87c643bfa4ce0f2ca0051608.form?token={ViewBag.token}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@""";
                        $(""#rptframe"").attr(""src"", url);
                        if (source) {
                            source.close();
                        }
                        submitBtn(modelText);

                    });
                });

                function submitBtn(modelText) {
                    var currentDate = new Date();
                    var year = currentDate.getFullYear();
                    var month = currentDate.getMonth() + 1;
                    var day = currentDate.getDate();

                    var variable = $(""#model_wrapR"").val();
                    
                    var prompt = "" 录音文本：“"" + variable + ""”。你好，根据录音文本以对话的方式进行展示。请用全部用中文回答。"";
                    if (prompt == """") {
                        layer.msg(""请输入报告描述"");
                        $(""#message-to-send"").focus();
                        return;
                    }
                    var i = 0;
                    source = new EventSource('/vform/GetChatStreamAnsw");
                WriteLiteral(@"er?&prompt=' + encodeURIComponent(prompt));
                    source.onmessage = function (event) {
                        var result = JSON.parse(event.data);
                        if (result.okMsg) {
                            var btnloading = document.getElementById(""btnloading"");
                            if (i == 0) {
                                var Info = result.data;
                                btnloading.innerHTML = Info;
                            }
                            else {
                                var Info = result.data;
                                btnloading.innerHTML += Info;
                            }
                            i = i + 1;
                        }
                        else {
                            layer.msg(result.errorMsg);
                            source.close();
                        }
                    };

                    source.addEventListener('end', function (event) {
                       ");
                WriteLiteral(@" $(""#submitBtn"").removeClass(""layui-btn-disabled"");
                        var result = JSON.parse(event.data);
                        if (result.okMsg) {
                            loadData();
                            var btnloading = document.getElementById(""btnloading"");
                            let content = btnloading.innerHTML;
                            content = content.replace(""有点焦虑"", ""<span style='color:red; font-weight:bold;'>有点焦虑</span>"");
                            content = content.replace(""影响到我睡眠。"", ""<span style='color:red; font-weight:bold;'>影响到我睡眠。</span>"");
                            content = content.replace(""影响到我的睡眠。"", ""<span style='color:red; font-weight:bold;'>影响到我的睡眠。</span>"");
                            content = content.replace(""睡眠情况还可以"", ""<span style='color:red; font-weight:bold;'>睡眠情况还可以</span>"");
                            content = content.replace(""有一点点头痛"", ""<span style='color:red; font-weight:bold;'>有一点点头痛</span>"");
                            content = c");
                WriteLiteral(@"ontent.replace(""按时吃药"", ""<span style='color:red; font-weight:bold;'>按时吃药</span>"");
                            content = content.replace(""没有发现什么不良反应"", ""<span style='color:red; font-weight:bold;'>没有发现什么不良反应</span>"");
                            btnloading.innerHTML = content;
                            layui.each(result.newContent, function (idx, item) {

                            });
                        }
                        else {
                            layer.msg(result.errorMsg);
                        }

                        // 结束事件源连接
                        source.close();
                    }, false);

                    source.onerror = function (event) {
                        source.close();
                    };
                }
                 var url = """);
                Write(
#nullable restore
#line 388 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\FollowUp\Views\SoundAnalysis\Index.cshtml"
                              $"{ViewBag.formUrl}ebc75c9b87c643bfa4ce0f2ca0051608.form?token={ViewBag.token}"

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@""";
                $(""#rptframe"").attr(""src"", url);

                    function loadData() {
                        // 获取当前日期
                        const now = new Date();
                        // 提取年、月、日
                        const year = now.getFullYear();
                        const month = ('0' + (now.getMonth() + 1)).slice(-2); // 月份是从0开始的，所以加1
                        const day = ('0' + now.getDate()).slice(-2);
                        // 组合日期
                        var time = `${year}-${month}-${day}`;

                        var data = ""{\""variables\"":[{\""variable_name\"":\""FUDATE\"",\""value\"":\"""" + time + ""\"",\""source\"":\""\""},{\""variable_name\"":\""PATIENTNAME\"",\""value\"":\""林兰\"",\""source\"":\""\""},{\""variable_name\"":\""FUWAY\"",\""value\"":\""电话\"",\""source\"":\""\""},{\""variable_name\"":\""SYMPTOM\"",\""value\"":\""头痛头晕\"",\""source\"":\""\""},{\""variable_name\"":\""OTHERSYMPTOM\"",\""value\"":\""\"",\""source\"":\""\""},{\""variable_name\"":\""MEDICATIONCOMPLIANCE\"",\""value\"":\""规律\"",\""source\"":\""\""},{\""variable_n");
                WriteLiteral(@"ame\"":\""ADVERSEDRUGREACTIONS\"",\""value\"":\""无\"",\""source\"":\""\""},{\""variable_name\"":\""ADVERSEDRUGREACTIONSNAME\"",\""value\"":\""\"",\""source\"":\""\""},{\""variable_name\"":\""SLEEPSTATUS\"",\""value\"":\""睡眠情况还可以。有点焦虑，影响到我睡眠。\"",\""source\"":\""\""},{\""variable_name\"":\""DIET\"",\""value\"":\""\"",\""source\"":\""\""}]}"";
                        var iframe = document.getElementById('rptframe');
                        iframe.contentWindow.postMessage({ action: ""show"", data: data }, ""*"");
                }

                //固定列表高度
                function tableheight() {
                    var winH = $(window).height();
                    var serH = $("".ser_div"").outerHeight(true);
                    $("".layui-table-main"").css(""height"", (winH - serH - 180) + ""px"");
                }


            });
        </script>
    </div>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
