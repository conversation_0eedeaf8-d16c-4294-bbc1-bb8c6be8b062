#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\sample\sampleIndex.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "b139f6da3bc8b5a183bbf845e9be95f2345f5a819a7d48852034d0342ebed154"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_sample_sampleIndex), @"mvc.1.0.view", @"/Views/sample/sampleIndex.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI

#nullable disable
    ;
#nullable restore
#line 2 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"b139f6da3bc8b5a183bbf845e9be95f2345f5a819a7d48852034d0342ebed154", @"/Views/sample/sampleIndex.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"a5bcd83cf27c8ffb8baf597d24314352bf7b52b5a9bb5ee83e83e9d0089a628e", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_sample_sampleIndex : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/querybuilder/css/bootstrap.min.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("fm_import"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("action", new global::Microsoft.AspNetCore.Html.HtmlString(""), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\sample\sampleIndex.cshtml"
  
    ViewBag.Title = "样本";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "b139f6da3bc8b5a183bbf845e9be95f2345f5a819a7d48852034d0342ebed1547046", async() => {
                WriteLiteral("\r\n    <meta charset=\"utf-8\">\r\n    <title> ");
                Write(
#nullable restore
#line 9 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\sample\sampleIndex.cshtml"
             ViewBag.SiteTitle

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</title>
    <meta name=""renderer"" content=""webkit"">
    <meta http-equiv=""X-UA-Compatible"" content=""IE=edge,chrome=1"">
    <meta name=""viewport""
          content=""width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0"">
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "b139f6da3bc8b5a183bbf845e9be95f2345f5a819a7d48852034d0342ebed1547935", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "b139f6da3bc8b5a183bbf845e9be95f2345f5a819a7d48852034d0342ebed1549137", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        .layui-form-label{
            width:130px;
        }
        .layui-input-block{
            margin-left:150px;
        }
        .layui-input-inline {
            margin-left: 20px;
        }
        .linewrap {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }
        .wrap {
            padding: 15px;
        }
        .experimenter_text {
            text-indent: 2em;
        }
        .valcolor {
            color: blue;
            font-weight: bold;
        }



        /* 偶数行背景色 */
        .layui-table[lay-even] tr:nth-child(even) {
            /* background-color: #aaffaa; */
            background-color: #eeffee;
        }

        /* 鼠标指向表格时,奇数行背景颜色 */
        .layui-table tbody tr:hover, .layui-table-hover {
            background-color: #eeffee;
        }
  
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "b139f6da3bc8b5a183bbf845e9be95f2345f5a819a7d48852034d0342ebed15411962", async() => {
                WriteLiteral(@"
    <div class=""layui-col-md12"">
        <div class=""layui-card"">
            <div class=""layui-card-body layui-form linewrap"">
                <div>
                    <div class=""layui-inline"">
                        <div class=""layui-input-inline"" style=""width:300px;"">
                            <input type=""text"" class=""layui-input"" name=""keyWord"" placeholder=""请输入病历号/患者姓名"" id=""keyWord"" />
                        </div>
                    </div>
                    <div class=""layui-inline"">
                        <button class=""layui-btn layui-bg-cyan"" id=""Search2"">查询</button>
                    </div>
                </div>
                <div class=""layui-inline"">
                    <button class=""layui-btn layui-btn-normal fr"" id=""addSample"" style=""background-color: #5792c6 "">新增</button>
                </div>
            </div>
        </div>
        <div class=""layui-card-body"">
            <table id=""tablelist"" lay-filter=""tablelist""></table>
            <script type=""t");
                WriteLiteral(@"ext/html"" id=""tableBar1"">
                <a class=""layui-btn layui-btn-normal layui-btn-xs"" lay-event=""redact"" style=""text-decoration: none; background-color: #5792c6 ""><i class=""layui-icon""></i>编辑</a>
                <a class=""layui-btn layui-btn-warm layui-btn-xs"" lay-event=""del"" style=""text-decoration:none""><i class=""layui-icon""></i>删除</a>
            </script>
        </div>
    </div>




    <div");
                BeginWriteAttribute("class", " class=\"", 2952, "\"", 2960, 0);
                EndWriteAttribute();
                WriteLiteral(" id=\"form_window_add\" style=\"display: none;\">\r\n        <div");
                BeginWriteAttribute("class", " class=\"", 3020, "\"", 3028, 0);
                EndWriteAttribute();
                WriteLiteral(" style=\"padding:15px 15px 15px 0\">\r\n            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "b139f6da3bc8b5a183bbf845e9be95f2345f5a819a7d48852034d0342ebed15414205", async() => {
                    WriteLiteral(@"


                <div class=""layui-form-item"">
                    <label class=""layui-form-label"">病历号:</label>
                    <div class=""layui-input-inline"" style=""width:76%"">
                        <input type=""text"" class=""layui-input"" name=""emrID"" placeholder=""请输入病厉号"" id=""emrID"" value=""3500496532"" />
                    </div>
                    <button class=""layui-btn layui-btn-normal"" style=""background-color: #5792c6"">检索</button>
                </div>

                <div class=""layui-form-item"">
                    <label class=""layui-form-label"">患者姓名:</label>
                    <div class=""layui-input-block"">
                        <input type=""text"" class=""layui-input"" name=""userName""");
                    BeginWriteAttribute("placeholder", " placeholder=\"", 3839, "\"", 3853, 0);
                    EndWriteAttribute();
                    WriteLiteral(@" id=""userName"" value=""郑桂香"" readonly=""true"" />
                    </div>
                </div>

                <div class=""layui-form-item"">
                    <label class=""layui-form-label"">患者年龄:</label>
                    <div class=""layui-input-block"">
                        <input type=""text"" class=""layui-input"" name=""userAge""");
                    BeginWriteAttribute("placeholder", " placeholder=\"", 4199, "\"", 4213, 0);
                    EndWriteAttribute();
                    WriteLiteral(@" id=""userAge"" value=""71"" readonly=""true"" />
                    </div>
                </div>

                <div class=""layui-form-item"">
                    <label class=""layui-form-label"">所在科室:</label>
                    <div class=""layui-input-block"">
                        <input type=""text"" class=""layui-input"" name=""userSix""");
                    BeginWriteAttribute("placeholder", " placeholder=\"", 4557, "\"", 4571, 0);
                    EndWriteAttribute();
                    WriteLiteral(@" id=""userSix"" value=""重症医学三科"" readonly=""true"" />
                    </div>
                </div>
                <div class=""layui-form-item"">
                    <label class=""layui-form-label"">样本编号:</label>
                    <div class=""layui-input-block"">
                        <input type=""text"" class=""layui-input"" name=""keySample"" placeholder=""请输入样本编号"" id=""keySample""  value=""20240229ygk"" readonly=""true""/>
                    </div>
                </div>

            ");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n        </div>\r\n    </div>\r\n\r\n\r\n\r\n    <div");
                BeginWriteAttribute("class", " class=\"", 5114, "\"", 5122, 0);
                EndWriteAttribute();
                WriteLiteral(@" id=""form_experimenter"" style=""display: none;"">
        <div class=""wrap"" >
            <p class=""experimenter_text"">当前发现适合科研目标患者<span class=""valcolor"">郑桂香</span>
                ，来自<span class=""valcolor"">重症医学三科</span>,是否留存当前样本采样？
            </p>
            <p class=""experimenter_text"">样本编号:<span class=""valcolor"">20240229ygk</span></p>
        </div>
    </div>
















    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "b139f6da3bc8b5a183bbf845e9be95f2345f5a819a7d48852034d0342ebed15418874", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "b139f6da3bc8b5a183bbf845e9be95f2345f5a819a7d48852034d0342ebed15419998", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
        layui.use(['element', 'layer', 'table', 'laydate', 'form'], function () {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , laydate = layui.laydate
                , $ = layui.$
                , form = layui.form;



            layer.ready(function () {
                openExperimenter();
            });

            function openExperimenter() {
                var openExp = layer.open({
                    skin: 'alert-skin',
                    type: 1,
                    title: '发现科研目标患者',
                    area: ['400px', '300px'],
                    resize: true,
                    content: $('#form_experimenter'),
                    btn: ['留存样本', '取消'],
                    yes: function (index, layero) {
                        console.log(""留存样本成功"")
                        layer.close(openExp);
                   
                    }
                    ,");
                WriteLiteral(@" btn2: function (index, layero) {
                        layer.close(openExp);
                    }

                })
            };

            function openSample() {

                var windowsIndex = layer.open({
                    skin: 'alert-skin',
                    type: 1,
                    title: '构建样本信息',
                    area: ['950px', '600px'],
                    resize: true,
                    content: $('#form_window_add'),
                    btn: ['保存', '取消'],
                    yes: function (index, layero) {
                        console.log(""保存成功"")
                        layer.close(windowsIndex);
                    }
                    , btn2: function (index, layero) {
                        layer.close(windowsIndex);
                    },
                    success: function () {
                       
                    }

                });

            }



            table.render({
                elem: '#tablelist'
");
                WriteLiteral(@"                , id: 'tablelist'
                , url: '/PatientDiscoveryManage/PatientDiscovery/GetData'
                , page: true
                , limit: 20
                , height: 'full-130'
                , cols: [[
                    { field: 'No', title: '序号', width: 60, type: 'numbers' }
                    , { field: 'BLH', title: '病历号', width: 100 }
                    , { field: 'HZXM', title: '患者姓名', width: 120 }
                    , { field: 'NL', title: '年龄', width: 80 }
                    , { field: 'CWDM', title: '床位代码', width: 100 }
                    , { field: 'KSDM', title: '科室代码', width: 100 }
                    , { field: 'KSMC', title: '科室名称', width: 100 }
                    , { field: 'BQDM', title: '病区代码', width: 100 }
                    , { field: 'BQMC', title: '病区名称', width: 100 }
                    , { field: 'ZXKSDM', title: '执行科室', maxWidth: 100 }
                    , { field: 'YLZDM', title: '医疗组代码', width: 100 }
                    , { field: ");
                WriteLiteral(@"'YLZMC', title: '医疗组名称', width: 100 }
                    , {
                        title: '操作',
                        width: 160,
                        fixed: 'right',
                        toolbar: '#tableBar1'
                    }
                ]],
                	done : function (res, curr, count) {

                tableList = res.data;
                $('th').css({ 'background-color': '#5792c6', 'color': '#fff', 'font-weight': 'bold' })
    
            }

            });

            //监听tablelist工具条
            table.on('tool(tablelist)', function (obj) {
                var data = obj.data;
                if (obj.event === 'redact') {
                    openSample()
                }


            })




            $(document).on('click', '#addSample', function () {
                openSample();
            });


        })
    </script>






");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html >\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
