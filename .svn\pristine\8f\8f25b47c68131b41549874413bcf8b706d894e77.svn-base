﻿@{
    ViewBag.Title = "更新日志";
    Layout = null;
}

<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>更新日志</title>
    <link href="~/layuiadmin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/layuiadmin/style/admin.css" rel="stylesheet" />

    <style>
        .layui-layer {
            background-color: #fff;
            border: 1px solid #eee;
            border-radius: 4px;
        }

        .layui-layer-title {
            padding: 0 80px 0 20px;
            height: 50px;
            line-height: 50px;
            border-bottom: 1px solid #F0F0F0;
            font-size: 14px;
            color: #333;
            overflow: hidden;
            border-radius: 2px 2px 0 0;
        }

        .layui-layer-content {
            height: 206px;
            padding: 10px;
            padding-left: 20px;
            overflow: auto;
        }

        .layui-layer-wrap li {
            padding: 6px 0;
            list-style: disc;
        }
    </style>
</head>
<body>
    <div class="layui-layer" id="layui-layer1"
         type="page" times="1" showtime="0" contype="object" style="z-index: 19891015;position: fixed; top: 36px;right:20px; margin-right: 10px;">
        <div class="layui-layer-title" style="cursor: move;">更新目录</div>
        <div id="" class="layui-layer-content">
			<ul class="site-dir layui-layer-wrap" style="display: block;">
				<li><a href="#2025-07-18"><cite>2025-07-18</cite></a></li>
				<li><a href="#2025-07-17"><cite>2025-07-17</cite></a></li>
				<li><a href="#2025-07-14"><cite>2025-07-14</cite></a></li>
				<li><a href="#2025-07-03"><cite>2025-07-03</cite></a></li>
				<li><a href="#2025-06-24"><cite>2025-06-24</cite></a></li>
				<li><a href="#2025-06-11"><cite>2025-06-11</cite></a></li>
				<li><a href="#2025-06-04"><cite>2025-06-04</cite></a></li>
				<li><a href="#2025-05-28"><cite>2025-05-28</cite></a></li>
				<li><a href="#2025-05-22"><cite>2025-05-22</cite></a></li>
				<li><a href="#2025-05-20"><cite>2025-05-20</cite></a></li>
				<li><a href="#2025-05-16"><cite>2025-05-16</cite></a></li>
				<li><a href="#2025-05-13"><cite>2025-05-13</cite></a></li>
				<li><a href="#2025-05-11"><cite>2025-05-11</cite></a></li>
				<li><a href="#2025-04-29"><cite>2025-04-29</cite></a></li>
				<li><a href="#2025-04-25"><cite>2025-04-25</cite></a></li>
				<li><a href="#2025-04-22"><cite>2025-04-22</cite></a></li>
				<li><a href="#2025-04-17"><cite>2025-04-17</cite></a></li>
				<li><a href="#2025-04-13"><cite>2025-04-13</cite></a></li>
				<li><a href="#2025-04-10"><cite>2025-04-10</cite></a></li>
				<li><a href="#2025-03-21"><cite>2025-03-21</cite></a></li>
				<li><a href="#2025-03-11"><cite>2025-03-11</cite></a></li>
				<li><a href="#2025-02-20"><cite>2025-02-20</cite></a></li>
				<li><a href="#2025-01-13"><cite>2025-01-13</cite></a></li>
				<li><a href="#2024-12-20"><cite>2024-12-20</cite></a></li>
				<li><a href="#2024-12-09"><cite>2024-12-09</cite></a></li>
				<li><a href="#2024-12-02"><cite>2024-12-02</cite></a></li>
				<li><a href="#2024-11-19"><cite>2024-11-19</cite></a></li>
				<li><a href="#2024-11-15"><cite>2024-11-15</cite></a></li>
				<li><a href="#2024-11-11"><cite>2024-11-11</cite></a></li>
				<li><a href="#2024-11-07"><cite>2024-11-07</cite></a></li>
				<li><a href="#2024-11-06"><cite>2024-11-06</cite></a></li>
				<li><a href="#2024-11-01"><cite>2024-11-01</cite></a></li>
				<li><a href="#2024-10-31"><cite>2024-10-31</cite></a></li>
				<li><a href="#2024-10-25"><cite>2024-10-25</cite></a></li>
				<li><a href="#2024-10-18"><cite>2024-10-18</cite></a></li>
				<li><a href="#2024-10-11"><cite>2024-10-11</cite></a></li>
				<li><a href="#2024-10-08"><cite>2024-10-08</cite></a></li>
				<li><a href="#2024-09-20"><cite>2024-09-20</cite></a></li>
				<li><a href="#2024-09-11"><cite>2024-09-11</cite></a></li>
				<li><a href="#2024-09-09"><cite>2024-09-09</cite></a></li>
				<li><a href="#2024-09-02"><cite>2024-09-02</cite></a></li>
				<li><a href="#2024-08-23"><cite>2024-08-23</cite></a></li>
				<li><a href="#2024-08-09"><cite>2024-08-09</cite></a></li>
				<li><a href="#2024-08-16"><cite>2024-08-16</cite></a></li>
				<li><a href="#2024-08-01"><cite>2024-08-01</cite></a></li>
				<li><a href="#2024-07-26"><cite>2024-07-26</cite></a></li>
				<li><a href="#2024-07-18"><cite>2024-07-18</cite></a></li>
				<li><a href="#2024-07-17"><cite>2024-07-17</cite></a></li>
				<li><a href="#2024-07-09"><cite>2024-07-09</cite></a></li>
				<li><a href="#2024-07-05"><cite>2024-07-05</cite></a></li>
				<li><a href="#2024-07-03"><cite>2024-07-03</cite></a></li>
			</ul>
        </div>
        <span class="layui-layer-setwin"></span>
        <span class="layui-layer-resize"></span>
    </div>
    <div class="layui-card">
		<ul class="layui-timeline" style="padding-top:20px;padding-left:20px;">
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-07-18"></a>2025-07-18</span>
					</h3>
					<ul>
						<li>1.急诊创伤损伤部位与下面不同部位隐藏/显示联动。急诊创伤与创伤指数联动；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-07-17"></a>2025-07-17</span>
					</h3>
					<ul>
						<li>1.CMIS数据详情新增数据的创建日期，添加时间范围筛选条件；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-07-14"></a>2025-07-14</span>
					</h3>
					<ul>
						<li>1.增加全院CRF相关页面；</li>
						<li>2.CMIS前结构化AI转写增加日志；前端AI转写结果换行处理</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-07-03"></a>2025-07-03</span>
					</h3>
					<ul>
						<li>1.增加语音病历文书转写界面；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-06-24"></a>2025-06-24</span>
					</h3>
					<ul>
						<li>1.新增全院级CRF详细数据页面；</li>
						<li>2.增加全院CRF心脏瓣膜病历文书转写；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-06-11"></a>2025-06-11</span>
					</h3>
					<ul>
						<li>1.患者管理：没有填写过表单的患者点击删除添加提示；</li>
						<li>2.CMIS前结构化：进一步调整细节优化prompt提示词token数量；</li>
						<li>3.基于anyreport数据库实现CRF表单基础字段和说明的导入功能；</li>
						<li>4.anyreport表单样式优化：字体重叠问题和下拉框底色和边框样式显示效果调整；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-06-04"></a>2025-06-04</span>
					</h3>
					<ul>
						<li>1、CMIS配置界面增加不包含空值配置选项;</li>
						<li>2、CMIS前结构化：AI报告转写根据配置提示词信息是否包含空值数据;</li>
						<li>2、CMIS前结构化：大模型出错时，页面抛出错误信息;</li>
						<li>3、用户管理批量导入功能调整：增加手机号和增加所属科室的导入;</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-05-28"></a>2025-05-28</span>
					</h3>
					<ul>
						<li>1、优化CMIS结构化配置页面，提示词改为直接在表单配置;</li>
						<li>2、修改提示词，把表单值和字段描述拼接一起，减少Token输入;</li>
						<li>3、CMIS数据详情：增加CRF表单对应报告的PDF信息展示;</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-05-22"></a>2025-05-22</span>
					</h3>
					<ul>
						<li>1、登陆后主界面直接是问答界面;</li>
						<li>2、登录页面logo换成医院logo;</li>
						<li>3、用户首次登录成功后，若是默认密码同登录名一致强制修改，方可继续使用;</li>
						<li>4、AI智能问答：增加深度思考模型的思考数据显示;</li>
						<li>5、AI智能问答：调整前端问答界面的显示效果;</li>
						<li>6、AI智能问答：针对多模态模型增加上传图片功能;</li>
						<li>
							7、CMIS批量采集任务：<br />
							&nbsp;&nbsp;7.1、数据排序为创建时间倒序;<br />
							&nbsp;&nbsp;7.2、检索条件增加执行状态:全部、已执行、未执行、执行异常;
						</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-05-20"></a>2025-05-20</span>
					</h3>
					<ul>
						<li>1、增加基于大模型的问答功能模块;</li>
						<li>2、用户管理;增加批量导入功能；</li>
						<li>3、CMIS数据详情调整：数据详情下载字段与值不匹配修改;</li>
						<li>4、AI问答：偶见报错问题处理;</li>
					</ul>
				</div>
			</li>

			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-05-16"></a>2025-05-16</span>
					</h3>
					<ul>
						<li>1、CMIS数据详情调整：新增数据类型查询条件，前结构化报告新增查看报告按钮;</li>
						<li>2、CMIS前结构化：增加对CMIS结构化数据类型的支持;</li>
						<li>3、CMIS后结构化：CMIS特征变量提取模块、批量提取作业，增加对CMIS结构化数据类型的支持;</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-05-13"></a>2025-05-13</span>
					</h3>
					<ul>
						<li>
							1、CMIS结构化报告调整：<br />
							&nbsp;&nbsp;1.1、CMIS数据详情展示字段根据CRFormFieldSets表的排序号进行排序；<br />
							&nbsp;&nbsp;1.2、CMIS数据详情代码优化；<br />
							&nbsp;&nbsp;1.3、CMIS批量提取特征变量查询时增加loading动画；<br />
						</li>
						<li>2、CMIS后结构化批量提取：批量提取作业出现卡顿不能往下执行问题处理；</li>
						<li>3、CMIS后结构化批量提取：创建超声甲状腺检查批量检查任务时偶见报错问题处理；</li>
						<li>4、CMIS前结构化：AI转写报告调整前结构化获取提示词的字段不对问题处理；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-05-11"></a>2025-05-11</span>
					</h3>
					<ul>
						<li>
							1、CMIS后结构化模块调整：<br />
							&nbsp;&nbsp;1.1、增加检查报告的诊断代码和诊断名称数据显示，增加根据诊断检索数据；<br />
							&nbsp;&nbsp;1.2、增加一键批量选择功能；<br />
							&nbsp;&nbsp;1.3、增加批量提取功能；<br />
							&nbsp;&nbsp;1.4、增加批量提取任务执行情况查看页面；<br />
							&nbsp;&nbsp;1.5、增加批量提取任务的作业逻辑；<br />
							&nbsp;&nbsp;1.6、CMIS数据详情：增加项目名称和申请单号的展示；
						</li>
						<li>2、CRF表单提取设置：增加表单字段的批量删除功能；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-04-29"></a>2025-04-29</span>
					</h3>
					<ul>
						<li>1、中医科经方推荐demo：同dify智能体对接，实现经方推荐demo；</li>
						<li>2、病历转写：按特定格式实现病历文书转写；</li>
						<li>3、中医科经方推荐demo：经方推荐多次对话显示问题调整，以及相关提示词的调整；</li>
						<li>4、菜单管理，当参数太长时报错问题处理；</li>
					</ul>
				</div>
			</li>

			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-04-25"></a>2025-04-25</span>
					</h3>
					<ul>
						<li>1、CMIS前结构化：从所有表单模版共用一套提示词修改为每个表单一个独立的提示词；</li>
					</ul>
				</div>
			</li>

			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-04-22"></a>2025-04-22</span>
					</h3>
					<ul>
						<li>1、CMIS前结构化转写病历文书，调用deepseek时，需要过滤思考过程的文本内容；</li>
						<li>2、CMIS后结构化演示demo调整：因CRF表单部分提取不到值，修改为默认的30个提取变量；</li>
					</ul>
				</div>
			</li>

			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-04-17"></a>2025-04-17</span>
					</h3>
					<ul>
						<li>1、批量检查特征提取：偶见无效的项目-变量名列；</li>
						<li>2、CRF字段导入功能：按顺序从hana数据库中获取列信息，增加排序号，调整修改逻辑；</li>
						<li>3、CRF表单提取设置：为表单设置表增加排序号、增加排序号的维护管理功能；</li>
						<li>4、批量检查特征提取:提取结果的展示顺序需要同crf表单的上下顺序一致；</li>
					</ul>
				</div>
			</li>

			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-04-13"></a>2025-04-13</span>
					</h3>
					<ul>
						<li>1、批量检查特征提取：为节约大模型返回token数，将大模型返回结果从json格式数据调整为csv格式数据；</li>
						<li>2、非结构化数据采集模块：大模型返回数据结构从标准json格式改成|隔开的csv格式，获取大模型返回结果后重新解析返回数据同crf表单对接；</li>
						<li>3、未信息化数据采集模块-病理jpg图片报告-AI分析提示词和返回格式，从标准json格式改成|隔开的csv格式，获取大模型返回结果后重新解析页面渲染成表格；</li>
						<li>4、批量检查特征提取：从数据获取的文本数据很多无效字符\u0000，导致提交给大模型的token数增多，需要替换数据中出来的无效字符；</li>
					</ul>
				</div>
			</li>

			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-04-10"></a>2025-04-10</span>
					</h3>
					<ul>
						<li>1、增加qwen2.5-72b模型，qwen2.5-vl-72b图片模型的支持，并可以根据配置切换不同的图片大模型；</li>
						<li>2、使用vs2022封装支持win7等低版本操作系统的webview，供CMIS系统调用；</li>
						<li>3、CMIS前结构化细节需求调整；</li>
						<li>4、CMIS后结构化细节需求调整；</li>
					</ul>
				</div>
			</li>

			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-03-21"></a>2025-03-21</span>
					</h3>
					<ul>
						<li>1、CMIS结构化设置页面开发：设置不同医技系统、检查部位、疾病编码不同的前后机构化CRF表单设置；</li>
						<li>2、医技前结构化页面开发(供CMIS调用)：根据CRF表单填写信息生成报告文书，提供一键复制功能；</li>
						<li>3、医技后结构化页面开发(供CMIS调用)：根据CMIS传入的报告信息提取关键特征变量填充CRF表单；</li>
						<li>4、CMIS前后结构化填写明细查看；</li>
						<li>5、CMIS后结构化：增加检查特征变量批量提取功能；</li>
					</ul>
				</div>
			</li>

			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-03-11"></a>2025-03-11</span>
					</h3>
					<ul>
						<li>1、DS智能体对接时，优化回复结果的展示样式，markdown改成合适的html显示，如数据表，标签等;</li>
						<li>2、调整内外网通道：利用医保稽核内外网通道，开发科研AI项目需要的查询院内hana数据库的接口；</li>
						<li>3、提示词维护管理：不同功能模块、不同大模型设置不同的提示词配置管理功能；</li>
						<li>3、提示词生成通用方法开发，将之前同AI对接的从硬编码提示词改成根据配置生成提示词；</li>
					</ul>
				</div>
			</li>


			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-02-20"></a>2025-02-20</span>
					</h3>
					<ul>
						<li>1、智能体管理功能开发：增加同智谱智能体对接功能，实现基于知识库智能问答；</li>
						<li>2、增加了DeepSeek知识库的支持；</li>
						<li>3、新增患者信息查询功能，可以根据住院号或姓名获取患者信息；</li>
					</ul>
				</div>
			</li>


			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2025-01-13"></a>2025-01-13</span>
					</h3>
					<ul>
						<li>1、智能体管理功能开发：增加同智谱智能体对接功能，实现生成图表；</li>
						<li>2、修正了切换语音识别时，切换完毕后自动执行语音转写病历或cf表单填充功能的bug；</li>
						<li>3、解决了在转写病历文书时停止录音，EventSource一直循环执行的问题；</li>
						<li>4、修正了线上HTTPS无法调用云知声接口的问题；</li>
					</ul>
				</div>
			</li>

			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-12-20"></a>2024-12-20</span>
					</h3>
					<ul>
						<li>1、增加语音角色分离功能；</li>
						<li>2、智能体调用功能、知识库调用功能；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-12-09"></a>2024-12-09</span>
					</h3>
					<ul>
						<li>1、增加智能体相关功能；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-12-02"></a>2024-12-02</span>
					</h3>
					<ul>
						<li>1、语音增加对云知声的支持；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-11-19"></a>2024-11-19</span>
					</h3>
					<ul>
						<li>1、因CRF表单系统域名调整，导致部分页面crf表单无法显示问题处理；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-11-15"></a>2024-11-15</span>
					</h3>
					<ul>
						<li>1、病理批量提取模块：页面功能开发；</li>
						<li>2、病理批量提取模块：获取内网病理数据API接口开发；</li>
						<li>3、语音实时转写功能发布上线后，实时转语音无效问题的处理；</li>
						<li>4、中医实时语音转写并转写病历文书和提取crf表单：语音处理和crf表单制作；</li>
						<li>5、康复科数据采集：历史语音记录增加删除功能；</li>
						<li>6、康复科数据采集：语音实时转写需要动态的将转写内容根据发言人实时的填写到对话框中；</li>
						<li>7、康复科数据采集：语音转写病历文书，AI返回结果显示效果调整；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-11-11"></a>2024-11-11</span>
					</h3>
					<ul>
						<li>1、数据采集模块：院内检查、病理数据表格化展示不便于溯源结果显示，调整为块状显示；</li>
						<li>2、发布后实时语音转写无法使用问题处理；</li>
						<li>3、康复科蒙特利尔认知评估量表自动绘图功能、自动统分功能的实现；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-11-07"></a>2024-11-07</span>
					</h3>
					<ul>
						<li>1、数据采集：表单支持数据源上传、表单修改历史查看；</li>
						<li>2、患者管理：增加根据身份证号自动提取患者信息，提取不到的信息手动录入；</li>
						<li>
							3、多中心功能开发：<br />
							&nbsp;&nbsp;3.1、增加多中心维护管理模块；<br />
							&nbsp;&nbsp;3.2、用户管理：增加用户所属多中心功能；<br />
							&nbsp;&nbsp;3.3、专病管理：增加参与该专病的多中心功能；<br />
							&nbsp;&nbsp;3.4、患者管理：增加患者所属多中心功能；<br />
							&nbsp;&nbsp;3.5、数据采集进度：增加区分数据所属多中心；<br />
							&nbsp;&nbsp;3.6、数据明细下载：增加区分数据所属多中心；
						</li>
						<li>4、新增康复科数据采集模块：支持语音、报告文件上传、语音转病历文书、语音提取CRF表单等功能；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-11-06"></a>2024-11-06</span>
					</h3>
					<ul>
						<li>1、提取字段过多，因AI返回结果不完整导致报错问题的处理；</li>
						<li>2、提取结果中的日期格式同CRF表单日期格式不一致问题的处理；</li>
						<li>3、AI提取结果时：调整prompt中报告结果的拼接方式优化多份报告提取结果的准确性；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-11-01"></a>2024-11-01</span>
					</h3>
					<ul>
						<li>1、采集任务模块：从数据采集跳转到批量采集任务页面，只显示当前患者的数据；</li>
						<li>2、数据采集模块：特定患者病理报告图片的显示；</li>
						<li>3、未信息化数据采集模块：调整提示词；</li>
						<li>4、任务管理模块：未显示科室问题处理；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-10-31"></a>2024-10-31</span>
					</h3>
					<ul>
						<li>1、溯源模块、数据采集模块：溯源结果定位到可见显示区域；</li>
						<li>2、科研组织管理细节显示调整；</li>
						<li>3、数据采集模块：检验溯源数据显示精简；</li>
						<li>4、数据采集页面：增加AI批量采集功能入口，并显示采集进度；</li>
						<li>5、数据采集和溯源模块：AI提取和分析的业务域增加病理的支持；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-10-25"></a>2024-10-25</span>
					</h3>
					<ul>
						<li>1、溯源原始数据显示一整块不便于阅读，调整显示效果方便阅读；</li>
						<li>2、病历文书转写模块存在转写病历文本和表单对照不上的问题处理；</li>
						<li>3、科研数据下载页面标题行冻结，避免拖动滚动条后看不到列头；</li>
						<li>4、数据采集模块：极端情况下存在溯源无效的问题处理；</li>
						<li>5、采集任务进度模块：优化显示效果；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-10-18"></a>2024-10-18</span>
					</h3>
					<ul>
						<li> 1、数据采集模块：增加溯源功能；</li>
						<li> 2、用户管理模块：增加自动采集按钮，实现一键自动采集CRF表单数据；</li>
						<li> 3、底层框架优化调整，增加自动作业自动采集数据；</li>
						<li> 4、任务管理模块开发：可以查看自动采集进度和自动采集情况；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-10-11"></a>2024-10-11</span>
					</h3>
					<ul>
						<li>
							1、CRF表单管理模块调整：<br />
							&nbsp;&nbsp;1.1、修复新增文件夹点击异常；<br />
							&nbsp;&nbsp;1.2、配置表单业务域时：选择多个时，优化频繁关闭需要多次点击的问题；
						</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-10-08"></a>2024-10-08</span>
					</h3>
					<ul>
						<li>1、处理欢迎页在低分辨率下显示的问题；</li>
						<li>2、表单总填报数量和已填报数量统计逻辑的调整好；</li>
						<li>3、优化数据采集树数据加载方式：增加loading、切换表单默认不勾选；</li>
						<li>4、溯源模块增加默认显示的表单；</li>
						<li>5、随访表单无法回填的问题原因分析，并处理该问题；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-09-20"></a>2024-09-20</span>
					</h3>
					<ul>
						<li>1、数据采集检查，检验数据根据来源系统，检验项目名称过滤数据；</li>
						<li>2、CRF溯源检查、检验数据进一步细分；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-09-11"></a>2024-09-11</span>
					</h3>
					<ul>
						<li>1、数据采集页面，检查、检验树列表子节点标题增加检查日期展示；</li>
						<li>2、点击表单时，清空原始数据表格，右侧自动收缩；</li>
						<li>3、修改数据采集页面布局，树列表变为可收缩DIV；</li>
						<li>4、检验原始数据列表增加定量结果、定性结果展示；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-09-09"></a>2024-09-09</span>
					</h3>
					<ul>
						<li>1、新增数据采集管理驾驶舱模块功能；</li>
						<li>2、新增医院临床科室管理功能；</li>
						<li>3、表单管理：增加表单数据业务域配置功能；</li>
						<li>4、科研机构管理：增加医学中心组成配置功能；</li>
						<li>5、表单变量提取设置：增加值范围设置功能；</li>
						<li>6、在crf表单管理、数据采集修改、数据下载等业务增加时间记录功能；</li>
						<li>7、数据采集模块调整：增加获取检验、检查、病历文书等数据，为AI自动填写表单提供数据源；</li>
						<li>8、数据源比对页面：引入科研基线，根据AI提取结果溯源；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-09-02"></a>2024-09-02</span>
					</h3>
					<ul>
						<li>1、用户管理模块功能开发；</li>
						<li>2、病理报告Al解析demo开发；</li>
						<li>3、CRF数据明细下载excel冻结标题行；</li>

					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-08-23"></a>2024-08-23</span>
					</h3>
					<ul>
						<li>1、CRF表单溯源比对功能模块开发；</li>
						<li>2、科研机构(医学中心、诊疗组、科室管理)管理模块开发；</li>
						<li>3、专病管理模块开发；</li>
						<li>
							4、CRF表单归属从科研机构调整到专病：<br />
							&nbsp;&nbsp;4.1、CRF表单管理页面调整，增加crf表单所属专病组功能；<br />
							&nbsp;&nbsp;4.2、表单填报页面管理，改为根据专病组出基线；<br />
							&nbsp;&nbsp;4.3、数据填报进度页面调整；<br />
							&nbsp;&nbsp;4.4、科研数据下载页面调整：<br />
							&nbsp;&nbsp;4.5、CRF表单提取设置页面调整，检索区域增加专病；
						</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-08-09"></a>2024-08-09</span>
					</h3>
					<ul>
						<li>1、数据采集：结构化数据采集模块开发；</li>
						<li>2、数据采集：未信息化数据采集模块开发；</li>
						<li>3、数据采集：非结构化数据采集模块开发；</li>
						<li>4、随访管理：AI随访语音分析模块开发；</li>
						<li>5、CRF表单转写病历文书模块开发；</li>
						<li>5、引入第三方溯源比对功能开发；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-08-01"></a>2024-08-01</span>
					</h3>
					<ul>
						<li>1、解决偶见登录后系统报错的问题；</li>
						<li>2、结构化信息自动填充CRF表单增加溯源功能；</li>
						<li>3、患者管理新增入组日期、患者卡号、住院号等患者信息；</li>
						<li>4、解决患者管理模块分页显示异常问题；</li>
						<li>5、Pad端主页增加退出登录功能；</li>
						<li>5、CRF表单回填一些异常问题处理；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-07-26"></a>2024-07-26</span>
					</h3>
					<ul>
						<li>1、开发院内外网通讯相关接口，从医院内网获取相关数据；</li>
						<li>2、院内人口学资料相关结构化数据获取，自动回填CRF表单；</li>
						<li>3、调整完善基础信息，便于信息自动回填；</li>
						<li>4、通过ticket自动登录到anyreport首页；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-07-18"></a>2024-07-18</span>
					</h3>
					<ul>
						<li>1、数据采集页面：调整隐藏图标，并优化展示效果；</li>
						<li>2、填报数据下载页面：无需填写的单元格设置变为灰色；</li>
						<li>3、数据采集进度页面：“无需填写” 前面加个小眼睛图标；</li>
						<li>4、CRF表单提取设置模块：增加医学中心的检索条件和信息展示；</li>
					</ul>
				</div>
			</li>
			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-07-17"></a>2024-07-17</span>
					</h3>
					<ul>
						<li>1、新增胸痛医学中心的CRF表单22个；</li>
						<li>2、新增麻醉医学中心的CRF表单12个；</li>
						<li>3、新增医学中心和专病组的权限控制功能，对不同医学中心和专病组的权限和数据进行隔离；</li>
						<li>4、填报模块增加CRF表单的隐藏和显示功能，以及相关模块数据显示和下载增加未填报和无需填报的区分；</li>
					</ul>
				</div>
			</li>

			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-07-09"></a>2024-07-09</span>
					</h3>
					<ul>
						<li>1、填报进度页面需要增加专病组的检索和展示，删除电话和身份证敏感信息；</li>
						<li>2、数据明细页面：检索和展示、下载Excel中增加专病组信息，删除电话和身份证敏感信息；</li>
						<li>3、解决部分CRF表单数据无法回显的问题；</li>
						<li>4、调整部分CRF表单展示效果；</li>
						<li>5、CRF表单管理模块：增加表单复制和移动功能；</li>
						<li>6、填报进度模块查看填报数据，增加提交功能；</li>
					</ul>
				</div>
			</li>

			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-07-05"></a>2024-07-05</span>
					</h3>
					<ul>
						<li>1、优化删除失败的提示词；</li>
						<li>2、优化填报明细数据下载模块明细数据列表显示、明细数据下载的排序方式；</li>
						<li>3、优化填报统计页面的排序方式；</li>
						<li>4、用户管理模块细节调整：减少新增用户的必填项，调整初始密码逻辑；</li>
						<li>5、需要根据使用场景来显示或隐藏CRF表单的提交按钮；</li>
					</ul>
				</div>
			</li>

			<li class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis"></i>
				<div class="layui-timeline-content layui-text">
					<h3 class="layui-timeline-title">
						更新日志
						<span class="layui-badge-rim"> <a name="2024-07-03"></a>2024-07-03</span>
					</h3>
					<ul>
						<li>
							1、患者管理界面调整：<br />
							&nbsp;&nbsp; 1.1、新建和修改患者弹层增加专病组选项；<br />
							&nbsp;&nbsp; 1.2、不显示身份证号、电话号码信息；增加显示专病组；
						</li>
						<li>
							2、填报页面：<br />
							&nbsp;&nbsp; 2.1、电话号码和身份证号隐私信息不显示、增加所属专病组显示；<br />
							&nbsp;&nbsp; 2.2、基线需要按设置的顺序排列；
						</li>
						<li>3、CRF表单管理模块：增加OrderBy排序功能：按OrderBy排序显示数据；</li>
						<li>4、新增专病组管理功能模块；</li>


					</ul>
				</div>
			</li>

		</ul>
    </div>

</body>
</html>
