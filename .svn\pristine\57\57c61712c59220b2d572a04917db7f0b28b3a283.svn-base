﻿@{
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>详细数据</title>

    <link href="~/layuiadmin/layui/css/layui.css" rel="stylesheet" />
    @*  <link href="~/layuiadmin/style/admin.css" rel="stylesheet" />*@

    <link href="~/querybuilder/css/bootstrap.min.css" rel="stylesheet" />
    <link href="~/querybuilder/css/chosen.min.css" rel="stylesheet" />
    <link href="~/querybuilder/css/awesome-bootstrap-checkbox.css" rel="stylesheet" />
    <link href="~/querybuilder/css/bootstrap-slider.min.css" rel="stylesheet" />
    <link href="~/querybuilder/css/selectize.bootstrap5.css" rel="stylesheet" />
    <link href="~/querybuilder/css/bootstrap-icons.min.css" rel="stylesheet" />
    <link href="~/querybuilder/css/query-builder.default.css" rel="stylesheet" />
    <script src="~/js/jquery-3.5.1.min.js"></script>
    <script src="~/js/common.js"></script>
    <script src="~/js/marked.min.js"></script>
    <style>
        .rectangle {
            width: 200px; /* 宽度 */
            float: left;
            margin-left: 20px;
            padding: 40px 15px;
        }

        .sex0 {
            color: #FF5722;
        }

        .sex1 {
            color: #1E9FFF;
        }
        /* 定义表头样式 */
        .table_header {
            font-weight: bold; /* 加粗 */
            font-size: 14px; /* 可选：调整字体大小以提升可读性 */
            color: #333; /* 可选：调整字体颜色 */
        }
    </style>
</head>

<body style="padding:5px;">
    <div class="layui-col-md12">
        <div class="layui-card">
            <div class="layui-card-body layui-form">
                <div class="layui-inline">
                    <div id="xmCRFIdsList" style="width:220px"></div>
                </div>
                <div class="layui-inline">
                    @*<label class="layui-form-label">数据类型:</label>*@
                    <div class="layui-input-inline">
                        <select name="modules" id="DataType" lay-search="">
                            <option value="" selected="selected">全部</option>
                            <option value="B">前结构化</option>
                            <option value="A">后结构化</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <input type="text" class="layui-input" name="keyWord" placeholder="请输入患者ID" id="keyWord" value="" />
                </div>
                <div class="layui-inline">
                    <input type="text" class="layui-input" id="test10" placeholder="创建日期" style=" width: 212px;">
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-normal fr" id="Search">查 &nbsp;&nbsp;询</button>
                    <button class="layui-btn fr" id="download">下 &nbsp;&nbsp;载</button>
                </div>
            </div>
            <div class="layui-card-body">
                <table id="tablelist" lay-filter="tablelist"></table>
                <script type="text/html" id="tableBar1">
                    {{# if(d.DataType == "前结构化"){ }}
                    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="show"><i class="layui-icon layui-icon-eye"></i>查看报告</a>
                    {{# } }}
                </script>
                <div class="layui-inline">
                    <span style="color:red;">注：“——”表示无需填写内容。</span>
                </div>
            </div>
            <div id="txtMedical" style="display:none; padding : 15px "></div>
        </div>
    </div>
    <script src="~/layuiadmin/layui/layui.js"></script>
    <script src="~/layuiadmin/layuiextend/xm-select.js"></script>
    <script src="~/js/jquery-3.5.1.min.js"></script>

    <script>
        layui.use(['element', 'layer', 'table', 'laydate', 'form'], function () {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                ,laydate = layui.laydate
                , $ = layui.$
                , form = layui.form;

            // 获取当前日期
            var currentDate = new Date();

            // 计算30天前的日期（复制当前日期对象，避免修改原对象）
            var startDate = new Date(currentDate);
            startDate.setDate(currentDate.getDate() - 30); // 自动处理跨月/跨年

            // 格式化为 YYYY-MM-DD 的字符串
            function formatDate(date) {
                var year = date.getFullYear();
                var month = date.getMonth() + 1; // 月份补0
                var day = date.getDate();        // 日期补0
                return year + '-' +
                    (month < 10 ? '0' + month : month) + '-' +
                    (day < 10 ? '0' + day : day);
            }

            // 生成范围
            var startDateStr = formatDate(startDate); // 30天前的日期
            var endDateStr = formatDate(currentDate);  // 当天日期

            laydate.render({
                elem: '#test10'
                , range: '~'
                , value: startDateStr + ' ~ ' + endDateStr // 设置默认值
            });

            var xmCRFIdsList = xmSelect.render({
                el: '#xmCRFIdsList',
                model: { label: { type: 'text' } },
                prop: {
                    name: 'title',
                    value: 'id',
                },
                minWidth: 200,
                height: 350,
                radio: true,
                filterable: true,
                clickClose: true,
                //树
                tree: {
                    show: true,
                    //非严格模式
                    strict: false,
                    //默认展开节点
                    expandedKeys: [-1],
                },
                data: []
            });
            function GetCRFIdsTree() {
                $.ajax({
                    url: '/CMIS/CMISDataDetails/GetCRFIdsList',
                    type: "post",
                    datatype: 'json',
                    success: function (result) {
                        xmCRFIdsList.update({
                            data: result
                        });
                        if (result[0].id) {
                            var arr = new Array();
                            arr.push(result[0].id);
                            xmCRFIdsList.setValue(arr);
                            setTimeout(function () {
                                SearchData();
                            }, 500);
                        }

                    }, error: function () {
                        layer.msg("获取失败！");
                    }
                })
            };

            $(document).ready(function () {
                GetCRFIdsTree();
                $(document).on('click', '#Search', function () {
                    SearchData();
                })

                $("#download").on("click", function () {
                    bdownload();
                });
            });



            function SearchData() {
                var indes = layer.load(1);
                var CRFId = xmCRFIdsList.getValue('valueStr');
                $.ajax({
                    type: 'post',
                    url: "/CMIS/CMISDataDetails/List?CRFId=" + CRFId + "&DataType=" + $("#DataType option:selected").val() + "&keyWord=" + $("#keyWord").val() + "&date=" + $("#test10").val(),
                    dataType: 'json',
                    contentType: "application/json; charset=utf-8",
                    success: function (result) {
                        if (result.okMsg) {
                            layer.close(indes);
                            if (result.columns[1] && Array.isArray(result.columns[1]) && result.columns[1].length > 0) {
                                result.columns[1].push({
                                    title: '<span class=\"table_header\">操作</span>',
                                    field: 'operation',
                                    toolbar: '#tableBar1',
                                    align: 'center',
                                    width: 100,
                                    fixed: 'right'
                                });
                            }
                            table.render({
                                elem: '#tablelist'
                                , id: 'tablelist'
                                , limit: Number.MAX_VALUE // 数据表格默认全部显示
                                , page: false
                                , cols: result.columns
                                , height: 'full-150' // 最大高度减去其他容器已占有的高度差
                                , data: result.list
                                , done: function (res, curr, count, origin) {
                                    // 找到class为NoNeedInputSpan的span元素
                                    $('.NoNeedInputSpan').closest('td').css('background-color', '#eee');
                                    element.render('progress');// 这里是延迟执行的代码

                                    $(".herfform").click(function () {

                                        var patientid = encodeURIComponent($(this).attr("patientid"));
                                        var docDetailedNo = encodeURIComponent($(this).attr("docDetailedNo"));
                                        var jsonstr = encodeURIComponent($(this).attr("jsonStr"));
                                        var formId = encodeURIComponent($(this).attr("formId"));
                                        var Fid = encodeURIComponent($(this).attr("fid"));
                                        //console.log(jsonstr);

                                        parent.layui.index.openTabsPage("/CMIS/CMISDataDetails/detail?json=" + jsonstr + "&formId=" + formId + "&ResearchPatientId=" + patientid + "&Fid=" + Fid + "&DocDetailedNo=" + docDetailedNo, "CRF表单详情");

                                    });
                                }
                            });
                        }
                        else {
                            layer.close(indes);
                            layer.msg(result.errorMsg);
                        }
                    },
                    error: function (data) {
                        layer.close(indes);
                    }
                });
            }

            function bdownload() {
                var keyWord = $("#keyWord").val();
                var CRFId = xmCRFIdsList.getValue('valueStr');
                var url = "/CMIS/CMISDataDetails/DownLoad?CRFId=" + CRFId + "&DataType=" + $("#DataType option:selected").val() + "&keyWord=" + $("#keyWord").val() + "&date=" + $("#test10").val();
                window.location.href = url;
            }

            //触发单元格工具事件
            table.on('tool(tablelist)', function (obj) { // 双击 toolDouble
                var data = obj.data;
                if (obj.event === 'show') {
                    var htmlContent = marked.parse(data.AIExtractJsonValue);
                    $("#txtMedical").html(htmlContent);

                    var div = $('#txtMedical');
                    div.scrollTop(div[0].scrollHeight - div.innerHeight());
                    layer.open({
                        type: 1,
                        title: '查看报告',
                        shadeClose: true, // 点击遮罩关闭层
                        area: ['45%', '65%'], // 设置弹窗大小
                        content: $("#txtMedical")
                    });
                }
            });
        });
    </script>


</body>



</html>
