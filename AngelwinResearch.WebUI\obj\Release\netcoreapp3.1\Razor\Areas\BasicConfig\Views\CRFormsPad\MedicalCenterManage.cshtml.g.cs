#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\CRFormsPad\MedicalCenterManage.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "494598782de5a119e83cd6014cadaffcb98713e08dde2ca9808b0748e2dd8e32"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_BasicConfig_Views_CRFormsPad_MedicalCenterManage), @"mvc.1.0.view", @"/Areas/BasicConfig/Views/CRFormsPad/MedicalCenterManage.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"494598782de5a119e83cd6014cadaffcb98713e08dde2ca9808b0748e2dd8e32", @"/Areas/BasicConfig/Views/CRFormsPad/MedicalCenterManage.cshtml")]
    #nullable restore
    internal sealed class Areas_BasicConfig_Views_CRFormsPad_MedicalCenterManage : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin\\layui\\font\\web_font\\iconfont.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/css/paddemo_style.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("type", new global::Microsoft.AspNetCore.Html.HtmlString("text/javascript"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\BasicConfig\Views\CRFormsPad\MedicalCenterManage.cshtml"
  
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "494598782de5a119e83cd6014cadaffcb98713e08dde2ca9808b0748e2dd8e326435", async() => {
                WriteLiteral("\r\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\r\n    <meta charset=\"utf-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n\t<title>医学中心管理</title>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "494598782de5a119e83cd6014cadaffcb98713e08dde2ca9808b0748e2dd8e326945", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "494598782de5a119e83cd6014cadaffcb98713e08dde2ca9808b0748e2dd8e328145", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "494598782de5a119e83cd6014cadaffcb98713e08dde2ca9808b0748e2dd8e329347", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n\t<style>\r\n\t\t.layui-form-select .layui-input{\r\n\t\t\tbackground-color:transparent;\r\n\t\t\tborder:none;\r\n\t\t\tcolor:#fff;\r\n\t\t\tpadding-left:25px;\r\n\t\t}\r\n\r\n\t\t.layui-form-select .layui-edge{\r\n\t\t\tborder-top-color:#fff;\r\n\r\n\t\t}\r\n\t</style>\r\n  ");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "494598782de5a119e83cd6014cadaffcb98713e08dde2ca9808b0748e2dd8e3211537", async() => {
                WriteLiteral("\r\n    <div class=\"wrap\">\r\n\r\n\r\n\t\t<header class=\"aui-navBar aui-navBar-fixed\">\r\n\t\t\t<div class=\"toptools layui-form\">\r\n\t\t\t\t<div class=\"search_wrap\">\r\n\t\t\t\t\t<div class=\"input_group\">\r\n\t\t\t\t\t\t<div class=\"layui-inline\">\r\n\t\t\t\t\t\t\t<div");
                BeginWriteAttribute("class", " class=\"", 970, "\"", 978, 0);
                EndWriteAttribute();
                WriteLiteral(" style=\"width:68vw;\">\r\n");
                WriteLiteral("\t\t\t\t\t\t\t\t<select name=\"interest\" lay-filter=\"aihao\" style=\"background-color:transparent;\">\r\n\t\t\t\t\t\t\t\t\t<option value=\"0\"");
                BeginWriteAttribute("selected", " selected=\"", 1163, "\"", 1174, 0);
                EndWriteAttribute();
                WriteLiteral(@">妇产儿保医学中心</option>
									<option value=""2"">麻醉医学中心</option>
									<option value=""3"">急诊医学中心</option>
									<option value=""4"" >老年医学中心</option>
									<option value=""5"">重症医学</option>
									<option value=""6"" >血液科</option>
									<option value=""7"">呼吸内科</option>
									<option value=""8"" >肠胃疾病医学中心</option>
									<option value=""1"">放射科</option>
								</select>
							</div>
						</div>
						<div class=""layui-input-inline"">
							<button class=""layui-btn layui-btn-primary"" id=""Search"">
								<i class=""layui-icon layui-icon-search""></i>
							</button>
						</div>
					</div>
				</div>

			</div>
		</header>
		<div style=""height:95px;""></div>

		<div class=""content_wrap"">
			<div class=""layui-card"">
				<div class=""layui-card-header""><i class=""layui-icon icon-shu""></i><span class=""card_title""></span>学科</div>
			
				<div class=""layui-card-body"">
					<div class=""layui-row"">

						<div class=""layui-col-xs4 layui-col-sm3 layui-col-md3"">
							<div class=""subj");
                WriteLiteral(@"ect_item"">
								<div class=""subject_icon button_bj4""><i class=""layui-icon icon-fuke""></i></div>
								<p>妇科</p>
							</div>
						</div>
						<div class=""layui-col-xs4 layui-col-sm3 layui-col-md3"">
							<div class=""subject_item"">
								<div class=""subject_icon button_bj4""><i class=""layui-icon icon-chanke""></i></div>
								<p>产科</p>
							</div>
						</div>
						<div class=""layui-col-xs4 layui-col-sm3 layui-col-md3"">
							<div class=""subject_item"">
								<div class=""subject_icon button_bj4""><i class=""layui-icon icon-Group81""></i></div>
								<p>儿童保健科</p>
							</div>
						</div>
						<div class=""layui-col-xs4 layui-col-sm3 layui-col-md3"">
							<div class=""subject_item"">
								<div class=""subject_icon button_bj4""><i class=""layui-icon icon-icon177""></i></div>
								<p>新生儿科</p>
							</div>
						</div>
						<div class=""layui-col-xs4 layui-col-sm3 layui-col-md3"">
							<div class=""subject_item"">
								<div class=""subject_icon button_bj4""><i class=""layu");
                WriteLiteral(@"i-icon icon-shengzhike""></i></div>
								<p>生殖科</p>
							</div>
						</div>

					</div>
					
				</div>
			
			</div>
			
			
			<div class=""layui-card"">
				<div class=""layui-card-header""><i class=""layui-icon icon-shu""></i><span class=""card_title""></span>专病库</div>
				<div class=""layui-card-body"">
					<div class=""specialty_disease_list"">
						<div class=""specialty_disease_item"">
							<div class=""sd_img button_bj2"">
								<i class=""layui-icon icon-chuangjianCRFbiaodanicon""></i>
							</div>
							<div class=""sd_info"">
								<div class=""sd_title text-limit-1-lines"">妊娠相关免疫性疾病专病库</div>
                                <div class=""sd_subtitle"">
                                    <h5>
                                        专病库简介：
                                    </h5>
                                    <p>
                                        育龄期女性是各种自身免疫性疾病的高发人群，由于机体自身免疫应答异常，攻击正常细胞，引起自身多系统功能障碍，导致不孕率和不良妊娠结局的发生率明显增加。本中心拟构建一个多学科合作的免疫性疾病妊娠女性及子代发育队列研究平台，系统收集和分析这类疾病患者");
                WriteLiteral(@"及其子代的临床数据、生物样本和随访信息，通过对数据的深入挖掘和分析，以发现新的疾病标志物、治疗靶点和预测模型，为疾病的精准医疗和个性化治疗奠定基础，努力创建此队列的数字疗法，并推动转化医学的发展。
                                    </p>
                                    <h5>入组标准：</h5>
                                        <p>
                                            1.既往已诊断自身免疫性疾病，包括抗磷脂综合征、系统性红斑狼疮、强直性脊柱炎、类风湿性关节炎、系统性硬化症、干燥综合征、未分化结缔组织病、遗传性易栓症等。
                                        </p>
                                        <p>2.病理妊娠史：</p>


                                        <p>
                                            2.1自然流产史（包括生化妊娠）≥1次，排除解剖、染色体和内分泌因素异常；
                                        </p>
                                        <p>
                                            2.2反复种植失败；
                                        </p>
                                        <p>
                                            2.3子痫前期病史，胎盘早剥、FGR病史；
                                        </p>
                                        <p>
                               ");
                WriteLiteral(@"             2.4胎盘功能不全所致1次或1次以上的胎儿形态学结构未见异常的早产病史（包括胎心监护提示胎儿低氧血症、脐动脉多普勒检测发现舒张末期血流缺失、羊水过少、出生体重在同胎龄平均体重的第10百分位数以下）。
                                        </p>
                                        <p>
                                            3.孕期反复不明原因阴道出血，巨大绒毛膜下血肿，反复皮炎。
                                        </p>
                                        <p>
                                            4.不明原因不孕症。
                                        </p>
                                        <p>
                                            以上情况签署知情同意书，自愿加入研究队列。
                                        </p>
                            
                                </div>
							</div>
						</div>
			
		
						
					</div>
				</div>
			</div>
		</div>

		<div style=""height:60px;""></div>
		<footer class=""aui-footer aui-footer-fixed"">
			<a href=""javascript:;"" class=""aui-tabBar-item aui-tabBar-item-active"" id=""yxzx"">
				<span class=""aui-tabBar-item-icon"">
					<i class=""icon");
                WriteLiteral(@" layui-icon icon-login_organization_icon""></i>
				</span>
				<span class=""aui-tabBar-item-text"">医学中心</span>
			</a>
			<a href=""javascript:;"" class=""aui-tabBar-item "" id=""bdgl"">
				<span class=""aui-tabBar-item-icon"">
					<i class=""icon layui-icon icon-biaodanguanli""></i>
				</span>
				<span class=""aui-tabBar-item-text"">eCRF</span>
			</a>
			<a href=""javascript:;"" class=""aui-tabBar-item "" id=""sjcj"">
				<span class=""aui-tabBar-item-icon"">
					<i class=""icon layui-icon icon-shujucaiji""></i>
				</span>
				<span class=""aui-tabBar-item-text"">数据采集</span>
			</a>
			<div class=""no-style aui-tabBar-item "">
				<span class=""aui-tabBar-item-icon"">
					<i class=""icon layui-icon icon-wode-copy""></i>
				</span>
				<span class=""aui-tabBar-item-text"">我的</span>
			</div>
		</footer>

    </div>

	");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "494598782de5a119e83cd6014cadaffcb98713e08dde2ca9808b0748e2dd8e3218899", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\t");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "494598782de5a119e83cd6014cadaffcb98713e08dde2ca9808b0748e2dd8e3220108", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
	<script type=""text/javascript"">
		layui.use(['jquery', 'layer', 'form', 'element', 'code', 'laytpl', 'table'], function () {
			var layer = layui.layer,
				$ = layui.$,
				form = layui.form,
				laytpl = layui.laytpl,
				element = layui.element,
				table = layui.table;

			$(document).ready(function () {
				$(""#yxzx"").on(""click"", function () {
					window.location.href = ""/BasicConfig/CRFormsPad/MedicalCenterManage"";
				})

				$(""#bdgl"").on(""click"", function () {
					window.location.href = ""/BasicConfig/CRFormsPad/Index"";
				})

				$(""#sjcj"").on(""click"", function () {
					window.location.href = ""/BasicConfig/CRFormsPad/DataProfiling"";
				})
				
			});
		


		})

	</script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
