﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace AngelwinResearch.Models
{
    // addbyzolf 20250620
    public partial class HospitalCRFData
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [MaxLength(20)]
        public string PatientId { get; set; }//患者Id
        [MaxLength(20)]
        public string PatientName { get; set; }//患者名称
        [MaxLength(20)]
        public string Sex { get; set; }//患者性别

        [MaxLength(10)]
        public string PatientSource { get; set; }//患者来源
        [MaxLength(20)]
        public string BLH { get; set; }//病历号、患者卡号

        [MaxLength(100)]
        public string FormId { get; set; }

        [ForeignKey("HospitalCRForm")]
        public int? HospitalCRFormId { get; set; }
        public virtual HospitalCRForm HospitalCRForm { get; set; }

        public string CRFJsonValue { get; set; }

        [DefaultValue(0)]
        public int TotalField { get; set; }  //变量总数

        [DefaultValue(0)]
        public int FillField { get; set; }  //已填写数据量

        /// <summary>
        /// AI保存结果json
        /// </summary>
        public string AIExtractJsonValue { get; set; }


        [Column(TypeName = "text")]
        public string SoureDatas { get; set; }  

        [MaxLength(50)]
        public string CreateUserName { get; set; }
        public DateTime CreatedTime { get; set; }
    }
}
