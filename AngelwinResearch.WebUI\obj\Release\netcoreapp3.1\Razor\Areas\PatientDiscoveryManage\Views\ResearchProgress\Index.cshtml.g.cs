#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\PatientDiscoveryManage\Views\ResearchProgress\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "c6d6d0be853ae9892a7dbd72308e7cc0d84a5aabdc18e26053825f70b5c2aa66"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_PatientDiscoveryManage_Views_ResearchProgress_Index), @"mvc.1.0.view", @"/Areas/PatientDiscoveryManage/Views/ResearchProgress/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"c6d6d0be853ae9892a7dbd72308e7cc0d84a5aabdc18e26053825f70b5c2aa66", @"/Areas/PatientDiscoveryManage/Views/ResearchProgress/Index.cshtml")]
    #nullable restore
    internal sealed class Areas_PatientDiscoveryManage_Views_ResearchProgress_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css?v1.0"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/vform/index1.min.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/vform/VFormDesigner.css?t=20210730"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/images/JY1.jpg"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/jquery-3.5.1.min.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\PatientDiscoveryManage\Views\ResearchProgress\Index.cshtml"
  
    ViewBag.Title = "查看科研进度采集";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("<!DOCTYPE html>\r\n<html lang=\"en\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "c6d6d0be853ae9892a7dbd72308e7cc0d84a5aabdc18e26053825f70b5c2aa666839", async() => {
                WriteLiteral(@"
    <meta charset=""UTF-8"">
    <meta name=""viewport""
          content=""width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"">
    <meta http-equiv=""X-UA-Compatible"" content=""ie=edge"">
    <title>科研AIDemo</title>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "c6d6d0be853ae9892a7dbd72308e7cc0d84a5aabdc18e26053825f70b5c2aa667393", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "c6d6d0be853ae9892a7dbd72308e7cc0d84a5aabdc18e26053825f70b5c2aa668595", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "c6d6d0be853ae9892a7dbd72308e7cc0d84a5aabdc18e26053825f70b5c2aa669797", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "c6d6d0be853ae9892a7dbd72308e7cc0d84a5aabdc18e26053825f70b5c2aa6611000", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@" <!-- 根据Web服务器或CDN路径修改 -->

    <style>
        .dis_flex_row {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
        }

        .user_info {
            padding-bottom: 10px;
            border-bottom: 1px solid #f6f6f6;
            background-color: #fff;
        }

        .user_info_item {
            padding: 0 15px;
        }

            .user_info_item span {
                padding-left: 10px;
            }

        .info_name {
            padding-right: 10px;
            font-size: 30px;
            line-height: 48px;
            color: #50314F;
            padding-left: 15px;
        }

        .nav_wrap {
            background-color: #fff;
            margin-top: 10px;
        }

        .white_nav {
            width: 100%;
            color: #333;
        }

        .layui-nav-item {
            padding-left: 15px;
        }

        .layui-nav-itemed {
            background-color: #1E9FFF;
        }

");
                WriteLiteral(@"        .layui-side-scroll {
            width: 100%;
        }

        .layui-nav-item > a:hover {
            background-color: transparent !important;
        }

        .result_table {
            padding-left: 10px;
        }

        .layui-table-view {
            background-color: #fff;
        }
        .openimg{
            width:100%;
        }
            .openimg img {
                width: 100%;
            }
    </style>

");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "c6d6d0be853ae9892a7dbd72308e7cc0d84a5aabdc18e26053825f70b5c2aa6614431", async() => {
                WriteLiteral(@"

    <div class=""user_info"">
        <div>
            <p class=""info_name""></p>
        </div>

        <div class=""dis_flex_row"">
            <p class=""user_info_item"">
                性别:<span class=""info_Sex""></span>
            </p>
            <p class=""user_info_item"">
                保健卡号:<span class=""info_ID""></span>
            </p>
            <p class=""user_info_item"">
                联系方式:<span class=""info_tel""></span>
            </p>
            <p class=""PID"" style=""display:none"">");
                Write(
#nullable restore
#line 106 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\PatientDiscoveryManage\Views\ResearchProgress\Index.cshtml"
                                                 ViewBag.PID

#line default
#line hidden
#nullable disable
                );
                WriteLiteral(@"</p>
        </div>
    </div>

    <div class=""layui-row"">
        <div class=""layui-col-md2 nav_wrap"">
            <div class=""layui-side-scroll"">
                <ul class=""white_nav layui-nav-tree layui-inline "" lay-filter=""demo"" id=""ResearchList"">
                </ul>
            </div>
        </div>
        <div class=""layui-col-md10 result_table"">
            <table class=""layui-hide "" id=""CollectionResult"" lay-filter=""CollectionResult""></table>
            <script type=""text/html"" id=""tableBar1"">
                <a class=""layui-btn layui-btn-normal layui-btn-xs"" lay-event=""attachment"" style=""text-decoration: none;""><i class=""layui-icon""></i>查看报告</a>
            </script>
        </div>
    </div>
    <div id=""openWindow"" style=""display:none;text-align:center;"">
        <div class=""openimg"">
            ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("img", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "c6d6d0be853ae9892a7dbd72308e7cc0d84a5aabdc18e26053825f70b5c2aa6616481", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n        </div>\r\n\r\n\r\n\r\n    </div>\r\n\r\n\r\n    <div class=\"window_wrap\" id=\"form_window_res\" style=\"display: none;\">\r\n\r\n        <form class=\"layui-form\" id=\"fm_res\"");
                BeginWriteAttribute("action", " action=\"", 3729, "\"", 3738, 0);
                EndWriteAttribute();
                WriteLiteral(@">

            <div class=""layui-form-item"" style=""padding-top:15px"">

                <div class=""layui-input-block"">
                    检查结果：
                </div>
            </div>
            <div class=""layui-form-item"" style=""padding-top:15px"">

                <div class=""layui-input-block"" id=""divRes"">

                </div>
            </div>
        </form>

    </div>



    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "c6d6d0be853ae9892a7dbd72308e7cc0d84a5aabdc18e26053825f70b5c2aa6618367", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "c6d6d0be853ae9892a7dbd72308e7cc0d84a5aabdc18e26053825f70b5c2aa6619491", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <script>
layui.use(['element', 'layer', 'table', 'laydate', 'form'], function () {
                var element = layui.element
                    , layer = layui.layer
                    , table = layui.table//表格
                    , laydate = layui.laydate
                    , $ = layui.$
                    , form = layui.form;
                var typename = ""体格检查"";
                $(document).ready(function () {
                    // 页面加载完成后发起 AJAX 请求
                    var patid = $("".PID"").html();
                    $.ajax({
                        url: '/PatientDiscoveryManage/ResearchProgress/GetPat?patid=' + patid,
                        type: 'post',
                        success: function (data) {
                            $("".info_name"").html(data.data.HZXM);
                            $("".info_Sex"").html(data.data.SEX);
                            $("".info_ID"").html(data.data.PATIENTID);
                            $("".info_tel"").html(data.data.TELEPHONE);

");
                WriteLiteral(@"                            $.ajax({
                                url: '/PatientDiscoveryManage/ResearchProgress/GetExam?patid=' + $("".info_ID"").html(),
                                type: 'post',
                                success: function (data) {
                                    console.log(data);
                                    var ResearchItem = '';
                                    var item;
                                    for (var i = 0; i < data.data.length; i++) {
                                        if (i == 0) {
                                            item = data.data[i].ExamClass;
                                            ResearchItem = '<li class=""layui-nav-item layui-nav-itemed""><a href=""javascript:;"">' + data.data[i].ExamClass + '</a></li>';
                                        }
                                        else {
                                            ResearchItem += '<li class=""layui-nav-item""><a href=""javascript:;"">' + data.da");
                WriteLiteral(@"ta[i].ExamClass + '</a></li>';
                                        }
                                    }
                                    $(""#ResearchList"").append(ResearchItem);

                                    getData(item);
                                    form.render();


                                },
                                error: function () {
                                    console.log('Error occurred');
                                }
                            });
                        },
                        error: function () {
                            console.log('Error occurred');
                        }
                    });
                });


                //监听tablelist工具条
                table.on('tool(CollectionResult)', function (obj) {
                    var data = obj.data;
                    if (obj.event === 'attachment') {

                        console.log($(this))


                        var names");
                WriteLiteral(@" = typename;
                        var res = """";
                        var type = 1;
                        var content = $('#form_window_res');
                        if (names == ""体格检查"") {
                            type = 1;
                            content = $('#form_window_res');
                            res = `该患者的体格检查结果如下：
    体温：36摄氏度，正常范围。
    脉搏：79次 / 分，处于正常范围。
    呼吸频率：16次 / 分，正常范围。
    未测量血压，无法评估。
    意识状态：清晰，正常。
    面色：无病容，正常。
    体位：自动体位，正常。
    眼睑：无浮肿，正常。
    结膜：无水肿，正常。
    巩膜：无黄染，正常。
    瞳孔：等大、等圆，正常。
    唇部：无紫绀，正常。
    甲状腺：无肿大，正常。
    颈静脉：无怒张，正常。
    颈部血管杂音：无，正常。
    肺部听诊：无异常呼吸音。
    啰音：无，正常。
    肝脾肋下触诊：无，正常。
    心音：正常，心音强弱一致，A2 > P2。
    心率：79次 / 分，正常范围。
    心音性质：心脏听诊无杂音。
    血压：未测量，无法评估。
    腹部检查：无异常，正常。
    肝脏触诊：无，正常。
    下肢水肿：无，正常。
    肝颈静脉反流征：无，正常。

            根据以上体格检查结果，患者整体情况良好，无明显异常。`

                        }
                        else if (names == ""现病史"") {
                            type = 1;
                            conten");
                WriteLiteral(@"t = $('#form_window_res');
                            res = `患者在2023年9月20日出现心悸等症状，病程约10小时。初始症状包括心悸，与该症状相关的其他症状包括无黑朦、无晕厥、无胸闷和无胸痛。患者曾于房山区良乡医院就诊，初步诊断为心房颤动，接受胺碘酮治疗，治疗效果良好，已转为窦律。此外，患者进行了冠脉造影，发现冠状动脉粥样硬化，建议择期进行射频消融治疗。患者在出院后继续服用利伐沙班和胺碘酮，最近的心电图检查显示窦性心律。目前，患者计划接受房颤射频消融手术治疗。`;
                        }
                        else if (names == ""CT报告"") {
                            type = 2;
                            content = ""/images/CT2.pdf"";
                        }
                        else if (names == ""血常规"") {
                            type = 1;
                            content = $('#openWindow');
                        }
                        else {
                            return;
                        }
                        $(""#divRes"").html(res);
                        layer.open({
                            type: type,
                            title: '报告详情',
                            area: ['950px', '600px'],
                            resize: true,
         ");
                WriteLiteral(@"                   content: content
                        })
                    }
                })

                $("".white_nav"").on(""click"", "".layui-nav-item"", function () {
                    $("".layui-nav-item"").removeClass('layui-nav-itemed');
                    $(this).addClass(""layui-nav-itemed"");
                    var item = $(this).find('a').html();
                    getData(item);
                    typename = item;

                })
                function getData(item) {
                    $.ajax({
                        url: '/PatientDiscoveryManage/ResearchProgress/GetResearchData?patid=' + $("".info_ID"").html() + '&ResearchPathId=' + item,
                        type: 'get',
                        success: function (data) {
                            var num = parseInt(data.count);

                            if (!isNaN(num) && num > 0)
                            {
                                var cols = [];
                                for (va");
                WriteLiteral(@"r key in data.data[0]) {
                                    cols.push({ field: key, title: key, edit: 'text' });
                                }
                                cols.push(
                                    {
                                        title: '操作',
                                        width: 160,
                                        fixed: 'right',
                                        toolbar: '#tableBar1'
                                    }
                                );

                                table.render({
                                    elem: '#CollectionResult',
                                    cols: [cols],
                                    data: data.data,
                                    cellMinWidth: 120,
                                    height: 'full-120',
                                    done: function (res, curr, count) {
                                        setNavHeight();
                          ");
                WriteLiteral(@"              var rows = res.data;
                                        var specialCharacter = '↑'; // 指定要检查的特定字符
                                        var specialCharacter2 = '↓'; // 指定要检查的特定字符

                                        for (var i = 0; i < rows.length; i++) {
                                            for (var key in rows[i]) {
                                                var value = rows[i][key];
                                                if (typeof value === 'string' && (value.indexOf(specialCharacter) !== -1 || value.indexOf(specialCharacter2) !== -1)) {
                                                    $('tr[data-index=""' + i + '""] td[data-field=""' + key + '""]').css('color', 'red');
                                                }
                                            }
                                        }
                                    }
                                });
                            }
                            else {
   ");
                WriteLiteral(@"                             table.render({
                                    elem: '#CollectionResult',
                                    cols: [cols],
                                    data: data.data,
                                    cellMinWidth: 120,
                                    height: 'full-120',
                                    done: function (res, curr, count) {
                                        setNavHeight();
                                    }
                                });
                            }
                        },
                        error: function () {
                            console.log('Error occurred');
                        }
                    });
                };
                $(window).resize(function () {
                    setNavHeight();
                });

                function setNavHeight() {
                    var rightH = $("".result_table"").height()
                    $("".nav_wrap"").css(""h");
                WriteLiteral("eight\", (rightH - 20) + \"px\");\r\n                }\r\n            });\r\n    </script>\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</html >\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
