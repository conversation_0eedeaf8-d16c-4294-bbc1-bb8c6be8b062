#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\PatientDiscoveryManage\Views\PatientDiscovery\Index2.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da7"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_PatientDiscoveryManage_Views_PatientDiscovery_Index2), @"mvc.1.0.view", @"/Areas/PatientDiscoveryManage/Views/PatientDiscovery/Index2.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da7", @"/Areas/PatientDiscoveryManage/Views/PatientDiscovery/Index2.cshtml")]
    #nullable restore
    internal sealed class Areas_PatientDiscoveryManage_Views_PatientDiscovery_Index2 : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/node_modules/bootstrap/dist/css/bootstrap.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("bt-theme"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/node_modules/chosenjs/chosen.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/node_modules/awesome-bootstrap-checkbox/awesome-bootstrap-checkbox.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/node_modules/bootstrap-slider/dist/css/bootstrap-slider.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/node_modules/bootstrap-icons/font/bootstrap-icons.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/dist/css/query-builder.default.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("qb-theme"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/node_modules/jquery/dist/jquery.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/node_modules/bootstrap/dist/js/bootstrap.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/node_modules/chosenjs/chosen.jquery.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/node_modules/bootstrap-slider/dist/bootstrap-slider.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_13 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/node_modules/jquery-extendext/jquery-extendext.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_14 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/node_modules/sql-parser-mistic/browser/sql-parser.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_15 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/node_modules/interactjs/dist/interact.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_16 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/node_modules/bootbox/dist/bootbox.all.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_17 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/dist/js/query-builder.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
            WriteLiteral("<!DOCTYPE html>\n<html data-bs-theme=\"light\">\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da710149", async() => {
                WriteLiteral("\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n    <title>jQuery QueryBuilder Example</title>\n\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da710596", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da711885", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da713087", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da714289", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da715491", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                BeginWriteTagHelperAttribute();
                WriteAttributeValue("", 608, "~/node_modules/", 608, 15, true);
                WriteLiteral("@");
                WriteAttributeValue("", 625, "selectize/selectize/dist/css/selectize.bootstrap5.css", 625, 53, true);
                __tagHelperStringValueBuffer = EndWriteTagHelperAttribute();
                __tagHelperExecutionContext.AddHtmlAttribute("href", Html.Raw(__tagHelperStringValueBuffer), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da717164", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagOnly, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da718366", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da720359", async() => {
                WriteLiteral(@"

    <div class=""container"">
        <div class=""col-md-12 col-lg-10 offset-lg-1"">
            <div class=""page-header"">
                <a class=""float-end"" href=""https://github.com/mistic100/jQuery-QueryBuilder"">
                    <img src=""https://github.githubassets.com/assets/GitHub-Mark-ea2971cee799.png"" style=""height:48px;"">
                </a>
                <h1>
                    jQuery QueryBuilder
                    <small>Example</small>
                </h1>
            </div>

            <div class=""well well-sm"">
                <label>Theme:</label>
                <div class=""btn-group"">
                    <button class=""btn btn-primary btn-sm change-theme"" data-qb=""/dist/css/query-builder.default.css""
                            data-bt=""default"">
                        Default
                    </button>
                    <button class=""btn btn-primary btn-sm change-theme"" data-qb=""/dist/css/query-builder.dark.css""
                            data-bt=""dark"">
                 ");
                WriteLiteral(@"       Dark
                    </button>
                </div>

                <label>Language:</label>
                <select name=""language"" class=""show-tick show-menu-arrow"" data-width=""auto"">
                    <option value=""sq"">Albanian</option>
                    <option value=""ar"">Arabic</option>
                    <option value=""az"">Azerbaijani</option>
                    <option value=""bg"">Bulgarian</option>
                    <option value=""zh-CN"">Simplified Chinese</option>
                    <option value=""cs"">Czech</option>
                    <option value=""de"">German</option>
                    <option value=""da"">Danish</option>
                    <option value=""nl"">Dutch</option>
                    <option value=""en"" selected>English</option>
                    <option value=""fa-IR"">Farsi</option>
                    <option value=""fr"">French</option>
                    <option value=""el"">Greek</option>
                    <option value=""he"">Hebrew</option>
                    ");
                WriteLiteral(@"<option value=""it"">Italian</option>
                    <option value=""no"">Norwegian</option>
                    <option value=""pl"">Polish</option>
                    <option value=""pt-PT"">Portuguese</option>
                    <option value=""pt-BR"">Brazilian Portuguese</option>
                    <option value=""ro"">Romanian</option>
                    <option value=""ru"">Russian</option>
                    <option value=""es"">Spanish</option>
                    <option value=""tr"">Turkish</option>
                    <option value=""ua"">Ukrainian</option>
                </select>
            </div>

            <div id=""builder""></div>

            <div class=""btn-group"">
                <button class=""btn btn-danger reset"">Reset</button>
                <button class=""btn btn-warning set-filters"" data-bs-toggle=""tooltip"" data-container=""body"" data-placement=""bottom""
                        title=""Adds a filter 'New filter' and removes 'Coordinates', 'State', 'BSON'"">
                    Set filters
    ");
                WriteLiteral(@"            </button>
            </div>

            <div class=""btn-group"">
                <button class=""btn btn-default"" disabled>Set:</button>
                <button class=""btn btn-success set"">From JSON</button>
                <button class=""btn btn-success set-mongo"">From MongoDB</button>
                <button class=""btn btn-success set-sql"">From SQL</button>
            </div>

            <div class=""btn-group"">
                <button class=""btn btn-default"" disabled>Get:</button>
                <button class=""btn btn-primary parse-json"">JSON</button>
                <button class=""btn btn-primary parse-sql"" data-stmt=""false"">SQL</button>
                <button class=""btn btn-primary parse-sql"" data-stmt=""question_mark"">SQL statement</button>
                <button class=""btn btn-primary parse-mongo"">MongoDB</button>
            </div>

            <div id=""result"" class=""hide"">
                <h3>Output</h3>
                <pre></pre>
            </div>
        </div>
    </div>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da724968", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da726090", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                BeginWriteTagHelperAttribute();
                WriteAttributeValue("", 5042, "~/node_modules/", 5042, 15, true);
                WriteLiteral("@");
                WriteAttributeValue("", 5059, "popperjs/core/dist/umd/popper.js", 5059, 32, true);
                __tagHelperStringValueBuffer = EndWriteTagHelperAttribute();
                __tagHelperExecutionContext.AddHtmlAttribute("src", Html.Raw(__tagHelperStringValueBuffer), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da727665", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da728788", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da729911", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da731034", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                BeginWriteTagHelperAttribute();
                WriteAttributeValue("", 5348, "~/node_modules/", 5348, 15, true);
                WriteLiteral("@");
                WriteAttributeValue("", 5365, "selectize/selectize/dist/js/selectize.js", 5365, 40, true);
                __tagHelperStringValueBuffer = EndWriteTagHelperAttribute();
                __tagHelperExecutionContext.AddHtmlAttribute("src", Html.Raw(__tagHelperStringValueBuffer), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da732617", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da733740", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_14);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da734863", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_15);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da735986", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_16);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\n\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "4012bc5c292751e162c5d90d0f54b04d4f42110d663fe9b46d2661e479a22da737111", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_17);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"

    <script>document.querySelectorAll('[data-bs-toggle=""tooltip""]').forEach(function (element) {
        new bootstrap.Tooltip(element);
  });

  var $b = $('#builder');

  var options = {
    allow_empty: true,

    //default_filter: 'name',
    sort_filters: true,

    optgroups: {
      core: {
        en: 'Core',
        fr: 'Coeur'
      }
    },

    plugins: {
      'bt-tooltip-errors': { delay: 100 },
      'sortable': null,
      'filter-description': { mode: 'bootbox' },
//      'chosen-selectpicker': null,
      'unique-filter': null,
      'bt-checkbox': { color: 'primary' },
      'invert': null,
      'not-group': null
    },

    // standard operators in custom optgroups
    operators: [
      { type: 'equal', optgroup: 'basic' },
      { type: 'not_equal', optgroup: 'basic' },
      { type: 'in', optgroup: 'basic' },
      { type: 'not_in', optgroup: 'basic' },
      { type: 'less', optgroup: 'numbers' },
      { type: 'less_or_equal', optgroup: 'numbers' },
      { type: 'greater', optgroup");
                WriteLiteral(@": 'numbers' },
      { type: 'greater_or_equal', optgroup: 'numbers' },
      { type: 'between', optgroup: 'numbers' },
      { type: 'not_between', optgroup: 'numbers' },
      { type: 'begins_with', optgroup: 'strings' },
      { type: 'not_begins_with', optgroup: 'strings' },
      { type: 'contains', optgroup: 'strings' },
      { type: 'not_contains', optgroup: 'strings' },
      { type: 'ends_with', optgroup: 'strings' },
      { type: 'not_ends_with', optgroup: 'strings' },
      { type: 'is_empty' },
      { type: 'is_not_empty' },
      { type: 'is_null' },
      { type: 'is_not_null' }
    ],

    filters: [
      /*
       * string with separator
       */
      {
        id: 'name',
        field: 'username',
        label: {
          en: 'Name',
          fr: 'Nom'
        },
        icon: 'bi-person-fill',
        value_separator: ',',
        type: 'string',
        optgroup: 'core',
        default_value: 'Mistic',
        size: 30,
        validation: {
          allow_empty_value: true
    ");
                WriteLiteral(@"    },
        unique: true
      },
      /*
       * integer with separator for 'in' and 'not_in'
       */
      {
        id: 'age',
        label: 'Age',
        icon: 'bi-calendar3',
        type: 'integer',
        input: 'text',
        value_separator: '|',
        optgroup: 'core',
        description: function(rule) {
          if (rule.operator && ['in', 'not_in'].indexOf(rule.operator.type) !== -1) {
            return 'Use a pipe (|) to separate multiple values with ""in"" and ""not in"" operators';
          }
        }
      },
      /*
       * textarea
       */
      {
        id: 'bson',
        label: 'BSON',
        icon: 'bi-qr-code',
        type: 'string',
        input: 'textarea',
        operators: ['equal'],
        size: 30,
        rows: 3
      },
      /*
       * checkbox
       */
      {
        id: 'category',
        label: 'Category',
        icon: 'bi-list-task',
        type: 'integer',
        input: 'checkbox',
        optgroup: 'core',
        values: {
          1: 'Bo");
                WriteLiteral(@"oks',
          2: 'Movies',
          3: 'Music',
          4: 'Tools',
          5: 'Goodies',
          6: 'Clothes'
        },
        colors: {
          1: 'foo',
          2: 'warning',
          5: 'success'
        },
        operators: ['equal', 'not_equal', 'in', 'not_in', 'is_null', 'is_not_null'],
        default_operator: 'in'
      },
      /*
       * select
       */
      {
        id: 'continent',
        label: 'Continent',
        icon: 'bi-globe-americas',
        type: 'string',
        input: 'select',
        optgroup: 'core',
        placeholder: 'Select something',
        values: [
          {
            label: 'Europe',
            value: 'eur',
            optgroup: 'North'
          },
          {
            label: 'Asia',
            value: 'asia',
            optgroup: 'North'
          },
          {
            label: 'Oceania',
            value: 'oce',
            optgroup: 'South'
          },
          {
            label: 'Africa',
            value: 'afr',
          ");
                WriteLiteral(@"  optgroup: 'South'
          },
          {
            label: 'North America',
            value: 'na',
            optgroup: 'North'
          },
          {
            label: 'South America',
            value: 'sa',
            optgroup: 'South'
          },
          {
            label: 'Mordor',
            value: 'mrd'
          }
        ],
        operators: ['equal', 'not_equal', 'is_null', 'is_not_null']
      },
      /*
       * Selectize
       */
      {
        id: 'state',
        label: 'State',
        icon: 'bi-globe-americas',
        type: 'string',
        input: 'select',
        multiple: true,
        plugin: 'selectize',
        plugin_config: {
          valueField: 'id',
          labelField: 'name',
          searchField: 'name',
          sortField: 'name',
          options: [
            { id: ""AL"", name: ""Alabama"" },
            { id: ""AK"", name: ""Alaska"" },
            { id: ""AZ"", name: ""Arizona"" },
            { id: ""AR"", name: ""Arkansas"" },
            { id: ""CA"", name:");
                WriteLiteral(@" ""California"" },
            { id: ""CO"", name: ""Colorado"" },
            { id: ""CT"", name: ""Connecticut"" },
            { id: ""DE"", name: ""Delaware"" },
            { id: ""DC"", name: ""District of Columbia"" },
            { id: ""FL"", name: ""Florida"" },
            { id: ""GA"", name: ""Georgia"" },
            { id: ""HI"", name: ""Hawaii"" },
            { id: ""ID"", name: ""Idaho"" }
          ]
        },
        valueSetter: function(rule, value) {
          rule.$el.find('.rule-value-container select')[0].selectize.setValue(value);
        }
      },
      /*
       * radio
       */
      {
        id: 'in_stock',
        label: 'In stock',
        icon: 'bi-box-arrow-in-right',
        type: 'integer',
        input: 'radio',
        optgroup: 'plugin',
        values: {
          1: 'Yes',
          0: 'No'
        },
        operators: ['equal']
      },
      /*
       * double
       */
      {
        id: 'price',
        label: 'Price',
        icon: 'bi-currency-dollar',
        type: 'double',
        size:");
                WriteLiteral(@" 5,
        validation: {
          min: 0,
          step: 0.01
        },
        data: {
          class: 'com.example.PriceTag'
        }
      },
      /*
       * slider
       */
      {
        id: 'rate',
        label: 'Rate',
        icon: 'bi-box-arrow-lightning-charge-fill',
        type: 'integer',
        validation: {
          min: 0,
          max: 100
        },
        plugin: 'slider',
        plugin_config: {
          min: 0,
          max: 100,
          value: 0
        },
        valueSetter: function(rule, value) {
          var input = rule.$el.find('.rule-value-container input');
          input.slider('setValue', value);
          input.val(value); // don't know why I need it
        }
      },
      /*
       * placeholder and regex validation
       */
      {
        id: 'id',
        label: 'Identifier',
        icon: 'bi-sunglasses',
        type: 'string',
        optgroup: 'plugin',
        placeholder: '____-____-____',
        size: 14,
        operators: ['equal', 'not_");
                WriteLiteral(@"equal'],
        validation: {
          format: /^.{4}-.{4}-.{4}$/,
          messages: {
            format: 'Invalid format, expected: AAAA-AAAA-AAAA'
          }
        }
      },
      /*
       * custom input
       */
      {
        id: 'coord',
        label: 'Coordinates',
        icon: 'bi-star',
        type: 'string',
        default_value: 'C.5',
        description: 'The letter is the cadran identifier:\
<ul>\
  <li><b>A</b>: alpha</li>\
  <li><b>B</b>: beta</li>\
  <li><b>C</b>: gamma</li>\
</ul>',
        validation: {
          format: /^[A-C]{1}.[1-6]{1}$/
        },
        input: function(rule, name) {
          var $container = rule.$el.find('.rule-value-container');

          $container.on('change', '[name=' + name + '_1]', function() {
            var h = '';

            switch ($(this).val()) {
              case 'A':
                h = '<option value=""-1"">-</option> <option value=""1"">1</option> <option value=""2"">2</option>';
                break;
              case 'B':
        ");
                WriteLiteral(@"        h = '<option value=""-1"">-</option> <option value=""3"">3</option> <option value=""4"">4</option>';
                break;
              case 'C':
                h = '<option value=""-1"">-</option> <option value=""5"">5</option> <option value=""6"">6</option>';
                break;
            }

            $container.find('[name$=_2]')
              .html(h).toggle(!!h)
              .val('-1').trigger('change');
          });

          return '\
<select name=""' + name + '_1""> \
  <option value=""-1"">-</option> \
  <option value=""A"">A</option> \
  <option value=""B"">B</option> \
  <option value=""C"">C</option> \
</select> \
<select name=""' + name + '_2"" style=""display:none;""></select>';
        },
        valueGetter: function(rule) {
          return rule.$el.find('.rule-value-container [name$=_1]').val()
            + '.' + rule.$el.find('.rule-value-container [name$=_2]').val();
        },
        valueSetter: function(rule, value) {
          if (rule.operator.nb_inputs > 0) {
            var val = value");
                WriteLiteral(@".split('.');

            rule.$el.find('.rule-value-container [name$=_1]').val(val[0]).trigger('change');
            rule.$el.find('.rule-value-container [name$=_2]').val(val[1]).trigger('change');
          }
        }
      }]
  };

  // init
  $('#builder').queryBuilder(options);

  $('#builder').on('afterCreateRuleInput.queryBuilder', function(e, rule) {
    if (rule.filter.plugin == 'selectize') {
      rule.$el.find('.rule-value-container').css('min-width', '200px')
        .find('.selectize-control').removeClass('form-control');
    }
  });

  // change language
  $('[name=language]').on('change', function() {
    var lang = $(this).val();

    var done = function() {
      var rules = $b.queryBuilder('getRules');
      if (!$.isEmptyObject(rules)) {
        options.rules = rules;
      }
      else {
        delete options.rules;
      }
      options.lang_code = lang;
      $b.queryBuilder('destroy');
      $('#builder').queryBuilder(options);
    };

    if ($.fn.queryBuilder.regional[lang] === un");
                WriteLiteral(@"defined) {
      $.getScript('../dist/i18n/query-builder.' + lang + '.js', done);
    }
    else {
      done();
    }
  });

  // change theme
  $('.change-theme').on('click', function() {
    $('#qb-theme').replaceWith('<link rel=""stylesheet"" href=""' + $(this).data('qb') + '"" id=""qb-theme"">');
    $('[data-bs-theme]').attr('data-bs-theme', $(this).data('bt'));
  });

  // set rules
  $('.set').on('click', function() {
    $('#builder').queryBuilder('setRules', {
      condition: 'AND',
      flags: {
        condition_readonly: true
      },
      data: {
        root: true
      },
      rules: [{
        id: 'price',
        operator: 'between',
        value: [10.25, 15.52],
        flags: {
          no_delete: true,
          filter_readonly: true
        },
        data: {
          unit: '€'
        }
      }, {
        id: 'state',
        operator: 'equal',
        value: 'AK'
      }, {
        condition: 'OR',
        not: true,
        flags: {
          no_delete: true,
          no_drop: true,");
                WriteLiteral(@"
          no_sortable: true
        },
        rules: [{
          id: 'category',
          operator: 'equal',
          value: 2
        }, {
          id: 'coord',
          operator: 'equal',
          value: undefined // will use filter's default value
        }]
      }, {
        id: 'name',
        operator: 'in',
        value: ['Mistic', 'Damien']
      }, {
        id: 'age',
        operator: 'in',
        value: [20,21,22]
      }, {
        empty: true
      }]
    });
  });

  // set rules from MongoDB
  $('.set-mongo').on('click', function() {
    $('#builder').queryBuilder('setRulesFromMongo', {
      ""$or"": [{
        ""username"": {
          ""$regex"": ""^(?!Mistic)""
        }
      }, {
        ""price"": { ""$gte"": 0, ""$lte"": 100 }
      }, {
        ""$nor"": [{
          ""$and"": [{
            ""category"": 2
          }, {
            ""category"": { ""$in"": [4, 5] }
          }]
        }]
      }]
    });
  });

  // set rules from SQL
  $('.set-sql').on('click', function() {
    $('#builder').q");
                WriteLiteral(@"ueryBuilder('setRulesFromSQL', 'username NOT LIKE ""Mistic%"" OR price BETWEEN 100 OR 200 OR NOT (category IN(1, 2) AND rate <= 20)');
  });

  // reset builder
  $('.reset').on('click', function() {
    $('#builder').queryBuilder('reset');
    $('#result').addClass('d-none').find('pre').empty();
  });

  // get rules
  $('.parse-json').on('click', function() {
    $('#result').removeClass('d-none')
      .find('pre').html(JSON.stringify(
      $('#builder').queryBuilder('getRules', {
        get_flags: true,
        skip_empty: true
      }),
      undefined, 2
    ));
  });

  $('.parse-sql').on('click', function() {
    var res = $('#builder').queryBuilder('getSQL', $(this).data('stmt'), false);
    $('#result').removeClass('d-none')
      .find('pre').html(
      res.sql + (res.params ? '\n\n' + JSON.stringify(res.params, undefined, 2) : '')
    );
  });

  $('.parse-mongo').on('click', function() {
    $('#result').removeClass('d-none')
      .find('pre').html(JSON.stringify(
      $('#builder').queryBuild");
                WriteLiteral(@"er('getMongo'),
      undefined, 2
    ));
  });

  // set filters
  $('.set-filters').on('click', function() {
    $(this).prop('disabled', true);
    bootstrap.Tooltip.getInstance($(this)).hide();

    // add a new filter after 'state'
    $('#builder').queryBuilder('addFilter',
      {
        id: 'new_one',
        label: 'New filter',
        type: 'string'
      },
      'state'
    );

    // remove filter 'coord'
    $('#builder').queryBuilder('removeFilter',
      ['coord', 'state', 'bson'],
      true
    );

    // also available : 'setFilters'
  });</script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n</html>\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
