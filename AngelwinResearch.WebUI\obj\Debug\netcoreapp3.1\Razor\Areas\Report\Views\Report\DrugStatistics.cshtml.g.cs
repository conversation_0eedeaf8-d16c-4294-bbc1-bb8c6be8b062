#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Areas\Report\Views\Report\DrugStatistics.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "258f44b7eda877add9896c73a356b92b019a02155e530c1cabe36da17df34c0e"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Areas_Report_Views_Report_DrugStatistics), @"mvc.1.0.view", @"/Areas/Report/Views/Report/DrugStatistics.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"258f44b7eda877add9896c73a356b92b019a02155e530c1cabe36da17df34c0e", @"/Areas/Report/Views/Report/DrugStatistics.cshtml")]
    #nullable restore
    internal sealed class Areas_Report_Views_Report_DrugStatistics : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
            WriteLiteral("<!DOCTYPE html>\n<html lang=\"zh-CN\">\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "258f44b7eda877add9896c73a356b92b019a02155e530c1cabe36da17df34c0e3073", async() => {
                WriteLiteral(@"
    <meta charset=""UTF-8"">
    <title>药品统计高级分析报表</title>
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <script src=""https://cdn.staticfile.org/echarts/5.4.3/echarts.min.js""></script>
    <script src=""https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js""></script>
    <style>
        body { font-family: ""Microsoft YaHei"", sans-serif; background: #f5f6fa; margin: 0; padding: 0; }
        .container { max-width: 1500px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; padding: 20px 0; background: linear-gradient(135deg, #1e88e5, #1565c0); color: white; border-radius: 10px; margin-bottom: 30px; }
        .overview { display: flex; gap: 20px; margin-bottom: 30px; }
        .overview-card { flex: 1; background: #fff; border-radius: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); padding: 20px; text-align: center; }
        .overview-title { font-size: 16px; color: #888; }
        .overview-value { font-size: 28px; color: #1e88e5; margin: 10px 0; }
    ");
                WriteLiteral(@"    .charts { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; }
        .chart-card { background: #fff; border-radius: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); padding: 20px; }
        .chart-title { font-size: 18px; color: #333; margin-bottom: 10px; }
        .chart-container { width: 100%; height: 350px; }
        .full-width { grid-column: 1 / -1; }
        .table-section { background: #fff; border-radius: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); padding: 20px; margin-top: 30px; }
        .table-toolbar { display: flex; justify-content: flex-end; margin-bottom: 10px; }
        .export-btn { background: #1e88e5; color: #fff; border: none; border-radius: 6px; padding: 6px 18px; font-size: 15px; cursor: pointer; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { border: 1px solid #e5e7eb; padding: 8px 12px; text-align: center; }
        th { background: #f1f5f9; }
        ");
                WriteLiteral("@media (max-width: 900px) {\n            .charts { grid-template-columns: 1fr; }\n            .overview { flex-direction: column; }\n        }\n    </style>\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "258f44b7eda877add9896c73a356b92b019a02155e530c1cabe36da17df34c0e6279", async() => {
                WriteLiteral(@"
    <div class=""container"">
        <div class=""header"">
            <h1>药品统计高级分析报表</h1>
            <p>数据更新时间：<span id=""updateTime""></span></p>
        </div>
        <div class=""overview"">
            <div class=""overview-card"">
                <div class=""overview-title"">药品总用量</div>
                <div class=""overview-value"" id=""totalUsage"">--</div>
            </div>
            <div class=""overview-card"">
                <div class=""overview-title"">药品总金额</div>
                <div class=""overview-value"" id=""totalAmount"">--</div>
            </div>
            <div class=""overview-card"">
                <div class=""overview-title"">同比增长</div>
                <div class=""overview-value"" id=""yoy"">--</div>
            </div>
            <div class=""overview-card"">
                <div class=""overview-title"">环比增长</div>
                <div class=""overview-value"" id=""mom"">--</div>
            </div>
        </div>
        <div class=""charts"">
            <div class=""chart-card"">
                <div class=""ch");
                WriteLiteral(@"art-title"">药品分类占比</div>
                <div id=""categoryChart"" class=""chart-container""></div>
            </div>
            <div class=""chart-card"">
                <div class=""chart-title"">重点药品用量与金额占比</div>
                <div id=""keyDrugChart"" class=""chart-container""></div>
            </div>
            <div class=""chart-card full-width"">
                <div class=""chart-title"">药品用量/金额同比环比趋势</div>
                <div id=""trendChart"" class=""chart-container""></div>
            </div>
            <div class=""chart-card full-width"">
                <div class=""chart-title"">科室-药品用量热力图</div>
                <div id=""deptDrugHeatmap"" class=""chart-container""></div>
            </div>
        </div>
        <div class=""table-section"">
            <div class=""table-toolbar"">
                <button class=""export-btn"" onclick=""exportTableToExcel()"">导出明细为Excel</button>
            </div>
            <div class=""chart-title"">药品明细数据</div>
            <table id=""detailTable"">
                <thead>
                ");
                WriteLiteral(@"    <tr>
                        <th>药品名称</th>
                        <th>类别</th>
                        <th>科室名称</th>
                        <th>送药日期</th>
                        <th>用量</th>
                        <th>金额</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 数据行后续可用JS动态填充 -->
                </tbody>
            </table>
        </div>
    </div>
    <script>document.getElementById('updateTime').textContent = new Date().toLocaleString('zh-CN');
        let allDetail = [];
        // 安全获取数组
        const safeArr = arr => Array.isArray(arr) ? arr : [];
        // 自动加载JSON数据并渲染
        fetch('/json/report/ComplexStatisticsData.json')
          .then(response => response.json())
          .then(data => {
            // 总览
            document.getElementById('totalUsage').textContent = data.total_usage || '--';
            document.getElementById('totalAmount').textContent = data.total_amount ? ('¥ ' + data.total_amount) : '--';
            do");
                WriteLiteral(@"cument.getElementById('yoy').textContent = data.yoy || '--';
            document.getElementById('mom').textContent = data.mom || '--';

            // 类别结构
            const categoryChart = echarts.init(document.getElementById('categoryChart'));
            categoryChart.setOption({
                tooltip: { trigger: 'item' },
                legend: { orient: 'vertical', left: 'left' },
                series: [{ type: 'pie', radius: '50%', data: safeArr(data.category_data) }]
            });
            categoryChart.on('click', function(params) {
                filterDetail('类别', params.name);
            });

            // 重点药品
            const keyDrugChart = echarts.init(document.getElementById('keyDrugChart'));
            keyDrugChart.setOption({
                tooltip: { trigger: 'item' },
                legend: { orient: 'vertical', left: 'left' },
                series: [{ type: 'pie', radius: '50%', data: safeArr(data.key_drug_data) }]
            });
            keyDrugChart.on('click', fu");
                WriteLiteral(@"nction(params) {
                filterDetail('药品名称', params.name);
            });

            // 趋势
            const trendChart = echarts.init(document.getElementById('trendChart'));
            trendChart.setOption({
                tooltip: { trigger: 'axis' },
                legend: { data: ['用量', '金额'] },
                xAxis: { type: 'category', data: safeArr(data.trend_data && data.trend_data.dates) },
                yAxis: { type: 'value' },
                series: [
                    { name: '用量', data: safeArr(data.trend_data && data.trend_data.usage), type: 'line', smooth: true },
                    { name: '金额', data: safeArr(data.trend_data && data.trend_data.amount), type: 'line', smooth: true }
                ]
            });

            // 热力图
            const deptDrugHeatmap = echarts.init(document.getElementById('deptDrugHeatmap'));
            deptDrugHeatmap.setOption({
                tooltip: { position: 'top' },
                grid: { height: '60%', top: '10%' },
         ");
                WriteLiteral(@"       xAxis: { type: 'category', data: safeArr(data.heatmap && data.heatmap.x) },
                yAxis: { type: 'category', data: safeArr(data.heatmap && data.heatmap.y) },
                visualMap: { min: 0, max: 100, calculable: true, orient: 'horizontal', left: 'center', bottom: '5%' },
                series: [{
                    name: '用量',
                    type: 'heatmap',
                    data: safeArr(data.heatmap && data.heatmap.data),
                    label: { show: true },
                    emphasis: { itemStyle: { shadowBlur: 10, shadowColor: 'rgba(0,0,0,0.5)' } }
                }]
            });
            deptDrugHeatmap.on('click', function(params) {
                filterDetail('科室名称', params.name);
            });

            // 明细表
            allDetail = safeArr(data.detail);
            renderDetailTable(allDetail);
          })
          .catch(err => {
            alert('药品统计数据加载失败，请确认json文件已生成并放在同目录下！');
            console.error(err);
          });

        // 明细表渲染");
                WriteLiteral(@"
        function renderDetailTable(rows) {
            const tbody = document.querySelector('#detailTable tbody');
            tbody.innerHTML = '';
            rows.forEach(row => {
                const tr = document.createElement('tr');
                tr.innerHTML = `<td>${row.药品名称||''}</td><td>${row.类别||''}</td><td>${row.科室名称||''}</td><td>${row.送药日期||''}</td><td>${row.用量||''}</td><td>${row.金额||''}</td>`;
                tbody.appendChild(tr);
            });
        }
        // 下钻筛选
        function filterDetail(field, value) {
            const filtered = allDetail.filter(row => row[field] === value);
            renderDetailTable(filtered);
        }
        // 导出Excel
        function exportTableToExcel() {
            const wb = XLSX.utils.table_to_book(document.getElementById('detailTable'), {sheet: ""明细数据""});
            XLSX.writeFile(wb, '药品明细数据.xlsx');
        }</script>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\n</html>");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
