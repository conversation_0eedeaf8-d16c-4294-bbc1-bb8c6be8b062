﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace AngelwinResearch.Models
{
    // addbyzolf 20250620
    public partial class HospitalCRForm
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [ForeignKey("HospitalDept")]
        public int HospitalDeptId { get; set; } 
        public virtual HospitalDept HospitalDept { get; set; }
        [MaxLength(100)]
        public string GroupName { get; set; } //表单分组名称，入院、出院、心脏瓣膜等

        [MaxLength(100)]
        public string FormId { get; set; }

        [MaxLength(200)]
        public string FormName { get; set; }//表单名称

        [DefaultValue(0)]
        public int OrderBy { get; set; }

        [DefaultValue(1)]
        public bool StopUsing { get; set; }//启用、停用开关   false/0:停用；    true/1:启用；

        [MaxLength(1000)]
        public string Remark { get; set; }
        /// <summary>
        /// 业务域(从基础字典中获取：基本信息、病历文书、检验、检查)
        /// </summary>
        [MaxLength(500)]
        public string BusinessDomain { get; set; }

        [MaxLength(50)]
        public string CreateUserName { get; set; }
        public DateTime CreatedTime { get; set; }
    }
}
