#pragma checksum "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Menu\Index.cshtml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "74b2ab55bf28fec6f85ffc45d002c8669999ca8e9b8a444a03949da8371ae1d9"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCoreGeneratedDocument.Views_Menu_Index), @"mvc.1.0.view", @"/Views/Menu/Index.cshtml")]
namespace AspNetCoreGeneratedDocument
{
    #line default
    using global::System;
    using global::System.Collections.Generic;
    using global::System.Linq;
    using global::System.Threading.Tasks;
    using global::Microsoft.AspNetCore.Mvc;
    using global::Microsoft.AspNetCore.Mvc.Rendering;
    using global::Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI

#nullable disable
    ;
#nullable restore
#line 2 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\_ViewImports.cshtml"
using AngelwinResearch.WebUI.Models

#nullable disable
    ;
    #line default
    #line hidden
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"74b2ab55bf28fec6f85ffc45d002c8669999ca8e9b8a444a03949da8371ae1d9", @"/Views/Menu/Index.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"Sha256", @"a5bcd83cf27c8ffb8baf597d24314352bf7b52b5a9bb5ee83e83e9d0089a628e", @"/Views/_ViewImports.cshtml")]
    #nullable restore
    internal sealed class Views_Menu_Index : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    #nullable disable
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/css/layui.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("rel", new global::Microsoft.AspNetCore.Html.HtmlString("stylesheet"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("href", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/style/admin.css"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layui/layui.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/lib/jquery/dist/jquery.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/js/common.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", new global::Microsoft.AspNetCore.Html.HtmlString("~/layuiadmin/layuiextend/xm-select.js"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding:5px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "模块目录", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("selected", new global::Microsoft.AspNetCore.Html.HtmlString("selected"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("layui-form"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("lay-filter", new global::Microsoft.AspNetCore.Html.HtmlString("fm"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("fm"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_13 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("action", new global::Microsoft.AspNetCore.Html.HtmlString(""), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_14 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("lay-filter", new global::Microsoft.AspNetCore.Html.HtmlString("fm_copy"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_15 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("fm_copy"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper;
        private global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 1 "D:\work space\project\三部\科研AI Demo\AngelwinResearch.WebUI\Views\Menu\Index.cshtml"
  
    ViewBag.Title = "模块管理";
    Layout = null;

#line default
#line hidden
#nullable disable

            WriteLiteral("\r\n<!DOCTYPE html>\r\n<html lang=\"en\">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("head", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "74b2ab55bf28fec6f85ffc45d002c8669999ca8e9b8a444a03949da8371ae1d99907", async() => {
                WriteLiteral(@"
    <meta charset=""UTF-8"">
    <meta name=""viewport""
          content=""width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"">
    <meta http-equiv=""X-UA-Compatible"" content=""ie=edge"">
    <title>模块管理</title>
    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "74b2ab55bf28fec6f85ffc45d002c8669999ca8e9b8a444a03949da8371ae1d910457", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("link", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "74b2ab55bf28fec6f85ffc45d002c8669999ca8e9b8a444a03949da8371ae1d911660", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "74b2ab55bf28fec6f85ffc45d002c8669999ca8e9b8a444a03949da8371ae1d912863", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "74b2ab55bf28fec6f85ffc45d002c8669999ca8e9b8a444a03949da8371ae1d913987", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "74b2ab55bf28fec6f85ffc45d002c8669999ca8e9b8a444a03949da8371ae1d915111", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
    <style>
        /*弹窗*/
        .window_wrap {
            padding: 15px;
        }

        #form_window .layui-form-label {
            width: 100px;
        }

        #form_window .layui-form-val {
            padding: 9px 15px;
        }

        #form_window .layui-form-item {
            margin-bottom: 0;
        }

        .layui-form-item {
            margin-bottom: 5px;
        }
    </style>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.HeadTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_HeadTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n\r\n\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "74b2ab55bf28fec6f85ffc45d002c8669999ca8e9b8a444a03949da8371ae1d917378", async() => {
                WriteLiteral(@"
    <div class=""layui-col-md12"">
        <div class=""layui-card"">
            <div class=""layui-card-header"">
                <div class=""layui-inline"">
                    <label class=""layui-form-label"">模块管理</label>
                </div>
            </div>
            <div class=""layui-card-body"">
                <table class=""layui-table layui-form"" id=""menuTreeTb""></table>
                <script type=""text/html"" id=""tableBar1"">
                    <a class=""layui-btn layui-btn-normal layui-btn-xs"" lay-event=""seerole""><i class=""layui-icon layui-icon-table""></i>查看角色</a>
                    {{# if(d.menuType != ""功能""){ }}
                    <a class=""layui-btn  layui-btn-xs"" lay-event=""add""><i class=""layui-icon layui-icon-add-circle""></i>新增</a>
                    {{# } }}
                    {{# if(d.menuType == ""模块""){ }}
                    <a class=""layui-btn  layui-btn-normal  layui-btn-xs"" lay-event=""move""><i class=""layui-icon layui-icon-transfer""></i>移动</a>
                    {{# }");
                WriteLiteral(@" }}
                    <a class=""layui-btn layui-btn-normal layui-btn-xs"" lay-event=""edit""><i class=""layui-icon layui-icon-edit""></i>编辑</a>
                    <a class=""layui-btn layui-btn-danger layui-btn-xs"" lay-event=""del""><i class=""layui-icon layui-icon-delete""></i>删除</a>
                </script>
            </div>
        </div>

        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "74b2ab55bf28fec6f85ffc45d002c8669999ca8e9b8a444a03949da8371ae1d919127", async() => {
                }
                );
                __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.UrlResolutionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_UrlResolutionTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral(@"
        <script>
            layui.config({
                base: '/layuiadmin/layuiextend/'
            }).use(['element', 'layer', 'treeTable', 'table', 'form'], function () {
                var element = layui.element
                    , layer = layui.layer
                    , table = layui.table//表格
                    , treeTable = layui.treeTable //表格
                    , $ = layui.$
                    , form = layui.form;
                ;
                var windowsIndex;
                var selectTableRowData = [];
                var url = '';

                // 渲染表格
                var menuTreeTb = treeTable.render({
                    elem: '#menuTreeTb',
                    url: ""/Menu/MenuList"",
                    tree: {
                        idName: 'Id',
                        pidName: 'parentId',
                        haveChildName: 'haveChildName',
                        iconIndex: 1,    // 折叠图标显示在第几列
                        isPidData: true,  // 是否是");
                WriteLiteral(@"pid形式数据
                        arrowType: 'arrow2',
                        getIcon: 'ew-tree-icon-style2'
                    },
                    cols: [[
                        { type: 'numbers', fixed: 'left' },
                        { field: 'menuName', title: '名称', fixed: 'left', minWidth: 220 },
                        { field: 'menuType', title: '模块类型' },
                        { field: 'isMenu', title: '是否主菜单' },
                        { field: 'menuArea', title: '区域名称' },
                        { field: 'menuController', title: '控制器名称' },
                        { field: 'menuAction', title: '行为名称' },
                        { field: 'buttonId', title: '按钮Id' },
                        { field: 'menuOrder', title: '排序' },
                        { field: 'menuIcon', title: '图标' },
                        { field: 'menuNote', title: '备注' },
                        { fixed: 'right', align: 'center', title: '操作', minWidth: 370, toolbar: '#tableBar1' }
                    ]],
");
                WriteLiteral(@"                    done: function () {
                        menuTreeTb.expand(1);
                    }
                });

                // 工具列点击事件
                treeTable.on('tool(menuTreeTb)', function (obj) {
                    var event = obj.event;
                    var data = obj.data;
                    if (data.haveChildName) {
                        $(""#parentId"").attr(""haveChildName"", ""1"");
                    }
                    else {
                        $(""#parentId"").attr(""haveChildName"", ""0"");
                    }
                    if (event === 'seerole') {
                        table.render({
                            elem: '#tablelist_Role'
                            , id: 'tablelist_Role'
                            , url: ""/Menu/RoleList?MenuId="" + data.Id
                            , page: true
                            , cols: [[
                                { type: 'numbers', fixed: 'left' }
                                , { fi");
                WriteLiteral(@"eld: 'Name', title: '角色名称' }
                                , { field: 'Description', title: '角色描述' }
                            ]]
                        });
                        windowsIndex = layer.open({
                            type: 1,
                            title: '查看【' + data.menuName + '】角色',
                            area: '600px',
                            content: $('#form_window')
                        });
                    }
                    else if (event === 'add') {
                        $(""#fm"")[0].reset();
                        $('#Id').val(0);
                        $('#parentId').val(data.Id);
                        $(""#btn_reset"").show();
                        $('#menuType').html("""");
                        if (data.menuType == ""根模块"") {
                            var option = $(""<option>"").val('模块目录').text('模块目录');
                            $(""#menuType"").append(option);
                        }
                        else if (d");
                WriteLiteral(@"ata.menuType == ""模块目录"") {
                            var option = $(""<option>"").val('模块').text('模块');
                            $(""#menuType"").append(option);
                        }
                        else if (data.menuType == ""模块"") {
                            var option1 = $(""<option>"").val('功能').text('功能');
                            $(""#menuType"").append(option1);
                            var option2 = $(""<option>"").val('模块').text('模块');
                            $(""#menuType"").append(option2);
                        }
                        else if (data.menuType == ""功能"") {
                            $.messager.show({
                                title: '提示信息',
                                msg: '不能在功能下添加模块。'
                            });
                            return false;
                        }
                        $(""#menuType"").get(0).selectedIndex = 0;
                        form.render('select');
                        $(""#parentId"").attr");
                WriteLiteral(@"(""reload"", ""0"");
                        windowsIndex = layer.open({
                            type: 1,
                            title: '添加【' + data.menuName + '】子模块',
                            area: '550px',
                            maxHeight: 650,
                            content: $('#form_window_Menu')
                        });
                        url = '/Menu/CreateMenu';
                    }
                    else if (event === 'edit') {
                        $('#menuType').html("""");
                        var option = $(""<option>"").val(data.menuType).text(data.menuType);
                        $(""#menuType"").append(option);
                        form.render('select');
                        $(""#btn_reset"").hide();
                        form.val('fm', data);
                        $(""#parentId"").attr(""reload"", ""1"");
                        windowsIndex = layer.open({
                            type: 1,
                            title: '修改【' + data.me");
                WriteLiteral(@"nuName + '】模块',
                            area: '600px',
                            maxHeight: 650,
                            content: $('#form_window_Menu')
                        });
                        url = '/Menu/EditMenu';
                    }
                    else if (event === 'del') {
                        layer.confirm('确定要删除【' + data.menuName + '】模块吗？', {
                            title: '',
                            btn: ['确定', '取消'], //按钮
                            resize: false
                        }, function (index) {
                            $.post('/Menu/DelMenu', { id: data.Id, pid: data.parentId }, function (result) {
                                if (result.okMsg) {
                                    layer.msg(result.okMsg);
                                    currentIndex = -1;
                                    //menuTreeTb.reload(); //重载表格
                                    if (data.parentId === '' || data.parentId == undefined || data.");
                WriteLiteral(@"parentId === 0) {
                                        menuTreeTb.reload({ url: ""/Menu/MenuList"", where: { 'pid': -1 } }); //重载表格
                                    }
                                    else {
                                        menuTreeTb.refresh(data.parentId); //重载表格
                                    }
                                } else {
                                    layer.msg(result.errorMsg);
                                }
                            }, 'json');
                            layer.close(index);
                        });
                    }
                    else if (event === 'move') {
                        $(""#CopyMenuName"").val(data.menuName);
                        $(""#Move"")[0].checked = false;
                        form.render('checkbox', 'fm_copy')
                        getMoveMenu();
                        windowsIndex = layer.open({
                            type: 1,
                            title: '移动【");
                WriteLiteral(@"' + data.menuName + '】模块',
                            area: ['650px', '500px'],
                            content: $('#copy_window'),
                            btn: ['保存', '取消'],
                            yes: function (index, layero) {
                                var menuToId = xmTreeMenu.getValue('valueStr');
                                var oper = $(""#Move"")[0].checked;
                                if (!menuToId) {
                                    layer.msg(""请选择模块名称！"");
                                    return false;
                                }
                                $.ajax({
                                    url: '/Menu/Move',
                                    type: ""post"",
                                    data: {
                                        ""menuFromId"": data.Id,
                                        ""menuToId"": menuToId,
                                        ""oper"": oper
                                    },
                ");
                WriteLiteral(@"                    datatype: 'json',
                                    success: function (re) {

                                        if (re.status) {

                                            layer.msg(re.msg);
                                            if (oper)
                                                menuTreeTb.refresh(parseInt(menuToId));//重载表格
                                            else {
                                                //复制只刷新目标节点 移动刷新两个节点
                                                menuTreeTb.refresh(parseInt(menuToId))
                                                menuTreeTb.refresh(data.parentId)

                                            }
                                            layer.close(windowsIndex);//关闭弹出层

                                        }
                                        else {
                                            layer.msg(re.msg);
                                        }
                          ");
                WriteLiteral(@"          }, error: function (res) {
                                        layer.msg(""加载统计信息错误："" + res.responseText);
                                        layer.close(windowsIndex);
                                    }
                                });
                            },
                            cancel: function (index, layro) { },
                        });
                    }
                });

                $(document).ready(function () {


                });
                var xmTreeMenu = xmSelect.render({
                    el: '#treeMenu',
                    model: { label: { type: 'text' } },
                    prop: {
                        name: 'name',
                        value: 'id',
                    },
                    minWidth: 200,
                    radio: true,
                    filterable: true,
                    clickClose: true,
                    //树
                    tree: {
                        show: tr");
                WriteLiteral(@"ue,
                        //非严格模式
                        strict: false,
                        //默认展开节点
                        expandedKeys: [-1],
                    }
                    ,
                    data: []
                });

                //自定义验证规则
                form.verify({
                    intVer: [
                        /^[1-9][0-9]{0,}$/
                        , '请输入正整数'
                    ]
                });

                //监听提交
                form.on('submit(MenuSubmit)', function (data) {
                    console.log(data);
                    var indes = layer.load(1);
                    //提交 Ajax 成功后，关闭当前弹层并重载表格
                    $.ajax({
                        url: url,
                        type: ""post"",
                        data: { 'node': data.field },
                        datatype: 'json',
                        success: function (result) {
                            if (result.okMsg) {
                        ");
                WriteLiteral(@"        debugger;
                                layer.msg(result.okMsg);
                                layer.close(windowsIndex);//关闭弹出层
                                if ($(""#parentId"").attr(""reload"") == ""1"") {
                                    menuTreeTb.refresh(parseInt(data.field.parentId)); //重载表格
                                }
                                else {
                                    if ($(""#parentId"").attr(""haveChildName"") == ""1"") {
                                        menuTreeTb.refresh(parseInt(data.field.parentId)); //重载表格
                                    }
                                    else {
                                        menuTreeTb.reload({ url: ""/Menu/MenuList"", where: { 'pid': -1 } }); //重载表格
                                    }
                                }
                            }
                            else {
                                layer.msg(result.errorMsg);
                            }
              ");
                WriteLiteral(@"              layer.close(indes);
                        }, error: function (res) {
                            layer.msg(""加载统计信息错误："" + res.responseText);
                            layer.close(indes);
                        }
                    });
                    return false;
                });
                function getMoveMenu() {
                    $.get('/Menu/GetMenuAll', function (result) {

                        xmTreeMenu.update({
                            data: result
                        });
                    })
                }
            });
        </script>
    </div>
");
            }
            );
            __Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.Razor.TagHelpers.BodyTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_Razor_TagHelpers_BodyTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral(@"

<!-- 查看角色列表 -->
<div class=""window_wrap  layui-form"" id=""form_window"" style=""display: none"">
    <div class=""layui-card"">
        <div class=""layui-card-body"">
            <table id=""tablelist_Role"" lay-filter=""tablelist_Role""></table>
        </div>
    </div>
</div>

<!--新增/修改模块-->
<div class=""window_wrap"" id=""form_window_Menu"" style=""display: none;"">
    ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "74b2ab55bf28fec6f85ffc45d002c8669999ca8e9b8a444a03949da8371ae1d935947", async() => {
                WriteLiteral(@"
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">模块名称:</label>
            <div class=""layui-input-block"">
                <input type=""text"" name=""Id"" id=""Id"" style=""display:none;"" />
                <input type=""text"" name=""parentId"" id=""parentId"" haveChildName=""0"" reload=""0"" style=""display:none;"" />
                <input type=""text"" name=""menuName"" id=""menuName"" required lay-verify=""required"" autocomplete=""off"" class=""layui-input"">
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">模块类型:</label>
            <div class=""layui-input-block"">
                <select id=""menuType"" name=""menuType"" lay-filter=""menuType"">
                    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "74b2ab55bf28fec6f85ffc45d002c8669999ca8e9b8a444a03949da8371ae1d937035", async() => {
                    WriteLiteral("模块目录");
                }
                );
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_8.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_8);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n                </select>\r\n            </div>\r\n        </div>\r\n        <div class=\"layui-form-item\">\r\n            <label class=\"layui-form-label\">是否主菜单:</label>\r\n            <div class=\"layui-input-block\">\r\n");
                WriteLiteral(@"                <input type=""checkbox"" name=""isMenu"" lay-skin=""switch"" lay-text=""是|否"" />
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">区域名称:</label>
            <div class=""layui-input-block"">
                <input type=""text"" name=""menuArea"" id=""menuArea"" autocomplete=""off"" class=""layui-input"" />
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">控制器名称:</label>
            <div class=""layui-input-block"">
                <input type=""text"" name=""menuController"" id=""menuController"" autocomplete=""off"" class=""layui-input"" />
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">行为名称:</label>
            <div class=""layui-input-block"">
                <input type=""text"" name=""menuAction"" id=""menuAction"" autocomplete=""off"" class=""layui-input"" />
            </div>
        </div>
        <div class=""");
                WriteLiteral(@"layui-form-item"">
            <label class=""layui-form-label"">按钮Id:</label>
            <div class=""layui-input-block"">
                <input type=""text"" name=""buttonId"" id=""buttonId"" autocomplete=""off"" class=""layui-input"" />
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">排序:</label>
            <div class=""layui-input-block"">
                <input type=""text"" name=""menuOrder"" id=""menuOrder"" required lay-verify=""required|intVer"" autocomplete=""off"" class=""layui-input"" />
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">图标:</label>
            <div class=""layui-input-block"">
                <input type=""text"" name=""menuIcon"" id=""menuIcon"" autocomplete=""off"" class=""layui-input"" />
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">备注:</label>
            <div class=""layui-input-block"">
      ");
                WriteLiteral(@"          <textarea name=""menuNote"" id=""menuNote"" class=""layui-textarea"" style=""resize: none""></textarea>
            </div>
        </div>
        <div class=""layui-form-item"">
            <div class=""layui-input-block"">
                <button type=""submit"" class=""layui-btn"" lay-submit lay-filter=""MenuSubmit"">提交</button>
                <button type=""reset"" class=""layui-btn layui-btn-primary"" id=""btn_reset"">重置</button>
            </div>
        </div>
    ");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</div>\r\n<!--复制/移动弹框-->\r\n<div class=\"window_wrap\" id=\"copy_window\" style=\"display: none ;min-height:90%; position:relative;\">\r\n    ");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "74b2ab55bf28fec6f85ffc45d002c8669999ca8e9b8a444a03949da8371ae1d942808", async() => {
                WriteLiteral(@"
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">模块名称</label>
            <div class=""layui-input-block"">
                <input type=""text"" name=""CopyMenuName"" id=""CopyMenuName"" readonly=""readonly"" class=""layui-input"" />
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">操作方式</label>
            <div class=""layui-input-block"">
                <input type=""checkbox"" name=""Move"" id=""Move"" lay-skin=""switch"" lay-text=""复制|移动"">
                <input type=""hidden"" name=""MoveValue"" id=""MoveValue"">
            </div>
        </div>
        <div class=""layui-form-item"">
            <label class=""layui-form-label"">目标菜单</label>
            <div class=""layui-input-block"">
                <div id=""treeMenu"">

                </div>
            </div>
        </div>

    ");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_14);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_15);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n</div>\r\n</html>\r\n");
        }
        #pragma warning restore 1998
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; } = default!;
        #nullable disable
        #nullable restore
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; } = default!;
        #nullable disable
    }
}
#pragma warning restore 1591
