﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.33214.272
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AngelwinResearch.Library", "AngelwinResearch.Library\AngelwinResearch.Library.csproj", "{6A9CD88B-A95A-455D-84E2-49559F1CA78D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AngelwinResearch.WebUI", "AngelwinResearch.WebUI\AngelwinResearch.WebUI.csproj", "{D28ABF9B-0E3C-4370-8722-E30AAEBDCB69}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Common.Tools", "Common.Tools\Common.Tools.csproj", "{D141C258-72B3-42A1-82F2-92E4F060AB69}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Common.QuartzNet", "Common.QuartzNet\Common.QuartzNet.csproj", "{F86CC962-E701-43D5-8F8B-38138FBABD1A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Docs", "Docs", "{BFE7033A-A01C-4835-9E0D-3CADA011BF26}"
	ProjectSection(SolutionItems) = preProject
		Docs\数据库调整说明(省立AI平台).docx = Docs\数据库调整说明(省立AI平台).docx
		Docs\版本说明文件(省立AI平台).docx = Docs\版本说明文件(省立AI平台).docx
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SignalRTest", "SignalRTest\SignalRTest.csproj", "{79FBEB7A-8B92-4296-961A-D0B9D8404044}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6A9CD88B-A95A-455D-84E2-49559F1CA78D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6A9CD88B-A95A-455D-84E2-49559F1CA78D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6A9CD88B-A95A-455D-84E2-49559F1CA78D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6A9CD88B-A95A-455D-84E2-49559F1CA78D}.Release|Any CPU.Build.0 = Release|Any CPU
		{D28ABF9B-0E3C-4370-8722-E30AAEBDCB69}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D28ABF9B-0E3C-4370-8722-E30AAEBDCB69}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D28ABF9B-0E3C-4370-8722-E30AAEBDCB69}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D28ABF9B-0E3C-4370-8722-E30AAEBDCB69}.Release|Any CPU.Build.0 = Release|Any CPU
		{D141C258-72B3-42A1-82F2-92E4F060AB69}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D141C258-72B3-42A1-82F2-92E4F060AB69}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D141C258-72B3-42A1-82F2-92E4F060AB69}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D141C258-72B3-42A1-82F2-92E4F060AB69}.Release|Any CPU.Build.0 = Release|Any CPU
		{F86CC962-E701-43D5-8F8B-38138FBABD1A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F86CC962-E701-43D5-8F8B-38138FBABD1A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F86CC962-E701-43D5-8F8B-38138FBABD1A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F86CC962-E701-43D5-8F8B-38138FBABD1A}.Release|Any CPU.Build.0 = Release|Any CPU
		{79FBEB7A-8B92-4296-961A-D0B9D8404044}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{79FBEB7A-8B92-4296-961A-D0B9D8404044}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{79FBEB7A-8B92-4296-961A-D0B9D8404044}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{79FBEB7A-8B92-4296-961A-D0B9D8404044}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C4BDED91-E90C-42F6-B0D8-24D12FDDA6CD}
	EndGlobalSection
EndGlobal
